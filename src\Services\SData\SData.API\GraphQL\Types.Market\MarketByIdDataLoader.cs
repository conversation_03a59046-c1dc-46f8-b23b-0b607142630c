using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.SData.API.GraphQL.Types.Timezone;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using Euroland.FlipIT.SData.API.Infrastructure.Factories;
using Euroland.FlipIT.Shared.CachingManager;
using GreenDonut;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Market;

public class MarketByIdDataLoader : BatchDataLoader<short, MarketDto>
{
  private readonly ISharkDbContextAbstractFactory _factory;
  private readonly IGraphQLExecutionContext _queryRequestContext;
  private readonly IConfigurableCacheManager _configurableCacheManager;
  private readonly TimezoneByNameDataLoader _timezoneByNameDataLoader;

  public MarketByIdDataLoader(
    ISharkDbContextAbstractFactory factory,
    IGraphQLExecutionContext queryRequestContext,
    IConfigurableCacheManager configurableCacheManager,
    TimezoneByNameDataLoader timezoneByNameDataLoader,
    IBatchScheduler batchScheduler,
    DataLoaderOptions? options = null)
    : base(batchScheduler, options)
  {
    _factory = factory ?? throw new ArgumentNullException(nameof(factory));
    _queryRequestContext = queryRequestContext ?? throw new ArgumentNullException(nameof(queryRequestContext));
    _configurableCacheManager = configurableCacheManager ?? throw new ArgumentNullException(nameof(configurableCacheManager));
    _timezoneByNameDataLoader = timezoneByNameDataLoader ?? throw new ArgumentNullException(nameof(timezoneByNameDataLoader));
  }

  protected override async Task<IReadOnlyDictionary<short, MarketDto>> LoadBatchAsync(IReadOnlyList<short> keys, CancellationToken cancellationToken)
  {

    // Retreive Market DTO from the cache with provided Market IDs
    var marketDTOsFromCache = new List<MarketDto>();
    var marketIDsNeedToFetchNew = new List<int>();

    foreach (var mid in keys.Distinct())
    {
      var dto = _configurableCacheManager.GetCache<MarketDto, string>(
        CacheKeyHelper.GenerateKeyByPropOfDtoObject<MarketDto, short>(mid.ToString(), m => m.Id));

      if (dto != null)
      {
        marketDTOsFromCache.Add(dto);
      }
      else
      {
        marketIDsNeedToFetchNew.Add(mid);
      }
    }

    await using var sharkDbContext = await _factory.CreateDbContextAsync(_queryRequestContext.UseRealtime, _queryRequestContext.UseCloud);

    var data = marketIDsNeedToFetchNew.Count > 0
      ? await sharkDbContext.Market
        .AsNoTracking()
        .Where(m => marketIDsNeedToFetchNew.Contains(m.MarketNumber))
        .WithAutoMapper()
        .ToProjection<MarketDto>()
        .ToListAsync(cancellationToken)
      : Enumerable.Empty<MarketDto>();

    if (data.Any())
    {
      var cities = sharkDbContext.City.AsNoTracking();
      var countries = sharkDbContext.Country.AsNoTracking();
      var cityIds = data.Select(m => m.CityId).ToList();

      var cityCountryList = cities
        .Join(
          countries,
          cit => cit.CountryId,
          coun => coun.Id,
          (cit, coun) => new { cityId = cit.Id, countryName = coun.Name, countryCode = coun.Code }
        )
        .Where(c => cityIds.Contains(c.cityId))
        .ToList();

      var timezones = await _timezoneByNameDataLoader.LoadAsync(
        data.Where(m => !string.IsNullOrEmpty(m.TimezoneName))
          .Select(m => m.TimezoneName.Trim().ToLower())
          .Distinct()
          .ToList()
        , cancellationToken) ?? Enumerable.Empty<TimezoneDto>().ToList();

      // Add fetched Market DTOs to cache and merge it into returned data.
      foreach (var market in data)
      {
        var timezone = timezones.Where(tz => tz != null && string.Equals(tz.Name, market.TimezoneName, System.StringComparison.InvariantCultureIgnoreCase)).FirstOrDefault();

        // Try to set timezone name to correct name corresponding to market region
        if (timezone != null && cityCountryList.Any(c => c.cityId == market.CityId))
        {
          var c = cityCountryList.Where(c => c.cityId == market.CityId).Select(c => c).First();

          timezone.NameIANA = Timezone.WindowsTimezoneToIANA.Convert(timezone.Name.Trim(), c.countryName?.Trim(), c.countryCode?.Trim());
        }

        market.Timezone = timezone;
        _configurableCacheManager.SetCache(
          market,
          CacheKeyHelper.GenerateKeyByPropOfDtoObject<MarketDto, short>(market.Id.ToString(), m => m.Id)
        );

        marketDTOsFromCache.Add(market);
      }
    }

    return marketDTOsFromCache.ToDictionary(x => x.Id);
  }
}
