using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Country;

/// <summary>
/// Extends 'country' field to type <see cref="City.CityType"/>
/// </summary>
public class CityTypeExtensions : ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(City.CityType.Name);

    descriptor.Field("country")
      .UseFirstOrDefault()
      .UseProjection()
      .ResolveWith<CountryResolvers>(
        resolvers => resolvers.GetCountryByCity(default!, default!, default!, default)
      );
  }
}
