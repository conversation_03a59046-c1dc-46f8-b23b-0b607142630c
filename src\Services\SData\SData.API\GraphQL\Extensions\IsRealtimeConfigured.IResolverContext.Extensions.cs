using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Infrastructure;
using HotChocolate.Resolvers;
using Microsoft.AspNetCore.Http;

namespace Microsoft.Extensions.DependencyInjection;

internal static class IsRealtimeConfiguredIResolverContextExtensions
{
  public static async Task<bool> IsRealtimeConfigured(this IResolverContext resolverContext, int instrumentId)
  {
    var realtimeConfiguration = resolverContext.RequestServices.GetRequiredService<IInstrumentRealtimeConfiguration>();

    return await realtimeConfiguration.IsConfigured(instrumentId);
  }
}
