.disclaimer-popup {
  @apply absolute inset-0 flex items-center justify-center z-[1000001];
  background: rgba(0,0,0,0.5);
  backdrop-filter: blur(2px);
  
  &__container {
    @apply bg-white rounded-xl p-6 w-[90%] max-w-[500px] shadow-lg;
    animation: slideIn 0.3s ease;
  }
  
  &__header {
    @apply mb-4 flex items-center gap-3;
  }
  
  &__icon {
    @apply text-2xl text-primary;
  }
  
  &__title {
    @apply m-0 text-gray-800 text-lg font-semibold;
  }
  
  &__content {
    @apply text-gray-600 leading-relaxed mb-6 text-sm;
  }
  
  &__actions {
    @apply flex justify-end gap-3;
  }
  
  &__button {
    @apply px-4 py-2 rounded-md text-sm font-medium cursor-pointer transition-all duration-200;
    
    &--accept {
      @apply bg-primary text-white border-none;
      
      &:hover {
        @apply bg-opacity-90;
      }
    }
  }
}