image: mcr.microsoft.com/dotnet/sdk:latest

stages:
  - build
  - test
  - pre:deploy
  - deploy:qa
  - deploy:staging
  - deploy:production
  - post:deploy
  - release

# ### Define variables
#
variables:
  GIT_SUBMODULE_STRATEGY: normal
  GIT_SSL_NO_VERIFY: '1'
  # Overwrite::
  # Uses fetch strategy and shallow cloning for faster building
  # More detail about gitlab strategy can be
  # found at: https://docs.gitlab.com/ce/ci/yaml/README.html#git-strategy
  GIT_STRATEGY: fetch
  #GIT_DEPTH: 500, # default git depth is 50
  # 1) Name of directory where restore and build objects are stored.
  OBJECTS_DIRECTORY: 'obj'
  # 2) Name of directory used for keeping restored dependencies.
  NUGET_PACKAGES_DIRECTORY: '.nuget'
  # 3) A relative path to the source code from project repository root.
  SOURCE_CODE_PATH: 'src'

before_script:
  - 'node -v'
  - 'dotnet --list-sdks'
  - 'dotnet --version'

include:
  - local: .gitlab/ci/global.gitlab-ci.yml
  - local: .gitlab/ci/test.gitlab-ci.yml
  - local: .gitlab/ci/predeploy.gitlab-ci.yml
  - local: .gitlab/ci/postdeploy.gitlab-ci.yml
  - local: .gitlab/ci/release.gitlab-ci.yml
  - local: .gitlab/ci/review.gitlab-ci.yml
  - local: .gitlab/ci/build.gitlab-ci.yml
  - local: .gitlab/ci/staging.gitlab-ci.yml
  - local: .gitlab/ci/production.gitlab-ci.yml
