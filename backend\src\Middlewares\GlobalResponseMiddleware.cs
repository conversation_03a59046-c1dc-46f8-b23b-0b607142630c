﻿using AISearch.Models;
using Microsoft.AspNetCore.Http.Extensions;
using System.Net;
using System.Text.Json;

namespace Euroland.FlipIT.WiseSearch.Middlewares
{
    public class GlobalResponseMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<GlobalResponseMiddleware> _logger;

        public GlobalResponseMiddleware(RequestDelegate next, ILogger<GlobalResponseMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task Invoke(HttpContext context)
        {
            var headerNotHandle = new string[] { "text/event-stream", "application/x-ndjson" };

            // Handle static contents
            if(!context.Request.Path.StartsWithSegments("/api", StringComparison.InvariantCultureIgnoreCase)) {
              await _next(context);
              return;
            }
            if (context.Request.Path.StartsWithSegments("/api/Search/stream") ||
                headerNotHandle.Any(s => context.Request.Headers.Accept.ToString().Contains(s, StringComparison.OrdinalIgnoreCase)))
            {
                await _next(context);
                return;
            }

            var originalBodyStream = context.Response.Body;

            try
            {
                using var responseBody = new MemoryStream();
                context.Response.Body = responseBody;

                await _next(context);

                int statusCode = context.Response.StatusCode;
                string statusName = ((HttpStatusCode)statusCode).ToString();

                responseBody.Seek(0, SeekOrigin.Begin);
                string responseText = await new StreamReader(responseBody).ReadToEndAsync();
                object? responseData = null;

                if (!string.IsNullOrWhiteSpace(responseText))
                {
                    try
                    {
                        responseData = JsonSerializer.Deserialize<object>(responseText);
                    }
                    catch (JsonException)
                    {
                        responseData = responseText;
                    }
                }

                var globalResponse = new GlobalResponseModel(
                    requestUrl: context.Request.GetDisplayUrl(),
                    data: statusCode >= 400 ? null : responseData,
                    error: statusCode >= 400 ? statusName : null,
                    isSuccess: statusCode < 400,
                    httpStatusCode: statusCode,
                    message: statusCode >= 400 ? responseText : "Success"
                );

                context.Response.ContentType = "application/json";
                context.Response.Body = originalBodyStream;
                await JsonSerializer.SerializeAsync(context.Response.Body, globalResponse);
            }
            catch (InvalidDataException ex)
            {
                _logger.LogError(ex, "InvalidDataException");

                context.Response.Body = originalBodyStream;
                context.Response.Clear();
                context.Response.ContentType = "application/json";

                var errorResponse = new GlobalResponseModel(
                    requestUrl: context.Request.GetDisplayUrl(),
                    data: null,
                    error: "UnprocessableEntity",
                    isSuccess: false,
                    httpStatusCode: (int)HttpStatusCode.UnprocessableEntity,
                    message: ex.Message
                );

                context.Response.StatusCode = errorResponse.HttpStatusCode.Value;

                await context.Response.WriteAsync(JsonSerializer.Serialize(errorResponse));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unhandled exception occurred.");

                context.Response.Body = originalBodyStream;
                context.Response.Clear();
                context.Response.ContentType = "application/json";
                context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;

                var errorResponse = new GlobalResponseModel(
                    requestUrl: context.Request.GetDisplayUrl(),
                    data: null,
                    error: "InternalServerError",
                    isSuccess: false,
                    httpStatusCode: (int)HttpStatusCode.InternalServerError,
                    message: ex.Message
                );

                await context.Response.WriteAsync(JsonSerializer.Serialize(errorResponse));
            }
            finally
            {
                context.Response.Body = originalBodyStream;
            }
        }
    }
}
