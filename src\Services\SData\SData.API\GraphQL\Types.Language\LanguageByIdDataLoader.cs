using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using Euroland.FlipIT.Shared.CachingManager;
using Euroland.NetCore.ToolsFramework.Localization;
using GreenDonut;
using Microsoft.Extensions.Options;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Language;

public class LanguageByIdDataLoader : BatchDataLoader<int, LanguageDto>
{
  private readonly IOptions<JsonLocalizationOptions> _languageOptions;
  private readonly IConfigurableCacheManager _configurableCacheManager;

  public LanguageByIdDataLoader(
    IOptions<JsonLocalizationOptions> languageOptions,
    IConfigurableCacheManager configurableCacheManager,
    IBatchScheduler batchScheduler,
    DataLoaderOptions? options = null)
    : base(batchScheduler, options)
  {
    _languageOptions = languageOptions ?? throw new ArgumentNullException(nameof(languageOptions));
    _configurableCacheManager = configurableCacheManager ?? throw new ArgumentNullException(nameof(configurableCacheManager));
  }

  protected override async Task<IReadOnlyDictionary<int, LanguageDto>> LoadBatchAsync(IReadOnlyList<int> keys, CancellationToken cancellationToken)
  {
    var filterKeys = keys.Where(k =>
        _configurableCacheManager.GetCache<LanguageDto, string>(CacheKeyHelper.GenerateKeyByPropOfDtoObject<LanguageDto, int>(k.ToString(), c => c.Id)) ==
        null)
      .ToList();

    if (filterKeys.Count > 0)
    {
      var languageProvider = _languageOptions.Value.LanguageToCultureProvider;
      if (!languageProvider.Loaded)
      {
        languageProvider.Load();
      }

      var languages = languageProvider.AllSupportedCultures.Select(culture => languageProvider.GetLanguage(culture));
      var languageToCultures = languages
        .Where(p => p.SupportedCultures.Count > 0 && filterKeys.Contains(p.DbId))
        .Select(p =>
        {
          var cultureInfo = p.SupportedCultures.First();
          return new LanguageDto
          {
            Id = p.DbId,
            CultureCode = cultureInfo.Name,
            Name = cultureInfo.NativeName,
            NameEnglish = cultureInfo.EnglishName ?? cultureInfo.DisplayName
          };
        })
        .DistinctBy(p => p.Id)
        .ToList();

      foreach (var language in languageToCultures)
      {
        _configurableCacheManager.SetCache(
          language,
          CacheKeyHelper.GenerateKeyByPropOfDtoObject<LanguageDto, int>(language.Id.ToString(), c => c.Id)
        );
      }
    }

    var result = keys.Select(k =>
        _configurableCacheManager.GetCache<LanguageDto, string>(CacheKeyHelper.GenerateKeyByPropOfDtoObject<LanguageDto, int>(k.ToString(), c => c.Id)))
      .ToList();

    return await Task.Run(() => result.ToDictionary(p => p.Id), cancellationToken);
  }
}
