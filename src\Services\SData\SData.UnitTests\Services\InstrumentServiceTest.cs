﻿using Euroland.FlipIT.SData.API.Constants;
using Euroland.FlipIT.SData.API.DTOs.Deprecated;
using Euroland.FlipIT.SData.API.Helpers;
using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Services;
using Euroland.FlipIT.SData.API.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Unicode;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using Euroland.FlipIT.SData.API.Infrastructure.Factories;

namespace UnitTest.SData.Application.Services
{
    public class InstrumentServiceTest
    {
        // [Apr 22, 2024] binh.nguyen:
        // Since we updated instrumentService.GetHistoricalDataAsync() to use FromSqlRaw()
        // // which is not supported by InMemoryDatabase. So we temporary comment out on this test.
        //[Fact]
        //public async Task Get_InstrumentHistories_EmptyDataShouldBeReturned()
        //{
        //    // arrange
        //    var mockCache = new Mock<IDistributedCache>();
        //    var mockConfiguration = new Mock<IConfiguration>();

        //    var cacheSettings = new DistributedCachingConfiguration()
        //    {
        //        UseDistributedMemoryCache = true,
        //        UseRedisCache = false,
        //        ExpirationTime = 60,
        //        CacheSizeLimit = 100,
        //        CompactionPercentage = 0.1
        //    };

        //    var mockCacheSettings = new Mock<IOptions<DistributedCachingConfiguration>>();
        //    mockCacheSettings.Setup(s => s.Value).Returns(cacheSettings);

        //    var mockDbFactory = new Mock<ISharkDbContextFactory>();
        //    mockDbFactory.Setup(f => f.CreateDbContext(false))
        //                 .Returns(() => new DefaultSharkDbContext(new DbContextOptionsBuilder<DefaultSharkDbContext>()
        //                 .UseInMemoryDatabase("InMemoryTest")
        //                 .Options));

        //    var mockCurrencyRateService = new Mock<ICurrencyRateService>();

        //    var instrumentService = new InstrumentService(
        //        mockDbFactory.Object,
        //        mockCache.Object,
        //        mockCacheSettings.Object,
        //        mockConfiguration.Object,
        //        mockCurrencyRateService.Object);


        //    // act
        //    var data = await instrumentService.GetHistoricalDataAsync(
        //        instrumentId: It.IsAny<int>(),
        //        isIntraday: true,
        //        period: Period.ONE_MINUTE,
        //        fromDate: It.IsAny<DateTime>(),
        //        toDate: It.IsAny<DateTime>(),
        //        isRT: It.IsAny<bool>(),
        //        ct: CancellationToken.None);

        //    // assert
        //    Assert.True(data.Count() == 0);
        //}


        [Fact]
        public async Task Get_InstrumentHistories_By_InstrumentIds_Happy_Case()
        {
            // arrange
            var mockCache = new Mock<IDistributedCache>();
            var mockConfiguration = new Mock<IConfiguration>();

            var cacheSettings = new DistributedCachingConfiguration()
            {
                UseDistributedMemoryCache = true,
                UseRedisCache = false,
                ExpirationTime = 60,
                CacheSizeLimit = 100,
                CompactionPercentage = 0.1
            };

            var mockCacheSettings = new Mock<IOptions<DistributedCachingConfiguration>>();
            mockCacheSettings.Setup(s => s.Value).Returns(cacheSettings);

            var mockDbFactory = new Mock<IDbContextFactory<DefaultSharkDbContext>>();
            mockDbFactory.Setup(f => f.CreateDbContext())
                         .Returns(() => new DefaultSharkDbContext(new DbContextOptionsBuilder<DefaultSharkDbContext>()
                         .UseInMemoryDatabase("InMemoryTest")
                         .Options));

            var mockRealtimeDbFactory = new Mock<IDbContextFactory<RealtimeSharkDbContext>>();
            mockRealtimeDbFactory.Setup(f => f.CreateDbContext())
                         .Returns(() => new RealtimeSharkDbContext(new DbContextOptionsBuilder<RealtimeSharkDbContext>()
                         .UseInMemoryDatabase("InMemoryTest")
                         .Options));

            var mockCurrencyRateService = new Mock<ICurrencyRateService>();
            var instrumentService = new InstrumentService(
                mockDbFactory.Object,
                mockRealtimeDbFactory.Object,
                mockCache.Object,
                mockCacheSettings.Object,
                mockConfiguration.Object,
                mockCurrencyRateService.Object);

            // act
            var data = instrumentService.GetInstruments(
                instrumentIds: new List<int> { },
                dayPeriod: (int)Period.ONE_MINUTE,
                isRT: It.IsAny<bool>(),
                ct: CancellationToken.None);

            // assert
            Assert.True(data.Count() == 0);
        }

        [Fact]
        public async Task Get_InstrumentHistories_ListInstrumentHistoriesShouldBeReturned()
        {
            // arrange
            var mockCache = new Mock<IDistributedCache>();
            var mockConfiguration = new Mock<IConfiguration>();

            var cacheSettings = new DistributedCachingConfiguration()
            {
                UseDistributedMemoryCache = true,
                UseRedisCache = false,
                ExpirationTime = 60,
                CacheSizeLimit = 100,
                CompactionPercentage = 0.1
            };

            var mockCacheSettings = new Mock<IOptions<DistributedCachingConfiguration>>();
            mockCacheSettings.Setup(s => s.Value).Returns(cacheSettings);

            var mockDbFactory = new Mock<IDbContextFactory<DefaultSharkDbContext>>();
            mockDbFactory.Setup(f => f.CreateDbContext())
                         .Returns(() => new DefaultSharkDbContext(new DbContextOptionsBuilder<DefaultSharkDbContext>()
                         .UseInMemoryDatabase("InMemoryTest")
                         .Options));

            var mockRealtimeDbFactory = new Mock<IDbContextFactory<RealtimeSharkDbContext>>();
            mockRealtimeDbFactory.Setup(f => f.CreateDbContext())
                         .Returns(() => new RealtimeSharkDbContext(new DbContextOptionsBuilder<RealtimeSharkDbContext>()
                         .UseInMemoryDatabase("InMemoryTest")
                         .Options));

            // fix up some data
            using (var db = mockDbFactory.Object.CreateDbContext())
            {
                db.Set<InstrumentHistory>().Add(new InstrumentHistory()
                {
                    ID = 1,
                    InstrumentId = 1,
                    Volume = 1,
                    DateTime = DateTime.UtcNow.ToCEST()
                });
                await db.SaveChangesAsync();
            }


            var mockCurrencyRateService = new Mock<ICurrencyRateService>();

            var instrumentService = new InstrumentService(
                mockDbFactory.Object,
                mockRealtimeDbFactory.Object,
                mockCache.Object,
                mockCacheSettings.Object,
                mockConfiguration.Object,
                mockCurrencyRateService.Object);


            // act
            var intradayData = await instrumentService.GetHistoricalDataAsync(
                instrumentId: 1,
                isIntraday: false,
                period: Period.ONE_HOUR,
                fromDate: DateTime.MinValue,
                toDate: null,
                ct: CancellationToken.None);

            // assert
            Assert.True(intradayData.Count() > 0);
        }

    }
}
