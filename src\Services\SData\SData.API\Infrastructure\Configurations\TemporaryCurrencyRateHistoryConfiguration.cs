using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Euroland.FlipIT.SData.API.Infrastructure.Configurations;

public class TemporaryCurrencyRateHistoryConfiguration : IEntityTypeConfiguration<TemporaryCurrencyRateHistory>
{
  public const string TemporaryCurrencyRateHistoryTableName = "#TemporaryCurrencyRateHistory";
  public void Configure(EntityTypeBuilder<TemporaryCurrencyRateHistory> builder)
  {
    // Configure temporary table as a View to be ignored during migration.
    // Also the entity TemporaryCurrencyRateHistory is created as keyless
    builder
      .ToView(TemporaryCurrencyRateHistoryTableName)
      .HasAlternateKey(x => x.Id);

  }
}
