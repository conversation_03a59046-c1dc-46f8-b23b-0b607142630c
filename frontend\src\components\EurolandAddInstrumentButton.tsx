import { useAuth } from "./watchlist/hooks/useAuth";

const isIFrame = window.self !== window.top;
interface AddInstrumentWidgetProps {
  instrumentId: string;
}

const AddInstrumentWidget = ({ instrumentId }: AddInstrumentWidgetProps) => {
  const auth = useAuth();
  if (!auth.isAuthenticated) {
    return null;
  }

  return (
    <button
      className="add-instrument-button"
      onClick={() => {
        const component = window.euroland?.components.WatchlistAddInstrument({
          instrumentId: instrumentId,
        });

        if (!component) {
          console.error("euroland component not available");
          return;
        }

        const integrationLayoutPosition =
          window.xprops?.layout?.middle || "#middleLayout";

        if (isIFrame) {
          component.renderTo(window.parent, integrationLayoutPosition);
        } else {
          let middle = document.getElementById("middleLayout");
          if (!middle) {
            middle = document.createElement("div");
            middle.id = "middleLayout";
            document.body.appendChild(middle);
          }
          component.renderTo(window.parent, integrationLayoutPosition);
        }
      }}
    >
      Add to watchlist
    </button>
  );
};

export default AddInstrumentWidget;
