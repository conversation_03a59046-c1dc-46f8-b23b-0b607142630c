using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure;
using HotChocolate;
using HotChocolate.Resolvers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Attachment;

public class AttachmentResolvers
{
  public IQueryable<AttachmentDto> GetAttachmentsByPressRelease(
    [Parent] PressReleaseDto pressRelease,
    NewsContext newsContext)
  {
    return newsContext.Attachment
      .AsNoTracking()
      .Where(a => a.PressreleaseId == pressRelease.Id && a.IsInBlob)
      .WithAutoMapper()
      .ToProjection<AttachmentDto>();
  }

  public async Task<IEnumerable<AttachmentDto>> GetAttachmentsByPressReleaseNonQueryable(
    [Parent] PressReleaseDto pressRelease,
    AttachmentsByPressReleaseIdDataLoader dataLoader,
    IResolverContext resolverContext,
    CancellationToken cancellation = default)
    => await dataLoader.LoadAsync(pressRelease.Id, cancellation);
}
