using Microsoft.Extensions.Primitives;

namespace Euroland.FlipIT.ApiGateway
{
  public class ForwardRequestHttpClientHandler : DelegatingHandler
  {
    private static List<string> _forwardedHeaders = new List<string>();

    private readonly IHttpContextAccessor _httpContextAccessor;
    public ForwardRequestHttpClientHandler(IHttpContextAccessor httpContextAccessor, IConfiguration configuration){
        _httpContextAccessor = httpContextAccessor ?? throw new System.ArgumentNullException(nameof(httpContextAccessor));
        if(_forwardedHeaders.Count == 0) {
          var headers = configuration.GetSection("ForwaredHeaders").Get<string[]>();
          if(headers != null && headers.Length > 0) {
            _forwardedHeaders.AddRange(headers);
          }
        }
    }
    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
      var originalRequest = _httpContextAccessor.HttpContext?.Request;
      var queryString = originalRequest?.Query;
      if (queryString != null)
      {
        var newUriBuilder = new System.UriBuilder(request.RequestUri);
        newUriBuilder.Query = $"companycode={queryString["companycode"]}&lang={queryString["lang"]}&v={queryString["v"]}";

        request.RequestUri = newUriBuilder.Uri;
      }
      if(_forwardedHeaders.Count > 0 && originalRequest != null) {
        //request.Headers.TryAddWithoutValidation()
        foreach (var headerName in _forwardedHeaders)
        {
#pragma warning disable CS8602 // Dereference of a possibly null reference.
#pragma warning disable S2259 // Null pointers should not be dereferenced
          if (originalRequest.Headers.TryGetValue(headerName, out StringValues headerValue))
          {
            request.Headers.TryAddWithoutValidation(headerName, headerValue.ToString());
          }
#pragma warning restore S2259 // Null pointers should not be dereferenced
#pragma warning restore CS8602 // Dereference of a possibly null reference.
        }
      }
      return await base.SendAsync(request, cancellationToken);
    }
  }
}
