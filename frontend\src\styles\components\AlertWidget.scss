@use "../_rtl-mixins"as *;

/* Alert bubbles styles */
.wise-notifications-container {
  display: flex;
  height: 100%;
  max-width: 344px;
  padding: 16px 0px;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
  overflow: hidden;
}

.wise-notification-bubble {
  background: white;
  border-radius: 8px;
  padding: 12px 20px;
  margin: 0px 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  animation-duration: 0.3s;
  width: 300px;
  @include border-start(4px solid #0066ff!important);
}

.wise-notification-content {
  display: flex;
  gap: 8px;
  align-items: center;
}

.wise-notification-icon {
  color: #0066ff;
  fill: #0066ff;
  font-size: 1.2rem;
  height: 22px;
}

.wise-notification-text {
  color: #333;
  font-size: 0.9rem;
  font-weight: 500;
}

.wise-notification-close {
  fill: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: all 0.2s ease;
  height: 24px;
  width: 24px;
}

.wise-notification-close:hover {
  background: #f0f0f0;
  fill: #333;
}

.wise-notification-info {
  @include ltr {
    border-left-color: #0066ff!important;
   }

   @include rtl {
    border-right-color: #0066ff!important;
   }
}

.wise-notification-info .wise-notification-icon {
  color: #0066ff;
  fill: #0066ff;
}

.wise-notification-positive {
  @include ltr {
    border-left-color: #00C851!important;
   }

   @include rtl {
    border-right-color: #00C851!important;
   }
}

.wise-notification-positive .wise-notification-icon {
  color: #00C851;
  fill: #00C851;
}

.wise-notification-negative {
  @include ltr {
    border-left-color: #ff4444!important;
   }

   @include rtl {
    border-right-color: #ff4444!important;
   }
}

.wise-notification-negative .wise-notification-icon {
  color: #ff4444;
  fill: #ff4444;
}