using AutoMapper;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;

namespace Euroland.FlipIT.SData.API.AutoMapper.Profiles;

public class DividendMapperProfile : Profile
{
  public DividendMapperProfile()
  {
    CreateMap<Dividend, DividendDto>()
      .ForMember(d => d.ExDate, cfg => cfg.MapFrom(e => e.Date))
      .ForMember(d => d.ExchangeCurrency, cfg => cfg.MapFrom(e => e.ExchangeCurrency));

    CreateMap<TotalDividendPerShare, AnnualDividendDto>()
      .ForMember(d => d.InstrumentId, cfg => cfg.MapFrom(e => e.InstrumentId))
      .ForMember(d => d.Total, cfg => cfg.MapFrom(e => e.TotalDividend))
      .ForMember(d => d.Year, cfg => cfg.MapFrom(e => e.Year))
      .ForMember(d => d.Currency, cfg => cfg.MapFrom(e => e.Currency))
      .ForMember(d => d.ExchangeCurrency, cfg => cfg.MapFrom(e => e.ExchangeCurrency))
      .ForMember(d => d.Yield, cfg => cfg.MapFrom(e => e.Percentage));
  }
}
