﻿using HotChocolate;
using Microsoft.EntityFrameworkCore;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.SData.API.DTOs.Deprecated
{
    [GraphQLName("LatestShareTradesDto_Deprecated")]
    [Keyless]
    public class LatestShareTradesDto
    {
        [Column(name: "Date", TypeName = "datetime")]
        public DateTime Date { get; set; }
        [Column(name: "Close", TypeName = "money")]
        public decimal Close { get; set; }
        [Column(name: "Size", TypeName = "bigint")]
        public long? Size { get; set; }
    }
}
