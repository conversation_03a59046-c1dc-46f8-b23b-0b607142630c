using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.Fundamental.API.Persistance.ReportHub;

public class Report
{
  [Key]
  public long Id { get; set; }
  public int CompanyID{ get; set; }
  public string Title { get; set; }
  public int ReportTypeId{get; set; }
  public int ReportParentTypeId{get;set;}
  public bool IsActive{get;set;}
  public int Year{get;set;}
  public DateTime UploadedDate{get;set;}
  public string LanguageCode{get;set;}
  public string? LanguageName{get;set;}
  public string ThumbnailFileLocation{get;set;}
  public string? BigThumbnailFileLocation{get;set;}
  public string? SubTitle{get;set;}
  public string? FileLocation{get;set;}
}
