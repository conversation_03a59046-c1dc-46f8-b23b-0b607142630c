using System;
using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.CurrencyRate;

public class CurrencyRateType: ObjectType<CurrencyRateDto>
{
  public const string Name = "CurrencyRate";
  protected override void Configure(IObjectTypeDescriptor<CurrencyRateDto> descriptor)
  {

    descriptor.Name(Name);

    descriptor.Field(f => f.Id).IsProjected();
  }
}
