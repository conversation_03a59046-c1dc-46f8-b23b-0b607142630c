using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Infrastructure.Factories;
using Euroland.FlipIT.SData.API.Services;
using Euroland.FlipIT.SData.API.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;

// using Euroland.FlipIT.SData.API.Infrastructure.Intercepters;

namespace Euroland.FlipIT.SData.API.Helpers
{
  public static class StartupHelpers
  {
    public static void RegisterDbContextFactories(this IServiceCollection services, IConfiguration configuration)
    {
      services.AddPooledDbContextFactory<DefaultSharkDbContext>((provider, options) =>
      {
        options.UseSqlServer(configuration.GetConnectionString("SharkDb"));
        options.AddInterceptors(
          new IntradayQueryWithTempTableGroupByMinuteCacheDbCommandInterceptor(),
          new IntradayQueryWithTempTableGroupBySecondCacheDbCommandInterceptor(),
          new TemporaryCurrencyRateHistoryDbCommandInterceptor(),
          new CorporateActionAdjustmentFactorCTEDbCommandInterceptor(),
          new IntradayQueryLoggingDbCommandInterceptor(provider.GetRequiredService<ILogger<IntradayQueryLoggingDbCommandInterceptor>>())
        );
      });
      services.AddPooledDbContextFactory<RealtimeSharkDbContext>((provider, options) =>
      {
        options.UseSqlServer(configuration.GetConnectionString("SharkRealtimeDb"));
        options.AddInterceptors(
          new IntradayQueryWithTempTableGroupByMinuteCacheDbCommandInterceptor(),
          new IntradayQueryWithTempTableGroupBySecondCacheDbCommandInterceptor(),
          new TemporaryCurrencyRateHistoryDbCommandInterceptor(),
          new CorporateActionAdjustmentFactorCTEDbCommandInterceptor(),
          new IntradayQueryLoggingDbCommandInterceptor(provider.GetRequiredService<ILogger<IntradayQueryLoggingDbCommandInterceptor>>())
        );
      });
      services.AddPooledDbContextFactory<DefaultCloudSharkDbContext>((provider, options) =>
      {
        options.UseSqlServer(configuration.GetConnectionString("SharkDb"));
        options.AddInterceptors(
          new IntradayQueryWithTempTableGroupByMinuteCacheDbCommandInterceptor(),
          new IntradayQueryWithTempTableGroupBySecondCacheDbCommandInterceptor(),
          new TemporaryCurrencyRateHistoryDbCommandInterceptor(),
          new CorporateActionAdjustmentFactorCTEDbCommandInterceptor(),
          new IntradayQueryLoggingDbCommandInterceptor(provider.GetRequiredService<ILogger<IntradayQueryLoggingDbCommandInterceptor>>())
        );
      });

      services.AddSingleton<ISharkDbContextAbstractFactory, SharkDbContextAbstractFactory>();
      services.TryAddScoped<IInstrumentRealtimeConfiguration, SimpleInstrumentRealtimeConfiguration>();

      #region Deprecated
      // TODO: Deprecated, will be removed soon
      services.AddTransient<ISharkDbContext>(sp => sp.GetRequiredService<IDbContextFactory<DefaultSharkDbContext>>().CreateDbContext());
      services.AddTransient<ISharkDbContext>(sp => sp.GetRequiredService<IDbContextFactory<RealtimeSharkDbContext>>().CreateDbContext());
      services.AddTransient<ISharkDbContextFactory, SharkDbContextFactory>();

      #endregion Deprecated

      services.AddPooledDbContextFactory<NewsContext>(
          options => options.UseSqlServer(configuration.GetConnectionString("NewsDb")));
      services.AddPooledDbContextFactory<WebcastContext>(
          options => options.UseSqlServer(configuration.GetConnectionString("WebcastDb")));

      services.AddPooledDbContextFactory<TimescaleDbContext>(
        (provider, options) =>
        {
          options.UseNpgsql(configuration.GetConnectionString("TimescaleDb"));
          options.AddInterceptors(
            new IntradayQueryLoggingDbCommandInterceptor(provider
              .GetRequiredService<ILogger<IntradayQueryLoggingDbCommandInterceptor>>()),
            new TemporaryCurrencyRateHistoryDbCommandInterceptorTimescale()
          );
        });

      services.AddTransient<WebcastContext>(sp => sp.GetRequiredService<IDbContextFactory<WebcastContext>>().CreateDbContext());
    }

    public static void AddCurrencyService(this IServiceCollection services)
    {
      services.AddTransient<ICurrencyService, CurrencyService>();
    }
    public static void AddCurrencyRateService(this IServiceCollection services, IConfiguration configuration)
    {
      //services.Configure
      services.Configure<CurrencyTransformationConfiguration>(options => CreateCurrencyTransformationConfiguration(options, configuration));
      services.AddScoped<ICurrencyRateService, CurrencyRateService>();
      services.AddScoped<ICurrencyRateTransformationStrategy>(_ =>
      {
        using var scope = _.CreateScope();
        return scope.ServiceProvider.GetService<ICurrencyRateService>() as CurrencyRateService;
      });
    }

    private static void CreateCurrencyTransformationConfiguration(CurrencyTransformationConfiguration transformation, IConfiguration configuration)
    {
      var currencyTransformationSection = configuration.GetSection("CurrencyTransformation");
      //var transformation = new CurrencyTransformationConfiguration();

      if (currencyTransformationSection.Exists())
      {
        transformation.Enabled = currencyTransformationSection.GetValue<bool>("Enabled");
        var transformsSection = currencyTransformationSection.GetSection("Transforms").GetChildren();
        foreach (var transformSection in transformsSection)
        {
          var baseCurrency = transformSection["BaseCurrency"];
          var quoteCurrency = transformSection["QuoteCurrency"];
          if (
              !string.IsNullOrEmpty(baseCurrency) && baseCurrency.Length == 3
              && !string.IsNullOrEmpty(quoteCurrency) && quoteCurrency.Length == 3
          )
          {
            var customRate = transformSection.GetValue<float>("CustomRate");
            if (customRate != 0)
            {
              transformation.Transforms.Add(new CustomRateTransformation(baseCurrency, quoteCurrency, customRate));
            }
          }
          else if (!string.IsNullOrEmpty(baseCurrency) && string.IsNullOrEmpty(quoteCurrency))
          {
            var baseFactor = transformSection.GetValue<float>("Factor");
            var baseTransformTo = transformSection.GetValue<string>("TransformTo");
            if (baseFactor != 0 && !string.IsNullOrEmpty(baseTransformTo) && baseTransformTo.Length == 3)
            {
              transformation.Transforms.Add(new BaseCurrencyTransformation(baseCurrency, baseTransformTo, baseFactor));
            }
          }
          else if (string.IsNullOrEmpty(baseCurrency) && !string.IsNullOrEmpty(quoteCurrency))
          {
            var quoteFactor = transformSection.GetValue<float>("Factor");
            var quoteTransformTo = transformSection.GetValue<string>("TransformTo");
            if (quoteFactor != 0 && !string.IsNullOrEmpty(quoteTransformTo) && quoteTransformTo.Length == 3)
            {
              transformation.Transforms.Add(new QuoteCurrencyTransformation(quoteCurrency, quoteTransformTo, quoteFactor));
            }
          }
        }
      }
    }
  }
}
