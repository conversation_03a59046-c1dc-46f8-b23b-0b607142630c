using System;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
    [Keyless]
    public class ActiveFnTradesHistory_RT
    {
        public DateTime Date { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal Close { get; set; }
        [Column("Volume", TypeName = "bigint")]
        public long? Size { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal High { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal Low { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal Open { get; set; }
    }
}