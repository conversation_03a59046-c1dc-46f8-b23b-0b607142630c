﻿using Euroland.FlipIT.SData.API.Helpers;
using HotChocolate;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
  [GraphQLName("PressRelease_Deprecated")]
    public class PressRelease
    {
        [Column("prID")]
        public long Id { get; set; }

        [Column("prDate")]
        public DateTime DateTime { get; set; }

        [NotMapped]
        public string Date { get; set; }

        [Column("prCompanyCode")]
        public string CompanyCode { get; set; }

        [Column("prLanguageID")]
        public int LanguageId { get; set; }

        [Column("prMessage")]
        public string Message { get; set; }

        [Column("prTitle")]
        public string Title { get; set; }

        [ForeignKey("MessageType")]
        [Column("prMessageTypeId")]
        public int? MessageTypeId { get; set; }

        [Column("prMessageGroupId")]
        public int? MessageGroupId { get; set; }

        [Column("prAttachment")]
        public bool HasAttachment { get; set; }

        [Column("prSourceID")]
        public int SourceId { get; set; }

        [Column("prHidden")]
        public bool? IsHidden { get; set; }

        private DateTime _insertedDate;
        [Column("prInsertedDate")]
        public DateTime InsertedDate
        {
          get
          {
            return _insertedDate;
          }
          set
          {
            _insertedDate = value.CestToUtc();
          }
        }

        public virtual List<Attachment> Attachment { get; set; }
        public virtual MessageType? MessageType { get; set;}
    }
}
