using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure;
using HotChocolate;
using HotChocolate.Resolvers;
using HotChocolate.Types;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.OrderDepth;

/// <summary>
/// Extends the <see cref="Instrument.InstrumentType"/> with the fields of <see cref="OrderDepthType"/>
/// </summary>
public class InstrumentTypeExtensions : ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(Instrument.InstrumentType.Name);

    descriptor.Field("orderDepth")
      .ResolveWith<Resolvers>(p => p.GetOrderDepth(default!, default!, default!, default!));
  }

  private class Resolvers
  {
    public async Task<OrderDepthDto?> GetOrderDepth(
      IResolverContext resolverContext,
      OrderDepthByInstrumentIdDataLoader dataLoader,
      [Parent] InstrumentDto instrument,
      CancellationToken cancellationToken = default
    ) => await dataLoader.LoadAsync(instrument.Id, cancellationToken);
  }
}
