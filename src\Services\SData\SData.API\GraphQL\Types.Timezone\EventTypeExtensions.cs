using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Timezone;

/// <summary>
/// Extends 'timezone' field to type <see cref="FCEvent.FCEventType"/>
/// </summary>
public class EventTypeExtensions: ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(FCEvent.FCEventType.TypeName);

    descriptor.Field("timezone")
      .ResolveWith<TimezoneResolvers>(resolvers => resolvers.GetTimezoneByFCEvent(default!, default!, default!, default));
  }
}
