using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Data.Sorting;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.CurrencyRateHistory;

/// <summary>
/// Custom sort type to enable ordering on <see cref="CurrencyRateHistoryDto.Date"/> only.
/// </summary>
public class CurrencyRateHistorySortInputType : SortInputType<CurrencyRateHistoryDto>
{
  protected override void Configure(ISortInputTypeDescriptor<CurrencyRateHistoryDto> descriptor)
  {
    descriptor.BindFieldsExplicitly()
      .Field(f => f.Date).Type<DefaultSortEnumType>();
  }
}
