using Euroland.FlipIT.SData.API.DTOs.Deprecated;
using Euroland.FlipIT.SData.API.GraphQL.Deprecated.Companies;
using Euroland.FlipIT.SData.API.Constants;
using Euroland.FlipIT.SData.API.GraphQL.Deprecated.Instruments;
using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Infrastructure.Repositories;
using Euroland.FlipIT.SData.API.Services;
using Euroland.FlipIT.SData.API.Services.Interfaces;
using HotChocolate;
using HotChocolate.Execution;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Newtonsoft.Json;
using Snapshooter.Xunit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using UnitTest.SData.Application.Models;
using Xunit;
using Microsoft.Extensions.Configuration;

namespace UnitTest.SData.Application
{

    public class SDataTests
    {
        //[Fact]
        //public async Task Ensure_Schema_IsCorrect()
        //{
        //    /*
        //     * The test take the service collection and builds from it a schema
        //     * (only integrates the part needed that we want to snapshot)
        //     * we will print out the GraphQL SDL representation of the schema on which
        //     * we do a MatchSnapshot that will create in the first run a snapshot file
        //     * and will compare the SDL in consecutive runs against the snapshot file.
        //     */

        //    // arrange
        //    ISchema schema = await new ServiceCollection()
        //        .AddGraphQL()
        //        .AddQueryType(d => d.Name("Query"))
        //        .AddTypeExtension<InstrumentQueries>()
        //        .RegisterDbContext<SharkContext>(kind: HotChocolate.Data.DbContextKind.Pooled)
        //        .RegisterDbContext<NewsContext>(kind: HotChocolate.Data.DbContextKind.Pooled)
        //        .AddProjections()
        //        .InitializeOnStartup()
        //        .BuildSchemaAsync();

        //    // assert
        //    schema.Print().MatchSnapshot();
        //}

        [Fact]
        public async Task Query_Instruments_With_Invalid_Argument_Return_Error()
        {
            // arrange
            var serviceCollection = new ServiceCollection();

            IRequestExecutor executor = await serviceCollection
                .AddGraphQLServer()
                .AddQueryType(d => d.Name("Query"))
                .AddTypeExtension<InstrumentQueries>()
                .RegisterDbContext<DefaultSharkDbContext>(kind: HotChocolate.Data.DbContextKind.Pooled)
                .RegisterDbContext<NewsContext>(kind: HotChocolate.Data.DbContextKind.Pooled)
                .AddProjections()
                .InitializeOnStartup()
                .BuildRequestExecutorAsync();

            // act
            IExecutionResult result = await executor.ExecuteAsync(@"
                query QueryInstruments{
                  instruments(instrumentIds: [invalid_args]){
                    instrumentId,
                    ticker
                  }
                }"
            );

            string expected = "The specified argument value does not match the argument type.";
            var actual = JsonConvert.DeserializeObject<ErrorResult>(result.ToJson())
                .Errors.FirstOrDefault()?.Message;

            // assert
            Assert.Contains(expected, actual);
        }

        [Fact]
        public async Task Query_Instruments_Histories_With_Invalid_Period_Return_Error()
        {
            // arrange
            var serviceCollection = new ServiceCollection();
            serviceCollection.AddScoped<IInstrumentService, InstrumentService>();

            IRequestExecutor executor = await serviceCollection
                .AddGraphQLServer()
                .AddQueryType(d => d.Name("Query"))
                .AddTypeExtension<InstrumentQueries>()
                .RegisterDbContext<DefaultSharkDbContext>(kind: HotChocolate.Data.DbContextKind.Pooled)
                .RegisterDbContext<NewsContext>(kind: HotChocolate.Data.DbContextKind.Pooled)
                .AddProjections()
                .InitializeOnStartup()
                .BuildRequestExecutorAsync();

            // act
            IExecutionResult result = await executor.ExecuteAsync(@"
                query {
                  instrumentHistorical(
                        instrumentId: 1,
                        isIntraday: true,
                        period: INVALID_PERIOD,
                        fromDate: null,
                        toCurrency: null,
                        toDate: null)
                  {
                    open,
                    high
                  }
                }"
            );

            string expected = "The specified argument value does not match the argument type.";
            var actual = JsonConvert.DeserializeObject<ErrorResult>(result.ToJson())
                .Errors.FirstOrDefault()?.Message;

            // assert
            Assert.Contains(expected, actual);
        }

        [Fact]
        public async Task Query_InstrumentHistorical_With_Invalid_FromDate__EmptyShouldBeReturned()
        {
            // arrange
            var serviceCollection = new ServiceCollection();
            var instrumentHistories = new List<InstrumentHistory> {
                new InstrumentHistory
                {
                    Close = 1,
                    Open = 1,
                    High = 1,
                    Low = 1
                }
            };

            var mockInstrumentService = new Mock<IInstrumentService>();
            mockInstrumentService.Setup(m => m.GetHistoricalDataAsync(
                It.IsAny<int>(),
                It.IsAny<bool>(),
                Period.ONE_MINUTE,
                It.IsAny<DateTime?>(), // @fromDate
                It.IsAny<DateTime?>(), // @toDate,
                It.IsAny<Boolean>(),
                It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult<IEnumerable<InstrumentHistory>>(instrumentHistories));

            serviceCollection.AddScoped<IInstrumentService>(_ => mockInstrumentService.Object);

            IRequestExecutor executor = await serviceCollection
                .AddGraphQLServer()
                .AddQueryType(d => d.Name("Query"))
                .AddTypeExtension<InstrumentQueries>()
                .RegisterDbContext<DefaultSharkDbContext>(kind: HotChocolate.Data.DbContextKind.Pooled)
                .RegisterDbContext<NewsContext>(kind: HotChocolate.Data.DbContextKind.Pooled)
                .AddProjections()
                .InitializeOnStartup()
                .BuildRequestExecutorAsync();

            // act: try to test case fromDate < toDate
            var query = @"
                query {
                  instrumentHistorical(
                        instrumentId: 1,
                        isIntraday: true,
                        period: ONE_MINUTE,
                        fromDate: null,
                        toCurrency: null
                        toDate: null)
                  {
                    open,
                    high
                  }
                }";

            IExecutionResult result = await executor.ExecuteAsync(query);
            var actual = JsonConvert.DeserializeObject<InstrumentHistoriesResult>(result.ToJson());

            // assert
            Assert.True(actual.Data == null);
        }

        [Fact]
        public async Task Query_CompanyInfo_Data_With_CompanyCode__ShouldReturneResult()
        {
            // arrange
            var serviceCollection = new ServiceCollection();
            serviceCollection.AddScoped<IUnitOfWork, UnitOfWork<DefaultSharkDbContext>>();

            IRequestExecutor executor = await serviceCollection
                .AddGraphQLServer()
                .AddQueryType(d => d.Name("Query"))
                .AddTypeExtension<CompanyQueries>()
                .AddProjections()
                .InitializeOnStartup()
                .BuildRequestExecutorAsync();

            // act
            IExecutionResult result = await executor.ExecuteAsync(@"
                query company{
                  company(companyCode: ""dk-cbg""){
                  companyCode
                  companyName
                  }
                }"
            );

            var actual = JsonConvert.DeserializeObject<CompanyInfo>(result.ToJson());

            // assert
            Assert.True(actual is not default(CompanyInfo));
        }


        [Fact]
        public async Task Query_DividendEvents_With_Invalid_Argument_Return_Error()
        {
            // arrange
            var serviceCollection = new ServiceCollection();

            IRequestExecutor executor = await serviceCollection
                .AddGraphQLServer()
                .AddQueryType(d => d.Name("Query"))
                .AddTypeExtension<InstrumentQueries>()
                .RegisterDbContext<DefaultSharkDbContext>(kind: HotChocolate.Data.DbContextKind.Pooled)
                .RegisterDbContext<NewsContext>(kind: HotChocolate.Data.DbContextKind.Pooled)
                .AddProjections()
                .InitializeOnStartup()
                .BuildRequestExecutorAsync();

            // act
            IExecutionResult result = await executor.ExecuteAsync(@"
                query QueryDividends{
                  dividendEvents(instrumentId: invalid_args){
                    currency,
                    dividend
                  }
                }"
            );

            string expected = "The specified argument value does not match the argument type.";
            var actual = JsonConvert.DeserializeObject<ErrorResult>(result.ToJson())
                .Errors.FirstOrDefault()?.Message;

            // assert
            Assert.Contains(expected, actual);
        }

        [Fact]
        public async Task Query_EarningsEvents_With_Invalid_Argument_Return_Error()
        {
            // arrange
            var serviceCollection = new ServiceCollection();

            IRequestExecutor executor = await serviceCollection
                .AddGraphQLServer()
                .AddQueryType(d => d.Name("Query"))
                .AddTypeExtension<InstrumentQueries>()
                .RegisterDbContext<DefaultSharkDbContext>(kind: HotChocolate.Data.DbContextKind.Pooled)
                .RegisterDbContext<NewsContext>(kind: HotChocolate.Data.DbContextKind.Pooled)
                .AddProjections()
                .InitializeOnStartup()
                .BuildRequestExecutorAsync();

            // act
            IExecutionResult result = await executor.ExecuteAsync(@"
                query QueryEarningsEvents{
                  earningsEvents(companyCode: 0){
                    date,
                    heading
                  }
                }"
            );

            string expected = "The specified argument value does not match the argument type.";
            var actual = JsonConvert.DeserializeObject<ErrorResult>(result.ToJson())
                .Errors.FirstOrDefault()?.Message;

            // assert
            Assert.Contains(expected, actual);
        }

        [Fact]
        public async Task Query_PressReleases_With_Invalid_Argument_Return_Error()
        {
            // arrange
            var serviceCollection = new ServiceCollection();

            IRequestExecutor executor = await serviceCollection
                .AddGraphQLServer()
                .AddQueryType(d => d.Name("Query"))
                .AddTypeExtension<InstrumentQueries>()
                .RegisterDbContext<DefaultSharkDbContext>(kind: HotChocolate.Data.DbContextKind.Pooled)
                .RegisterDbContext<NewsContext>(kind: HotChocolate.Data.DbContextKind.Pooled)
                .AddProjections()
                .InitializeOnStartup()
                .BuildRequestExecutorAsync();

            // act
            IExecutionResult result = await executor.ExecuteAsync(@"
                query QueryPressReleases{
                  pressReleases(companyCode: invalid_ccode){
                    id,
                    title
                  }
                }"
            );

            string expected = "The specified argument value does not match the argument type.";
            var actual = JsonConvert.DeserializeObject<ErrorResult>(result.ToJson())
                .Errors.FirstOrDefault()?.Message;

            // assert
            Assert.Contains(expected, actual);
        }
    }
}
