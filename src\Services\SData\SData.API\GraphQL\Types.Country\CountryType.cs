using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Types;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Country;

public class CountryType : ObjectType<CountryDto>
{
  public const string Name = "Country";
  protected override void Configure(IObjectTypeDescriptor<CountryDto> descriptor)
  {
    descriptor.Name(Name);
    descriptor.Field(f => f.Id);
    descriptor.TranslationField();
  }
}
