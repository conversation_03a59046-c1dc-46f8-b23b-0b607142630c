﻿namespace Euroland.FlipIT.WiseSearch.Models.Authentication
{
    public class APIKeyAuthenticationContext
    {
        public string Username { get; set; } = string.Empty;

        public string? APIKey { get; set; } = string.Empty;
        public string? Timestamp { get; set; }
        public string? Signature { get; set; }

        public bool HasMissingRequiredFields(out List<string> missing)
        {
            missing = new();

            if (string.IsNullOrWhiteSpace(Timestamp)) missing.Add(nameof(Timestamp));
            if (string.IsNullOrWhiteSpace(Signature)) missing.Add(nameof(Signature));

            return missing.Count > 0;
        }
    }
}
