.Tabs {
  background-color: #ffffff;
  min-height: 100%;
  padding: 0;
  max-height: 100%;
  display: flex;
  flex-direction: column;
  
  &__container {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 3px;
  }

  &__tab {
    background: transparent;
    border: none;
    color: #0f0f0f;
    cursor: pointer;
    font-family: inherit;
    font-size: 14px;
    font-weight: 400;
    padding: 7px 14px;
    position: relative;
    transition: all 0.2s ease;
    white-space: nowrap;
    border-radius: 6px;

    &:hover {
      background-color: #f8f8f8;
      color: #0f0f0f;
    }

    &:focus {
      outline: none;
      background-color: #f8f8f8;
    }

    &--active {
      background-color: #dbdbdb;
      color: #0f0f0f;
      

      &:hover {
        background-color: #dbdbdb;
      }

      &:focus {
        background-color: #dbdbdb;
      }
    }
  }

  &__content {
    padding: 10px;
    border-top: 1px solid #e0e0e0;
    background-color: #ffffff;
    flex: 1;
    overflow: auto;
  }

  &__tab-content {
    width: 100%;
  }
}

