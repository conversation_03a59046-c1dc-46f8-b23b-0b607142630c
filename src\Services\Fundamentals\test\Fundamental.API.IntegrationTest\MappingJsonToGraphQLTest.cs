namespace Fundamental.API.IntegrationTest;

public class MappingJsonToGraphQLTest
    : IClassFixture<MockApplicationFactory<Program>>
{
    private readonly MockApplicationFactory<Program> _factory;
    private readonly HttpClient _httpClient;

    public MappingJsonToGraphQLTest(MockApplicationFactory<Program> factory)
    {
        _factory = factory;
        _httpClient = _factory.CreateClient();
    }
    
    [Fact]
    public void Test1()
    {

    }
}