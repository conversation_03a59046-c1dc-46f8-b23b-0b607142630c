using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.CustomerType;

/// <summary>
/// Extends 'customerType' field to type <see cref="Company.CompanyType"/>
/// </summary>
public class CompanyTypeExtensions : ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(Company.CompanyType.Name);

    descriptor.Field("customerType")
      .UseFirstOrDefault()
      .UseProjection()
      .ResolveWith<CustomerTypeResolvers>(
        resolvers => resolvers.GetCustomerTypeByCompany(default!, default!)
      );
  }
}
