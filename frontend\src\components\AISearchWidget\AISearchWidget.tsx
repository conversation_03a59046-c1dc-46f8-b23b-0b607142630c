import { FC, useEffect, useRef } from "react";
import { SearchIcon } from "@/components/Common";
import { XProps } from "@/zoid";
import clsx from "clsx";
import { useAppStore } from "@/store/useAppStore";
import {ICompanyInfo} from "@/services/apiEndpoints";
import {throttle} from "es-toolkit";
import {LayoutPosition} from "@/config/interface";
import { AISearchSettings } from "@/services/getAISearchSettings";
import {useSetupTheme} from "@/hooks/useSetupTheme";
export interface IAISearchWidgetProps extends XProps {
  companyInfo: ICompanyInfo
  position: LayoutPosition
  aiSearchSettings: AISearchSettings
}

/**
 * AISearchWidget Component.
 *
 * A functional React component that renders an AI-powered search widget.
 * This component leverages an animation function to expand and collapse the search input,
 * and it integrates with the global application state to manage notifications,
 * profile menu, and search widget visibility.
 */
const AISearchWidget: FC<IAISearchWidgetProps> = ({ animate, position, aiSearchSettings }) => {
  const setSearchWidgetOpen = useAppStore(
    (state) => state.searchWidget.setOpen
  );
  
  const isSearchWidgetOpen = useAppStore((state) => state.searchWidget.isOpen);


  const animateStateRef = useRef<'collapse' | 'expand'>('collapse')
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const el = containerRef.current;
    if(!el) return;
    const observer = new ResizeObserver(throttle((entries) => {
      const {width} = entries[0].contentRect;
      // sync button width to iframe width
      animate({
        duration: 0,
        width: [`${width}px`, `${width}px`]
      })
    }, 200));
    observer.observe(el, {box: 'border-box'});
    return () => observer.disconnect();
  }, [])

  useEffect(() => {
    const action = isSearchWidgetOpen ? 'expand' : 'collapse';
    if(animateStateRef.current === action) return;
    switch(action) {
      case 'expand':
        animate({
          easing: "linear",
          duration: 200,
          transform: 
            position === LayoutPosition.LEFT
            ? ["translateX(0vw)", "translateX(-100vw)"]
            : ["translateX(0vw)", "translateX(100vw)"],
        }).then(() => animateStateRef.current = 'expand');
        break;
      case 'collapse':
        animate({
          easing: "linear",
          duration: 200,
          transform: 
            position === LayoutPosition.LEFT
            ? ["translateX(-100vw)", "translateX(0vw)"]
            : ["translateX(100vw)", "translateX(0vw)"],
        }).then(() => animateStateRef.current = 'collapse');
        break;
      default:
        break
    }
    
  }, [isSearchWidgetOpen])

  useSetupTheme(aiSearchSettings)

  return (
    <div
      className={clsx("w-max", { opened: isSearchWidgetOpen })}
      ref={containerRef}
    >
      <button 
        onClick={() => setSearchWidgetOpen(!isSearchWidgetOpen)} 
        className={clsx(
          "flex rounded-full text-white py-5 px-6 text-sm justify-between items-center gap-3 transition-all duration-300", 
          // "relative overflow-hidden group"
          "wise-gradient-button overflow-hidden relative"
        )}
      >
          <span>
            <SearchIcon className="w-6 h-6" />
          </span>
          <span>{aiSearchSettings.placeholders}</span>
      </button>
    </div>
  );
};

export default AISearchWidget;
