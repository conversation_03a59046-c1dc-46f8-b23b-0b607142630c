import {useAuth} from "@/auth";
import {ZoidProfileMenu} from "@/components/ProfileMenu";
import {appSettings} from "@/config/appSettings";
import useZoidEvents from "@/hooks/useZoidEvents";
import {useAppStore} from "@/store/useAppStore"
import {useEffect} from "react";

const ProfileMenuControl = () => {
  const isOpen = useAppStore(state => state.profileMenu.isOpen);
  const setProfileMenuOpen = useAppStore(state => state.setProfileMenuOpen);
  const setExternalLinkOpen = useAppStore(state => state.confirm.setExternalLinkOpen)
  const setLogoutOpen = useAppStore(state => state.confirm.setLogoutOpen)
  const auth = useAuth();
  const profileMenu = ZoidProfileMenu.use({
    onLogout: () => setLogoutOpen(true),
    onExternalLink:  () => setExternalLinkOpen(true)
  }, {
    animate: {
      show: {
        duration: 200,
        easing: 'in-out-cubic',
        opacity: [0, 1],
        transform: appSettings.isRTL
          ? ['translateY(20px) translateX(-20px)', 'translateY(0px) translateX(0px)']
          : ['translateY(20px) translateX(20px)', 'translateY(0px) translateX(0px)']
      },
      close: {
        duration: 200,
        easing: 'in-out-cubic',
        opacity: [1, 0],
        transform: appSettings.isRTL
          ? ['translateY(0px) translateX(0px)', 'translateY(20px) translateX(-20px)']
          : ['translateY(0px) translateX(0px)', 'translateY(20px) translateX(20px)']
      }
    }
  });

  useZoidEvents(profileMenu, {
    'BACKDROP_CLICKED': () => setProfileMenuOpen(false)
  })

  useEffect(() => {
    if(isOpen) {
      profileMenu.show();
    } else {
      profileMenu.hide();
    }
  }, [isOpen]);

  useEffect(() => {
    if(!auth.isAuthenticated) {
      setProfileMenuOpen(false);
    }
  }, [auth.isAuthenticated]);
  return null
}

export default ProfileMenuControl