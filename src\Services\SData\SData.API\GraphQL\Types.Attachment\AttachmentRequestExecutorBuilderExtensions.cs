using Euroland.FlipIT.SData.API.GraphQL.Types.Attachment;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class AttachmentRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddAttachmentType(this IRequestExecutorBuilder builder)
  {
    builder.AddType<AttachmentType>();
    builder.AddTypeExtension<PressReleaseTypeExtensions>();

    builder.AddDataLoader<AttachmentsByPressReleaseIdDataLoader>();
    builder.AddDataLoader<AttachmentsByIdDataLoader>();

    return builder;
  }
}
