using System.Globalization;
using System.Linq;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure;
using HotChocolate;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.List;

public class ListResolvers
{
  public IQueryable<ListDto> GetListByInstrument(
    [Parent] InstrumentDto instrument,
    SharkDbContextBase sharkDbContext,
    string cultureName)
  {
    return sharkDbContext.List
      .AsNoTracking()
      .Where(c => c.ID == instrument.ListID)
      .WithAutoMapper()
      .ToProjection<ListDto>(new { cultureName });
  }
}
