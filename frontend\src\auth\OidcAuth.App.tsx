import { useEffect } from 'react';
import { Routes, Route, BrowserRouter } from 'react-router-dom';

import { LoginComponent } from './pages/Login';
import { LoginCallbackComponent } from './pages/LoginCallback';
import { LogoutComponent } from './pages/Logout';
import { LogoutCallbackComponent } from './pages/LogoutCallback';
import { OidcAuthAppRoutes } from './core/OidcAuthAppRoutes';
/**
 * OidcAuthApp component is responsible for handling the OpenID Connect (OIDC) authentication flow.
 * It sets the text direction of the document body based on the application settings and provides
 * routing for different authentication-related paths.
 *
 * Ideally, this component should be used as a top-level component in the application besign the App component to reduce
 * the number assets loaded in the application for signin/signout functionality.
 *
 * @returns {JSX.Element} The OidcAuthApp component.
 *
 * @remarks
 * - The `getBaseName` function retrieves the base URL from the document's base element and formats it.
 * - The `useEffect` hook sets the text direction of the document body to 'ltr' (left-to-right) and cleans up on unmount.
 * - The component uses `BrowserRouter` with a dynamic basename and defines routes for sign-in, sign-out, and their callbacks.
 *
 * @component
 * @example
 * ```tsx
 * import OidcAuthApp from './auth/OidcAuthApp';
 *
 * function App() {
 *   return <OidcAuthApp />;
 * }
 *
 * export default App;
 * ```
 */
interface OidcAuthAppProps {
  baseUrl: string;
}

export function OidcAuthApp({ baseUrl }: OidcAuthAppProps) {

  useEffect(() => {
    //document.body.setAttribute('dir', appSettings?.isRtl ? 'rtl' : 'ltr');
    document.body.setAttribute('dir', 'ltr');
    return () => {
      document.body.removeAttribute('dir');
    };
  }, []);

  return (
    <BrowserRouter basename={baseUrl}>
      <Routes>
        <Route path={OidcAuthAppRoutes.SIGNIN} element={<LoginComponent/>} />
        <Route path={OidcAuthAppRoutes.SIGNOUT} element={<LogoutComponent/>} />
        <Route path={OidcAuthAppRoutes.SIGNIN_CALLBACK} element={<LoginCallbackComponent/>} />
        <Route path={OidcAuthAppRoutes.SIGNOUT_CALLBACK} element={<LogoutCallbackComponent/>} />
      </Routes>
    </BrowserRouter>
  );
}
