using System.Collections.Generic;
using System.Linq;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure;
using HotChocolate;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.FinancialEvent;

public class FCEventResolvers
{
  public IQueryable<FCEventTypeDto> GetAllFCEventTypes(
    [Parent] CompanyDto company,
    SharkDbContextBase sharkDbContext,
    string cultureName
  )
  {
    var fCalendars = sharkDbContext.FCalendars
      .AsNoTracking()
      .Include(c => c.FCEvent)
      .Where(c => c.CompanyCode.Equals(company.Code.ToUpper()) && c.FCEvent != null)
      .Select(c => c.FCEvent!.TypeId);

    return sharkDbContext.FcEventTypes
      .AsNoTracking()
      .Where(et => fCalendars.Contains(et.Id))
      .WithAutoMapper()
      .ToProjection<FCEventTypeDto>(new { cultureName });
  }

  public IQueryable<FCalendarDto> GetFCEventsByCompany(
    [Parent] CompanyDto company,
    SharkDbContextBase sharkDbContext,
    string cultureName
  )
  {
    var result = sharkDbContext.FCalendars
      .FromSqlRaw(@"
      SELECT
        fc.[fc_ID],
        fc.[fc_cCode] ,
        fc.[fc_DateTime],
        fc.[fc_Event],
        fc.[fc_Location] ,
        fc.[fc_TimeZone],
        fc.[fc_FYEndMo],
        fc.[fc_LastUpdated],
        fc.[fc_Market],
        fc.[fc_MonthOnly],
        e.*
      FROM dbo.CS_FCalendar_V4 fc
      LEFT JOIN CS_FCEvents e ON fc.[fc_Event] = e.[fce_ID]
      WHERE fc.[fc_cCode] = @companyCode",
        new SqlParameter("companyCode", System.Data.SqlDbType.NVarChar) { Value = company.Code.ToUpper() })
      .AsNoTracking()
      .WithAutoMapper()
      .ToProjection<FCalendarDto>(new { cultureName });

    return result;
  }

  public IQueryable<FCalendarDto> GetFCEventByTypes(
    [Parent] CompanyDto company,
    SharkDbContextBase sharkDbContext,
    List<string> fcEventTypeNames,
    string cultureName
  )
  {
    var sqlParameters = new List<SqlParameter>
    {
      new("companyCode", System.Data.SqlDbType.NVarChar) { Value = company.Code }
    };

    var eventTypeParams = new List<string>();
    for (int i = 0; i < fcEventTypeNames.Count; i++)
    {
      var paramName = $"@eventType{i}";
      sqlParameters.Add(new SqlParameter(paramName[1..], System.Data.SqlDbType.NVarChar)
        { Value = fcEventTypeNames[i].ToLower() });
      eventTypeParams.Add(paramName);
    }

    var eventTypeList = string.Join(",", eventTypeParams);

    return sharkDbContext.FCalendars
      .FromSqlRaw($@"
            SELECT
                fc.[fc_ID],
                fc.[fc_cCode],
                fc.[fc_DateTime],
                fc.[fc_Event],
                fc.[fc_Location],
                fc.[fc_TimeZone],
                fc.[fc_FYEndMo],
                fc.[fc_LastUpdated],
                fc.[fc_Market],
                fc.[fc_MonthOnly],
                e.*
            FROM dbo.CS_FCalendar_V4 fc
            LEFT JOIN CS_FCEvents e ON fc.[fc_Event] = e.[fce_ID]
            LEFT JOIN CS_FCEventTypes et ON e.[fce_Type] = et.[fcet_ID]
            WHERE fc.[fc_cCode] = @companyCode
            AND LOWER(et.[fcet_english_Type]) IN ({eventTypeList})",
        sqlParameters.ToArray())
      .AsNoTracking()
      .WithAutoMapper()
      .ToProjection<FCalendarDto>(new { cultureName });
  }
}
