using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.CurrencyRateHistory;

public class CurrencyRateHistoryType: ObjectType<CurrencyRateHistoryDto>
{
  public const string Name = "CurrencyRateHistory";
  protected override void Configure(IObjectTypeDescriptor<CurrencyRateHistoryDto> descriptor)
  {
    descriptor.Name(Name);

    descriptor.Field(f => f.CurrencyRateId).IsProjected();
  }
}

