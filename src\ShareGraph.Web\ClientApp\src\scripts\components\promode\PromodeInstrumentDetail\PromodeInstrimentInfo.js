import { useEffect, useState } from "react";
import { useInstrumentDetail } from "./service/instrument-info-service";
import "./PromodeInstrimentInfo.scss";
import { formatNumber, formatPercentage, formatVolume, getSelectedInstrumentId } from "./utils";
const PromodeInstrumentDetail = () => {
  const [selectedInstrumentId, setSelectedInstrumentId] = useState(
    getSelectedInstrumentId()
  );
  const { data, loading, error } = useInstrumentDetail(selectedInstrumentId);

  useEffect(() => {
    const handleAuthChange = () => {
      setSelectedInstrumentId(getSelectedInstrumentId());
    };

    window.EurolandAppContext?.on("instrument-selected", handleAuthChange);

    return () => {
      window.EurolandAppContext?.off("instrument-selected", handleAuthChange);
    };
  }, []);

  if (loading) {
    return (
      <div className="promode-instrument-detail">
        <div className="loading-skeleton">
          <div className="skeleton-header"></div>
          <div className="skeleton-content">
            <div className="skeleton-line"></div>
            <div className="skeleton-line"></div>
            <div className="skeleton-line"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="promode-instrument-detail">
        <div className="error-state">
          <div className="error-icon">⚠</div>
          <div className="error-message">Failed to load instrument data</div>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="promode-instrument-detail">
        <div className="empty-state">
          <div className="empty-icon">📊</div>
          <div className="empty-message">Select an instrument to view info</div>
        </div>
      </div>
    );
  }

  const { shareName, symbol, market, currency, currentPrice, fifty_two_weeks } = data;
  const tickerData = currentPrice?.tickerData;
  const isPositive = tickerData?.change >= 0;

  return (
    <div className="promode-instrument-detail">
      <div className="instrument-header">
        <div className="instrument-title">
          <h2 className="instrument-name">{shareName}</h2>
          <div className="instrument-meta">
            <span className="symbol">{symbol}</span>
            {market?.translation?.value && (
              <span className="market">{market.translation.value}</span>
            )}
            {currency?.code && (
              <span className="currency">{currency.code}</span>
            )}
          </div>
        </div>
      </div>

      <div className="price-section">
        <div className="current-price">
          <span className="price-value">
            {formatNumber(tickerData?.last || currentPrice?.officialClose)}
          </span>
          <span className="currency-code">{currency?.code}</span>
        </div>
        <div className={`price-change ${isPositive ? "positive" : "negative"}`}>
          <span className="change-value">
            {formatNumber(tickerData?.change, 2)}
          </span>
          <span className="change-percentage">
            ({formatPercentage(tickerData?.changePercentage)})
          </span>
        </div>
      </div>

      <div className="market-data-grid">
        <div className="data-row">
          <div className="data-item">
            <span className="label">Open</span>
            <span className="value">{formatNumber(currentPrice?.open)}</span>
          </div>
          <div className="data-item">
            <span className="label">High</span>
            <span className="value">{formatNumber(currentPrice?.high)}</span>
          </div>
        </div>
        
        <div className="data-row">
          <div className="data-item">
            <span className="label">Low</span>
            <span className="value">{formatNumber(currentPrice?.low)}</span>
          </div>
          <div className="data-item">
            <span className="label">Prev Close</span>
            <span className="value">{formatNumber(tickerData?.prevClose)}</span>
          </div>
        </div>

        <div className="data-row">
          <div className="data-item">
            <span className="label">Volume</span>
            <span className="value">{formatVolume(currentPrice?.volume)}</span>
          </div>
          <div className="data-item">
            <span className="label">Bid/Ask</span>
            <span className="value">
              {formatNumber(currentPrice?.bid)}/{formatNumber(currentPrice?.ask)}
            </span>
          </div>
        </div>
      </div>

      {fifty_two_weeks && (
        <div className="week-range-section">
          <div className="section-title">52 Week Range</div>
          <div className="range-container">
            <div className="range-values">
              <span className="range-low">{formatNumber(fifty_two_weeks.lowest)}</span>
              <span className="range-high">{formatNumber(fifty_two_weeks.highest)}</span>
            </div>
            <div className="range-bar">
              <div className="range-track">
                <div 
                  className="range-indicator"
                  style={{
                    left: `${((tickerData?.last || currentPrice?.officialClose) - fifty_two_weeks.lowest) / 
                           (fifty_two_weeks.highest - fifty_two_weeks.lowest) * 100}%`
                  }}
                ></div>
              </div>
            </div>
            <div className="range-performance">
              <span className={`performance ${fifty_two_weeks.changePercentage >= 0 ? "positive" : "negative"}`}>
                {formatPercentage(fifty_two_weeks.changePercentage)} (52W)
              </span>
            </div>
          </div>
        </div>
      )}

      <div className="last-updated">
        {currentPrice?.date && (
          <span>Last updated: {new Date(currentPrice.date).toLocaleString()}</span>
        )}
      </div>
    </div>
  );
};

export default PromodeInstrumentDetail;
