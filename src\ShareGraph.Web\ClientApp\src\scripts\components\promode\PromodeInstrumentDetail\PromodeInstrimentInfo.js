import { useEffect, useState } from "react";
import { useInstrumentDetail } from "./service/instrument-info-service";
import "./PromodeInstrimentInfo.scss";
import { formatNumber, formatPercentage, formatVolume, getSelectedInstrumentId } from "./utils";
const PromodeInstrumentDetail = () => {
  const [selectedInstrumentId, setSelectedInstrumentId] = useState(
    getSelectedInstrumentId()
  );
  const { data, loading, error } = useInstrumentDetail(selectedInstrumentId);

  useEffect(() => {
    const handleAuthChange = () => {
      setSelectedInstrumentId(getSelectedInstrumentId());
    };

    window.EurolandAppContext?.on("instrument-selected", handleAuthChange);

    return () => {
      window.EurolandAppContext?.off("instrument-selected", handleAuthChange);
    };
  }, []);

  if (loading) {
    return (
      <div className="promode-instrument-detail">
        <div className="promode-instrument-detail__loading-skeleton">
          <div className="promode-instrument-detail__skeleton-header"></div>
          <div className="promode-instrument-detail__skeleton-content">
            <div className="promode-instrument-detail__skeleton-line"></div>
            <div className="promode-instrument-detail__skeleton-line"></div>
            <div className="promode-instrument-detail__skeleton-line"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="promode-instrument-detail">
        <div className="promode-instrument-detail__error-state">
          <div className="promode-instrument-detail__error-icon">⚠</div>
          <div className="promode-instrument-detail__error-message">Failed to load instrument data</div>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="promode-instrument-detail">
        <div className="promode-instrument-detail__empty-state">
          <div className="promode-instrument-detail__empty-icon">📊</div>
          <div className="promode-instrument-detail__empty-message">Select an instrument to view info</div>
        </div>
      </div>
    );
  }

  const { shareName, symbol, market, currency, currentPrice, fifty_two_weeks } = data;
  const tickerData = currentPrice?.tickerData;
  const isPositive = tickerData?.change >= 0;

  return (
    <div className="promode-instrument-detail">
      <div className="promode-instrument-detail__header">
        <div className="promode-instrument-detail__title">
          <h2 className="promode-instrument-detail__name">{shareName}</h2>
          <div className="promode-instrument-detail__meta">
            <span className="promode-instrument-detail__symbol">{symbol}</span>
            {market?.translation?.value && (
              <span className="promode-instrument-detail__market">{market.translation.value}</span>
            )}
            {currency?.code && (
              <span className="promode-instrument-detail__currency">{currency.code}</span>
            )}
          </div>
        </div>
      </div>

      <div className="promode-instrument-detail__price-section">
        <div className="promode-instrument-detail__current-price">
          <span className="promode-instrument-detail__price-value">
            {formatNumber(tickerData?.last || currentPrice?.officialClose)}
          </span>
          <span className="promode-instrument-detail__currency-code">{currency?.code}</span>
        </div>
        <div className={`promode-instrument-detail__price-change ${isPositive ? "promode-instrument-detail__price-change--positive" : "promode-instrument-detail__price-change--negative"}`}>
          <span className="promode-instrument-detail__change-value">
            {formatNumber(tickerData?.change, 2)}
          </span>
          <span className="promode-instrument-detail__change-percentage">
            ({formatPercentage(tickerData?.changePercentage)})
          </span>
        </div>
      </div>

      <div className="promode-instrument-detail__market-data-grid">
        <div className="promode-instrument-detail__data-row">
          <div className="promode-instrument-detail__data-item">
            <span className="promode-instrument-detail__label">Open</span>
            <span className="promode-instrument-detail__value">{formatNumber(currentPrice?.open)}</span>
          </div>
          <div className="promode-instrument-detail__data-item">
            <span className="promode-instrument-detail__label">High</span>
            <span className="promode-instrument-detail__value">{formatNumber(currentPrice?.high)}</span>
          </div>
        </div>

        <div className="promode-instrument-detail__data-row">
          <div className="promode-instrument-detail__data-item">
            <span className="promode-instrument-detail__label">Low</span>
            <span className="promode-instrument-detail__value">{formatNumber(currentPrice?.low)}</span>
          </div>
          <div className="promode-instrument-detail__data-item">
            <span className="promode-instrument-detail__label">Prev Close</span>
            <span className="promode-instrument-detail__value">{formatNumber(tickerData?.prevClose)}</span>
          </div>
        </div>

        <div className="promode-instrument-detail__data-row">
          <div className="promode-instrument-detail__data-item">
            <span className="promode-instrument-detail__label">Volume</span>
            <span className="promode-instrument-detail__value">{formatVolume(currentPrice?.volume)}</span>
          </div>
          <div className="promode-instrument-detail__data-item">
            <span className="promode-instrument-detail__label">Bid/Ask</span>
            <span className="promode-instrument-detail__value">
              {formatNumber(currentPrice?.bid)}/{formatNumber(currentPrice?.ask)}
            </span>
          </div>
        </div>
      </div>

      {fifty_two_weeks && (
        <div className="promode-instrument-detail__week-range-section">
          <div className="promode-instrument-detail__section-title">52 Week Range</div>
          <div className="promode-instrument-detail__range-container">
            <div className="promode-instrument-detail__range-values">
              <span className="promode-instrument-detail__range-low">{formatNumber(fifty_two_weeks.lowest)}</span>
              <span className="promode-instrument-detail__range-high">{formatNumber(fifty_two_weeks.highest)}</span>
            </div>
            <div className="promode-instrument-detail__range-bar">
              <div className="promode-instrument-detail__range-track">
                <div
                  className="promode-instrument-detail__range-indicator"
                  style={{
                    left: `${((tickerData?.last || currentPrice?.officialClose) - fifty_two_weeks.lowest) /
                           (fifty_two_weeks.highest - fifty_two_weeks.lowest) * 100}%`
                  }}
                ></div>
              </div>
            </div>
            <div className="promode-instrument-detail__range-performance">
              <span className={`promode-instrument-detail__performance ${fifty_two_weeks.changePercentage >= 0 ? "promode-instrument-detail__performance--positive" : "promode-instrument-detail__performance--negative"}`}>
                {formatPercentage(fifty_two_weeks.changePercentage)} (52W)
              </span>
            </div>
          </div>
        </div>
      )}

      <div className="promode-instrument-detail__last-updated">
        {currentPrice?.date && (
          <span>Last updated: {new Date(currentPrice.date).toLocaleString()}</span>
        )}
      </div>
    </div>
  );
};

export default PromodeInstrumentDetail;
