using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Timezone;

/// <summary>
/// Extends 'timezone' field to type <see cref="Market.MarketType"/>
/// </summary>
public class MarketTypeExtensions : ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(Market.MarketType.Name);

    descriptor.Field("timezone")
      .ResolveWith<TimezoneResolvers>(resolvers => resolvers.GetTimezoneByMarket(default!, default!, default!, default));
  }
}
