import { StateCreator } from 'zustand';
import {IStoreState} from './useAppStore';
import {produce} from 'immer';

export interface IProfileMenuSlice {
  profileMenu: {
    isOpen: boolean
  },
  setProfileMenuOpen: (isOpen: boolean) => void
}

const profileMenuSlice: StateCreator<IStoreState, [], [], IProfileMenuSlice> = set => ({
  profileMenu: {
    isOpen: false
  },
  setProfileMenuOpen: (isOpen) => set(state => produce(state, draft => {
    draft.profileMenu.isOpen = isOpen
  }))
});

export default profileMenuSlice