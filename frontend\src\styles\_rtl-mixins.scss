// RTL Mixins
@mixin rtl {
  [dir="rtl"] & {
    @content;
  }
}

@mixin ltr {
  [dir="ltr"] & {
    @content;
  }
}

// Directional properties
@mixin margin-start($value) {
  margin-left: $value;
  @include rtl {
    margin-right: $value;
    margin-left: initial;
  }
}

@mixin margin-end($value) {
  margin-right: $value;
  @include rtl {
    margin-left: $value;
    margin-right: initial;
  }
}

@mixin padding-start($value) {
  padding-left: $value;
  @include rtl {
    padding-right: $value;
    padding-left: initial;
  }
}

@mixin padding-end($value) {
  padding-right: $value;
  @include rtl {
    padding-left: $value;
    padding-right: initial;
  }
}

// Border directional properties
@mixin border-start($value) {
  border-left: $value;
  @include rtl {
    border-right: $value;
    border-left: none;
  }
}

@mixin border-end($value) {
  border-right: $value;
  @include rtl {
    border-left: $value;
    border-right: none;
  }
}

// Transform for RTL
@mixin transform-rtl($value) {
  transform: $value;
  @include rtl {
    transform: scaleX(-1) $value;
  }
}

$breakpoint-small: 576px;
$breakpoint-medium: 768px;
$breakpoint-large: 992px;
$breakpoint-xlarge: 1200px;
$breakpoint-xxlarge: 1400px;

// Breakpoint mixins
@mixin breakpoint($point) {
  @if $point == $breakpoint-small {
    @media (min-width: 576px) { @content; }
  }
  @else if $point == $breakpoint-medium {
    @media (min-width: 768px) { @content; }
  }
  @else if $point == $breakpoint-large {
    @media (min-width: 992px) { @content; }
  }
  @else if $point == $breakpoint-xlarge {
    @media (min-width: 1200px) { @content; }
  }
  @else if $point == $breakpoint-xxlarge {
    @media (min-width: 1400px) { @content; }
  }
  @else {
    @media (min-width: $point) { @content; }
  }
}
