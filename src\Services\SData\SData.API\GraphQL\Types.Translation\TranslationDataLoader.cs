using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using Euroland.FlipIT.Shared.CachingManager;
using Euroland.NetCore.ToolsFramework.Translation;
using GreenDonut;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Translation;

public class TranslationDataLoader : GroupedDataLoader<int, TranslationDto>
{
  private readonly ITranslationService _translationService;
  private readonly IServiceProvider _serviceProvider;

  public TranslationDataLoader(
    ITranslationService translationService,
    IBatchScheduler batchScheduler, IServiceProvider serviceProvider, DataLoaderOptions? options = null
  ) : base(batchScheduler, options)
  {
    _translationService = translationService ?? throw new ArgumentNullException(nameof(translationService));
    _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
  }

  protected override async Task<ILookup<int, TranslationDto>> LoadGroupedBatchAsync(IReadOnlyList<int> keys,
    CancellationToken cancellationToken)
  {
    var cache = _serviceProvider.GetRequiredService<IConfigurableCacheManager>();

    var filterKeys = new List<int>();
    var cachedTranslations = new List<NetCore.ToolsFramework.Translation.Translation>();

    foreach (var key in keys)
    {
      var cachedItem = cache.GetCache<NetCore.ToolsFramework.Translation.Translation, string>(
        CacheKeyHelper.GenerateKeyByPropOfObject<NetCore.ToolsFramework.Translation.Translation, int>(
          key.ToString(), t => t.Id));

      if (cachedItem != null)
      {
        cachedTranslations.Add(cachedItem);
      }
      else
      {
        filterKeys.Add(key);
      }
    }

    if (filterKeys.Count > 0)
    {
      var newTranslations = _translationService.GetTranslation(filterKeys);

      foreach (var translation in newTranslations)
      {
        cache.SetCache(
          translation,
          CacheKeyHelper.GenerateKeyByPropOfObject<NetCore.ToolsFramework.Translation.Translation, int>(
            translation.Id.ToString(), t => t.Id)
        );
        cachedTranslations.Add(translation);
      }
    }

    return await Task.Run(() => cachedTranslations
      .SelectMany(t =>
        t.TranslationMap
          .Select(m => new TranslationDto { Id = t.Id, CultureName = m.Key, Value = m.Value })
      ).ToLookup(t => t.Id), cancellationToken);
  }
}
