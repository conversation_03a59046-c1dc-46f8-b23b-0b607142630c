.default-retry:
  retry:
    max: 2  # This is confusing but this means "3 runs at max".
    when:
      - unknown_failure
      - api_failure
      - runner_system_failure
      - job_execution_timeout
      - stuck_or_timeout_failure

.prevent-fetching:
  variables:
    GIT_STRATEGY: none
    GIT_CHECKOUT: "false"

.disable-artifact-passing:
  dependencies: []

# Pack deployment resources into *.zip files of deployment packages
.pack-deployment:
  after_script:
    - 'call "./build/zip.bat" --output "src/Services/SData/SData.API/bin/Release/net6.0/publish/SData.API.zip" --input "src/Services/SData/SData.API/bin/Release/net6.0/publish/*"'
    - 'call "./build/zip.bat" --output "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/publish/Fundamentals.API.zip" --input "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/publish/*"'
    - 'call "./build/zip.bat" --output "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/publish/ApiGateway.API.zip" --input "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/publish/*"'
  artifacts:
    expose_as: 'publish-package'
    untracked: false
    paths:
      - 'src/Services/SData/SData.API/bin/Release/net6.0/publish/SData.API.zip'
      - 'src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/publish/Fundamentals.API.zip'
      - 'src/ApiGateway/ApiGateway.API/bin/Release/net6.0/publish/ApiGateway.API.zip'

.unpack-deployment:
  before_script:
    - 'call "./build/zip.bat" --unzip --input "src/Services/SData/SData.API/bin/Release/net6.0/publish/SData.API.zip" --output "src/Services/SData/SData.API/bin/Release/net6.0/package"'
    - 'call "./build/zip.bat" --unzip --input "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/publish/Fundamentals.API.zip" --output "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/package"'
    - 'call "./build/zip.bat" --unzip --input "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/publish/ApiGateway.API.zip" --output "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/package"'

