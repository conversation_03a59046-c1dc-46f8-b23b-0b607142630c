﻿using Euroland.FlipIT.WiseSearch.Infrastructure.Entities.WiseSearch;
using Euroland.FlipIT.WiseSearch.Infrastructure.UnitOfWorks;

namespace Euroland.FlipIT.WiseSearch.Services
{
    public interface IIngestActivityService
    {
        Task IngestActivityAsync(IngestedActivityEntity activity, CancellationToken cancellationToken = default);
    }

    public class IngestActivityService : IIngestActivityService
    {
        private readonly IWiseSearchUoW _wiseSearchUoW;
        private readonly ILogger<IngestActivityService> _logger;

        public IngestActivityService(IWiseSearchUoW wiseSearchUoW, ILogger<IngestActivityService> logger)
        {
            _wiseSearchUoW = wiseSearchUoW;
            _logger = logger;
        }

        public async Task IngestActivityAsync(IngestedActivityEntity activity, CancellationToken cancellationToken = default)
        {
            try
            {
                await _wiseSearchUoW.WsIngestRepository.AddAsync(activity, cancellationToken);
                await _wiseSearchUoW.SaveChangesAsync(cancellationToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("IngestActivity was canceled.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error occurred in IngestActivity.");
            }
        }
    }
}
