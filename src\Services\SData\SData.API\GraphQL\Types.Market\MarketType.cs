using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Types;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Market;

public class MarketType: ObjectType<MarketDto>
{
  public const string Name = "Market";
  protected override void Configure(IObjectTypeDescriptor<MarketDto> descriptor)
  {
    descriptor.Name(Name);

    descriptor.Field(f => f.Id).IsProjected();
    descriptor.Field(f => f.CityId).IsProjected();
    descriptor.Field(f => f.TimezoneName).IsProjected();
    descriptor.Field(f => f.TranslationId).IsProjected();
    descriptor.TranslationField();
    // descriptor
    //   .Field(f => f.Timezone)
    //   .Resolve(async (ctx, ct) => {
    //     var timezoneName = ctx.Parent<MarketDto>().TimezoneName?.Trim().ToLower();
    //     if(string.IsNullOrEmpty(timezoneName)) {
    //       return null;
    //     }

    //     return await ctx.TimezoneByNameDataLoader().LoadAsync(timezoneName, ct);
    //   });
    descriptor
      .Field(f => f.Status)
      .Resolve((ctx, ct) => ctx.DataLoader<MarketStatusByMarketIdDataLoader>().LoadAsync(ctx.Parent<MarketDto>().Id, ct));
  }
}
