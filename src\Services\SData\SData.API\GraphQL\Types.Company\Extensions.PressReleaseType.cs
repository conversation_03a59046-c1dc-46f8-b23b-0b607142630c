using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Company;

/// <summary>
/// Extends 'company' field to type <see cref="PressRelease.PressReleaseType"/>
/// </summary>
public class PressReleaseTypeExtensions: ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(PressRelease.PressReleaseType.Name);

    descriptor.Field("company")
      .Type<CompanyType>()
      .UseProjection()
      .ResolveWith<CompanyResolvers>(resolvers => resolvers.GetCompanyByPressRelease(default!, default!, default!, default));
  }
}
