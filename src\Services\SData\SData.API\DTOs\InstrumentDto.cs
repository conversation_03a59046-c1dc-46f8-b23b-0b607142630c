using System;

namespace Euroland.FlipIT.SData.API.Dto;

public class InstrumentDto: IDtoObject
{
  public int Id { get; set; }
  public string ShareName { get; set; }
  public string Symbol { get; set; }
  public short MarketID { get; set; }
  public int? MarCat { get; set; }
  public int? EurCat { get; set; }
  public int? ListID { get; set; }
  public string ISIN { get; set; }
  public byte? Customer { get; set; }
  public string? CurrencyCode { get; set; }
  public decimal? YTD { get; set; }
  public decimal? Week { get; set; }
  public decimal? TwoWeek { get; set; }
  public decimal? Month { get; set; }
  public decimal? NoShares { get; set; }
  public DateTime? Agentupdate { get; set; }
  public string? MarketSpecSign { get; set; }
  public decimal? High52W { get; set; }
  public decimal? Low52W { get; set; }
  public decimal? Percent52W { get; set; }
  public decimal? Volatility { get; set; }
  public decimal? Correlation { get; set; }
  public decimal? BetaFactor { get; set; }
  public short? TokyoEurope { get; set; }
  public byte? PrimaryMarket { get; set; }
  public decimal? ThreeMonthHigh { get; set; }
  public decimal? ThreeMonthLow { get; set; }
  public decimal? ThreeMonthChange { get; set; }
  public decimal? FiveYearsChange { get; set; }
  public DateTime? NumSharesDate { get; set; }
  public double? SplitRatio { get; set; }
  public decimal? EPS { get; set; }
  public double? SPS { get; set; }
  public decimal? DPS { get; set; }
  public double? PayoutRatio { get; set; }
  public decimal? Turnover { get; set; }
  public decimal? NetIncome { get; set; }
  public double? TurnoverGrowth { get; set; }
  public double? NetInComeGrowth { get; set; }
  public double? BookValueOfShare { get; set; }
  public int? LotSize { get; set; }
  public decimal? AllTimeHigh { get; set; }
  public decimal? AllTimeLow { get; set; }
  public DateTime? ListedFrom { get; set; }
  public decimal? TotalMarketCap { get; set; }
  public decimal? PrevMid { get; set; }
  public decimal? Highest52w { get; set; }
  public decimal? Lowest52w { get; set; }
  public DateTime? LastRowChange { get; set; }
  public InstrumentTypeDto InstrumentType { get; set; }
  public bool? MainIndex { get; set; }
  public string? YahooSymbol { get; set; }
  public int? CompanyID { get; set; }
  public string? CompanyCode { get; set; }
  public decimal? HighYTD { get; set; }
  public decimal? LowYTD { get; set; }
  public int? RegionID { get; set; }
  public decimal? MarketCapEUR { get; set; }
  public long? NumberOfUnlistedShares { get; set; }
  public decimal? VolumeTurnover { get; set; }
  public decimal? VolumeTurnoverUSD { get; set; }
  public int DataSourceID { get; set; }
  public string? WKN { get; set; }
  public string? RicCode { get; set; }

  public decimal? CurrencyRate { get; set; }
}
