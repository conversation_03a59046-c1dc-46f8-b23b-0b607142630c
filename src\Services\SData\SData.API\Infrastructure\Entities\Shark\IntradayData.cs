﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
  public class IntradayData
    {
        [Key]
        public int InstrumentId { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal Close { get; set; }
        public long Volume { get; set; }
        public DateTime Date { get; set; }

        /*
         * If data come from InstrumentPrice: source = 1
         * otherwise, data come from daily_history_chart: source = 2
         */
        public int Source { get; set; }
    }
}
