<Project Sdk="Microsoft.NET.Sdk.Web">
  <Import Project="..\..\..\Version.props" />
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>Euroland.FlipIT.Fundamental.API</RootNamespace>
    <AssemblyName>Euroland.FlipIT.Fundamental.API</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <None Include="Stitching.graphql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Schema.graphql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
    <PackageReference Include="Euroland.NetCore.ToolsFramework.Localization" Version="3.0.0" />
    <PackageReference Include="Hotchocolate.Types.Analyzers" Version="13.9.6" />
    <PackageReference Include="Hotchocolate.Types.Json" Version="13.9.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.32" />
    <PackageReference Include="TimeZoneConverter" Version="6.0.1" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Persistance\Fundamental\" />
  </ItemGroup>
</Project>
