.promode-resizable-panels{
  width: 100%;
  height: 600px;
  
}

.promode-modal {
  background: #ffffff;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 90%;
  margin-left: auto;
  max-width: 1650px;
  border-radius: 5px 0 0 5px;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 7px 10px;
    border-bottom: 1px solid #e8eaed;
    background: #ffffff;
    
    h2 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #202124;
      font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
  }
  
  &__close-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    color: #5f6368;
    border-radius: 20px;
    transition: background-color 0.2s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover {
      background: #f1f3f4;
    }
    
    &:active {
      background: #e8eaed;
    }
  }
  
  &__body {
    display: flex;
    flex: 1;
  }
  
  &__tabs {
    display: flex;
    flex-direction: column;
    width: 60px;
    background: #ffffff;
    border-right: 1px solid #e8eaed;
    height: 100%;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 10px;
  }
  &__tabs-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
  &__tabs-wrapper-down {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .user-info__avatar {
      margin-top: 5px;
    }
  }
  
  &__tab {
    background: none;
    border: none;
    padding: 12px 8px;
    text-align: center;
    cursor: pointer;
    font-size: 16px;
    font-weight: 400;
    color: #202020;
    transition: all 0.2s ease;
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 55px;
    & svg {
      width: 35px;
      height: 35px;
    }

    &:hover {
      background: #f1f3f4;
    }
    
    & .fs-alert {
      padding: 0;
    }

    &.active {
      color: #1a73e8;
      background: #e8f0fe;
      
      &::after {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background: #1a73e8;
      }
    }
  }
  
  &__content {
    flex: 1;
    overflow-y: auto;
    background: #ffffff;
    height: 100%;
  }
  
  &__tab-content {
    h3 {
      margin-top: 0;
      margin-bottom: 16px;
      font-size: 18px;
      font-weight: 500;
      color: #202124;
      font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    p {
      margin: 0;
      color: #5f6368;
      line-height: 1.5;
      font-size: 14px;
      font-family: Roboto, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .promode-modal {
    margin: 16px;
    max-width: none;
    width: calc(100% - 32px);
    border-radius: 8px;
    
    &__header {
      padding: 12px 16px;
      
      h2 {
        font-size: 14px;
      }
    }
    
    &__body {
      flex-direction: column;
      height: auto;
    }
    
    &__tabs {
      width: 100%;
      flex-direction: row;
      overflow-x: auto;
      padding: 8px 16px;
      border-right: none;
      border-bottom: 1px solid #e8eaed;
      
      &::-webkit-scrollbar {
        height: 0;
      }
    }
    
    &__tab {
      flex-shrink: 0;
      white-space: nowrap;
      margin: 0 4px;
      
      &.active::after {
        left: 0;
        right: 0;
        top: auto;
        bottom: 0;
        width: auto;
        height: 2px;
        border-radius: 2px 2px 0 0;
      }
    }
    
    &__content {
      padding: 16px;
    }
  }
}
.euroland-tool-modal-overlay {
  padding-right: 0px !important;
}

.euroland-tool-modal-overlay-iframe-container {
  margin: 0px auto !important;
}

.promode-panel__title {
  font-size: 14px;
  font-weight: 500;
  color: #202124;
  margin-bottom: 10px;
}
.promode-panel__content-watchlist {
  padding: 5px;
}
.promode-panel__content-news {
  padding: 5px;
}

.promode-panel__content-share-alert {
  padding: 5px;
}

.tab-separator {
  align-self: stretch;
  background-color: #e8eaed;
  flex: 0 0 auto;
  height: 1px;
  margin: 6px;
}