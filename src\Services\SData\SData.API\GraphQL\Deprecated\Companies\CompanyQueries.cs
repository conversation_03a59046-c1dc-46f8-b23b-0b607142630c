﻿using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Infrastructure.Repositories;
using Euroland.FlipIT.SData.API.Mappers.Deprecated;
using HotChocolate;
using HotChocolate.Data;
using HotChocolate.Types;
using Microsoft.EntityFrameworkCore;
using System.Globalization;
using System.Linq;

namespace Euroland.FlipIT.SData.API.GraphQL.Deprecated.Companies
{
  [ExtendObjectType(OperationTypeNames.Query)]
    public class CompanyQueries
    {
        /// <summary>
        /// Get company information by company code
        /// </summary>
        /// /// <param name="companyCode"></param>
        /// <returns></returns>
        [UseProjection]
        [Serial]
        public IQueryable<Company> GetCompany(
            [Service] IUnitOfWork uow,
            string companyCode)
        {
            var lang = CultureInfo.CurrentCulture.DisplayName;
            lang = lang.Substring(0, lang.IndexOf("(")).Trim();

            var companyInformation = uow.GetRepository<Company>().BuildQueryableCommand(
                predicate: c => c.CompanyCode.ToUpper() == companyCode.ToUpper(),
                include: source => source.Include(c => c.CompanyNames))
            .ToModel(lang);

            return companyInformation;
        }
    }
}
