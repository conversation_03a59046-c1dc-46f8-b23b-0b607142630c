using HotChocolate.Execution.Configuration;

namespace Euroland.FlipIT.Fundamental.API.GraphQL;

public static class JsonRequestExecutorBuilderExtensions
{
    /// <summary>
    /// Supports NewtonJson for schema.
    /// </summary>
    /// <param name="builder">
    /// The GraphQL configuration builder.
    /// </param>
    /// <returns>
    /// Returns the GraphQL configuration builder for configuration chaining.
    /// </returns>
    /// <exception cref="ArgumentNullException">
    /// The GraphQL configuration builder is null.
    /// </exception>
    public static IRequestExecutorBuilder AddNewtonJsonSupport(this IRequestExecutorBuilder builder)
    {
        if (builder is null)
        {
            throw new ArgumentNullException(nameof(builder));
        }

        builder.ConfigureSchema(sb => sb.TryAddSchemaDirective(new FromNewtonJsonSchemaDirective()));
        return builder;
    }
}