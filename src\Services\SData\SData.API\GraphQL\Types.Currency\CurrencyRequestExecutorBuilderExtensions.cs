using Euroland.FlipIT.SData.API.GraphQL.Types.Currency;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class CurrencyRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddCurrencyType(this IRequestExecutorBuilder builder)
  {
    return builder
      .AddType<CurrencyType>()
      .AddTypeExtension<CurrencyTypeDeprecatedExtensions>()
      .AddTypeExtension<InstrumentTypeExtensions>()
      .AddDataLoader<CurrencyByCodeDataLoader>();
  }
}
