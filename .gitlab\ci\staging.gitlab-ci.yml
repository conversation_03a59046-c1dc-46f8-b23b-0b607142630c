"Gamma: SData":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
    - .unpack-deployment
  stage: deploy:staging
  dependencies:
    - build-staging
  variables:
    DEPLOY_ENVIRONMENT: Gamma
    DEPLOY_SERVICE_URL: 'https://ee-v-gamma1.euroland.com:8172/msdeploy.axd'
  script:
    - 'call ".\build\msdeploy.bat" --source "src/Services/SData/SData.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/sdata-api" --usr "%GAMMA_DEPLOY_USER%" --pwd "%GAMMA_DEPLOY_PSW%" --url "%DEPLOY_SERVICE_URL%" --env %DEPLOY_ENVIRONMENT%'
    - 'call ".\build\msdeploy.bat" --source "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/fundamental-api" --usr "%GAMMA_DEPLOY_USER%" --pwd "%GAMMA_DEPLOY_PSW%" --url "%DEPLOY_SERVICE_URL%" --env %DEPLOY_ENVIRONMENT%'
    - 'call ".\build\msdeploy.bat" --source "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/ApiGateway" --usr "%GAMMA_DEPLOY_USER%" --pwd "%GAMMA_DEPLOY_PSW%" --url "%DEPLOY_SERVICE_URL%" --env %DEPLOY_ENVIRONMENT%'
  only:
    refs:
      - next
  environment:
    name: Gamma
    url: 'https://gamma.euroland.com/tools/sdata-api/graphql?companycode=dk-cbg'

"Staging: SData":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
    - .prevent-fetching
    - .unpack-deployment
  stage: deploy:staging
  dependencies:
    - build-staging
  variables:
    DEPLOY_ENVIRONMENT: Staging
    DEPLOY_SERVICE_URL_WEBCAT15: 'https://ee-v-webcat151.euroland.com:8172/msdeploy.axd'
    DEPLOY_SERVICE_URL_WEBCAT16: 'https://ee-v-webcat161.euroland.com:8172/msdeploy.axd'
  script:
    - |
      ECHO Deploying to ee-v-webcat161...
      call ".\build\msdeploy.bat" --source "src/Services/SData/SData.API/bin/Release/net6.0/package" --iisAppPath "staging-site/tools/sdata-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_WEBCAT16%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/package" --iisAppPath "staging-site/tools/fundamental-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_WEBCAT16%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/package" --iisAppPath "staging-site/tools/ApiGateway" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_WEBCAT16%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying to ee-v-webcat151...
      call ".\build\msdeploy.bat" --source "src/Services/SData/SData.API/bin/Release/net6.0/package" --iisAppPath "staging-site/tools/sdata-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_WEBCAT15%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/package" --iisAppPath "staging-site/tools/fundamental-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_WEBCAT15%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/package" --iisAppPath "staging-site/tools/ApiGateway" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_WEBCAT15%" --env %DEPLOY_ENVIRONMENT%
  only:
    refs:
      - next
  when: manual
  environment:
    name: Staging
    url: 'https://staging-gr.eurolandir.com/tools/sdata-api/graphql?companycode=dk-cbg'
