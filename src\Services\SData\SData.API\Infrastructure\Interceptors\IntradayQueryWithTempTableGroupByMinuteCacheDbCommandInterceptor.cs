using System;
using System.Data.Common;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities;

/// <summary>
/// Intercepts database commands to manipulate the command text for intraday queries
/// by inserting temporary table creation and data manipulation logic.
/// </summary>
internal class IntradayQueryWithTempTableGroupByMinuteCacheDbCommandInterceptor : DbCommandInterceptor
{
  /// <summary>
  /// Name of the temporary table for intraday date ranges.
  /// </summary>
  public const string IntradayDateRangesTable = "@IntradayDateRanges";
  public const string TOKEN = "<Intraday-By-Minute>";
  const string TAG_TOKEN = $"-- {TOKEN}";

  /// <summary>
  /// Intercepts the synchronous execution of a command that returns a <see cref="DbDataReader"/>.
  /// </summary>
  /// <param name="command">The command being executed.</param>
  /// <param name="eventData">The event data associated with the command.</param>
  /// <param name="result">The result of the command execution.</param>
  /// <returns>The result of the command execution, potentially modified.</returns>
  public override InterceptionResult<DbDataReader> ReaderExecuting(
    DbCommand command,
    CommandEventData eventData,
    InterceptionResult<DbDataReader> result)
  {
    ManipulateCommand(eventData.Context!, command);
    return base.ReaderExecuting(command, eventData, result);
  }

  /// <summary>
  /// Manipulates the command text by inserting temporary table creation and data manipulation logic.
  /// </summary>
  /// <param name="command">The command to manipulate.</param>
  public override ValueTask<InterceptionResult<DbDataReader>> ReaderExecutingAsync(
    DbCommand command,
    CommandEventData eventData,
    InterceptionResult<DbDataReader> result,
    CancellationToken cancellationToken = default)
  {
    ManipulateCommand(eventData.Context!, command);
    return base.ReaderExecutingAsync(command, eventData, result, cancellationToken);
  }

  /// <summary>
  /// Manipulates the command text by inserting temporary table creation and data manipulation logic.
  /// </summary>
  /// <param name="command">The command to manipulate.</param>
  private static void ManipulateCommand(DbContext dbContext, DbCommand command)
  {
    var originalCmdText = command.CommandText;

    if(!HasToken(originalCmdText)) {
      return;
    }

    // It's better to get configured table name from Entity instead
    // (dbContext as SharkDbContextBase).Instrument.EntityType.GetSchemaQualifiedTableName();
    var tempTableQuery = CreateTempTableQuery(
      "Instrument",
      "InstrumentHistory",
      "daily_history",
      "CurrencyRate",
      "CurrencyRateHistory"
    );

    command.CommandText = originalCmdText.Replace(TAG_TOKEN, tempTableQuery);
  }

  private static bool HasToken(string commandText) {
    return commandText.IndexOf(TAG_TOKEN, StringComparison.Ordinal) != -1;
  }

  /// <summary>
  /// Creates the query for temporary table creation and data manipulation.
  /// </summary>
  /// <returns>The query string.</returns>
  private static string CreateTempTableQuery(
    string instrumentTbl,
    string instrumentHistoryTbl,
    string dailyHistoryTbl,
    string currencyRateTbl,
    string currencyRateHistoryTbl)
  {
    // We assume that bellow parameters must be passed from InstrumentResolvers object
    //  @instrumentId INT;
    //  @marketOpenTimeInputTimezone FLOAT;
    //  @minutesDifference INT;
    //  @marketOpenTimeLocal CHAR(8);
    //  @groupingTimeInterval INT;
    //  @adjClose BIT;
    //  @marketTimeZone NVARCHAR(23)';
    return @$"
      -- Pre-calculate market open time
      DECLARE @MarketOpenDateTime datetime = CONVERT(datetime, CONVERT(char(10), GETDATE(), 120) + ' ' + @marketOpenTimeLocal, 120);

      -- Create temp table for date calculations to avoid repeated conversions
      DECLARE {IntradayDateRangesTable} TABLE (
          InstrumentId INT,
          GroupedDate DATETIME,
          MinDate DATETIME,
          MaxDate DATETIME,
          [High] MONEY,
          [Low] MONEY,
          Volume BIGINT
      );
      -- Populate date ranges first
      INSERT INTO {IntradayDateRangesTable}
      SELECT
          InstrumentId,
          DATEADD(MINUTE,
              ((DATEDIFF(MINUTE, @MarketOpenDateTime,
                  DATEADD(MINUTE, @minutesDifference, dh.hDate)) / @groupingTimeInterval)
              * @groupingTimeInterval),
              @MarketOpenDateTime) AS GroupedDate,
          MIN(dh.hDate) AS MinDate,
          MAX(dh.hDate) AS MaxDate,
          MAX(dh.hClose) AS [High],
          MIN(dh.hClose) AS [Low],
          SUM(dh.hSize) AS Volume
      FROM [{dailyHistoryTbl}] dh WITH (NOLOCK)
      WHERE dh.InstrumentID = @instrumentId
          AND CAST(dh.hDate AS float) - FLOOR(CAST(dh.hDate AS float)) >= @marketOpenTimeInputTimezone
      GROUP BY
          InstrumentId,
          DATEADD(
            MINUTE,
            ((DATEDIFF(MINUTE, @MarketOpenDateTime,
                DATEADD(MINUTE, @minutesDifference, dh.hDate)) / @groupingTimeInterval)
            * @groupingTimeInterval),
            @MarketOpenDateTime
          );
    ";
  }
}
