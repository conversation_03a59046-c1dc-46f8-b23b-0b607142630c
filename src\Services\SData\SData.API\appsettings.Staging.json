{"Serilog": {"Using": ["Serilog.Sinks.Syslog"], "MinimumLevel": "Warning", "WriteTo": [{"Name": "UdpSyslog", "Args": {"host": "*************", "port": "514", "appName": "FlipIT.SData.API_Staging", "restrictedToMinimumLevel": "Warning", "outputTemplate": "[{Timestamp:HH:mm:ss}] {Message:lj}{NewLine}{Exception}"}}], "Properties": {"Application": "FlipIT.SData.API_Staging"}}, "ConnectionStrings": {"NewsDb": "Server=************;Database=News;User ID=uShark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=SData.API", "SharkDb": "Server=************;Database=shark;User ID=uShark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=SData.API", "SharkRealtimeDb": "Server=tcp:eurolandeurope.database.windows.net,1433;Database=shark;User ID=uShark;Password=**********;Trusted_Connection=False;TrustServerCertificate=True;MultipleActiveResultSets=True;Application Name=SData.API", "WebcastDb": "Server=************;Database=Webcast;User ID=uShark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=SData.API"}}