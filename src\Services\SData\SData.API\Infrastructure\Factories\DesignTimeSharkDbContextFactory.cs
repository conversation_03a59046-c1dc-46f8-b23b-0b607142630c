using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using System.IO;
using System.Reflection;

namespace Euroland.FlipIT.SData.API.Infrastructure.Factories
{
  public class DesignTimeSharkDbContextFactory : IDesignTimeDbContextFactory<DefaultSharkDbContext>
    {
        public DesignTimeSharkDbContextFactory()
        {

        }

        public DefaultSharkDbContext CreateDbContext(string[] args)
        {
            var config = new ConfigurationBuilder()
               .SetBasePath(Path.Combine(Directory.GetCurrentDirectory()))
               .AddJsonFile("appsettings.json")
               .AddEnvironmentVariables()
               .Build();

            var optionsBuilder = new DbContextOptionsBuilder<DefaultSharkDbContext>();

            var migrationsAssembly = typeof(DbMigrationHelpers).GetTypeInfo().Assembly.GetName().Name;

            optionsBuilder.UseSqlServer(config.GetConnectionString("SharkDb"),
                sqlServerOptionsAction: o => o.MigrationsAssembly(migrationsAssembly));

            return new DefaultSharkDbContext(optionsBuilder.Options);
        }

    }
}
