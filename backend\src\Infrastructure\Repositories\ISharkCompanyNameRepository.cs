using Euroland.FlipIT.WiseSearch.Infrastructure.Entities.Shark.Views;

namespace Euroland.FlipIT.WiseSearch.Infrastructure.Repositories
{
  public interface ISharkCompanyNameRepository : IRepositoryBase<SharkDbContext, CompanyNameView, string>
  {

  }

  public class SharkCompanyNameRepository : RepositoryBase<SharkDbContext, CompanyNameView, string>, ISharkCompanyNameRepository
  {
    public SharkCompanyNameRepository(SharkDbContext context) : base(context)
    {

    }
  }
}
