using HotChocolate.Types;
using Euroland.FlipIT.SData.API.Dto;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.PerformanceShare;

public class PerformancePeriodType: EnumType<PerformancePeriodDto>
{
  protected override void Configure(IEnumTypeDescriptor<PerformancePeriodDto> descriptor)
  {
    descriptor.Name("PerformancePeriod");
    descriptor.Value(PerformancePeriodDto.YTD).Description("Year to date");
    descriptor.Value(PerformancePeriodDto.ONE_MONTH).Description("One month ago");
    descriptor.Value(PerformancePeriodDto.THREE_MONTHS).Description("Three months ago");
    descriptor.Value(PerformancePeriodDto.SIX_MONTHS).Description("Six months ago");
    descriptor.Value(PerformancePeriodDto.ONE_WEEK).Description("One week ago");
    descriptor.Value(PerformancePeriodDto.TWO_WEEKS).Description("Two weeks ago");
    descriptor.Value(PerformancePeriodDto.FIFTY_TWO_WEEKS).Description("Fifty two weeks ago");
    descriptor.Value(PerformancePeriodDto.ONE_YEAR).Description("One year ago");
    descriptor.Value(PerformancePeriodDto.TWO_YEARS).Description("Two years ago");
    descriptor.Value(PerformancePeriodDto.THREE_YEARS).Description("Three years ago");
    descriptor.Value(PerformancePeriodDto.FIVE_YEARS).Description("Five years ago");
    descriptor.Value(PerformancePeriodDto.TEN_YEARS).Description("Ten years ago");
    descriptor.Value(PerformancePeriodDto.ALL_TIME).Description("All time of prices");
    descriptor.Value(PerformancePeriodDto.CUSTOM).Description("Custom with any input date");
  }
}
