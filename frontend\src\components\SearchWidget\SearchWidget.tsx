import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@euroland/react-tablist";
import { Info } from "../Common";
import { zoidComponentCreator } from "@/zoid-components";
import { i18n } from "@euroland/libs";
import { apiClient } from "@/services/clientInstance";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import { Header } from "./components/Header";
import { SearchPanel } from "./components/SearchPanel";
import { HistoryPanel } from "./components/HistoryPanel";
import { Footer } from "./components/Footer";
import { appSettings } from "@/config/appSettings";
import PriceFeed from "./components/PriceFeed";
import { ApiClient } from "@/services/client";
import SearchLayout from "./SearchLayout";
import { useSearchWidgetPropsContext, wrapWithContext } from "./context/propContext";
import {useSetupTheme} from "@/hooks/useSetupTheme";
import DisclaimerPopup from "./DisclaimerPopup";
import {AUTH_ENABLED} from "@/helpers/constants";

const SearchWidget = () => {
  const getAccessToken = useSearchWidgetPropsContext(s => s.getAccessToken);
  const isLogin = useSearchWidgetPropsContext(s => s.isLogin);
  
  const aiSearchSettings = useSearchWidgetPropsContext(s => s.aiSearchSettings);
  
  apiClient.instance = new ApiClient(() => getAccessToken());

  useSetupTheme(aiSearchSettings)
  return (
    <SearchLayout>
      <Header />
      <PriceFeed />
      <Tabs>
        <TabList className="wise-widget-tabs" ariaLabel="search tabs">
          <Tab
            className="tab"
            activeClassName="active"
            value="search"
            ariaLabel="search"
          >
            {i18n.translate("search")}
          </Tab>
          <Tab
            className="tab"
            activeClassName="active"
            value="history"
            ariaLabel="history"
          >
            {isLogin && i18n.translate("history")}
            {!isLogin && (
              <div className="flex items-center gap-2">
                {i18n.translate("localHistory")}

                {AUTH_ENABLED && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                      <span>
                        <Info className="history-info-icon" />
                      </span>
                    </TooltipTrigger>
                    <TooltipContent className="bg-gray-800 text-white border-gray-800">
                      <p className="max-w-[300px]">
                        {i18n.translate("localHistoryTooltip")}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                  </TooltipProvider>
                )}
              </div>
            )}
          </Tab>
        </TabList>
        <TabPanels className="wise-widget-content">
          <TabPanel
            className="tab-panel"
            activeClassName="active"
            forTab="search"
          >
            <SearchPanel
              queryLimit={10}
            />
          </TabPanel>
          <TabPanel
            className="tab-panel"
            activeClassName="active"
            forTab="history"
          >
            <HistoryPanel />
          </TabPanel>
        </TabPanels>
        <Footer />
      </Tabs>
      <DisclaimerPopup />
    </SearchLayout>
  );
};

export const ZoidSearchWidget = zoidComponentCreator(
  wrapWithContext(SearchWidget),
  {
    dimensions: {
      width: "100%",
      height: "100%",
    },
    template: {
      name: "dynamic",
      styles: {
        position: "fixed",
        bottom: "0px",
        [appSettings.isRTL ? "left" : "right"]: "0px",
        "z-index": "200001",
        opacity: "1",
      },
      backdropBgColor: "rgba(0, 0, 0, 0.5)",
    },
    props: {
      onCloseWindow: {
        type: "function",
        required: true,
      },
      onSignOutSuccess: {
        type: "function",
        required: true,
      },
      isLogin: {
        type: "boolean",
        required: true,
      },
      onLogin: {
        type: "function",
        required: true,
      },
      onOpenExternalLink: {
        type: "function",
        required: true,
      },
      updateStatus: {
        type: "function",
        required: true,
      },
      avatarUrl: {
        type: "string",
        required: true,
      },
      companyInfo: {
        type: "object",
        required: true,
      },
      isOpen: {
        type: "boolean",
        required: true,
      },
      getAccessToken: {
        type: "function",
        required: true,
      },
      shortName: {
        type: "string",
        required: true,
      },
      position: {
        type: "string",
        required: true,
      },
      userFollowStatus: {
        type: "number",
        required: true,
      },
      aiSearchSettings: {
        type: "object",
        required: true,
      },
    },
  }
);

export default SearchWidget;
