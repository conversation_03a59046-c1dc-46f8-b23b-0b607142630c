import {useAuth} from "@/auth";
import {useAuthSession} from "@/auth/core/useAuthSession";
import {AUTH_ENABLED, FOLLOWING_STATUS} from "@/helpers/constants";
import {API_PATHS} from "@/services/apiEndpoints";
import {apiUserPreferenceClient} from "@/services/clientInstance";
import {useAppStore} from "@/store/useAppStore";
import {useEffect} from "react";

const AuthControl = () => {
  const auth = useAuth();
  const setStatus = useAppStore(state => state.userProfile.setStatus)
  const setUserProfile = useAppStore(state => state.userProfile.setUserProfile);
  const resetUserProfile = useAppStore(state => state.userProfile.reset);
  useAuthSession({onSigninSilentFailed: async () => {
    await auth.clearStaleState();
    await auth.removeUser();
    resetUserProfile()
  }});

  useEffect(() => {
    if(!auth.isAuthenticated) return;

    apiUserPreferenceClient.instance
      .get<{
        data: {
          currentFollowingStatus: FOLLOWING_STATUS;
          avatar: string | null;
          fullName: string;
        };
      }>(API_PATHS.USER_PROFILE)
      .then((response) => {
        const { currentFollowingStatus, avatar, fullName } = response.data;
        setStatus(currentFollowingStatus);
        setUserProfile({ avatar, fullName });
      });
  }, [auth.isAuthenticated]);

  useEffect(() => {
    if(!AUTH_ENABLED && auth.isAuthenticated) {
      auth.signoutSilent();
      console.warn('Login has been disabled, but the user was previously authenticated. Signing out silently.')
    }
  }, []);

  return null
}

export default AuthControl
