using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using Euroland.FlipIT.SData.API.Infrastructure.Factories;
using Euroland.FlipIT.Shared.CachingManager;
using GreenDonut;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Currency;

public class CurrencyByCodeDataLoader : BatchDataLoader<string, CurrencyDto>
{
  private readonly ISharkDbContextAbstractFactory _factory;
  private readonly IGraphQLExecutionContext _queryRequestContext;
  private readonly IConfigurableCacheManager _configurableCacheManager;

  public CurrencyByCodeDataLoader(
    ISharkDbContextAbstractFactory factory,
    IGraphQLExecutionContext queryRequestContext,
    IConfigurableCacheManager configurableCacheManager,
    IBatchScheduler batchScheduler,
    DataLoaderOptions? options = null)
    : base(batchScheduler, options)
  {
     _factory = factory ?? throw new ArgumentNullException(nameof(factory));
    _queryRequestContext = queryRequestContext ?? throw new ArgumentNullException(nameof(queryRequestContext));
    _configurableCacheManager = configurableCacheManager ?? throw new ArgumentNullException(nameof(configurableCacheManager));
  }

  protected override async Task<IReadOnlyDictionary<string, CurrencyDto>> LoadBatchAsync(IReadOnlyList<string> keys, CancellationToken cancellationToken)
  {
    var filterKeys = keys.Where(k =>
        _configurableCacheManager.GetCache<CurrencyDto, string>(CacheKeyHelper.GenerateKeyByPropOfDtoObject<CurrencyDto, string>(k, c => c.Code)) == null)
      .ToList();

    await using var sharkDbContext = await _factory.CreateDbContextAsync(_queryRequestContext.UseRealtime, _queryRequestContext.UseCloud);

    var data = filterKeys.Count > 0
      ? await sharkDbContext
        .Currency
        .AsNoTracking()
        .Where(c => filterKeys.Contains(c.Code))
        .WithAutoMapper()
        .ToProjection<CurrencyDto>()
        .ToListAsync(cancellationToken)
      : Enumerable.Empty<CurrencyDto>();

    foreach (var currency in data)
    {
      _configurableCacheManager.SetCache(
        currency,
        CacheKeyHelper.GenerateKeyByPropOfDtoObject<CurrencyDto, string>(currency.Code, c => c.Code)
      );
    }

    var result = keys.Select(k =>
      _configurableCacheManager.GetCache<CurrencyDto, string>(CacheKeyHelper.GenerateKeyByPropOfDtoObject<CurrencyDto, string>(k, c => c.Code))).ToList();

    return result.ToDictionary(x => x.Code.ToLower()!);
  }
}
