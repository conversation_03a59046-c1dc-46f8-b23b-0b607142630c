import React, { useState } from "react";
import Tabs from "./Tabs";
import "./InfoTickerTabs.scss";

const InfoTickerTabs = () => {
  const defaultTabs = [
    { id: "overview", label: "Overview", render: () => <div>Overview</div> },
    {
      id: "financials",
      label: "Financials",
      render: () => <div>Financials</div>
    },
    { id: "news", label: "News", render: () => <div><euroland-news-widget /></div> },
    { id: "seasonal", label: "Seasonal", render: () => <div>Seasonal</div> },
    { id: "bonds", label: "Bonds", render: () => <div>Bonds</div> }
  ];
  const [activeTab, setActiveTab] = useState(defaultTabs[0].id);

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
    console.log("InfoTicker tab changed to:", tabId);
  };

  return (
    <div className="InfoTickerTabs">
      <Tabs
        tabs={defaultTabs}
        activeTab={activeTab}
        onTabChange={handleTabChange}
      >
        {defaultTabs.map((tab) => (
          <Tabs.TabContent key={tab.id} tabId={tab.id}>
            {tab.render()}
          </Tabs.TabContent>
        ))}
      </Tabs>
    </div>
  );
};

export default InfoTickerTabs;
