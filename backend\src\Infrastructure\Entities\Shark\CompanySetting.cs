﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.WiseSearch.Infrastructure.Entities.Shark;

public partial class CompanySetting
{
    [Key]
    [StringLength(50)]
    [Unicode(false)]
    public string CompanyCode { get; set; } = null!;

    [StringLength(255)]
    [Unicode(false)]
    public string? CompanyLogo { get; set; }

    [Unicode(false)]
    public string? EnabledLanguages { get; set; }

    [StringLength(10)]
    [Unicode(false)]
    public string? DefaultLanguage { get; set; }

    [StringLength(120)]
    [Unicode(false)]
    public string? Timezone { get; set; }

    public bool? UseLocalTimezone { get; set; }

    [StringLength(255)]
    [Unicode(false)]
    public string? Industry { get; set; }
}
