import {XProps} from '@/zoid';
import { useAuth } from '../core';
import {AuthPros} from '../core/interfaces';

/**
 * LogoutCallbackComponent handles the logout callback process.
 *
 * This component checks if the user is authenticated using the `useAuth` hook.
 * If the user is not authenticated and `window.xprops` is available, it calls
 * the `onLogoutSuccess` and `close` functions from `window.xprops` if they are defined.
 *
 * @returns A React fragment containing a message indicating the sign-out process.
 */
export const LogoutCallbackComponent = () => {
  const auth = useAuth();
  const xprops = window.xprops as unknown as XProps<AuthPros>
  if (!auth.isAuthenticated && xprops) {
    if (typeof (xprops.onLogoutSuccess) === 'function') {
      xprops.onLogoutSuccess();
    }
    if (typeof (xprops.close) === 'function') {
      xprops.close();
    }
  }

  return null

  // return (
  //   <>
  //     <div>Signing out...</div>
  //   </>
  // );
};
