using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.CustomerType;

/// <summary>
/// Extends 'customerType' field to type <see cref="Instrument.InstrumentType"/>
/// </summary>
public class InstrumentTypeExtensions : ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(Instrument.InstrumentType.Name);

    descriptor.Field("customerType")
      .UseFirstOrDefault()
      .UseProjection()
      .ResolveWith<CustomerTypeResolvers>(
        resolvers => resolvers.GetCustomerTypeByInstrument(default!, default!)
      );
  }
}
