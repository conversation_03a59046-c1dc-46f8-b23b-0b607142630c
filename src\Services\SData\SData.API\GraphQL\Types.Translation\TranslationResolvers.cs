using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.NetCore.ToolsFramework.Localization;
using HotChocolate;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Translation;

public class TranslationResolvers
{
  public async Task<TranslationDto?> GetTranslation(
    [Parent] HasTranslation parent,
    [Service] ILanguageToCultureProvider languageToCultureProvider,
    TranslationDataLoader dataLoader,
    string cultureName,
    CancellationToken cancellationToken)
  {
    if (parent.TranslationId == null) return null;
    var translations = await dataLoader.LoadAsync(parent.TranslationId.Value, cancellationToken);

    if (string.IsNullOrEmpty(cultureName))
    {
      cultureName = "en-gb";
    }

    var language = languageToCultureProvider.GetLanguage(cultureName);
    if(language != null) {
      var result = translations.FirstOrDefault(t => t.CultureName.Equals(language.TwoLetterOfLanguage, StringComparison.OrdinalIgnoreCase));

      if(result != null) {
        return new TranslationDto {
          CultureName = result.CultureName,
          Id = result.Id,
          Value = result.Value
        };
      }
    }
    return null;
  }
}
