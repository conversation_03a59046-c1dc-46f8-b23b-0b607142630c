using AutoMapper;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Helpers;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;

namespace Euroland.FlipIT.SData.API.AutoMapper.Profiles;

public class FCalendarMapperProfile : Profile
{
  public FCalendarMapperProfile()
  {
    var cultureName = "en-gb";

    CreateMap<FCalendar, FCalendarDto>()
      .ForMember(dto => dto.FCalendarId, opt => opt.MapFrom(src => src.Id))
      .ForMember(dto => dto.FCEventId, opt => opt.MapFrom(src => src.Event))
      .ForMember(dto => dto.EventName, conf =>
        conf.MapFrom(ol => cultureName.IsCulture("en-us") ? ol.FCEvent.English :
          cultureName.IsCulture("fr-fr") ? ol.FCEvent.French :
          cultureName.IsCulture("ar-sa") ? ol.FCEvent.Arabic :
          cultureName.IsCulture("ar-ae") ? ol.FCEvent.Arabic :
          cultureName.IsCulture("fi-fi") ? ol.FCEvent.Finnish :
          cultureName.IsCulture("sv-se") ? ol.FCEvent.Swedish :
          cultureName.IsCulture("de-de") ? ol.FCEvent.German :
          cultureName.IsCulture("es-mx") ? ol.FCEvent.Spanish :
          cultureName.IsCulture("it-it") ? ol.FCEvent.Italian :
          cultureName.IsCulture("nl-nl") ? ol.FCEvent.Dutch :
          cultureName.IsCulture("ru-ru") ? ol.FCEvent.Russian :
          cultureName.IsCulture("pl-pl") ? ol.FCEvent.Polish :
          cultureName.IsCulture("zh-cn") ? ol.FCEvent.Chinese :
          cultureName.IsCulture("ko-kr") ? ol.FCEvent.Korean :
          cultureName.IsCulture("da-dk") ? ol.FCEvent.Danish :
          cultureName.IsCulture("is-is") ? ol.FCEvent.Icelandic :
          cultureName.IsCulture("vi-vn") ? ol.FCEvent.Vietnamese :
          cultureName.IsCulture("ja-jp") ? ol.FCEvent.Japanese :
          cultureName.IsCulture("pt-pt") ? ol.FCEvent.Portuguese :
          cultureName.IsCulture("et-ee") ? ol.FCEvent.Estonian :
          cultureName.IsCulture("pt-br") ? ol.FCEvent.Portuguese :
          cultureName.IsCulture("he-il") ? ol.FCEvent.Hebrew :
          cultureName.IsCulture("ku-arab") ? ol.FCEvent.Kurdish :
          cultureName.IsCulture("ca-es") ? ol.FCEvent.Catalan :
          cultureName.IsCulture("ro-ro") ? ol.FCEvent.Romanian :
          cultureName.IsCulture("lt-lt") ? ol.FCEvent.Lithuanian :
          cultureName.IsCulture("el-gr") ? ol.FCEvent.Greek :
          ol.FCEvent.English));
  }
}
