using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using Euroland.FlipIT.SData.API.Infrastructure.Factories;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.Shared.CachingManager;
using GreenDonut;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;

public class InstrumentByIdDataLoader : BatchDataLoader<int, InstrumentDto>
{
  private readonly ISharkDbContextAbstractFactory _factory;
  private readonly IGraphQLExecutionContext _queryRequestContext;
  private readonly IConfigurableCacheManager _cache;

  // <summary>
  /// The timeout duration in seconds for caching instrument price data.
  /// </summary>
  const short CACHE_TIMEOUT_SECOND = 10;

  public InstrumentByIdDataLoader(
    ISharkDbContextAbstractFactory factory,
    IGraphQLExecutionContext queryRequestContext,
    IConfigurableCacheManager configurableCacheManager,
    IBatchScheduler batchScheduler,
    DataLoaderOptions? options = null
  ) : base(batchScheduler, options)
  {
    _factory = factory ?? throw new ArgumentNullException(nameof(factory));
    _queryRequestContext = queryRequestContext ?? throw new ArgumentNullException(nameof(queryRequestContext));
    _cache = configurableCacheManager ?? throw new ArgumentNullException(nameof(configurableCacheManager));
  }

  protected override async Task<IReadOnlyDictionary<int, InstrumentDto>> LoadBatchAsync(IReadOnlyList<int> keys, CancellationToken cancellationToken)
  {

    var dtoFromCache = new List<InstrumentDto>();
    var idNeedToFetchNew = new List<int>();

    foreach (var instrumentId in keys.Distinct())
    {
      var dto = _cache.GetCache<InstrumentDto, string>(
        CacheKeyHelper.GenerateKeyByPropOfDtoObject<InstrumentDto, int>(instrumentId.ToString(), m => m.Id)
      );

      if (dto != null)
      {
        dtoFromCache.Add(dto);
      }
      else
      {
        idNeedToFetchNew.Add(instrumentId);
      }
    }

    await using var sharkDbContext = await _factory.CreateDbContextAsync(_queryRequestContext.UseRealtime, _queryRequestContext.UseCloud);

    var results = new List<InstrumentDto>(dtoFromCache);

    if (idNeedToFetchNew.Count > 0)
    {
      var datas = await (
        from instrument in sharkDbContext.Instrument.AsNoTracking()
        where idNeedToFetchNew.Contains(instrument.InstrumentId)
        join company in sharkDbContext.Company.AsNoTracking()
          on instrument.CompanyID equals company.CompanyId into grouping
        from company in grouping.DefaultIfEmpty()
        select new { instrument, company.CompanyCode }
      ).ToListAsync(cancellationToken);

      datas.ForEach(x => x.instrument.CompanyCode = x.CompanyCode);

      var instrumentDtos = datas.Select(d => d.instrument)
        .WithAutoMapper()
        .Map<InstrumentDto>();

      foreach (var instrument in instrumentDtos)
      {
        _cache.SetCache(
          instrument,
          CacheKeyHelper.GenerateKeyByPropOfDtoObject<InstrumentDto, int>(instrument.Id.ToString(), i => i.Id),
          TimeSpan.FromSeconds(CACHE_TIMEOUT_SECOND)
        );

        results.Add(instrument);
      }
    }

    return results.ToDictionary(i => i.Id);
  }
}
