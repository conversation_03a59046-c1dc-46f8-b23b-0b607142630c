using HotChocolate.Types.Pagination;

namespace Euroland.FlipIT.Fundamental.API.GraphQL.Types.FinCalender;

/// <summary>
/// Extends FinCalender fields to type <see cref="FundamentalType"/>
/// </summary>
public class FundamentalTypeExtensions : ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(FundamentalType.TypeName);

    descriptor.Field("financialEvents")
      .Description("Get all events for the company")
      .Argument("cultureName", d => d.Type<StringType>()
        .DefaultValue("en-gb")
        .Description("culture name for localization, default is en-gb"))
      .UsePaging(options: new PagingOptions
      {
        IncludeTotalCount = true,
        MaxPageSize = int.MaxValue,
        DefaultPageSize = int.MaxValue
      })
      .UseProjection()
      .UseFiltering()
      .UseSorting()
      .ResolveWith<FincalenderResolver>(resolvers =>
        resolvers.GetFinancialEventsByCompanyCode(default!, default!, default!, default!));

    descriptor.Field("financialEventTypes")
      .Description("Get all event types for the company")
      .Argument("cultureName", d => d.Type<StringType>()
        .DefaultValue("en-gb")
        .Description("culture name for localization, default is en-gb"))
      .UsePaging(options: new PagingOptions
      {
        IncludeTotalCount = true,
        MaxPageSize = int.MaxValue,
        DefaultPageSize = int.MaxValue
      })
      .UseProjection()
      .UseFiltering()
      .UseSorting()
      .ResolveWith<FincalenderResolver>(resolvers =>
        resolvers.GetFinancialEventTypesByCompanyCode(default!, default!, default!, default!));
  }
}
