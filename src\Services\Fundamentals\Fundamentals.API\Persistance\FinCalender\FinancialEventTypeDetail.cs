using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.Fundamental.API.Persistance.FinCalender;

[Table("EventTypeDetail")]
public class FinancialEventTypeDetail
{
  [Column("EventTypeID"), ForeignKey("FinancialEventType")]
  public int FinancialEventTypeId { get; set; }
  public FinancialEventType FinancialEventType { get; set; }

  public int LanguageId { get; set; }

  public string Name { get; set; }

}
