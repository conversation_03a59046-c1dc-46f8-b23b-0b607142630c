import React, { useEffect, useCallback } from 'react'
import { LayoutPosition } from '../../config/interface';
import {useSearchWidgetPropsContext} from './context/propContext';

export interface SearchLayoutProps {
  children: React.ReactNode;
  position: LayoutPosition;
  onBackdropClick: () => void;
}

const SearchLayoutCenter = ({children, onBackdropClick}: Omit<SearchLayoutProps, 'position'>) => {
  return <div className='relative'>
    <div className="w-screen h-screen" onClick={onBackdropClick}></div>
    <div className="w-full md:max-w-[min(100%,800px)] h-full md:h-3/4 lg:w-2/3 lg:h-2/3 left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2 absolute flex flex-col shadow-[-5px_0_30px_rgba(0,0,0,0.2)] bg-white rounded-lg overflow-hidden">{children}</div>
  </div>
}

const SearchLayoutBottom = ({children, onBackdropClick}: Omit<SearchLayoutProps, 'position'>) => {
  return <div className='relative'>
    <div className="w-screen h-screen" onClick={onBackdropClick}></div>
    <div className="w-full h-full md:h-2/3 lg:h-1/2 bottom-0 left-0 right-0 absolute flex flex-col shadow-[-5px_0_30px_rgba(0,0,0,0.2)] bg-white rounded-lg overflow-hidden">{children}</div>
  </div>
}


const SearchLayoutRight = ({children, onBackdropClick}: Omit<SearchLayoutProps, 'position'>) => {
  return <div className='flex rtl:flex-row-reverse'>
    <div className="h-screen flex-1" onClick={onBackdropClick}></div>
    <div className="w-full md:max-w-[min(100%,800px)] h-screen flex flex-col shadow-[-5px_0_30px_rgba(0,0,0,0.2)] bg-white relative">{children}</div>
  </div>
}

const SearchLayoutLeft = ({children, onBackdropClick}: Omit<SearchLayoutProps, 'position'>) => {
  return <div className='flex ltr:flex-row-reverse'>
    <div className="h-screen flex-1" onClick={onBackdropClick}></div>
    <div className="w-full md:max-w-[min(100%,800px)] h-screen flex flex-col shadow-[-5px_0_30px_rgba(0,0,0,0.2)] bg-white relative">{children}</div>
  </div>
}

const SearchLayout = ({children}:{children: React.ReactNode}) => {
  const onCloseWindow = useSearchWidgetPropsContext(s => s.onCloseWindow);
  const position = useSearchWidgetPropsContext(s => s.position);

  const onBackdropClick = useCallback(() => {
    onCloseWindow();
  }, [onCloseWindow]);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onCloseWindow();
      }
    };
    document.addEventListener("keydown", handleEscape);
    return () => {
      document.removeEventListener("keydown", handleEscape);
    };
  }, [onCloseWindow]);

  switch (position) {
    case LayoutPosition.CENTER:
      return <SearchLayoutCenter children={children} onBackdropClick={onBackdropClick} />
    case LayoutPosition.BOTTOM:
      return <SearchLayoutBottom children={children} onBackdropClick={onBackdropClick} />
    case LayoutPosition.RIGHT:
      return <SearchLayoutRight children={children} onBackdropClick={onBackdropClick} />
    case LayoutPosition.LEFT:
      return <SearchLayoutLeft children={children} onBackdropClick={onBackdropClick} />
    default:
      throw new Error("Invalid layout position");
  }
}

export default SearchLayout