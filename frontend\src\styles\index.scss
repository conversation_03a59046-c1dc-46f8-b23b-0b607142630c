@use './animation.scss';
@use "_rtl-mixins" as *;
@use './components/SearchWidget.scss';
@use './components/AISearchWidget.scss';
@use './components/AlertWidget.scss';
@use './components/ProfileMenu.scss';
@use './components/DisclaimerPopup.scss';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-transparent text-foreground;
  }
}

html {
  height: 100%;
  width: 100%;
}

// #root {
//   height: 100%;
//   width: 100%;
// }

body {
  background-color: transparent;
  font-family: Arial, sans-serif;
}

.markdown-content {
  line-height: 1.5;
  font-size: 1rem;

  br {
    @apply mb-3 block content-['']
  }

  sup {
    font-size: 0.75em;
    color: var(--primary-color, #0000CD);
    font-weight: 500;
    padding: 0 2px;
    cursor: pointer;
    transition: color 0.2s ease;
  }
  
  sup:hover {
    color: var(--primary-color, #0000CDdd);
  }
  
  p {
    margin: 0 0 1.2em;
    color: #2c3e50;
  }
  
  p:last-child {
    margin-bottom: 0;
  }
  
  ul, 
  ol {
    margin: 0 0 1.2em;
    padding-left: 1.5em;
  }
  
  li {
    margin-bottom: 0.5em;
    position: relative;
  }
  
  ul li::before {
    content: '';
    position: absolute;
    left: -1em;
    top: 0.7em;
    width: 5px;
    height: 5px;
    background: var(--primary-color, #0000CD);
    border-radius: 50%;
  }
  
  h1, 
  h2, 
  h3, 
  h4 {
    margin: 1.5em 0 0.8em;
    font-weight: 600;
    line-height: 1.3;
    color: #1a202c;
  }
  
  h1:first-child,
  h2:first-child,
  h3:first-child,
  h4:first-child {
    margin-top: 0;
  }
  
  h1 { font-size: 1.5em; }
  h2 { font-size: 1.3em; }
  h3 { font-size: 1.1em; }
  h4 { font-size: 1em; }
  
  code {
    background: #f5f7fa;
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: 'SF Mono', Consolas, Monaco, 'Andale Mono', monospace;
    font-size: 0.85em;
    color: #476582;
  }
  
  pre {
    background: #f5f7fa;
    padding: 1em;
    border-radius: 6px;
    overflow-x: auto;
    margin: 1em 0;
  }
  
  blockquote {
    margin: 1em 0;
    padding: 0.5em 1em;
    border-left: 4px solid var(--primary-color, #0000CD);
    background: #f8f9fa;
    color: #4a5568;
    font-style: italic;
  }
  
  a {
    color: var(--primary-color, #0000CD);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: border-color 0.3s ease;
  }
  
  a:hover {
    border-bottom-color: var(--primary-color, #0000CD);
  }
  
  table {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;
  }
  
  th,
  td {
    padding: 0.75em;
    border: 1px solid #e2e8f0;
    text-align: left;
  }
  
  th {
    background: #f8f9fa;
    font-weight: 600;
  }
  
  tr:nth-child(even) {
    background: #f8f9fa;
  }

  .reference-card {
    @apply overflow-hidden rounded-lg ;
  }

  .reference-link {
    @apply bg-[#f8f9fa] rounded-lg p-3 flex items-center gap-3 text-[#333] no-underline transition-all;

    &:hover {
      border-bottom-color: transparent;
      background-color: #f0f0f0;
      transform: translateX(4px);
    }
  }

  .reference-icon {
    @apply ml-1
  }

  .reference-title {
    @apply text-sm
  }
}

