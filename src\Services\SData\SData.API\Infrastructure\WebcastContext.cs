﻿using Euroland.FlipIT.SData.API.Infrastructure.Entities.Webcast;
using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.SData.API.Infrastructure
{
  public class WebcastContext : DbContext
    {
        public WebcastContext(DbContextOptions<WebcastContext> options) :
           base(options)
        {

        }

        public DbSet<CompanyVimeoFolder> CompanyVimeoFolders { get; set; } = null!;
        public DbSet<UploadStatus> UploadStatuses { get; set; } = null!;
        public DbSet<Webcast> Webcasts { get; set; } = null!;
        public DbSet<WebcastHost> WebcastHosts { get; set; } = null!;
        public DbSet<WebcastSource> WebcastSources { get; set; } = null!;
        public DbSet<WebcastType> WebcastTypes { get; set; } = null!;
        public DbSet<WebcastUrl> WebcastUrls { get; set; } = null!;
        public DbSet<WebcastTranslation> WebcastTranslations { get; set; } = null!;
    }
}
