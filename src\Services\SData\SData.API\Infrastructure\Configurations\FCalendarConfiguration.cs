using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Euroland.FlipIT.SData.API.Infrastructure.Configurations;

public class FCalendarConfiguration : IEntityTypeConfiguration<FCalendar>
{
  private const string ViewName = "CS_FCalendar_V4";

  public void Configure(EntityTypeBuilder<FCalendar> builder)
  {
    builder.ToView(ViewName);
  }
}
