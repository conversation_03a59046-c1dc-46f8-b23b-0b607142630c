﻿using AutoMapper;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using System;

namespace Euroland.FlipIT.SData.API.Mappers.Deprecated.Profiles
{
  public class PressReleaseMapperProfile : Profile
    {
        public PressReleaseMapperProfile()
        {
            CreateMap<PressRelease, PressRelease>(MemberList.Destination)

                .ForMember(dto => dto.DateTime, conf =>
                    conf.MapFrom(ol => DateTime.SpecifyKind(ol.DateTime, DateTimeKind.Utc)))

                .ForMember(dto => dto.Date, conf =>
                        conf.MapFrom(ol => ol.DateTime.Date.ToString("yyyy-MM-dd")));
        }
    }
}
