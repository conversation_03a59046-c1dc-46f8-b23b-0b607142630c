using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.PressRelease;

/// <summary>
/// Extends 'pressReleases' field to type <see cref="Company.CompanyType"/>
/// </summary>
public class CompanyTypeExtensions: ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(Company.CompanyType.Name);

    descriptor.Field("pressReleases")
      .Description(
        "Gets list of pressreleases. Default pageSize=200. If you want to get message of pressrelease, should use `pressReleaseById` query for improving performance.")
      .Argument("groupByMessageType", cfg =>
        cfg.Type<BooleanType>()
          .DefaultValue(false)
          .Description("If `false`, pressRelease groups will be ungrouped and the types will show up instead of the groups. Default to `false`.")
      )
      .UsePaging(options: new HotChocolate.Types.Pagination.PagingOptions
      {
        MaxPageSize = int.MaxValue,
        DefaultPageSize = 100,
        IncludeTotalCount = true
      })
      .UseProjection()
      .UseFiltering()
      .UseSorting(typeof(PressReleaseSortType))
      .ResolveWith<PressReleaseResolvers>(
        resolvers => resolvers.GetCompanyPressReleases(default!, default!, default!, default, default)
      );

    descriptor.Field("pressReleaseById")
      .Argument("id", cfg =>
        cfg.Type<NonNullType<LongType>>()
          .Description("The ID of pressrelease")
      )
      .Argument("includeHidden", cfg =>
        cfg.Type<BooleanType>()
          .DefaultValue(true)
          .Description("Force getting a soft-deleted PressRelease if available. Default `true`")
      )
      .Argument("groupByMessageType", cfg =>
        cfg.Type<BooleanType>()
          .DefaultValue(false)
          .Description("If `false`, pressRelease groups will be ungrouped and the types will show up instead of the groups. Default to `false`")
      )
      .ResolveWith<PressReleaseResolvers>(resolver => resolver.GetPressReleaseById(default!, default!, default!, default, default, default, default));

    descriptor.Field("pressReleaseAvailableYearRange")
      .Description("Gets year range that have announcements.")
      .Argument("languageId", cfg =>
        cfg.Type<NonNullType<IntType>>()
          .DefaultValue(32)
          .Description("ID of language. Default 32 for English.")
      )
      .Argument("sourceId", cfg =>
        cfg.Type<IntType>()
          .DefaultValue(null)
          .Description("ID of data source where to get announcement. Default null.")
      )
      .Argument("from", cfg =>
        cfg.Type<DateType>()
          .DefaultValue(null)
      )
      .Argument("to", cfg =>
        cfg.Type<DateType>()
          .DefaultValue(null)
      )
      .UseProjection()
      .ResolveWith<PressReleaseResolvers>(
        resolvers => resolvers.GetAvailablePressReleaseYearRangeByCompany(
          default!,
          default!,
          default,
          default,
          default,
          default,
          default
        )
      );
  }
}
