using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Infrastructure.Factories;
using Euroland.FlipIT.SData.API.GraphQL.Types.PerformanceShare;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.Shared.CachingManager;
using Euroland.FlipIT.SData.API.GraphQL.Types.CurrencyRate;

using GreenDonut;
using entity = Euroland.FlipIT.SData.API.Infrastructure.Entities;
using System;
using System.Linq;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.Data.SqlClient;
using System.Text.Json;
using System.Threading.Tasks;
using System.Threading;

namespace Microsoft.Extensions.DependencyInjection;

public class PerformanceShareGroupDataLoader: GroupedDataLoader<int, PerformanceShareDto>
{
  private static DateTime EPOCH_DATETIME = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Unspecified);

  private readonly ISharkDbContextAbstractFactory _factory;
  private readonly IGraphQLExecutionContext _queryRequestContext;
  private readonly IConfigurableCacheManager _configurableCacheManager;
  private readonly CurrencyRateByCurrencyPairDataLoader _currencyRateByCurrencyPairDataLoader;

  public PerformanceShareGroupDataLoader(
    ISharkDbContextAbstractFactory factory,
    IGraphQLExecutionContext queryRequestContext,
    IConfigurableCacheManager configurableCacheManager,
    CurrencyRateByCurrencyPairDataLoader currencyRateByCurrencyPairDataLoader,
    IBatchScheduler batchScheduler,
    DataLoaderOptions? options = null)
    : base(batchScheduler, options)
  {
    _factory = factory ?? throw new ArgumentNullException(nameof(factory));
    _queryRequestContext = queryRequestContext ?? throw new ArgumentNullException(nameof(queryRequestContext));
    _configurableCacheManager = configurableCacheManager ?? throw new ArgumentNullException(nameof(configurableCacheManager));
    _currencyRateByCurrencyPairDataLoader = currencyRateByCurrencyPairDataLoader ?? throw new ArgumentNullException(nameof(currencyRateByCurrencyPairDataLoader));
  }

  protected override async Task<ILookup<int, PerformanceShareDto>> LoadGroupedBatchAsync(IReadOnlyList<int> keys, CancellationToken cancellationToken)
  {
    var batchInputKey = nameof(PerformanceShareType);

    var batchInputPeriods = _queryRequestContext.ScopedContextData.ContainsKey(batchInputKey)
      ? _queryRequestContext.ScopedContextData[batchInputKey] as Dictionary<int, List<PerformanceInput>>
      : new Dictionary<int, List<PerformanceInput>>();

    var exchangeCurrency = _queryRequestContext.ExchangeCurrency;
    var hasExchangeCurrency = !string.IsNullOrEmpty(exchangeCurrency);
    // Flatten the dictionary
    var bathInputs = batchInputPeriods.Values.SelectMany(inputs => inputs, (inputs, i) => i).ToList();

    using var sharkDbContext = await _factory.CreateDbContextAsync(_queryRequestContext.UseRealtime, _queryRequestContext.UseCloud);

    if (hasExchangeCurrency)
    {
      var inputsHasCurrency = bathInputs.Where(input => !string.IsNullOrWhiteSpace(input.InstrumentCurrencyCode)).ToList();

      var currencyPairs = inputsHasCurrency
        .Select(input => $"{input.InstrumentCurrencyCode}{exchangeCurrency}".ToLower())
        .Distinct()
        .ToList();

      // Preload the Id of CurrencyRates
      var currencyRates = currencyPairs.Any()
        ? await _currencyRateByCurrencyPairDataLoader.LoadAsync(currencyPairs, cancellationToken)
        : Enumerable.Empty<CurrencyRateDto>();

      foreach (var i in inputsHasCurrency)
      {
        var currencyRate = currencyRates.FirstOrDefault(p => p != null &&
          p.Pair.ToLower() == $"{i.InstrumentCurrencyCode}{exchangeCurrency}".ToLower());

        if (currencyRate != null)
        {
          i.CurrencyRateId = currencyRate.Id;
        }
      }
    }

    var shares = await LoadPerformanceShareByDateAsync(
      sharkDbContext,
      bathInputs,
      _queryRequestContext.UseRealtime,
      _queryRequestContext.UseAdjClose
    )
    .WithAutoMapper()
    .ToProjection<PerformanceShareDto>()
    .ToListAsync(cancellationToken);

    return shares.ToLookup(p => p.InstrumentId);
  }

  public static DateTime GetStartDateFromPeriod(PerformancePeriodDto period)
  {
    var today = DateTime.UtcNow.Date;

    switch (period)
    {
      case PerformancePeriodDto.YTD: return new DateTime(today.Year, 1, 1).Date;
      case PerformancePeriodDto.ONE_YEAR: return today.AddYears(-1).Date;
      case PerformancePeriodDto.TWO_YEARS: return today.AddYears(-2).Date;
      case PerformancePeriodDto.THREE_YEARS: return today.AddYears(-3).Date;
      case PerformancePeriodDto.FIVE_YEARS: return today.AddYears(-5).Date;
      case PerformancePeriodDto.TEN_YEARS: return today.AddYears(-10).Date;

      case PerformancePeriodDto.ONE_MONTH: return today.AddMonths(-1).Date;
      case PerformancePeriodDto.THREE_MONTHS: return today.AddMonths(-3).Date;
      case PerformancePeriodDto.SIX_MONTHS: return today.AddMonths(-6).Date;

      case PerformancePeriodDto.ONE_WEEK: return today.AddDays(-1 * 7).Date;
      case PerformancePeriodDto.TWO_WEEKS: return today.AddDays(-2 * 7).Date;
      case PerformancePeriodDto.FIFTY_TWO_WEEKS: return today.AddDays(-52 * 7).Date;
      case PerformancePeriodDto.ALL_TIME: return EPOCH_DATETIME;
      default: throw new NotSupportedException($"Not support type of period PerformancePeriodDto.{period}");
    }
  }

  public static string ComputeCacheKey(int instrumentId, PerformancePeriodDto period)
  {
    return $"{instrumentId}-{period}";
  }

  public static int DecomputeInstrumentIdFromCacheKey(string key)
  {
    return int.Parse(key.Split('-')[0]);
  }

  private static IQueryable<entity.PerformanceShare> LoadPerformanceShareByDateAsync(
    SharkDbContextBase sharkDbContext,
    IEnumerable<PerformanceInput> inputs,
    bool useRealtimeData,
    bool adjClosed)
  {
    var instrumentPriceTable = sharkDbContext.InstrumentPrice.EntityType.GetSchemaQualifiedTableName();
    var instrumentHistoryTable = sharkDbContext.InstrumentHistory.EntityType.GetSchemaQualifiedTableName();
    var currencyRateTable = sharkDbContext.CurrencyRates.EntityType.GetSchemaQualifiedTableName();
    var currencyRateHistoryTable = sharkDbContext.CurrencyRateHistories.EntityType.GetSchemaQualifiedTableName();

    var inputJsonParam = new SqlParameter("json", System.Data.SqlDbType.VarChar)
    {
      Value = JsonSerializer.Serialize(inputs)
    };

    // Requried for CorporateActionAdjustmentFactorCTE, otherwise, it it will be added @adjClosed=0 by default.
    var adjCloseParam = new SqlParameter(CorporateActionAdjustmentFactorCTEDbCommandInterceptor.ADJ_CLOSED_PARAMETER_NAME, System.Data.SqlDbType.Bit)
    {
      Value = adjClosed
    };

    // Requried for CorporateActionAdjustmentFactorCTE
    var instrumentIdsParam = new SqlParameter(CorporateActionAdjustmentFactorCTEDbCommandInterceptor.INSTRUMENT_IDS_PARAMETER_NAME, System.Data.SqlDbType.VarChar)
    {
      Value = JsonSerializer.Serialize(inputs.Select(i => i.InstrumentId))
    };

    var sqlQuery = BuildSqlQuery(
      currencyRateTable!,
      currencyRateHistoryTable!,
      instrumentPriceTable!,
      instrumentHistoryTable!
    );
    return sharkDbContext.Set<entity.PerformanceShare>().FromSqlRaw(
      sqlQuery,
      inputJsonParam,
      adjCloseParam,
      instrumentIdsParam,
      new SqlParameter("realtime", System.Data.SqlDbType.Bit) { Value = useRealtimeData }
    ).TagWith(CorporateActionAdjustmentFactorCTEDbCommandInterceptor.TOKEN);
  }

  /// <summary>
  /// Builds SQL query to return the change, lowest, highest of price at which
  /// a security has traded during the time period.
  /// Query includes 3 <see cref="SqlParameter"/>(s) for parameterizing:
  ///   @json: The input of period in form of string: e.g. [{"Period":0,"FromDate":"1989-07-27"},{"Period":5,"FromDate":"2024-04-27"},{"Period":4,"FromDate":"2024-06-27"}]
  ///   input.[CurrencyRateId]: The ID of currency-pair (instrument currency-quote currency) if we want to convert instrument's price into another currency
  ///   @instrumentId: The ID of instrument to get prices.
  /// Query will return the table formed with 4 column: [Period, FromDate, High, Low, ChangePercentage]
  /// </summary>
  /// <param name="currencyRateTableName">The [{currencyRateTableName}] table</param>
  /// <param name="currencyRateHistoryTableName">The [{currencyRateHistoryTableName}] table</param>
  /// <param name="instrumentPriceTableName">The InstrumentPrice table</param>
  /// <param name="instrumentHistoryTableName">The {instrumentHistoryTableName} table</param>
  /// <returns></returns>
  private static string BuildSqlQuery(
    string currencyRateTableName,
    string currencyRateHistoryTableName,
    string instrumentPriceTableName,
    string instrumentHistoryTableName)
  {
    // Query includes 3 parameters:
    //	@json VARCHAR(MAX)
    //	input.[CurrencyRateId] INT
    //	@instrumentId INT
    return @$"
      SELECT
        input.[InstrumentId],
        input.[Period],
        input.[FromDate],
        high.[High] AS [High],
        high.[HighDate] AS [HighDate],
        low.[Low] AS [Low],
        low.[LowDate] AS [LowDate],
        performance.[ChangePercentage],
        performance.[ClosePrice],
			  performance.[CloseRate]
      FROM OPENJSON(@json)
        WITH (
          [InstrumentId] INT,
          [CurrencyRateId] INT,
          [AdjClose] BIT,
          [Period] INT,
          [FromDate] SMALLDATETIME
        ) AS input
      OUTER APPLY (
        SELECT TOP(1)
          ((p.[Last] * COALESCE(cRate.[cRate], 1)) - (h.[Close] * COALESCE(hRate.[hRate], 1))) / (h.[Close] * COALESCE(hRate.[hRate], 1)) * 100
          AS ChangePercentage,
          h.[Close] as ClosePrice,
			    COALESCE(hRate.[hRate], 1) as CloseRate
        FROM (
          SELECT
            p1.[InstrumentId],
            CAST(p1.[Last] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Last],
            p1.[Date]
          FROM (
            SELECT [InstrumentId], [Last], [Date] FROM rt_InstrumentPrice rt WHERE @realtime = 1
            UNION ALL
            SELECT [InstrumentId], [Last], [Date] FROM InstrumentPrice nrt
            WHERE @realtime = 0 OR NOT EXISTS (SELECT 1 FROM rt_InstrumentPrice WHERE  @realtime = 1 AND InstrumentId = nrt.InstrumentId)
          ) AS p1
          OUTER APPLY (
            SELECT TOP 1 EXP(SUM(LOG(CumulativeAdjustmentFactor))) AS CumulativeAdjustmentFactor
            FROM {CorporateActionAdjustmentFactorCTEDbCommandInterceptor.CumulativeAdjustmentsTable}
            WHERE InstrumentId = p1.InstrumentId AND p1.[Date] < ActionDate
            GROUP BY InstrumentId
          ) AS ca
        ) AS p
        CROSS APPLY (
          SELECT TOP(1)
            CAST(h1.[Close] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Close]
            ,h1.[Date]
          FROM [{instrumentHistoryTableName}] AS h1
          OUTER APPLY (
            SELECT TOP 1 EXP(SUM(LOG(CumulativeAdjustmentFactor))) AS CumulativeAdjustmentFactor
            FROM {CorporateActionAdjustmentFactorCTEDbCommandInterceptor.CumulativeAdjustmentsTable}
            WHERE InstrumentId = h1.InstrumentId AND h1.[Date] < ActionDate
            GROUP BY InstrumentId
          ) AS ca
          WHERE h1.InstrumentId = p.InstrumentId
			      AND (
              input.FromDate IS NULL -- All the time
              OR CAST(h1.[Date] AS Date) <= CAST(input.FromDate AS Date)
            )
          ORDER BY h1.[Date] DESC
        ) AS h
        OUTER APPLY (
          SELECT TOP(1)
            [cRate] AS cRate
          FROM
          (
            SELECT TOP(1)
              [cRate],
              CAST(cDate AS Date) as [cDate]
            FROM [{currencyRateTableName}]
            WHERE cId = input.[CurrencyRateId]

            UNION ALL

            SELECT
              ch.chRate AS [cRate],
              ch.chDate AS [cDate]
            FROM [{currencyRateHistoryTableName}] as ch
            WHERE ch.cId = input.[CurrencyRateId]
          ) AS cc WHERE cc.cDate = CAST(p.[Date] AS Date)
        ) as cRate
        OUTER APPLY (
          SELECT chRate AS [hRate]
          FROM [{currencyRateHistoryTableName}] as ch
          WHERE ch.cId = input.[CurrencyRateId] AND ch.chDate = h.[Date]
        ) as hRate
        WHERE p.InstrumentId = input.[InstrumentId]
      ) AS performance

      OUTER APPLY (
        SELECT TOP(1) h.[High] * COALESCE(rate.chRate, 1) AS [High], h.[Date] AS [HighDate]
        FROM
        (
          SELECT TOP(1)
            p.[High],
            p.[Date],
            p.[InstrumentId]
          FROM (
            SELECT
              p1.[InstrumentId],
              CAST(COALESCE(p1.[High], p1.[Last]) * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [High],
              p1.[Date]
            FROM (
              SELECT [InstrumentId], [High], [Last], [Date] FROM rt_InstrumentPrice rt WHERE @realtime = 1
              UNION ALL
              SELECT [InstrumentId], [High], [Last], [Date] FROM InstrumentPrice nrt
              WHERE @realtime = 0 or NOT EXISTS (SELECT 1 FROM rt_InstrumentPrice WHERE @realtime = 1 AND InstrumentId = nrt.InstrumentId)
            ) AS p1
            OUTER APPLY (
              SELECT TOP 1 EXP(SUM(LOG(CumulativeAdjustmentFactor))) AS CumulativeAdjustmentFactor
              FROM {CorporateActionAdjustmentFactorCTEDbCommandInterceptor.CumulativeAdjustmentsTable}
              WHERE InstrumentId = p1.InstrumentId AND p1.[Date] < ActionDate
              GROUP BY InstrumentId
            ) AS ca
          ) AS p

          UNION ALL

          SELECT
            CAST(COALESCE(h.[High], h.[Close]) * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [High],
            h.[Date],
            h.[InstrumentId]
          FROM [{instrumentHistoryTableName}] as h
          OUTER APPLY (
            SELECT TOP 1 EXP(SUM(LOG(CumulativeAdjustmentFactor))) AS CumulativeAdjustmentFactor
            FROM {CorporateActionAdjustmentFactorCTEDbCommandInterceptor.CumulativeAdjustmentsTable}
            WHERE InstrumentId = h.InstrumentId AND h.[Date] < ActionDate
            GROUP BY InstrumentId
          ) AS ca
        ) AS h
        OUTER APPLY (
          SELECT chRate
          FROM [{currencyRateHistoryTableName}] as ch
          WHERE ch.cId = input.[CurrencyRateId] AND ch.chDate = h.[Date]
        ) as rate
        WHERE h.InstrumentId = input.[InstrumentId]
          AND (
            input.FromDate IS NULL -- All the time
            OR CAST(h.[Date] AS Date) >= CAST(input.FromDate AS Date)
          )
        ORDER BY h.[High] * COALESCE(rate.chRate, 1) DESC
      ) AS high

      OUTER APPLY (
        SELECT TOP(1) l.[Low] * COALESCE(rate.chRate, 1) AS [Low], l.[Date] AS [LowDate]
        FROM
        (
          SELECT TOP(1)
            p.[Low],
            p.[Date],
            p.[InstrumentId]
          FROM (
            SELECT
              p1.[InstrumentId],
              CAST(COALESCE(p1.[Low], p1.[Last]) * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Low],
              p1.[Date]
            FROM (
              SELECT [InstrumentId], [Low], [Last], [Date] FROM rt_InstrumentPrice rt WHERE @realtime = 1
              UNION ALL
              SELECT [InstrumentId], [Low], [Last], [Date] FROM InstrumentPrice nrt
              WHERE @realtime = 0 OR NOT EXISTS (SELECT 1 FROM rt_InstrumentPrice WHERE @realtime = 1 AND InstrumentId = nrt.InstrumentId)
            ) AS p1
            OUTER APPLY (
              SELECT TOP 1 EXP(SUM(LOG(CumulativeAdjustmentFactor))) AS CumulativeAdjustmentFactor
              FROM {CorporateActionAdjustmentFactorCTEDbCommandInterceptor.CumulativeAdjustmentsTable}
              WHERE InstrumentId = p1.InstrumentId AND p1.[Date] < ActionDate
              GROUP BY InstrumentId
            ) AS ca
          ) AS p

          UNION ALL

          SELECT
            CAST(COALESCE(h.[Low], h.[Close]) * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Low],
            h.[Date],
            h.[InstrumentId]
          FROM [{instrumentHistoryTableName}] as h
          OUTER APPLY (
            SELECT TOP 1 EXP(SUM(LOG(CumulativeAdjustmentFactor))) AS CumulativeAdjustmentFactor
            FROM {CorporateActionAdjustmentFactorCTEDbCommandInterceptor.CumulativeAdjustmentsTable}
            WHERE InstrumentId = h.InstrumentId AND h.[Date] < ActionDate
            GROUP BY InstrumentId
          ) AS ca
        ) AS l
        OUTER APPLY (
          SELECT chRate
          FROM [{currencyRateHistoryTableName}] as ch
          WHERE ch.cId = input.[CurrencyRateId] AND ch.chDate = l.[Date]
        ) as rate
        WHERE l.InstrumentId = input.[InstrumentId]
          AND (
            input.FromDate IS NULL -- All the time
            OR CAST(l.[Date] AS Date) >= CAST(input.FromDate AS Date)
          )
        ORDER BY l.[Low] * COALESCE(rate.chRate, 1) ASC
      ) AS low
    ";
  }
}
