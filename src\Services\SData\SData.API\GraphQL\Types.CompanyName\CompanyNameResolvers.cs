using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.NetCore.ToolsFramework.Localization;
using HotChocolate;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.CompanyName;

public class CompanyNameResolvers
{
  public IQueryable<CompanyNameDto?> GetCompanyNameByCompany(
    [Parent] CompanyDto company,
    [Service] ILanguageToCultureProvider languageToCultureProvider,
    SharkDbContextBase sharkDbContextBase,
    string cultureName,
    CancellationToken cancellationToken)
  {
    if (languageToCultureProvider == null)
    {
      throw new ArgumentNullException(nameof(languageToCultureProvider), $"Not found registered service {typeof(ILanguageToCultureProvider).FullName}. Consider whether it is registered do DI system.");
    }

    var defaultCulture = CultureInfo.CurrentCulture.Name;

    if (!string.IsNullOrEmpty(cultureName))
    {
      var language = languageToCultureProvider.GetLanguage(cultureName);

      if (language != null)
      {
        cultureName = Regex.Replace(language.SupportedCultures[0].EnglishName, @"^\s*(\w+)(\s+[(][^()]+[)])?$", m => m.Groups[1].Value);
      }
    }
    else
    {
      cultureName = "english";
    }

    cultureName = cultureName.Trim();


    return sharkDbContextBase
      .CompanyNames
      .AsNoTracking()
      .Where(cn => cn.CompanyCode == company.Code && cn.CompanyLang == cultureName)
      .WithAutoMapper()
      .ToProjection<CompanyNameDto>();
  }
}
