using System;
using Euroland.FlipIT.SData.API.Helpers;

namespace Euroland.FlipIT.SData.API.Dto;

public class InstrumentPriceDto: IDtoObject
{
  public int InstrumentId { get; set; }
  public decimal? Bid { get; set; }
  public decimal? Ask { get; set; }
  public decimal? Open { get; set; }
  public decimal? Last { get; set; }
  public decimal? High { get; set; }
  public decimal? Low { get; set; }
  public long? Volume { get; set; }
  public decimal? Mid { get; set; }
  public decimal? PrevClose { get; set; }
  public decimal? TodayTurnover { get; set; }
  public decimal? Vwap { get; set; }
  public int? BidSize { get; set; }
  public int? AskSize { get; set; }
  public decimal? OfficialClose { get; set; }
  // Change, ChangePercentage, MidChange, MidChangePercentage can be calculated at client side
  // Change = Last - PrevClose
  // ChangePercentage = (Last-PrevClose) * 100 / PrevClose
  // MidChange = Mid - PrevClose
  // MidChangePercentage = (Mid-PrevClose) * 100 / PrevClose
  public decimal MidChange { get; set; }
  public decimal MidChangePercentage { get; set; }
  public decimal Change { get; set; }
  public decimal ChangePercentage { get; set; }
  private DateTime? _date;
  private DateTime? _lastRowChange;
  private DateTime? _officialCloseDate;
  public DateTime? Date
  {
    get
    {
      return _date;
    }
    set
    {
      // Assume DateTime values of InstrumentPrice in Database always be in CEST timezone.
      // We need to convert to UTC before return to client.
      if(value != null)
      {
        _date = value.Value.Kind != DateTimeKind.Utc ? value.Value.CestToUtc() : value;
      }
    }
  }
  public DateTime? LastRowChange
  {
    get
    {
      return _lastRowChange;
    }
    set
    {
      if(value != null)
      {
        _lastRowChange = value.Value.Kind != DateTimeKind.Utc ? value.Value.CestToUtc() : value;
      }
    }
  }

  public DateTime? OfficialCloseDate
  {
    get
    {
      return _officialCloseDate;
    }
    set
    {
      if(value != null)
      {
        _officialCloseDate = value.Value.Kind != DateTimeKind.Utc ? value.Value.CestToUtc() : value;
      }
    }
  }
}
