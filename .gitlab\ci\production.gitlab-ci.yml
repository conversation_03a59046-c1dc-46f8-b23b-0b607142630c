"ee-v-webcat15,16":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
    - .prevent-fetching
    - .unpack-deployment
  stage: deploy:production
  dependencies:
    - build-prod
  variables:
    DEPLOY_ENVIRONMENT: Production
    DEPLOY_SERVICE_URL_SERVER1: 'https://ee-v-webcat151.euroland.com:8172/msdeploy.axd'
    DEPLOY_SERVICE_URL_SERVER2: 'https://ee-v-webcat161.euroland.com:8172/msdeploy.axd'
  script:
    - |
      ECHO Deploying to ee-v-webcat161...
      call ".\build\msdeploy.bat" --source "src/Services/SData/SData.API/bin/Release/net6.0/package" --iisAppPath "production-site/tools/sdata-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/package" --iisAppPath "production-site/tools/fundamental-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/package" --iisAppPath "production-site/tools/ApiGateway" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying to ee-v-webcat151...
      call ".\build\msdeploy.bat" --source "src/Services/SData/SData.API/bin/Release/net6.0/package" --iisAppPath "production-site/tools/sdata-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/package" --iisAppPath "production-site/tools/fundamental-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/package" --iisAppPath "production-site/tools/ApiGateway" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
  only:
    refs:
      - master
  environment:
    name: Ground
    url: 'https://gr-web-ws1.eurolandir.com/tools/sdata-api/graphql?companycode=dk-cbg'

"ne-web-ws1,2":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
    - .prevent-fetching
    - .unpack-deployment
  stage: deploy:production
  dependencies:
    - build-prod
  variables:
    DEPLOY_ENVIRONMENT: CloudNE
    DEPLOY_SERVICE_URL_SERVER2: 'https://ne-web-haproxy.northeurope.cloudapp.azure.com:8173/msdeploy.axd'
    DEPLOY_SERVICE_URL_SERVER1: 'https://ne-web-haproxy.northeurope.cloudapp.azure.com:8172/msdeploy.axd'
  script:
    - |
      ECHO Deploying to ne-web-ws2...
      call ".\build\msdeploy.bat" --source "src/Services/SData/SData.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/sdata-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/fundamental-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/ApiGateway" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying to ne-web-ws1...
      call ".\build\msdeploy.bat" --source "src/Services/SData/SData.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/sdata-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/fundamental-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/ApiGateway" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
  only:
    refs:
      - master
  environment:
    name: CloudNE
    url: 'https://ne-web-ws1.eurolandir.com/tools/sdata-api/graphql?companycode=dk-cbg'

"ea-web-ws1,2":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
    - .prevent-fetching
    - .unpack-deployment
  stage: deploy:production
  dependencies:
    - build-prod
  variables:
    DEPLOY_ENVIRONMENT: CloudEA
    DEPLOY_SERVICE_URL_SERVER2: 'https://ea-web-haproxy.eastasia.cloudapp.azure.com:8173/msdeploy.axd'
    DEPLOY_SERVICE_URL_SERVER1: 'https://ea-web-haproxy.eastasia.cloudapp.azure.com:8172/msdeploy.axd'
  script:
    - |
      ECHO Deploying to ne-web-ws2...
      call ".\build\msdeploy.bat" --source "src/Services/SData/SData.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/sdata-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/fundamental-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/ApiGateway" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying to ne-web-ws1...
      call ".\build\msdeploy.bat" --source "src/Services/SData/SData.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/sdata-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/fundamental-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/ApiGateway" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
  only:
    refs:
      - master
  environment:
    name: CloudEA
    url: 'https://ea-web-ws1.eurolandir.com/tools/sdata-api/graphql?companycode=dk-cbg'

"uae-web-ws1,2":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
    - .prevent-fetching
    - .unpack-deployment
  stage: deploy:production
  dependencies:
    - build-prod
  variables:
    DEPLOY_ENVIRONMENT: CloudUAE
    DEPLOY_SERVICE_URL_SERVER2: 'https://uaewebhaproxy1.uaenorth.cloudapp.azure.com:8173/msdeploy.axd'
    DEPLOY_SERVICE_URL_SERVER1: 'https://uaewebhaproxy1.uaenorth.cloudapp.azure.com:8172/msdeploy.axd'
  script:
    - |
      ECHO Deploying to ne-web-ws2...
      call ".\build\msdeploy.bat" --source "src/Services/SData/SData.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/sdata-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/fundamental-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/ApiGateway" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying to ne-web-ws1...
      call ".\build\msdeploy.bat" --source "src/Services/SData/SData.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/sdata-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/fundamental-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/ApiGateway" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
  only:
    refs:
      - master
  environment:
    name: CloudUAE
    url: 'https://uae-web-ws1.eurolandir.com/tools/sdata-api/graphql?companycode=dk-cbg'

"ksa-web-ws1,2":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
    - .prevent-fetching
    - .unpack-deployment
  stage: deploy:production
  dependencies:
    - build-prod
  variables:
    DEPLOY_ENVIRONMENT: CloudKSA
    DEPLOY_SERVICE_URL_SERVER2: 'https://**************:8173/msdeploy.axd'
    DEPLOY_SERVICE_URL_SERVER1: 'https://**************:8172/msdeploy.axd'
  script:
    - |
      ECHO Deploying to ne-web-ws2...
      call ".\build\msdeploy.bat" --source "src/Services/SData/SData.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/sdata-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/fundamental-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/ApiGateway" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying to ne-web-ws1...
      call ".\build\msdeploy.bat" --source "src/Services/SData/SData.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/sdata-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/fundamental-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/ApiGateway" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
  only:
    refs:
      - master
  environment:
    name: CloudKSA
    url: 'https://ksa-web-ws1.eurolandir.com/tools/sdata-api/graphql?companycode=dk-cbg'

"cn-web-ws1,2":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
    - .prevent-fetching
    - .unpack-deployment
  stage: deploy:production
  dependencies:
    - build-prod
  variables:
    DEPLOY_ENVIRONMENT: CloudCN
    DEPLOY_SERVICE_URL_SERVER2: 'https://cn-web-haproxy.chinanorth.cloudapp.chinacloudapi.cn:8173/msdeploy.axd'
    DEPLOY_SERVICE_URL_SERVER1: 'https://cn-web-haproxy.chinanorth.cloudapp.chinacloudapi.cn:8172/msdeploy.axd'
  script:
    - |
      ECHO Deploying to ne-web-ws2...
      call ".\build\msdeploy.bat" --source "src/Services/SData/SData.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/sdata-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/fundamental-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/ApiGateway" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying to ne-web-ws1...
      call ".\build\msdeploy.bat" --source "src/Services/SData/SData.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/sdata-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/fundamental-api" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
      call ".\build\msdeploy.bat" --source "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/package" --iisAppPath "Default Web Site/tools/ApiGateway" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
  only:
    refs:
      - master
  environment:
    name: CloudCN
    url: 'https://cn-web-ws1.eurolandir.com/tools/sdata-api/graphql?companycode=dk-cbg'
