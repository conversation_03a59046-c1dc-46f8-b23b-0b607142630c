using System.Threading.Tasks;

namespace Euroland.FlipIT.Shared.CachingManager;

/// <summary>
/// Interface for managing caching operations.
/// </summary>
/// <typeparam name="T">The type of the cached object.</typeparam>
/// <typeparam name="T2">The type of the key used for caching.</typeparam>
/// <remarks>
/// Provides methods to get, set, and remove cache entries asynchronously and synchronously.
/// </remarks>
public interface ICachingManager
{
  /// <summary>
  /// Asynchronously retrieves a cached object based on the provided key.
  /// </summary>
  /// <typeparam name="T">The type of the cached object to retrieve.</typeparam>
  /// <typeparam name="T2">The type of the key used to identify the cached object.</typeparam>
  /// <param name="key">The key associated with the cached object.</param>
  /// <returns>A task representing the asynchronous operation, with the cached object as its result.</returns>
  Task<T> GetCacheAsync<T, T2>(T2 key);

  /// <summary>
  /// Retrieves a cached object based on the provided key.
  /// </summary>
  /// <typeparam name="T">The type of the cached object to retrieve.</typeparam>
  /// <typeparam name="T2">The type of the key used to identify the cached object.</typeparam>
  /// <param name="key">The key associated with the cached object.</param>
  /// <returns>The cached object associated with the specified key.</returns>
  T GetCache<T, T2>(T2 key);

  /// <summary>
  /// Stores an object in the cache with the specified key.
  /// </summary>
  /// <typeparam name="T">The type of the object to cache.</typeparam>
  /// <typeparam name="T2">The type of the key used to store the object in the cache.</typeparam>
  /// <param name="obj">The object to be cached.</param>
  /// <param name="key">The key associated with the object to be cached.</param>
  /// <returns>The cached object.</returns>
  T SetCache<T, T2>(T obj, T2 key);

  /// <summary>
  /// Removes one or more cached objects based on the provided keys.
  /// </summary>
  /// <typeparam name="T">The type of the keys used to identify the cached objects.</typeparam>
  /// <param name="keys">An array of keys associated with the cached objects to be removed.</param>
  void RemoveCache<T>(params T[] keys);
}
