using System;
using Euroland.FlipIT.SData.API.Helpers;

namespace Euroland.FlipIT.SData.API.Dto;

public class FCalendarDto: IDtoObject
{
  public int FCalendarId { get; set; }

  public string CompanyCode { get; set; }

  private DateTime? _dateTime { get; set; }

  public DateTime? DateTime
  {
    get => _dateTime;
    set
    {
      if(value != null)
      {
        _dateTime = value.Value.Kind != DateTimeKind.Utc ? value.Value.ToUtcKindOnly() : value;
      }
    }
  }

  public short? FCEventId { get; set; }

  public short? LocationId { get; set; }

  public short? TimezoneId { get; set; }

  public string? FYEndMonth { get; set; }

  private DateTime? _lastUpdated { get; set; }

  public DateTime? LastUpdated
  {
    get => _lastUpdated;
    set
    {
      if(value != null)
      {
        _lastUpdated = value.Value.Kind != DateTimeKind.Utc ? value.Value.CestToUtc() : value;
      }
    }
  }

  public short? MarketId { get; set; }

  public bool? MonthOnly { get; set; }

  public string EventName { get; set; }
}
