using Euroland.FlipIT.SData.API.Constants;
using Euroland.FlipIT.SData.API.DTOs.Deprecated;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Euroland.FlipIT.SData.API.Services.Interfaces
{
  public interface IInstrumentService: IAsyncDisposable, IDisposable
    {
        /// <summary>
        /// Get instrument historical data by InstrumentId
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<InstrumentHistory>> GetHistoricalDataAsync(
            int instrumentId,
            bool isIntraday,
            Period period,
            DateTime? fromDate,
            DateTime? toDate,
            bool isRT = false,
            CancellationToken ct = default);

        /// <summary>
        /// Get instrument performance by InstrumentId and number of years
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<InstrumentYearlyPerformancePrice>> GetYearlyPerformanceDataAsync(
            List<int> instrumentIds,
            int numberOfYears,
            CancellationToken cancellationToken,
            string? toCurrency = null);

        Task<StockData> GetStockOverview(
            int instrumentId,
            Periods period = Periods.ONE_MONTH,
            DateTime? from = null,
            DateTime? to = null,
            string? toCurrency = null);

        Task<IEnumerable<StockData>?> GetDailyStockData(
            int instrumentId,
            DateTime fromDate,
            DateTime toDate,
            List<int> peers,
            List<int> indices,
            List<int> mas,
            string? toCurrency = null,
            bool isRT = false);

        Task<IEnumerable<StockData>?> GetWeeklyStockData(
            int instrumentId,
            DateTime fromDate,
            DateTime toDate,
            List<int> peers,
            List<int> indices,
            List<int> mas,
            string? toCurrency = null,
            bool isRT = false);

        Task<IEnumerable<StockData>?> GetMonthlyStockData(
            int instrumentId,
            DateTime fromDate,
            DateTime toDate,
            List<int> peers,
            List<int> indices,
            List<int> mas,
            string? toCurrency = null,
            bool isRT = false);

        IQueryable<DTOs.Deprecated.Instrument> GetInstruments(List<int> instrumentIds,
            int dayPeriod = 10,
            bool isRT = false,
            string? toCurrency = null,
            CancellationToken ct = default);

        Task<DateTime> GetStartingDateByInstrument(int instrumentId);

        Task<List<LatestShareTradesDto>> GetLatestShareTrades(int instrumentId, int count,
            string? toCurrency = null, bool isRT = false, CancellationToken cancellationToken = default);

        Task<List<LatestShareTradesDto>> GetTodayShareTrades(int instrumentId, string? toCurrency, bool isRT = false, CancellationToken cancellationToken = default);

        Task<Infrastructure.Entities.Instrument?> GetInstrumentByIdAsync̣(int instrumentId, bool isRT = false);

        IQueryable<DividendEvent> GetDividendEvents(int instrumentId, DateTime? fromDate, DateTime? toDate, bool isRT = false);

        IQueryable<EarningEvent> GetEarningsEvents(string companyCode, DateTime? fromDate, DateTime? toDate, bool isRT = false);

        Task<IEnumerable<InstrumentDailyData>> GetLatestDailyDataAsync(int instrumentId, bool isRT = false);
    }
}
