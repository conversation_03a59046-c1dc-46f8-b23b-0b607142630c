using System.Linq;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.SData.API.GraphQL.Types.Instrument.Dtos;
using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using HotChocolate;
using HotChocolate.Resolvers;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;

#pragma warning disable CS8601, CS8602 // Possible null reference assignment.

internal partial class InstrumentResolvers
{
  public IQueryable<InstrumentDailyDataDto> GetLastDayIntraday(
    [Parent] InstrumentDto instrument,
    [Service] IGraphQLExecutionContext graphQLExecutionContext,
    SharkDbContextBase sharkDbContext,
    IResolverContext resolverContext
  )
  {
    var hasAdjustedClosePrice = graphQLExecutionContext.UseAdjClose;
    var useRealtimeData = graphQLExecutionContext.UseRealtime;
    var exchangeCurrency = graphQLExecutionContext.ExchangeCurrency;
    var hasExchangeCurrency = !string.IsNullOrEmpty(exchangeCurrency);

    var intradayQuery = GetLastDayIntradayQuery(sharkDbContext, instrument.Id, hasAdjustedClosePrice, useRealtimeData)
      .TagWith($"{CorporateActionAdjustmentFactorCTEDbCommandInterceptor.TOKEN}");

    if (!hasExchangeCurrency || string.IsNullOrEmpty(instrument.CurrencyCode))
    {
      return intradayQuery
        .OrderByArgumentOrDefault(
          resolverContext,
          (q) => q.OrderByDescending(p => p.Date)
        )
        .WithAutoMapper()
        .ToProjection<InstrumentDailyDataDto>();
    }

    var rateView = sharkDbContext.InstrumentCurrencyPairQueryable($"{instrument.CurrencyCode}{exchangeCurrency}");

    var query = (
      from price in intradayQuery
      from rate in rateView.Where(r => r.cDate.Date == price.Date.Date).DefaultIfEmpty()
      select new { price, rate }
    ).Select(grouping => new InstrumentDailyData()
      {
        ID = grouping.price.ID,
        InstrumentID = grouping.price.InstrumentID,
        Date = grouping.price.Date,
        Close = grouping.price.Close * (grouping.rate != null ? grouping.rate.cRate : 1m),
        Volume = grouping.price.Volume,
      }).OrderByArgumentOrDefault(
        resolverContext,
        q => q.OrderByDescending(p => p.Date)
      );

    return query.WithAutoMapper().ToProjection<InstrumentDailyDataDto>();
  }

  private IQueryable<InstrumentDailyData> GetLastDayIntradayQuery(SharkDbContextBase sharkDbContext, int instrumentId,
    bool hasAdjustedClosePrice, bool useRealtimeData)
  {
    var instrumentDailyDataTable = useRealtimeData ? "rt_daily_history" : "daily_history";
    var idFieldOfInstrumentDailyData = (instrumentDailyDataTable == "daily_history" && sharkDbContext is RealtimeSharkDbContext) ? "hID" : "ID";

    var (
      cet_timezone_offset, // Used to convert from CET to UTC
      market_timezone_offset // Used to convert from CET to Market's timezone
    ) = TimezoneOffset.GetTimezoneOffset(sharkDbContext, instrumentId);

    return sharkDbContext.InstrumentDailyData
      .FromSqlRaw(@$"
        SELECT
          idd.[{idFieldOfInstrumentDailyData}] AS ID,
          idd.InstrumentID,
          CAST(DATEADD(MINUTE, -@cet_offset, idd.hDate) AS DATETIME) AS [hDate],
          CAST(idd.[hClose] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [hClose],
          idd.hSize AS [hSize]
        FROM [{instrumentDailyDataTable}] idd
        OUTER APPLY (
          SELECT TOP 1 EXP(SUM(LOG(CumulativeAdjustmentFactor))) AS CumulativeAdjustmentFactor
          FROM {CorporateActionAdjustmentFactorCTEDbCommandInterceptor.CumulativeAdjustmentsTable}
          WHERE InstrumentId = idd.InstrumentId AND idd.[hDate] < ActionDate
          GROUP BY InstrumentId
        ) AS ca
        CROSS APPLY (
              SELECT
                MAX(dh.hDate) as maxDate
                FROM [{instrumentDailyDataTable}] dh WHERE dh.InstrumentID = @instrumentId
        ) AS d
        WHERE idd.InstrumentID = @instrumentId
          AND CAST(DATEADD(MINUTE, @market_offset, idd.hDate) AS DATE) = CAST(DATEADD(MINUTE, @market_offset, d.maxDate) AS DATE)
        ",
        new SqlParameter("adjClose", System.Data.SqlDbType.Bit) { Value = hasAdjustedClosePrice },
        new SqlParameter("instrumentId", System.Data.SqlDbType.Int) { Value = instrumentId },
        new SqlParameter("cet_offset", System.Data.SqlDbType.Int) { Value = cet_timezone_offset },
        new SqlParameter("market_offset", System.Data.SqlDbType.Int) { Value = market_timezone_offset });
  }
}
#pragma warning restore CS8601,CS8602 // Possible null reference assignment.
