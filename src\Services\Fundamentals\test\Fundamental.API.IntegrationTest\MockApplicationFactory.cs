using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;

namespace Fundamental.API.IntegrationTest;

public class MockApplicationFactory<TProgram>
    : WebApplicationFactory<TProgram> where TProgram: class
{
   protected override IHost CreateHost(IHostBuilder builder)
    {
        builder.ConfigureHostConfiguration(config =>
        {
            config.AddInMemoryCollection(new Dictionary<string, string?> { 
              { "EarningEstimateApiUrl", "<EMAIL>" } ,
              { "ConnectionStrings:AnalystEstimatesDb", "Server=************;Database=AnalystEstimates;User ID=ushark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=FlipIT.Fundamentals.API;"},
              { "ConnectionStrings:ReportHubDB", "Server=************;Database=ARP2018;User ID=arpuser;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=FlipIT.Fundamentals.API;"},
              { "ConnectionStrings:FinCalendarDB", "Server=************;Database=FinCalendar2;User ID=ushark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=FlipIT.Fundamentals.API;"},
              { "ReportHub:ThumbnailBlob", "https://arp2018.blob.core.windows.net/report-thumbnails/" },
              { "Serilog:Using", "[\"Serilog.Sinks.Console\"]" },
              { "Serilog:MinimumLevel", "Debug" },
              { "Serilog:WriteTo:Name", "Console" },
              { "Serilog:Args:outputTemplate", "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}" }
            });
        });

        builder.ConfigureServices(services =>
        {
            // var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(DbContextOptions<TodoGroupDbContext>));

            // if (descriptor != null)
            // {
            //     services.Remove(descriptor);
            // }

            // services.AddDbContext<TodoGroupDbContext>(options =>
            // {
            //     var path = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            //     options.UseSqlite($"Data Source={Path.Join(path, "WebMinRouteGroup_tests.db")}");
            // });
        });

        return base.CreateHost(builder);
    }
}