﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Euroland.FlipIT.WiseSearch.Filters
{
    public class AddMobileHeadersFilter : IOperationFilter
    {
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            string[] headers = new[]
            {
            "X-Client-Type", "X-API-Key", "X-User-Email", "X-User-Username","X-User-Surname","X-User-GivenName", "X-Timestamp", "X-Signature"
        };

            foreach (var header in headers)
            {
                operation.Parameters ??= new List<OpenApiParameter>();
                operation.Parameters.Add(new OpenApiParameter
                {
                    Name = header,
                    In = ParameterLocation.Header,
                    Required = false,
                    Schema = new OpenApiSchema { Type = "string" }
                });
            }
        }
    }
}
