using System;
using System.Linq;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.SData.API.GraphQL.Types.Instrument.Dtos;
using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using HotChocolate;
using HotChocolate.Resolvers;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;

#pragma warning disable CS8601, CS8602 // Possible null reference assignment.

/// <summary>
/// Provides methods to resolve intraday data for GraphQL queries.
/// </summary>
internal partial class InstrumentResolvers
{
  const string UTC_TIMEZONE = "UTC";
  const string CENTRAL_EUROPEAN_TIMEZONE = "Central European Standard Time";

  /// <summary>
  /// Gets intraday data for a given instrument.
  /// </summary>
  /// <param name="resolverContext">The resolver context.</param>
  /// <param name="instrument">The instrument data transfer object.</param>
  /// <param name="sharkDbContext">The database context.</param>
  /// <param name="timeIntervalGrouping">The time interval grouping in minutes.</param>
  /// <returns>An IQueryable of InstrumentHistoryDto containing the intraday data.</returns>
  /// <exception cref="ArgumentOutOfRangeException">Thrown when the time interval grouping is less than 1.</exception>
  public IQueryable<InstrumentHistoryDto> GetIntraday(
    IResolverContext resolverContext,
    [Service] IGraphQLExecutionContext executionContext,
    [Parent] InstrumentDto instrument,
    SharkDbContextBase sharkDbContext,
    int timeIntervalGrouping
  )
  {
    var hasAdjustedClosePrice = executionContext.UseAdjClose;

    if (timeIntervalGrouping < 1)
    {
      throw new ArgumentOutOfRangeException(nameof(timeIntervalGrouping),
        "The time interval must be greater than or equal to 1.");
    }

    var dataByPeriod = (executionContext.UseRealtime && resolverContext.IsRealtimeConfigured(instrument.Id).GetAwaiter().GetResult())
      ? GetOHLCQuery(sharkDbContext, instrument.Id, timeIntervalGrouping, hasAdjustedClosePrice)
        .TagWith(CorporateActionAdjustmentFactorCTEDbCommandInterceptor.TOKEN)
        .TagWith("<intraday_log>")
      : GetDailyHistoryQuery(sharkDbContext, instrument.Id, timeIntervalGrouping, hasAdjustedClosePrice)
        .TagWith(CorporateActionAdjustmentFactorCTEDbCommandInterceptor.TOKEN)
        .TagWith(IntradayQueryWithTempTableGroupByMinuteCacheDbCommandInterceptor.TOKEN)
        .TagWith("<intraday_log>");

    var exchangeCurrency = executionContext.ExchangeCurrency;
    var hasExchangeCurrency = !string.IsNullOrEmpty(exchangeCurrency);

    if (!hasExchangeCurrency ||
        string.IsNullOrEmpty(instrument.CurrencyCode))
    {
      return dataByPeriod.OrderByArgumentOrDefault(
        resolverContext,
        q => q.OrderByDescending(p => p.DateTime)
      ).WithAutoMapper().ToProjection<InstrumentHistoryDto>();
    }

    var rateView = sharkDbContext.InstrumentCurrencyPairQueryable($"{instrument.CurrencyCode}{exchangeCurrency}");

    var query = (
      from price in dataByPeriod
      from rate in rateView.Where(r => r.cDate.Date == price.DateTime.Date).DefaultIfEmpty()
      select new { price, rate }
    ).Select(grouping => new InstrumentHistory {
        InstrumentId = grouping.price.InstrumentId,
        DateTime = grouping.price.DateTime,
        Close = grouping.price.Close * (grouping.rate != null ? grouping.rate.cRate : 1m),
        High = grouping.price.High * (grouping.rate != null ? grouping.rate.cRate : 1m),
        Open = grouping.price.Open * (grouping.rate != null ? grouping.rate.cRate : 1m),
        Low = grouping.price.Low * (grouping.rate != null ? grouping.rate.cRate : 1m),
        Volume = grouping.price.Volume,
    });

    return query.OrderByArgumentOrDefault(
        resolverContext,
        q => q.OrderByDescending(p => p.DateTime)
      )
      .WithAutoMapper().ToProjection<InstrumentHistoryDto>();
  }

  /// <summary>
  /// Gets intraday data from the OHLC table. The OHLC table is available at Cloud DB only where
  /// data is filled from Azure Stream Analytics service. The date in this table is already
  /// in UTC timezone and grouped by 1 minute interval.
  /// </summary>
  /// <param name="sharkDbContext">The database context.</param>
  /// <param name="instrumentId">The instrument ID.</param>
  /// <param name="groupingTimeIntervalInMinute">The grouping time interval in minutes.</param>
  /// <param name="hasAdjustedClosePrice">Indicates whether the close price is adjusted.</param>
  /// <returns>An IQueryable of InstrumentHistory containing the OHLC data.</returns>
  private IQueryable<InstrumentHistory> GetOHLCQuery(SharkDbContextBase sharkDbContext, int instrumentId,
    int groupingTimeIntervalInMinute, bool hasAdjustedClosePrice)
  {
    var paramForFilterByMarketOpenTime = GetParamForFilterByMarketOpenTime(sharkDbContext, instrumentId, UTC_TIMEZONE);
    var ohlcTable = "rt_daily_history_ohlc";
    return sharkDbContext.InstrumentHistory
      .FromSqlRaw(@$"
              SELECT
                  d.InstrumentId
                  ,CONVERT(DATETIME, d.[Date] AT TIME ZONE @marketTimeZone AT TIME ZONE 'UTC') AS [Date]
                  ,CAST(d.[Close] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Close]
                  ,CAST(d.[High] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [High]
                  ,CAST(d.[Low] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Low]
                  ,CAST(d.[Open] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Open]
                  ,d.[Volume]
              FROM (
                  SELECT
                  InstrumentId,
                  DATEADD(MINUTE,
                        (DATEDIFF(MINUTE, CONVERT(DATETIME, CONVERT(CHAR(10), DATEADD(MINUTE, @minutesDifference, [Date]), 120) + ' ' + @marketOpenTimeLocal, 120), DATEADD(MINUTE, @minutesDifference, [Date])) / @groupingTimeInterval) * @groupingTimeInterval,
                        CONVERT(DATETIME, CONVERT(CHAR(10), DATEADD(MINUTE, @minutesDifference, [Date]), 120) + ' ' + @marketOpenTimeLocal, 120)) AS [Date],
                  MIN(CASE WHEN rn_asc = 1 THEN [Open] END) AS [Open],
		              MIN(CASE WHEN rn_desc = 1 THEN [Close] END) AS [Close],
                  MAX([High]) AS [High],
                  MIN([Low]) AS [Low],
                  SUM([Volume]) AS [Volume]
		              FROM(
                      SELECT  ROW_NUMBER() OVER (
			                          PARTITION BY InstrumentId,
                                CAST(DATEADD(MINUTE, @minutesDifference, [Date]) AS DATE),
                                (DATEDIFF(MINUTE, CONVERT(DATETIME, CONVERT(CHAR(10), DATEADD(MINUTE, @minutesDifference, [Date]), 120) + ' ' + @marketOpenTimeLocal, 120), DATEADD(MINUTE, @minutesDifference, [Date])) / @groupingTimeInterval)
			                          ORDER BY [Date]) AS rn_asc
		                          ,ROW_NUMBER() OVER (
			                          PARTITION BY InstrumentId,
                                CAST(DATEADD(MINUTE, @minutesDifference, [Date]) AS DATE),
                                (DATEDIFF(MINUTE, CONVERT(DATETIME, CONVERT(CHAR(10), DATEADD(MINUTE, @minutesDifference, [Date]), 120) + ' ' + @marketOpenTimeLocal, 120), DATEADD(MINUTE, @minutesDifference, [Date])) / @groupingTimeInterval)
			                          ORDER BY [Date] DESC) AS rn_desc
		                          ,*
					            FROM (
                        SELECT TOP(1)
				                  InstrumentId,
				                  CONVERT(DATETIME, @latestRealTimeDailyHistoryDateTime AT TIME ZONE 'Central European Standard Time' AT TIME ZONE 'UTC') AS [Date],
				                  (SELECT TOP 1 hClose
				                  FROM rt_daily_history sub WITH (NOLOCK)
				                  WHERE sub.instrumentid = @instrumentId
				                  AND DATEDIFF(MINUTE, @latestRealTimeDailyHistoryDateTime, sub.hdate) = 0
				                  ORDER BY sub.hdate DESC) AS [Close],
				                  MAX(hClose) AS [High],
				                  MIN(hClose) AS [Low],
				                  (SELECT TOP 1 hClose
				                  FROM rt_daily_history sub WITH (NOLOCK)
				                  WHERE sub.instrumentid = @instrumentId
				                  AND DATEDIFF(MINUTE, @latestRealTimeDailyHistoryDateTime, sub.hdate) = 0) AS [Open],
				                  SUM(hSize) AS [Volume]
			                  FROM (
				                  SELECT InstrumentID, hDate, hClose, hSize
				                  FROM rt_daily_history sub WITH (NOLOCK)
				                  WHERE sub.instrumentid = @instrumentId
				                  AND DATEDIFF(MINUTE, @latestRealTimeDailyHistoryDateTime, sub.hdate) = 0
				                  AND DATEDIFF(MINUTE, @latestRealTimeDailyHistoryOHLCDateTime, sub.hdate) > 0
				                  ) AS [lastMinuteQuery]
			                  GROUP BY
				                  instrumentid,
				                  DATEADD(MINUTE, DATEDIFF(MINUTE, 0, hdate), 0)
			                  ORDER BY
				                  [date] DESC

                        UNION ALL

                        SELECT
                            InstrumentId,
                            [Date],
                            [Close] AS [Close],
                            [High],
                            [Low],
                            [Open],
                            [Volume] AS [Volume]
                        FROM [{ohlcTable}] WITH (NOLOCK)
                        WHERE InstrumentID = @instrumentId
                    ) AS rt_daily_history_ohlc_unioned)
                    AS SubQueryAlias
                    WHERE CAST([Date] AS FLOAT) - FLOOR(CAST([Date] AS FLOAT)) >= @marketOpenTimeInputTimezone
                    GROUP BY
                    InstrumentId,
                    DATEADD(MINUTE,
                            (DATEDIFF(MINUTE, CONVERT(DATETIME, CONVERT(CHAR(10), DATEADD(MINUTE, @minutesDifference, [Date]), 120) + ' ' + @marketOpenTimeLocal, 120), DATEADD(MINUTE, @minutesDifference, [Date])) / @groupingTimeInterval) * @groupingTimeInterval,
                            CONVERT(DATETIME, CONVERT(CHAR(10), DATEADD(MINUTE, @minutesDifference, [Date]), 120) + ' ' + @marketOpenTimeLocal, 120))
              ) AS d
              OUTER APPLY (
                SELECT TOP 1 EXP(SUM(LOG(CumulativeAdjustmentFactor))) AS CumulativeAdjustmentFactor
                FROM {CorporateActionAdjustmentFactorCTEDbCommandInterceptor.CumulativeAdjustmentsTable}
                WHERE @adjClose = 1 AND InstrumentId = d.InstrumentId AND d.[Date] < ActionDate
                GROUP BY InstrumentId
              ) AS ca
      ",
        new SqlParameter(CorporateActionAdjustmentFactorCTEDbCommandInterceptor.ADJ_CLOSED_PARAMETER_NAME, System.Data.SqlDbType.Bit) { Value = hasAdjustedClosePrice },
        new SqlParameter("instrumentId", System.Data.SqlDbType.Int) { Value = instrumentId },
        new SqlParameter("groupingTimeInterval", System.Data.SqlDbType.Int) { Value = groupingTimeIntervalInMinute },
        new SqlParameter("minutesDifference", System.Data.SqlDbType.Int)
        { Value = paramForFilterByMarketOpenTime.MinutesDifference },
        new SqlParameter("marketTimeZone", System.Data.SqlDbType.NVarChar)
        { Value = paramForFilterByMarketOpenTime.MarketTimezone },
        new SqlParameter("marketOpenTimeLocal", System.Data.SqlDbType.Char)
        { Value = paramForFilterByMarketOpenTime.MarketOpenTimeLocal },
        new SqlParameter("marketOpenTimeInputTimezone", System.Data.SqlDbType.Float)
        { Value = paramForFilterByMarketOpenTime.MarketOpenTimeInputTimezone },
        new SqlParameter("latestRealTimeDailyHistoryDateTime", System.Data.SqlDbType.DateTime)
        { Value = paramForFilterByMarketOpenTime.LatestRealTimeDailyHistoryDateTime },
        new SqlParameter("latestRealTimeDailyHistoryOHLCDateTime", System.Data.SqlDbType.DateTime)
        { Value = paramForFilterByMarketOpenTime.LatestRealTimeDailyHistoryOHLCDateTime });
  }

  /// <summary>
  /// Gets daily history data for a given instrument.
  /// </summary>
  /// <param name="sharkDbContext">The database context.</param>
  /// <param name="instrumentId">The instrument ID.</param>
  /// <param name="groupingTimeIntervalInMinute">The grouping time interval in minutes.</param>
  /// <param name="hasAdjustedClosePrice">Indicates whether the close price is adjusted.</param>
  /// <returns>An IQueryable of InstrumentHistory containing the daily history data.</returns>
  private IQueryable<InstrumentHistory> GetDailyHistoryQuery(SharkDbContextBase sharkDbContext, int instrumentId,
    int groupingTimeIntervalInMinute, bool hasAdjustedClosePrice)
  {
    var paramForFilterByMarketOpenTime = GetParamForFilterByMarketOpenTime(sharkDbContext, instrumentId, CENTRAL_EUROPEAN_TIMEZONE);

    return sharkDbContext.InstrumentHistory
      .FromSqlRaw(@$"
        SELECT
          d.InstrumentId,
          CONVERT(DATETIME, CONVERT(DATETIMEOFFSET, d.[GroupedDate] AT TIME ZONE @marketTimeZone AT TIME ZONE 'UTC')) AS [Date]
          ,CAST(o.[Open] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Open]
          ,CAST(d.[High] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [High]
          ,CAST(d.[Low] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Low]
          ,CAST(c.[Close] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Close]
          ,d.[Volume]
        FROM {IntradayQueryWithTempTableGroupByMinuteCacheDbCommandInterceptor.IntradayDateRangesTable} d

        OUTER APPLY (
          SELECT TOP(1) dh.[hClose] AS [Open]
          FROM [daily_history] AS dh WITH (NOLOCK)
          WHERE dh.InstrumentId = @instrumentId AND dh.[hDate] = d.[MinDate]
        ) AS o

        OUTER APPLY (
          SELECT TOP(1) dh.[hClose] AS [Close]
          FROM [daily_history] AS dh WITH (NOLOCK)
          WHERE dh.InstrumentId = @instrumentId AND dh.[hDate] = d.[MaxDate]
        ) AS c

        OUTER APPLY (
          SELECT TOP 1 EXP(SUM(LOG(CumulativeAdjustmentFactor))) AS CumulativeAdjustmentFactor
          FROM {CorporateActionAdjustmentFactorCTEDbCommandInterceptor.CumulativeAdjustmentsTable}
          WHERE d.GroupedDate < ActionDate
          GROUP BY InstrumentId
        ) AS ca
      ",
        new SqlParameter("adjClose", System.Data.SqlDbType.Bit) { Value = hasAdjustedClosePrice },
        new SqlParameter("instrumentId", System.Data.SqlDbType.Int) { Value = instrumentId },
        new SqlParameter("groupingTimeInterval", System.Data.SqlDbType.Int) { Value = groupingTimeIntervalInMinute },
        new SqlParameter("minutesDifference", System.Data.SqlDbType.Int)
        { Value = paramForFilterByMarketOpenTime.MinutesDifference },
        new SqlParameter("marketTimeZone", System.Data.SqlDbType.NVarChar)
        { Value = paramForFilterByMarketOpenTime.MarketTimezone },
        new SqlParameter("marketOpenTimeLocal", System.Data.SqlDbType.Char)
        { Value = paramForFilterByMarketOpenTime.MarketOpenTimeLocal },
        new SqlParameter("marketOpenTimeInputTimezone", System.Data.SqlDbType.Float)
        { Value = paramForFilterByMarketOpenTime.MarketOpenTimeInputTimezone });
  }

  /// <summary>
  /// Gets parameters for filtering by market open time.
  /// </summary>
  /// <param name="sharkDbContext">The database context.</param>
  /// <param name="instrumentId">The instrument ID.</param>
  /// <param name="inputTimeZone">The input time zone.</param>
  /// <returns>A GetParamsForIntradayQueryInput object containing the parameters.</returns>
  private GetParamsForIntradayQueryInput GetParamForFilterByMarketOpenTime(SharkDbContextBase sharkDbContext,
    int instrumentId, string inputTimeZone)
  {
    var instrumentIdParam = new SqlParameter("instrumentId", System.Data.SqlDbType.Int) { Value = instrumentId };
    var inputTimeZoneParam = new SqlParameter("inputTimeZone", System.Data.SqlDbType.NVarChar)
    { Value = inputTimeZone };
    var dbHasOhlcTableParam = new SqlParameter("dbHasOhlcTable", System.Data.SqlDbType.Bit)
    {
      Value = sharkDbContext is RealtimeSharkDbContext
    };
    var marketTimezone = new SqlParameter
    {
      ParameterName = "marketTimezone",
      SqlDbType = System.Data.SqlDbType.NVarChar,
      Size = 1000,
      Direction = System.Data.ParameterDirection.Output
    };
    var minutesDifferenceParam = new SqlParameter
    {
      ParameterName = "minutesDifference",
      SqlDbType = System.Data.SqlDbType.Int,
      Direction = System.Data.ParameterDirection.Output
    };
    var marketOpenTimeLocal = new SqlParameter
    {
      ParameterName = "marketOpenTimeLocal",
      SqlDbType = System.Data.SqlDbType.Char,
      Size = 8,
      Direction = System.Data.ParameterDirection.Output
    };
    var marketOpenTimeInputTimezone = new SqlParameter
    {
      ParameterName = "marketOpenTimeInputTimezone",
      SqlDbType = System.Data.SqlDbType.Float,
      Size = 8,
      Direction = System.Data.ParameterDirection.Output
    };
    var latestRealTimeDailyHistoryDateTime = new SqlParameter
    {
      ParameterName = "latestRealTimeDailyHistoryDateTime",
      SqlDbType = System.Data.SqlDbType.DateTime,
      Direction = System.Data.ParameterDirection.Output,
      IsNullable = true
    };
    var latestRealTimeDailyHistoryOHLCDateTime = new SqlParameter
    {
      ParameterName = "latestRealTimeDailyHistoryOHLCDateTime",
      SqlDbType = System.Data.SqlDbType.DateTime,
      Direction = System.Data.ParameterDirection.Output,
      IsNullable = true
    };

    sharkDbContext.Database.ExecuteSqlRaw(@"
        DECLARE @marketId INT;

        SELECT @marketId = MarketID
        FROM Instrument
        WHERE Id = @instrumentId;

        SELECT @marketTimezone = TimeZone,
        @marketOpenTimeLocal = MarketOpenTimeLocal
        FROM Market
        WHERE MarketNumber = @marketId;

        DECLARE @inputTime DATETIME = SYSDATETIME();
        DECLARE @marketTime DATETIME = CONVERT(DATETIME, @inputTime AT TIME ZONE @inputTimezone AT TIME ZONE @marketTimezone);

        SET @minutesDifference = DATEDIFF(MINUTE, @inputTime, @marketTime);
        SET @marketOpenTimeInputTimezone = CAST(DATEADD(MINUTE, -@minutesDifference, CAST(@marketOpenTimeLocal AS DATETIME)) AS FLOAT);

        SELECT TOP 1
        @latestRealTimeDailyHistoryDateTime = [hdate]
        FROM rt_daily_history WITH (NOLOCK)
        WHERE instrumentid = @instrumentId
        ORDER BY [hdate] DESC;

        IF @dbHasOhlcTable = 1
        BEGIN
            SELECT TOP 1
            @latestRealTimeDailyHistoryOHLCDateTime = CONVERT(DATETIME, [Date] AT TIME ZONE 'UTC' AT TIME ZONE 'Central European Standard Time')
            FROM rt_daily_history_ohlc WITH (NOLOCK)
            WHERE instrumentid = @instrumentId
            ORDER BY [Date] DESC;
        END",
    instrumentIdParam,
    inputTimeZoneParam,
    marketTimezone,
    dbHasOhlcTableParam,
    minutesDifferenceParam,
    marketOpenTimeLocal,
    marketOpenTimeInputTimezone,
    latestRealTimeDailyHistoryDateTime,
    latestRealTimeDailyHistoryOHLCDateTime);


    return new GetParamsForIntradayQueryInput()
    {
      MarketTimezone = marketTimezone.Value.ToString(),
      MarketOpenTimeLocal = marketOpenTimeLocal.Value.ToString(),
      MinutesDifference = minutesDifferenceParam.Value.ToString(),
      MarketOpenTimeInputTimezone = (double)marketOpenTimeInputTimezone.Value,
      LatestRealTimeDailyHistoryDateTime = latestRealTimeDailyHistoryDateTime.Value != DBNull.Value
        ? (DateTime?)latestRealTimeDailyHistoryDateTime.Value
        : null,
      LatestRealTimeDailyHistoryOHLCDateTime = latestRealTimeDailyHistoryOHLCDateTime.Value != DBNull.Value
        ? (DateTime?)latestRealTimeDailyHistoryOHLCDateTime.Value
        : null
    };

  }
}
#pragma warning restore CS8601, CS8602 // Possible null reference assignment.
