import { useLayoutEffect } from "react";
import { AISearchSettings } from "@/services/getAISearchSettings";
import { Color, HSLColor } from "@/helpers/color";

export const useSetupTheme = (aiSearchSettings: AISearchSettings) => {
  useLayoutEffect(() => {
    const primaryColor = aiSearchSettings.primaryColor;
    const hsl = HSLColor.parse(primaryColor);
    const gradientColor = aiSearchSettings.gradientColor;
    const linearGradient = `linear-gradient(135deg, ${gradientColor.from}, ${gradientColor.to})`;
    document.body.style.setProperty("--primary-color", primaryColor);
    document.body.style.setProperty(
      "--primary-linear-gradient",
      linearGradient
    );
    document.body.style.setProperty(
      "--primary-color-light",
      Color.applyAlpha(primaryColor, 0.2)
    );
    document.body.style.setProperty(
      "--primary-color-dark",
      hsl.saturate(0.4).toString()
    );
  }, []);
};
