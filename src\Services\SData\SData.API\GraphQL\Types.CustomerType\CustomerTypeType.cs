using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.CustomerType;

public class CustomerTypeType: ObjectType<CustomerTypeDto>
{
  public const string Name = "CustomerType";
  protected override void Configure(IObjectTypeDescriptor<CustomerTypeDto> descriptor)
  {
    descriptor.Name(Name);

    descriptor.Field(f => f.Id).IsProjected();
  }
}
