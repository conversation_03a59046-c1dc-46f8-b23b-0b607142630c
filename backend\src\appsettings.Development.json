{"ConnectionStrings": {"WiseSearchDbConnection": "Server=**********;Database=Wise;uid=uShark;pwd=**********;Trusted_Connection=false; Application Name=Euroland.FlipIT.WiseSearch; TrustServerCertificate=True;", "SharkDbConnection": "Server=**********;Database=shark;uid=uShark;pwd=**********;Trusted_Connection=false; Application Name=Euroland.FlipIT.WiseSearch; TrustServerCertificate=True;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": "Debug", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}, "AppSettings": {"AllowedOrigins": ["http://localhost:3005", "http://localhost", "http://************", "https://************"], "HttpClientRequest": {"WiseApi": {"BaseUrl": "https://se-wise-api.euroland.com", "DefaultLanguageToTranslate": "English", "Endpoints": {"Query": "/api/v1/query"}, "Query": {"ResponseModel": "openai", "SemanticCount": 3}}}}}