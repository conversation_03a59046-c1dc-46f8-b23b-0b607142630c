﻿using HotChocolate;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
  [GraphQLName("Company_ Deprecated")]
    public class Company
    {
        [GraphQLIgnore]
        [Column("cId")]
        public int CompanyId { get; set; }
        [Key, Column("cCode")]
        public string? CompanyCode { get; set; }
        [Column("cName")]
        public string? CompanyName { get; set; }

        [Column("cMarket")]
        public short CompanyMarket { get; set; }
        [Column("cHomePage")]
        public string? CompanyHomePage { get; set; }
        [Column("cCountry")]
        public string? CompanyCountry { get; set; }
        [Column("cTown")]
        public string? CompanyTown { get; set; }
        [Column("cTel")]
        public string? CompanyTel { get; set; }
        [Column("cFax")]
        public string? CompanyFax { get; set; }
        [Column("cEMail")]
        public string? CompanyEMail { get; set; }
        [Column("cCustomer")]
        public short CompanyCustomer { get; set; }

        [GraphQLIgnore]
        public virtual List<CompanyNames> CompanyNames { get; set; }

    }
}
