using HotChocolate;
using System.ComponentModel.DataAnnotations;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
    public class Currency
    {
        [Key]
        public string Code { get; set; }

        public string? Name { get; set; }
        public bool? IsRegionMajor { get; set; }
        public byte? RegionId { get; set; }
        public int? TranslationId { get; set; }
        public int? DecimalPlace { get; set; }
        public string? Symbol { get; set; }

        [GraphQLIgnore]
        public Instrument Instrument { get; set; }

        [GraphQLIgnore]
        public virtual Translation Translation { get; set; }
    }
}
