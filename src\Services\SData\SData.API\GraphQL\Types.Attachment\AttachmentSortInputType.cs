using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Data.Sorting;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Attachment;

/// <summary>
/// Custom sort type to enable ordering on <see cref="AttachmentDto.Date"/> only.
/// </summary>
public class AttachmentSortInputType : SortInputType<AttachmentDto>
{
  protected override void Configure(ISortInputTypeDescriptor<AttachmentDto> descriptor)
  {
    descriptor.BindFieldsExplicitly()
      .Field(f => f.Order).Type<DefaultSortEnumType>();
  }
}
