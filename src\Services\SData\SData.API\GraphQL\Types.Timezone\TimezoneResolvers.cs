using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using HotChocolate;
using HotChocolate.Resolvers;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Timezone;

public class TimezoneResolvers
{
  public async Task<TimezoneDto?> GetTimezoneByMarket(
    [Parent] MarketDto market,
    IResolverContext resolverContext,
    TimezoneByNameDataLoader dataLoader,
    CancellationToken cancellationToken = default)
    {
      if(string.IsNullOrEmpty(market.TimezoneName)) {
        return null;
      }

      return await dataLoader.LoadAsync(market.TimezoneName.Trim().ToLower(), cancellationToken);
    }

  public async Task<TimezoneDto?> GetTimezoneByFCEvent(
    [Parent] FCalendarDto fCalendar,
    TimezoneByIdDataLoader dataLoader,
    IResolverContext resolverContext,
    CancellationToken cancellationToken = default)
   {
    if(fCalendar.TimezoneId == null) {
      return null;
    }

    return await dataLoader.LoadAsync(fCalendar.TimezoneId.Value, cancellationToken);
   }
}
