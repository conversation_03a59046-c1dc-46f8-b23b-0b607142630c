﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.WiseSearch.Infrastructure.Entities.Shark;

[Table("CompanySettingsLanguage")]
public partial class CompanySettingsLanguage
{
    [Key]
    public int Id { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string CompanyCode { get; set; } = null!;

    [StringLength(10)]
    [Unicode(false)]
    public string Language { get; set; } = null!;

    public string Name { get; set; } = null!;

    [StringLength(250)]
    [Unicode(false)]
    public string? HomePage { get; set; }

    [StringLength(250)]
    [Unicode(false)]
    public string? EmailSender { get; set; }

    [StringLength(250)]
    [Unicode(false)]
    public string? EmailSenderName { get; set; }
}
