{"Urls": "https://*:5007", "Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": "Debug", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}, "ConnectionStrings": {"AnalystEstimatesDb": "Server=**********;Database=AnalystEstimates;User ID=ushark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=FlipIT.Fundamentals.API;", "ReportHubDB": "Server=**********;Database=ARP2018;User ID=arpuser;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=FlipIT.Fundamentals.API;", "FinCalendarDB": "Server=**********;Database=FinCalendar2;User ID=ushark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=FlipIT.Fundamentals.API;"}}