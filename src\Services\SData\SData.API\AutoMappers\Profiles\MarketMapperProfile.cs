using AutoMapper;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;

namespace Euroland.FlipIT.SData.API.AutoMapper.Profiles;

public class MarketMapperProfile : Profile
{
  public MarketMapperProfile()
  {
    CreateMap<Market, MarketDto>(MemberList.Destination)
      .ForMember(d => d.Id, cfg => cfg.MapFrom(e => e.MarketNumber))
      .ForMember(d => d.OpenTimeLocal, cfg => cfg.MapFrom(e => e.MarketOpenTimeLocal))
      .ForMember(d => d.CloseTimeLocal, cfg => cfg.MapFrom(e => e.MarketCloseTimeLocal))
      .ForMember(d => d.TimezoneName, cfg => cfg.MapFrom(e => e.TimeZone))
      .ForMember(d => d.Abbreviation, cfg => cfg.MapFrom(e => e.MarketAbbreviation))
      .ForMember(d => d.Id, cfg => cfg.MapFrom(e => e.Market<PERSON>umber))
      .ForMember(d => d.Timezone, cfg => cfg.Ignore());
  }
}
