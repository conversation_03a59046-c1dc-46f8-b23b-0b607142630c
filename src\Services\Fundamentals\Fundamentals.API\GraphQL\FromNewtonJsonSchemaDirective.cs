using HotChocolate.Language;
using HotChocolate.Resolvers;
using HotChocolate.Types.Descriptors;
using HotChocolate.Types.Descriptors.Definitions;
using Newtonsoft.Json.Linq;

namespace Euroland.FlipIT.Fundamental.API.GraphQL;

public class FromNewtonJsonSchemaDirective : ISchemaDirective
{

  public string Name => "fromNewtonJson";

  public void ApplyConfiguration(IDescriptorContext context, DirectiveNode directiveNode, IDefinition definition, Stack<IDefinition> path)
  {
    if (definition is ObjectFieldDefinition fieldDef)
    {
      fieldDef.Configurations.Add(new CompleteConfiguration<ObjectFieldDefinition>(
        (ctx, def) =>
        {
          var propertyName = GetPropertyName(directiveNode);
          propertyName ??= def.Name;
          var type = ctx.GetType<IType>(def.Type!);
          var namedType = type.NamedType();

          if (type.IsListType())
          {
            CreateListResolver(def);
            return;
          }
          if (namedType is ScalarType scalarType)
          {
            CreatePureResolver(ctx.Type, def, scalarType, propertyName);
            return;
          }

          throw ThrowException(ctx.Type.Name);
        },
        fieldDef,
        ApplyConfigurationOn.BeforeCompletion));
    }
  }

  static void CreateListResolver(ObjectFieldDefinition def)
  {
    def.PureResolver = ctx => new ValueTask<object?>(ToEnumerable(ctx));
  }

  static void CreatePureResolver(
        ITypeSystemObject type,
        ObjectFieldDefinition def,
        ScalarType scalarType,
        string propertyName)
  {
    switch (scalarType.Name)
    {
      case ScalarNames.ID:
      case ScalarNames.String:
        def.PureResolver = ctx => GetProperty(ctx, propertyName)?.Value<string>();
        return;

      case ScalarNames.Boolean:
        def.PureResolver = ctx =>
        {
          var property = GetProperty(ctx, propertyName);
          return property is null or { Type: JTokenType.Null }
                      ? null
                      : property.Value<bool>();
        };
        return;

      case ScalarNames.Short:
        def.PureResolver = ctx =>
        {
          var property = GetProperty(ctx, propertyName);

          return property is null or { Type: JTokenType.Null }
                      ? null
                      : property.Value<Int16>();
        };
        return;

      case ScalarNames.Int:
        def.PureResolver = ctx =>
        {
          var property = GetProperty(ctx, propertyName);

          return property is null or { Type: JTokenType.Null } || string.IsNullOrEmpty(property.Value<string>())
                      ? null
                      : property.Value<Int32>();
        };
        return;

      case ScalarNames.Long:
        def.PureResolver = ctx =>
        {
          var property = GetProperty(ctx, propertyName);

          return property is null or { Type: JTokenType.Null }
                      ? null
                      : property.Value<UInt64>();
        };
        return;

      case ScalarNames.Float:
        def.PureResolver = ctx =>
        {
          var property = GetProperty(ctx, propertyName);

          return property is null or { Type: JTokenType.Null }
                      ? null
                      : property.Value<double>();
        };
        return;

      case ScalarNames.Decimal:
        def.PureResolver = ctx =>
        {
          var property = GetProperty(ctx, propertyName);

          return property is null or { Type: JTokenType.Null }
                      ? null
                      : property.Value<decimal>();
        };
        return;

      case ScalarNames.URL:
        def.PureResolver = ctx =>
        {
          var property = GetProperty(ctx, propertyName);

          if (property is null or { Type: JTokenType.Null })
          {
            return null;
          }

          return new Uri(property.Value<string>()!);
        };
        return;

      case ScalarNames.UUID:
        def.PureResolver = ctx =>
        {
          var property = GetProperty(ctx, propertyName);

          return property is null or { Type: JTokenType.Null }
                      ? null
                      : property.Value<Guid>();
        };
        return;

      case ScalarNames.Byte:
        def.PureResolver = ctx =>
        {
          var property = GetProperty(ctx, propertyName);

          return property is null or { Type: JTokenType.Null }
                      ? null
                      : property.Value<byte>();
        };
        return;
      case ScalarNames.Date:
        def.PureResolver = ctx =>
        {
          var property = GetProperty(ctx, propertyName);

          return property is null or { Type: JTokenType.Null }
            ? null
            : property.Value<DateTime>();
        };
        return;

      case ScalarNames.DateTime:
        def.PureResolver = ctx =>
        {
          var property = GetProperty(ctx, propertyName);

          return property is null or { Type: JTokenType.Null }
            ? null
            : property.Value<DateTime>();
        };
        return;
      case ScalarNames.ByteArray: // Unsupported Type, but it's absolutely possible to support it
      default:
        throw ThrowException(type.Name);
    }
  }

  static SchemaException ThrowException(string typeName) => new SchemaException(
            SchemaErrorBuilder.New()
                .SetMessage(
                    "Cannot not infer the correct mapping for the JSON object type `{0}`.",
                    typeName)
                .Build());

  static JToken? GetProperty(IPureResolverContext context, string propertyName)
  {
    return context.Parent<JToken>().SelectToken(propertyName, false);
  }

  static IEnumerable<JToken> ToEnumerable(IPureResolverContext context) => context.Parent<JToken>().AsJEnumerable();

  static string? GetPropertyName(DirectiveNode directive)
  {
    if (directive.Arguments.Count == 0)
    {
      return null;
    }

    if (directive.Arguments.Count == 1)
    {
      var argument = directive.Arguments[0];
      if (string.Equals(argument.Name.Value, "name", StringComparison.Ordinal) &&
          argument.Value is StringValueNode { Value: { Length: > 0, } name, })
      {
        return name;
      }
    }

    return null;
  }
}
