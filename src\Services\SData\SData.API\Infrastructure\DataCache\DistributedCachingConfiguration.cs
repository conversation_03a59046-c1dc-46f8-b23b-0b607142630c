﻿namespace Euroland.FlipIT.SData.API.Infrastructure.DataCache
{
  public class DistributedCachingConfiguration
    {
        public bool UseDistributedMemoryCache { get; set; }
        public bool UseRedisCache { get; set; }
        public string RedisConnectionString { get; set; }
        public string RedisInstanceName { get; set; }
        public int ExpirationTime { get; set; }
        public int CacheSizeLimit { get; set; }
        public double CompactionPercentage { get; set; }
    }
}
