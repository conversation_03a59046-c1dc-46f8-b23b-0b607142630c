using System;
using System.Collections.Generic;

namespace Euroland.FlipIT.SData.API.GraphQL.RequestContext;

/// <summary>
/// Represents the context for a query request, providing properties
/// to access the request ID, database connection kind, and cloud usage settings.
/// </summary>
public interface IGraphQLExecutionContext
{
  /// <summary>
  /// Gets the current request ID.
  /// </summary>
  string? RequestId { get; set; }

  /// <summary>
  /// Gets or sets the kind of database connection to use.
  /// This property can be set to "cloud" or "default" to control the database connection.
  /// If not set, the default behavior is to use the default database.
  /// </summary>
  public bool UseRealtime { get; set; }

  /// <summary>
  /// To indicate whether the request should use the cloud database.
  /// This property can be set to "true" or "false" to control the database connection.
  /// If not set, the default behavior is to use the default database.
  /// </summary>
  public bool UseCloud { get; set; }

  /// <summary>
  /// Gets or sets a value indicating whether to use adjusted closing prices in queries.
  /// </summary>
  public bool UseAdjClose { get; set; }

  /// <summary>
  /// Gets or sets the currency code to be used for exchange operations.
  /// </summary>
  public string? ExchangeCurrency { get; set; }
  /// <summary>
  /// Gets or sets a dictionary containing context-specific data that is scoped to the current GraphQL execution.
  /// This data can be used to share information between different components during the execution of a GraphQL request.
  /// </summary>
  /// <remarks>
  /// The dictionary uses string keys and object values, allowing for flexible storage of various types of contextual information.
  /// </remarks>
  IDictionary<string, object> ScopedContextData { get; set; }
}
