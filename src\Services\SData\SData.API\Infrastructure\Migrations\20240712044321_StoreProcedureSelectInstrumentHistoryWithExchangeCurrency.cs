﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Euroland.FlipIT.SData.API.Infrastructure.Migrations
{
  /// <inheritdoc />
  public partial class StoreProcedureSelectInstrumentHistoryWithExchangeCurrency : Migration
  {
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
      migrationBuilder.Sql(@"
        CREATE PROC spSHG3InstrumentHistorySelectByInstrumentIdAndDate
        (
          @instrumentId INT,
          @dateFrom DATETIME,
          @dateTo DATETIME,
          @currencyPair CHAR(6) = NULL,
          @factor DECIMAL(18,2) = 1,
          @fixedRate DECIMAL(18,2) = NULL
        )
        AS
        BEGIN
          --DECLARE @dateFrom DATETIME = '2000-03-30'
          --DECLARE @dateTo DATETIME = '2023-03-31'
          --DECLARE @instrumentId INT = 16569
          --DECLARE @currencyPair char(6) = 'EURUSD'
          --DECLARE @factor DECIMAL(18,2) = 1
          --DECLARE @fixedRate DECIMAL(18,2) = NULL
          DECLARE @currencyRateId INT = NULL
          DECLARE @maxCurrencyDateTo DATETIME
          DECLARE @maxCurrencyDateFrom DATETIME

          if isnull(@factor, 0) <= 0 set @factor = 1

          SELECT TOP(1) @currencyRateId = cId
            FROM CurrencyRate
            WHERE cCurr = @currencyPair

          SELECT TOP(1) @maxCurrencyDateFrom = MAX(chDate)
          FROM CurrencyRateHistory
          WHERE cId = @currencyRateId
            AND DATEDIFF(DAY, [chDate], @dateFrom) >= 0

          SELECT TOP(1) @maxCurrencyDateTo = MAX(chDate)
          FROM CurrencyRateHistory
          WHERE cId = @currencyRateId
            AND DATEDIFF(DAY, [chDate], @dateTo) >= 0

          CREATE TABLE #CurrencyBoundering
          (
            cRate NUMERIC(28,10),
            cDate DATETIME INDEX IX1 NONCLUSTERED(cDate ASC)
          )

          INSERT INTO #CurrencyBoundering
            SELECT TOP(1)
              cRate * @factor AS cRate,
              cDate
            FROM CurrencyRate
            WHERE cId = @currencyRateId

            UNION ALL

            SELECT
              (ch.chRate * @factor) AS cRate,
              ch.chDate AS cDate
            FROM CurrencyRateHistory ch
            WHERE @fixedRate IS NULL
              AND ch.cId = @currencyRateId
              AND ch.chDate >= @maxCurrencyDateFrom
              AND ch.chDate <= @maxCurrencyDateTo

          SELECT
            h.[ID],
            h.[InstrumentId],
            h.[Date],
            h.[Close] * COALESCE(@fixedRate, COALESCE(cur.cRate, 1)) AS [Close],
            h.[High] * COALESCE(@fixedRate, COALESCE(cur.cRate, 1)) AS [High],
            h.[Low] * COALESCE(@fixedRate, COALESCE(cur.cRate, 1)) AS [Low],
            h.[Open] * COALESCE(@fixedRate, COALESCE(cur.cRate, 1)) AS [Open],
            h.[Volume]
            --,cur.cRate
          FROM (
            SELECT TOP(1)
              0 AS ID,
              [InstrumentId],
              [Date],
              COALESCE([Last], 0) AS [Close],
              [High],
              [Low],
              [Open],
              COALESCE(Volume, 0) AS [Volume]
            FROM [InstrumentPrice]
            WHERE InstrumentId = @instrumentId
              AND DATEDIFF(DAY, @dateFrom, [Date]) >= 0
              AND DATEDIFF(DAY, @dateTo, [Date]) <= 0

            UNION ALL

            SELECT
              [ID],
              [InstrumentId],
              [Date],
              [Close],
              [High],
              [Low],
              [Open],
              [Volume]
            FROM [InstrumentHistory]
            WHERE InstrumentId = @instrumentId
              AND DATEDIFF(DAY, @dateFrom, [Date]) >= 0
              AND DATEDIFF(DAY, @dateTo, [Date]) <= 0
          ) as h
          OUTER APPLY (
            SELECT TOP(1)
              ce.cRate
            FROM #CurrencyBoundering ce
            WHERE ce.cDate = (
              SELECT MAX(cDate)
              FROM #CurrencyBoundering
              WHERE DATEDIFF(DAY, [cDate], h.[Date]) >= 0
            )
          ) AS cur
          ORDER BY h.[Date] DESC

          DROP TABLE #CurrencyBoundering
        END
      ");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
      migrationBuilder.Sql("DROP PROC spSHG3InstrumentHistorySelectByInstrumentIdAndDate");
    }
  }
}
