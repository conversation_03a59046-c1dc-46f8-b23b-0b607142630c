using Euroland.FlipIT.Fundamental.API.Dtos;
using Euroland.FlipIT.Fundamental.API.Persistance;
using HotChocolate.Resolvers;
using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.Fundamental.API.Services;

public class ReportHubService : IAsyncDisposable
{
  private readonly ReportHubDbContext _dbContext;
  private readonly IConfiguration _configuration;

  public ReportHubService(IDbContextFactory<ReportHubDbContext> dbContextFactory, IConfiguration configuration)
  {
    _dbContext = dbContextFactory.CreateDbContext();
    _configuration = configuration;
  }

  public ValueTask DisposeAsync()
  {
    GC.SuppressFinalize(this);

    return _dbContext.DisposeAsync();
  }

  public async Task<List<ReportDto>?> GetReports(IResolverContext resolverContext, CancellationToken cancellationToken)
  {
    var companyCode = resolverContext.GetScopedState<string>("companyCode");

    var thumbnailBlob = _configuration["ReportHub:ThumbnailBlob"];

    var query = from R in _dbContext.Report
                join C in _dbContext.Company on R.CompanyID equals C.CompanyID
                join RT in _dbContext.ReportType on R.ReportTypeId equals RT.Id
                join RPT in _dbContext.ReportParentType on R.ReportParentTypeId equals RPT.Id
                where C.CompanyCode == @companyCode && R.IsActive && C.IsActive
                orderby 
                    R.Year descending,
                    RPT.ReportParentTypeOrder,
                    RT.ReportTypeOrder,
                    RT.Name,
                    R.UploadedDate descending,
                    (R.LanguageCode.ToUpper() == "EN" ? 0 : 1),
                    R.LanguageName
                select new ReportDto
                {
                    Title = R.Title,
                    Id = R.Id,
                    LanguageCode= R.LanguageCode,
                    LanguageName = R.LanguageName,
                    SubTitle= R.SubTitle,
                    FileLocation = R.FileLocation,
                    UploadedDate = R.UploadedDate,
                    UploadedDateUTC = R.UploadedDate,
                    ReportParentTypeDisplayName = RPT.DisplayName,
                    ReportTypeName = RT.Name,
                    ThumbnailFileLocation = R.ThumbnailFileLocation,
                    ThumbnailFileLocationFull = $"{thumbnailBlob}{R.CompanyID}/{R.ThumbnailFileLocation}",
                    ReportTypeId = R.ReportTypeId,
                    ReportParentTypeId = R.ReportParentTypeId,
                    Year = R.Year,
                    ReportParentTypeOrder = RPT.ReportParentTypeOrder,
                    ReportTypeOrder = RT.ReportTypeOrder
                };

    var result = await query.ToListAsync();

    if (result.Count == 0)
    {
      return null;
    }

    return result;
  }

}
