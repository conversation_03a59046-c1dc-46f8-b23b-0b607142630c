namespace Euroland.FlipIT.SData.API.Infrastructure.Entities;

public class SubSector
{
  public int Id { get; set; }
  public byte? MarketId { get; set; }
  public int SubSectorId2 { get; set; }
  public string Description { get; set; }
  public string? German { get; set; }
  public string? Spanish { get; set; }
  public string? Russian { get; set; }
  public string? Polish { get; set; }
  public string? Chinese { get; set; }
  public string? Korean { get; set; }
  public string? Japanese { get; set; }
  public string? Dutch { get; set; }
  public string? Italian { get; set; }
  public string? Finnish { get; set; }
  public string? Swedish { get; set; }
  public string? French { get; set; }
  public string? Danish { get; set; }
  public string? Arabic { get; set; }
  public string? Icelandic { get; set; }
  public string? Vietnamese { get; set; }
  public string? Taiwanese { get; set; }
  public string? Norwegian { get; set; }
}
