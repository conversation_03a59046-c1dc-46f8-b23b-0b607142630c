using Euroland.FlipIT.Fundamental.API.Dtos.Common;

namespace Euroland.FlipIT.Fundamental.API.Dtos.FinCalender;

public class FinancialEventTypeDetailDto : IDtoObject
{
  #region Common

  public int Id { get; set; }
  public string CompanyCode { get; set; }
  public byte Order { get; set; }
  public bool IsActive { get; set; }
  public bool IsDeleted { get; set; }
  public bool IsCustom { get; set; }
  // public int? OldId { get; set; }

  #endregion

  #region Detail

  public int EventTypeId { get; set; }

  public int LanguageId { get; set; }

  public string Name { get; set; }

  #endregion
}
