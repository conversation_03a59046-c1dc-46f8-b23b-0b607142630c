import {StateCreator} from "zustand";
import {IStoreState} from "./useAppStore";
import {produce} from "immer";

export interface IConfirmPopupSlice {
  confirm: {
    isLogoutOpen: boolean,
    isExternalLinkOpen: boolean,
    setExternalLinkOpen: (isOpen: boolean) => void
    setLogoutOpen: (isOpen: boolean) => void
  }
}

const confirmPopupSlice: StateCreator<IStoreState, [], [], IConfirmPopupSlice> = set => ({
  confirm: {
    isLogoutOpen: false,
    isExternalLinkOpen: false,
    setLogoutOpen: isOpen => set(state => produce(state, draft => {
      draft.confirm.isLogoutOpen = isOpen;
    })),
    setExternalLinkOpen: isOpen => set(state => produce(state, draft => {
      draft.confirm.isExternalLinkOpen = isOpen;
    }))
  }
})

export default confirmPopupSlice