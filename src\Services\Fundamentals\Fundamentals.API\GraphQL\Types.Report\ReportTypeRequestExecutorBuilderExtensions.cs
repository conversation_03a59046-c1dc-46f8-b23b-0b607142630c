using Euroland.FlipIT.Fundamental.API.GraphQL.Types;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class ReportTypeRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddReportType(this IRequestExecutorBuilder builder)
  {
    builder
      .AddType<ReportType>()
      .AddTypeExtension<FundamentalTypeExtensions>();

    return builder;
  }
}
