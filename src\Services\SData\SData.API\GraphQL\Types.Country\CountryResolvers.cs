using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure;
using HotChocolate;
using HotChocolate.Resolvers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Country;

public class CountryResolvers
{
  public async Task<CountryDto?> GetCountryByCity(
    [Parent] CityDto city,
    CountryByIdDataLoader dataLoader,
    IResolverContext resolverContext,
    CancellationToken cancellation)
    => await dataLoader.LoadAsync(city.CountryId, cancellation);
}
