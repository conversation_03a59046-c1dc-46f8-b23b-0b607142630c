﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.33530.505
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{0533A1CB-C4C9-41F3-89CD-ADD60CBDB5F0}"
	ProjectSection(SolutionItems) = preProject
		NuGet.config = NuGet.config
		VERSION = VERSION
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{509FA1D0-427C-4739-9130-E9BC334E2D4F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{F463392C-E6C4-47B4-B970-08FF4B348402}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Services", "Services", "{965B0BB2-E4C8-45B3-A14B-6308D11823C4}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ApiGateway", "ApiGateway", "{1CCA2605-2235-4FB8-822B-E8D6406B26BF}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "SData", "SData", "{F55C225C-163A-4681-8ED3-B22BC3CC578A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{A41CCAE4-1457-42E1-8E2D-AE571738F1F3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SData.API", "src\Services\SData\SData.API\SData.API.csproj", "{0659981C-C552-48D9-A040-42DB56BEE77B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SData.UnitTests", "src\Services\SData\SData.UnitTests\SData.UnitTests.csproj", "{61C72517-4FB7-4D94-ACB8-1A624191ACD9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Announcement", "Announcement", "{003914C7-DEF4-4BAB-B458-FC4212D2DA14}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{4B579348-39A8-4C1A-9E4C-11C62854AF22}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Company", "Company", "{F5DA59A9-4070-400C-90F1-412326C84A5A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{11915D86-E685-4E7E-B444-602A4A85BD7C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Fundamentals", "Fundamentals", "{71E3D758-21CA-4E8F-92AA-58864D451A9C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Fundamentals.API", "src\Services\Fundamentals\Fundamentals.API\Fundamentals.API.csproj", "{1980D795-B3E0-4C53-A212-3D473FB28D10}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ApiGateway.API", "src\ApiGateway\ApiGateway.API\ApiGateway.API.csproj", "{9751BEED-1860-4FA3-99DA-B46B29D3F7E9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{0F3D98AE-1794-4B02-A5D9-F1091C583A30}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Fundamental.API.IntegrationTest", "src\Services\Fundamentals\test\Fundamental.API.IntegrationTest\Fundamental.API.IntegrationTest.csproj", "{0F80A312-7D1B-4C1D-9498-EB19D6BC95B2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Common", "Common", "{1DBD9104-B928-4D45-8EDD-9C29260CEADB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CachingManager", "src\Common\CachingManager\CachingManager.csproj", "{2A1FA370-612E-4C99-8DEA-983E50BD967F}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{0659981C-C552-48D9-A040-42DB56BEE77B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0659981C-C552-48D9-A040-42DB56BEE77B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0659981C-C552-48D9-A040-42DB56BEE77B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0659981C-C552-48D9-A040-42DB56BEE77B}.Release|Any CPU.Build.0 = Release|Any CPU
		{61C72517-4FB7-4D94-ACB8-1A624191ACD9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{61C72517-4FB7-4D94-ACB8-1A624191ACD9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{61C72517-4FB7-4D94-ACB8-1A624191ACD9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{61C72517-4FB7-4D94-ACB8-1A624191ACD9}.Release|Any CPU.Build.0 = Release|Any CPU
		{1980D795-B3E0-4C53-A212-3D473FB28D10}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1980D795-B3E0-4C53-A212-3D473FB28D10}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1980D795-B3E0-4C53-A212-3D473FB28D10}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1980D795-B3E0-4C53-A212-3D473FB28D10}.Release|Any CPU.Build.0 = Release|Any CPU
		{9751BEED-1860-4FA3-99DA-B46B29D3F7E9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9751BEED-1860-4FA3-99DA-B46B29D3F7E9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9751BEED-1860-4FA3-99DA-B46B29D3F7E9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9751BEED-1860-4FA3-99DA-B46B29D3F7E9}.Release|Any CPU.Build.0 = Release|Any CPU
		{0F80A312-7D1B-4C1D-9498-EB19D6BC95B2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0F80A312-7D1B-4C1D-9498-EB19D6BC95B2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0F80A312-7D1B-4C1D-9498-EB19D6BC95B2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0F80A312-7D1B-4C1D-9498-EB19D6BC95B2}.Release|Any CPU.Build.0 = Release|Any CPU
		{2A1FA370-612E-4C99-8DEA-983E50BD967F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2A1FA370-612E-4C99-8DEA-983E50BD967F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2A1FA370-612E-4C99-8DEA-983E50BD967F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2A1FA370-612E-4C99-8DEA-983E50BD967F}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{965B0BB2-E4C8-45B3-A14B-6308D11823C4} = {509FA1D0-427C-4739-9130-E9BC334E2D4F}
		{1CCA2605-2235-4FB8-822B-E8D6406B26BF} = {509FA1D0-427C-4739-9130-E9BC334E2D4F}
		{F55C225C-163A-4681-8ED3-B22BC3CC578A} = {965B0BB2-E4C8-45B3-A14B-6308D11823C4}
		{A41CCAE4-1457-42E1-8E2D-AE571738F1F3} = {F55C225C-163A-4681-8ED3-B22BC3CC578A}
		{0659981C-C552-48D9-A040-42DB56BEE77B} = {F55C225C-163A-4681-8ED3-B22BC3CC578A}
		{61C72517-4FB7-4D94-ACB8-1A624191ACD9} = {A41CCAE4-1457-42E1-8E2D-AE571738F1F3}
		{003914C7-DEF4-4BAB-B458-FC4212D2DA14} = {965B0BB2-E4C8-45B3-A14B-6308D11823C4}
		{4B579348-39A8-4C1A-9E4C-11C62854AF22} = {003914C7-DEF4-4BAB-B458-FC4212D2DA14}
		{F5DA59A9-4070-400C-90F1-412326C84A5A} = {965B0BB2-E4C8-45B3-A14B-6308D11823C4}
		{11915D86-E685-4E7E-B444-602A4A85BD7C} = {F5DA59A9-4070-400C-90F1-412326C84A5A}
		{71E3D758-21CA-4E8F-92AA-58864D451A9C} = {965B0BB2-E4C8-45B3-A14B-6308D11823C4}
		{1980D795-B3E0-4C53-A212-3D473FB28D10} = {71E3D758-21CA-4E8F-92AA-58864D451A9C}
		{9751BEED-1860-4FA3-99DA-B46B29D3F7E9} = {1CCA2605-2235-4FB8-822B-E8D6406B26BF}
		{0F3D98AE-1794-4B02-A5D9-F1091C583A30} = {71E3D758-21CA-4E8F-92AA-58864D451A9C}
		{0F80A312-7D1B-4C1D-9498-EB19D6BC95B2} = {0F3D98AE-1794-4B02-A5D9-F1091C583A30}
		{1DBD9104-B928-4D45-8EDD-9C29260CEADB} = {509FA1D0-427C-4739-9130-E9BC334E2D4F}
		{2A1FA370-612E-4C99-8DEA-983E50BD967F} = {1DBD9104-B928-4D45-8EDD-9C29260CEADB}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {F031F96A-4992-47BC-A47D-061609EA54CB}
	EndGlobalSection
EndGlobal
