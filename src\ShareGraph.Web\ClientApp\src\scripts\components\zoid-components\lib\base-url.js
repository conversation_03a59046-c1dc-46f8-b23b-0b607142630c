import { ensureNotNull } from "../helpers";

// Ensure that a <base> element with a valid "href" attribute exists in the document's head.
export const baseUrl = ensureNotNull(document.querySelector("base[href]")).href;

// Determine the deployment subpath of the application.
// This will be "/" if the app is deployed at the root, or a subdirectory (e.g., "/path/to") if deployed in a nested path.
export const basePath = baseUrl.substring(location.origin.length) || '/';