import { useEffect, useMemo, useState } from "react";
import DataFetch from "./services/chart-data-service";
import Loading from "./components/Loading";
import NoData from "./components/NoData";
import { ChartProvider } from "./context";
import Settings from "./components/Settings";
import renderLegend from "./legend/renderLegend";
import PanesRenderer from "./components/PanesRenderer";
import MainLegend from "./legend/MainLegend";
import PromodeChartTicker from "./PromodeChartTicker";

const getSelectedWatchListInsId = () => {
  const instrumentId = window.EurolandAppContext?.command(
    "instrument-selected"
  )?.id ;
  return instrumentId ? parseInt(instrumentId) : undefined;
};

const PromodeChart = ({ mainInstrumentId }) => {
  const [selectedInstrumentId, setSelectedInstrumentId] = useState(mainInstrumentId);

  useEffect(() => {
    const selectedWatchListInsId = getSelectedWatchListInsId();
    if (selectedWatchListInsId) return;
    setSelectedInstrumentId(mainInstrumentId);
  }, [mainInstrumentId]);

  useEffect(() => {
    const handleSelectedWatchListInsIdChange = () => {

      setSelectedInstrumentId(getSelectedWatchListInsId());
    };

    window.EurolandAppContext?.on("instrument-selected", handleSelectedWatchListInsIdChange);

    return () => {
      window.EurolandAppContext?.off("instrument-selected", handleSelectedWatchListInsIdChange);
    };
  }, []);

  const instanceDataFetch = useMemo(() => new DataFetch(), []);

  return (
    <>
      <PromodeChartTicker instrumentId={selectedInstrumentId} />
      <ChartProvider instanceDataFetch={instanceDataFetch} >
        <Loading />
        <NoData />

        <PanesRenderer
          render={({ pane, indicators }, advanceChart) => (
            <>
              <div className="chart-legends">
                {pane.paneIndex() === 0 ? <MainLegend /> : null}
                <div className="chart-legends__indicators">{indicators.map((item, index) => renderLegend(item, index, advanceChart))}</div>
              </div>
            </>
          )}
        />

        <Settings selectedInstrumentId={selectedInstrumentId} />
      </ChartProvider>
    </>
  );
};

export default PromodeChart;
