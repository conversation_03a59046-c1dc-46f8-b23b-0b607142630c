﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
    public class FCEvent
    {
        [Column("fce_Id"), Key]
        public short Id { get; set; }
        [Column("fce_Type")]
        public short TypeId { get; set; }
        [Column("fce_english_Event")]
        public string English { get; set; }
        [Column("fce_german_Event")]
        public string German { get; set; }
        [Column("fce_spanish_Event")]
        public string Spanish { get; set; }
        [Column("fce_russian_Event")]
        public string Russian { get; set; }
        [Column("fce_polish_Event")]
        public string Polish { get; set; }
        [Column("fce_chinese_Event")]
        public string Chinese { get; set; }
        [Column("fce_korean_Event")]
        public string Korean { get; set; }
        [Column("fce_japanese_Event")]
        public string Japanese { get; set; }
        [Column("fce_dutch_Event")]
        public string Dutch { get; set; }
        [Column("fce_italian_Event")]
        public string Italian { get; set; }
        [Column("fce_finnish_Event")]
        public string Finnish { get; set; }
        [Column("fce_swedish_Event")]
        public string Swedish { get; set; }
        [Column("fce_french_Event")]
        public string French { get; set; }
        [Column("fce_danish_Event")]
        public string Danish { get; set; }
        [Column("fce_arabic_Event")]
        public string Arabic { get; set; }
        [Column("fce_icelandic_Event")]
        public string Icelandic { get; set; }
        [Column("fce_vietnamese_Event")]
        public string Vietnamese { get; set; }
        [Column("fce_taiwanese_Event")]
        public string Taiwanese { get; set; }
        [Column("fce_norwegian_Event")]
        public string Norwegian { get; set; }

        [Column("fce_estonian_Event")]
        public string? Estonian { get; set; }

        [Column("fce_portugese_Event")]
        public string? Portuguese { get; set; }

        [Column("fce_hebrew_Event")]
        public string? Hebrew { get; set; }

        [Column("fce_kurdish_Event")]
        public string? Kurdish { get; set; }

        [Column("fce_catalan_Event")]
        public string? Catalan { get; set; }

        [Column("fce_romanian_Event")]
        public string? Romanian { get; set; }

        [Column("fce_lithuanian_Event")]
        public string? Lithuanian { get; set; }

        [Column("fce_greece_Event")]
        public string? Greek { get; set; }

        public bool? JPtranslated { get; set; }
        public bool? KOtranslated { get; set; }
        public bool? DKtranslated { get; set; }
        public bool? VItranslated { get; set; }
        public bool? ETtranslated { get; set; }
        public FCalendar FCalendar { get; set; }
    }
}
