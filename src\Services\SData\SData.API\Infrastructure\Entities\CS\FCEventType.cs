﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
    public class FCEventType
    {
        [Column("fcet_ID"), Key]
        public short Id { get; set; }

        [Column("fcet_english_Type")]
        public string? English { get; set; }

        [Column("fcet_dutch_Type")]
        public string? Dutch { get; set; }

        [Column("fcet_finnish_Type")]
        public string? Finnish { get; set; }

        [Column("fcet_french_Type")]
        public string? French { get; set; }

        [Column("fcet_german_Type")]
        public string? German { get; set; }

        [Column("fcet_italian_Type")]
        public string? Italian { get; set; }

        [Column("fcet_spanish_Type")]
        public string? Spanish { get; set; }

        [Column("fcet_polish_Type")]
        public string? Polish { get; set; }

        [Column("fcet_swedish_Type")]
        public string? Swedish { get; set; }

        [Column("fcet_chinese_Type")]
        public string? Chinese { get; set; }

        [Column("fcet_japanese_Type")]
        public string? Japanese { get; set; }

        [Column("fcet_korean_Type")]
        public string? Korean { get; set; }

        [Column("fcet_russian_Type")]
        public string? Russian { get; set; }

        [Column("fcet_danish_Type")]
        public string? Danish { get; set; }

        [Column("fcet_arabic_Type")]
        public string? Arabic { get; set; }

        [Column("fcet_vietnamese_Type")]
        public string? Vietnamese { get; set; }

        [Column("fcet_estonian_Type")]
        public string? Estonian { get; set; }

        [Column("fcet_icelandic_Type")]
        public string? Icelandic { get; set; }

        [Column("fcet_portugese_Type")]
        public string? Portuguese { get; set; }

        [Column("fcet_hebrew_Type")]
        public string? Hebrew { get; set; }

        [Column("fcet_kurdish_Type")]
        public string? Kurdish { get; set; }

        [Column("fcet_catalan_Type")]
        public string? Catalan { get; set; }

        [Column("fcet_romanian_Type")]
        public string? Romanian { get; set; }

        [Column("fcet_lithuanian_Type")]
        public string? Lithuanian { get; set; }

        [Column("fcet_Greece_Type")]
        public string? Greek { get; set; }

        public bool? JPtranslated { get; set; }
        public bool? KOtranslated { get; set; }
        public bool? DKtranslated { get; set; }
        public bool? VItranslated { get; set; }
        public bool? ETtranslated { get; set; }
    }
}
