
import QuestionCard from "./QuestionCard";
import clsx from "clsx";
import {useSearchWidgetPropsContext} from "../context/propContext";

interface CommonQuestionsProps {
  showCommonQuestions: boolean;
  onSubmitQuestion: (question: string) => void;
}

const CommonQuestions: React.FC<CommonQuestionsProps> = ({
  onSubmitQuestion,
  showCommonQuestions
}) => {
  const suggestedQuestions = useSearchWidgetPropsContext(s => s.aiSearchSettings.suggestedQuestionsRandom);
  const showSuggestions = useSearchWidgetPropsContext(s => s.aiSearchSettings.showSuggestions);

  if (!showSuggestions) return null;

  return (
    <div className={clsx("wise-widget__suggestions", {'hidden': !showCommonQuestions})}>
      {suggestedQuestions.map((question, index) => {
        return (
          <QuestionCard
            key={index}
            question={question}
            onClick={() => onSubmitQuestion(question)}
          />
        )
      })}
    </div>
  );
};

export default CommonQuestions;
