using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using AutoMapper;
using Euroland.FlipIT.SData.API.AutoMapper.Profiles;
using Euroland.FlipIT.SData.API.Dto;

namespace Microsoft.Extensions.DependencyInjection;

public static class IMapperExtensions
{
  public class IQueryableInternalMapper<TEntity> where TEntity: class
  {
    private IQueryable<TEntity> _entities;
    internal IQueryableInternalMapper(IQueryable<TEntity> entities)
    {
      _entities = entities;
    }

    public IQueryable<TDto> ToProjection<TDto>() where TDto: IDtoObject
    {
      return _mapper.ProjectTo<TDto>(_entities);
    }

    public IQueryable<TDto> ToProjection<TDto>(
      #pragma warning disable S3427 // Method overloads with default parameter values should not overlap
      object parameters = null,
      #pragma warning restore S3427 // Method overloads with default parameter values should not overlap
      params Expression<Func<TDto, object>>[] membersToExpand
    ) where TDto: IDtoObject {
      return _mapper.ProjectTo<TDto>(_entities, parameters, membersToExpand);
    }

    public IQueryable<TDto> ToProjection<TDto>(
      IDictionary<string, object> parameters,
      params string[] membersToExpand
    ) where TDto: IDtoObject
    {
      return _mapper.ProjectTo<TDto>(_entities, parameters, membersToExpand);
    }

    public IEnumerable<TDto> Map<TDto>() where TDto: IDtoObject
    {
      return _entities.AsEnumerable().Select(e => _mapper.Map<TEntity, TDto>(e));
    }
  }

  public class IEnumerableInternalMapper<TEntity> where TEntity: class
  {
    private IEnumerable<TEntity> _entities;
    internal IEnumerableInternalMapper(IEnumerable<TEntity> entities)
    {
      _entities = entities;
    }
    public IEnumerable<TDto> Map<TDto>() where TDto: IDtoObject
    {
      return _entities.Select(e => _mapper.Map<TEntity, TDto>(e));
    }
  }

  private readonly static MapperConfiguration _configuration = new (
    cfg => {
      cfg.AddProfile<CompanyMapperProfile>();
      cfg.AddProfile<CurrencyMapperProfile>();
      cfg.AddProfile<InstrumentMapperProfile>();
      cfg.AddProfile<InstrumentHistoryMapperProfile>();
      cfg.AddProfile<PressReleaseMapperProfile>();
      cfg.AddProfile<CurrencyRateMapperProfile>();
      cfg.AddProfile<CurrencyRateHistoryMapperProfile>();
      cfg.AddProfile<AttachmentMapperProfile>();
      cfg.AddProfile<MarketMapperProfile>();
      cfg.AddProfile<CityMapperProfile>();
      cfg.AddProfile<CountryMapperProfile>();
      cfg.AddProfile<TimezoneMapperProfile>();
      cfg.AddProfile<CustomerTypeMapperProfile>();
      cfg.AddProfile<DividendMapperProfile>();
      cfg.AddProfile<FCalendarMapperProfile>();
      cfg.AddProfile<FCEventTypeMapperProfile>();
      cfg.AddProfile<PerformanceShareMapperProfile>();
      cfg.AddProfile<ListMapperProfile>();
      cfg.AddProfile<SubSectorMapperProfile>();
      cfg.AddProfile<LanguageMapperProfile>();
      cfg.AddProfile<InstrumentDailyDataMapperProfile>();
      cfg.AddProfile<WebcastMapperProfile>();
    }
  );

  private readonly static IMapper _mapper = new Mapper(_configuration);

  public static IMapper Mapper => _mapper;

  public static IQueryableInternalMapper<TEntity> WithAutoMapper<TEntity>(this IQueryable<TEntity> entities) where TEntity: class {
    return new IQueryableInternalMapper<TEntity>(entities);
  }

  public static IEnumerableInternalMapper<TEntity> WithAutoMapper<TEntity>(this IEnumerable<TEntity> entities) where TEntity: class {
    return new IEnumerableInternalMapper<TEntity>(entities);
  }
}
