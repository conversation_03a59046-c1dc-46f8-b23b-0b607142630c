using HotChocolate.Types;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.SubSector;

/// <summary>
/// Extends 'subSector' field to type <see cref="Instrument.InstrumentType"/>
/// </summary>
public class InstrumentTypeExtensions : ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(Instrument.InstrumentType.Name);

    descriptor.Field("subSector")
      .UseFirstOrDefault()
      .UseProjection()
      .AddCultureNameArgument()
      .ResolveWith<SubSectorResolver>(
        resolvers => resolvers.GetSubSectorByInstrument(default!, default!, default!)
      );
  }
}
