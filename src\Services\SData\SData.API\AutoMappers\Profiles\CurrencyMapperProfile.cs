using AutoMapper;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;

namespace Euroland.FlipIT.SData.API.AutoMapper.Profiles;

public class CurrencyMapperProfile : Profile
{
  /// <summary>
  /// AutoMapper Profile: <see cref="CurrencyDto"/>
  /// </summary>
  public CurrencyMapperProfile()
  {
    CreateMap<Currency, CurrencyDto>(MemberList.Destination);
  }
}
