import {FC} from 'react';
import { useAuth } from '../core';
import { type XProps } from '@/zoid';
import { type AuthPros } from '../core/interfaces';

/**
 * LogoutComponent is a React functional component that handles the logout process.
 * It checks if the user is authenticated using the `useAuth` hook.
 * If the user is authenticated, it triggers a sign-out redirect to the IdentityServer.
 * The component conditionally renders a message based on the authentication status:
 * - If authenticated, it displays "Redirecting...".
 * - If not authenticated, it displays "You're not logged in.".
 *
 */
export const LogoutComponent: FC = () => {
  const auth = useAuth();

  if(auth.isLoading) return null;

  if (auth.isAuthenticated) {
    // Redirect to the IdentityServer.
    auth.signoutRedirect();
  }
  else {
    const xprops = window.xprops as unknown as XProps<AuthPros>

    if (typeof (xprops.onLogoutSuccess) === 'function') {
      xprops.onLogoutSuccess();
    }
    if (typeof (xprops.close) === 'function') {
      xprops.close();
    }

  }

  return null
};
