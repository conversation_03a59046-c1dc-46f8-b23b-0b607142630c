using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Types;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;

/// <summary>
/// Extends type of <see cref="InstrumentType"/> to other type like <see cref="Company.CompanyType"/>
/// </summary>
public class CompanyTypeExtensions: ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(Company.CompanyType.Name);

    descriptor.Field("instruments")
      .Argument(
        "type",
        argDescriptor =>
          argDescriptor.Type<EnumType<InstrumentTypeDto>>()
            .DefaultValue(InstrumentTypeDto.ALL)
            .Description("Type of instrument. Default is ALL")
      )
      .AddExchangeCurrencyArgument()
      .AddAdjCloseArgument()
      .AddUseCloudArgument()
      .ResolveWith<InstrumentResolvers>(
        q => q.GetInstruments(default!, default!, default!, default!, default)
      );
  }
}
