using System;
using System.Linq.Expressions;
using System.Security.Cryptography;
using System.Text;
using Euroland.FlipIT.SData.API.Dto;

namespace Euroland.FlipIT.SData.API.Infrastructure.DataCache;

public static class CacheKeyHelper
{
    /// <summary>
    /// Generates a cache key based on the specified type, property, and property value.
    /// </summary>
    /// <typeparam name="T">The type of the object used in the cache.</typeparam>
    /// <typeparam name="TProp">The type of the property used for generating the cache key.</typeparam>
    /// <param name="propertyValue">Property value of corresponding object</param>
    /// <param name="propertySelector">An expression that selects the property to be used in the cache key.</param>
    /// <returns>A string representing the generated cache key.</returns>
    /// <exception cref="ArgumentException">Thrown when the provided expression is not a valid member expression.</exception>
    /// <remarks>
    /// This method constructs a cache key by combining the name of the type, the name of the selected property,
    /// and value of the selected property, separated by underscores. The value of the selected property is converted to lowercase.
    /// The resulting cache key format is: "{TypeName}_{PropertyName}_{PropertyValue}".
    /// </remarks>
    public static string GenerateKeyByPropOfObject<T, TProp>(string propertyValue, Expression<Func<T, TProp>> propertySelector)
    {
        var propertyName = GetPropertyName(propertySelector);
        var keyString = $"{typeof(T).Name}_{propertyName}_{propertyValue.ToLower()}";

        return StringToInt32(keyString).ToString();
    }

    /// <summary>
    /// Generates a cache key based on the specified DTO type, property, and property value.
    /// </summary>
    /// <typeparam name="TDto">The type of the Data Transfer Object (DTO) used in the DataLoader.</typeparam>
    /// <typeparam name="TProp">The type of the property used for generating the cache key.</typeparam>
    /// <param name="propertyValue">Property value of corresponding Dto object </param>
    /// <param name="propertySelector">An expression that selects the property from the DTO to be used in the cache key.</param>
    /// <returns>A string representing the generated cache key.</returns>
    /// <exception cref="ArgumentException">Thrown when the provided expression is not a valid member expression.</exception>
    /// <remarks>
    /// This method constructs a cache key by combining the name of the DTO type, the name of the selected property,
    /// and value of the selected property, separated by underscores. The value of the selected property is string and be converted to lowercase.
    /// The resulting cache key format is: "{DTOTypeName}_{PropertyName}_{PropertyValue}".
    /// </remarks>
    public static string GenerateKeyByPropOfDtoObject<TDto, TProp>(string propertyValue, Expression<Func<TDto, TProp>> propertySelector) where TDto : IDtoObject
    {
        var propertyName = GetPropertyName(propertySelector);
        var keyString = $"{typeof(TDto).Name}_{propertyName}_{propertyValue.ToLower()}";

    return StringToInt32(keyString).ToString();
    }

    /// <summary>
    /// Extracts the property name from the given property selector expression.
    /// </summary>
    /// <typeparam name="TDto">The type of the object containing the property.</typeparam>
    /// <typeparam name="TProp">The type of the property.</typeparam>
    /// <param name="propertySelector">An expression that selects the property.</param>
    /// <returns>The name of the selected property.</returns>
    /// <exception cref="ArgumentException">Thrown when the provided expression is not a valid member expression.</exception>
    private static string GetPropertyName<TDto, TProp>(Expression<Func<TDto, TProp>> propertySelector)
    {
      return propertySelector.Body switch
      {
        MemberExpression memberExpression => memberExpression.Member.Name,
        UnaryExpression { Operand: MemberExpression member } => member.Member.Name,
        _ => throw new ArgumentException("Invalid property selector expression.", nameof(propertySelector))
      };
    }

    private static uint StringToInt32(string inputStr) {
      var encoded = SHA256.Create().ComputeHash(Encoding.UTF8.GetBytes(inputStr));
      return BitConverter.ToUInt32(encoded, 0) % 1000000;
    }
}
