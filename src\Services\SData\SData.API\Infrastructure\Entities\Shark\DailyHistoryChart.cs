﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
  [Table("daily_history_chart")]
    public class DailyHistoryChart
    {
        [Key]
        public int InstrumentId { get; set; }
        [Column("hDate")]
        public DateTime Date { get; set; }
        [Column("hClose", TypeName = "decimal(18,4)")]
        public decimal Close { get; set; }
        [Column("hVolume")]
        public long? Volume { get; set; }
    }
}
