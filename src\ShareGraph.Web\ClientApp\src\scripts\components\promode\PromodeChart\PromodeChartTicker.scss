.promode-ticker-horizontal {

  padding: 12px;

  .ticker-main {
    display: flex;
    align-items: baseline;
    gap: 8px;
    flex-wrap: wrap;

    .ticker-price {
      font-size: 24px;
      font-weight: 700;
      color: #212529;
      line-height: 1;
    }

    .ticker-currency {
      font-size: 14px;
      font-weight: 500;
      color: #6c757d;
      text-transform: uppercase;
      margin-right: 4px;
    }

    .ticker-change {
      font-size: 14px;
      font-weight: 600;

      &.positive {
        color: #22c55e;
      }

      &.negative {
        color: #ef4444;
      }

      &.neutral {
        color: #6c757d;
      }
    }
  }

  .ticker-time {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
    font-weight: 400;
  }
}


