﻿using System;
using HotChocolate;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
  // Deprecated for old schema only, but still keep entity as it is
  /// <summary>
  /// Represents the daily data for an instrument.
  /// </summary>
  [GraphQLName("InstrumentDailyData_Deprecated")]
  public class InstrumentDailyData
  {
    /// <summary>
    /// Gets or sets the unique identifier for the daily data.
    /// </summary>
    public long ID { get; set; }

    /// <summary>
    /// Gets or sets the unique identifier for the instrument.
    /// </summary>
    public int InstrumentID { get; set; }

    /// <summary>
    /// Gets or sets the instrument associated with the daily data.
    /// This property is ignored by GraphQL.
    /// </summary>
    [GraphQLIgnore]
    public virtual Instrument Instrument { get; set; }

    /// <summary>
    /// Gets or sets the UTC date of the daily data.
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// Gets or sets the closing value of the instrument on the specified date.
    /// </summary>
    public decimal Close { get; set; }

    /// <summary>
    /// Gets or sets the volume of the instrument on the specified date.
    /// </summary>
    public long? Volume { get; set; }
  }
}
