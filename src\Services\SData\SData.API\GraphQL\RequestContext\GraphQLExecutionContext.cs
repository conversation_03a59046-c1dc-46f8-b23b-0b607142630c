using System;
using System.Collections.Generic;

namespace Euroland.FlipIT.SData.API.GraphQL.RequestContext;

/// <summary>
/// Represents the context for a query request, providing options for request identification and data source preferences.
/// </summary>
public class GraphQLExecutionContext: IGraphQLExecutionContext
{
  /// <inheritdoc />
  public string? RequestId { get; set; }

  /// <inheritdoc />
  public bool UseRealtime { get; set; } = false;

  /// <inheritdoc />
  public bool UseCloud { get; set; } = false;

  /// <inheritdoc />
  public bool UseAdjClose { get; set; }

  /// <inheritdoc />
  public string? ExchangeCurrency { get; set; }

  /// <inheritdoc />
  public IDictionary<string, object> ScopedContextData { get; set; } = new Dictionary<string, object>();
}
