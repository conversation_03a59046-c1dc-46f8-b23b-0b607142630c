using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.CompanyName;

/// <summary>
/// Extends 'companyName' field to type <see cref="Company.CompanyType"/>
/// </summary>
public class CompanyTypeExtensions : ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(Company.CompanyType.Name);

    descriptor.Field("translation")
      .Argument(
        "cultureName",
        argDescriptor =>
          argDescriptor.Type<NonNullType<StringType>>()
            .Description("The culture name to get translation of company name. E.g. en-US, en-GB")
      )
      .UseFirstOrDefault()
      .UseProjection()
      .ResolveWith<CompanyNameResolvers>(
        p => p.GetCompanyNameByCompany(default!, default!, default!, default!, default)
      );
  }
}

