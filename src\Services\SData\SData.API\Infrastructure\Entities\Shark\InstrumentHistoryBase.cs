﻿using HotChocolate;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
  public abstract class InstrumentHistoryBase
  {
    [Key]
    [GraphQLIgnore]
    public int ID { get; set; }

    public int InstrumentId { get; set; }

    [Column("Date", TypeName = "datetime")]
    public DateTime DateTime { get; set; }

    [NotMapped]
    public string? Date { get; set; }

    [Column(TypeName = "decimal(18,4)")]
    public decimal Close { get; set; }

    public long? Volume { get; set; }

    [Column(TypeName = "decimal(18,4)")]
    public decimal? High { get; set; }

    [Column(TypeName = "decimal(18,4)")]
    public decimal? Low { get; set; }

    [Column("Open", TypeName = "decimal(18,4)")]
    public decimal? Open { get; set; }

    [NotMapped]
    public decimal? Rate { get; set; }

    [GraphQLIgnore]
    public virtual Instrument Instrument { get; set; }
  }
}
