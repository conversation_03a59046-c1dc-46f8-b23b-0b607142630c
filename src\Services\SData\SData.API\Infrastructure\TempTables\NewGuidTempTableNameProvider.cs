using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

namespace Euroland.FlipIT.SData.API.Infrastructure.TempTables;

public class NewGuidTempTableNameProvider : ITempTableNameProvider
{
  public static readonly ITempTableNameProvider Instance = new NewGuidTempTableNameProvider();

  /// <inheritdoc />
  public ITempTableNameLease LeaseName(DbContext ctx, IEntityType entityType)
  {
    if (entityType == null)
      throw new ArgumentNullException(nameof(entityType));

    var tableName = entityType.GetSchemaQualifiedTableName();

    tableName = $"{tableName}_{Guid.NewGuid():N}";

    return new TempTableName(tableName);
  }
}
