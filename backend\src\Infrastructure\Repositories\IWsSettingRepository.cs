﻿using Microsoft.EntityFrameworkCore;
using Euroland.FlipIT.WiseSearch.Models.Setting;
using AutoMapper;
using Euroland.FlipIT.WiseSearch.Infrastructure.Entities.WiseSearch;

namespace Euroland.FlipIT.WiseSearch.Infrastructure.Repositories
{
    public interface IWsSettingRepository : IRepositoryBase<WiseSearchDbContext, SettingEntity, Guid>
    {
        Task<SettingResponse?> GetSettingByCodeAndVersion(string companyCode, string? version);
    }

    public class WsSettingRepository : RepositoryBase<WiseSearchDbContext, SettingEntity, Guid>, IWsSettingRepository
    {
        private readonly IMapper _mapper;
        private readonly IConfiguration _config;

        public WsSettingRepository(WiseSearchDbContext context, IMapper mapper, IConfiguration config) : base(context)
        {
            _mapper = mapper;
            _config = config;
        }

        public async Task<SettingResponse?> GetSettingByCodeAndVersion(string companyCode, string? version)
        {
            var query = await _dbContext.Settings
                                        .FirstOrDefaultAsync(s => s.CompanyCode == companyCode
                                                                   && (string.IsNullOrEmpty(version) || s.Version == version));

            var result = _mapper.Map<SettingEntity, SettingResponse>(query);

            return result;
        }
    }
}
