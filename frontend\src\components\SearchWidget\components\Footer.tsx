import { FC } from "react";
import { i18n } from "@euroland/libs";

export const Footer: FC = () => (
  <div className="wise-widget-footer">
    <div className="wise-widget-footer-main">
      <div className="wise-widget-footer-left">
        <span>{i18n.translate("poweredByAI")}</span>
        <img
          src={import.meta.env.VITE_LOGO_URL}
          alt="Euroland Logo"
          className="wise-widget-footer-logo"
        />
      </div>
      <div className="wise-widget-footer-links">
        <span>© 2025 Euroland</span>
        <span className="wise-widget-footer-separator">|</span>
        <a href={import.meta.env.VITE_PRIVACY_POLICY_URL} target="_blank">
          {i18n.translate("privacyPolicy")}
        </a>
        <span className="wise-widget-footer-separator">|</span>
        <a href={import.meta.env.VITE_TERMS_OF_SERVICE_URL} target="_blank">
          {i18n.translate("termsOfService")}
        </a>
      </div>
    </div>
    <div className="wise-widget-footer-disclaimer">
      {i18n.translate("footerDisclaimer")}
    </div>
  </div>
);
