using HotChocolate.Types;
using HotChocolate.Types.Pagination;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Webcast;

public class CompanyTypeExtensions : ObjectTypeExtension
{
  private const int WebcastsFieldDefaultPageSize = 200;

  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(Company.CompanyType.Name);

    descriptor.Field("webcasts")
      .Description($"Gets list of webcasts. Default pageSize = {WebcastsFieldDefaultPageSize}")
      .UsePaging(connectionName: "Webcasts",
        options: new PagingOptions
        {
          IncludeTotalCount = true,
          MaxPageSize = int.MaxValue,
          DefaultPageSize = WebcastsFieldDefaultPageSize
        })
      .UseProjection()
      .UseFiltering()
      .UseSorting(typeof(WebcastSortType))
      .ResolveWith<WebcastResolver>(
        resolvers => resolvers.GetCompanyWebcasts(default!, default!)
      );
  }
}
