﻿using HotChocolate;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
    [GraphQLName("Attachment_Deprecated")]
    public class Attachment
    {
        [Column("atID")]
        public long Id { get; set; }
        [Column("atPressreleaseID")]
        public long PressreleaseId { get; set; }
        [Column("atFilename")]
        public string? FileName { get; set; }
        [Column("atLocation")]
        public string? Location { get; set; }
        [Column("atOrder")]
        public byte? Order { get; set; }
        [Column("atLanguageID", TypeName = "tinyint")]
        public int LanguageId { get; set; }
        [Column("atMime")]
        public string Mime { get; set; }
        [Column("atIsInBlob")]
        public bool IsInBlob { get; set; }

        [GraphQLIgnore]
        public PressRelease PressRelease { get; set; }
    }
}
