import { createContext } from "react";
import type {User, UserManager} from "oidc-client-ts";
import {AuthState} from "./interfaces.ts";

export const userManagerContextKeys = [
  'removeUser',
  'clearStaleState',
  'querySessionStatus',
  'revokeTokens',
  'startSilentRenew',
  'stopSilentRenew',
  "getUser"
] as const;

export const navigatorKeys = [
  'signinSilent',
  'signinRedirect',
  'signinResourceOwnerCredentials',
  'signoutRedirect',
  'signoutSilent'
] as const;


type TupleToUnion<T extends readonly string[]> = T[number];
type Include<T, K extends keyof T> = {
  [P in K]: T[P];
};

export type UserManagerContext = Include<
  UserManager,
  | TupleToUnion<typeof userManagerContextKeys>
  | TupleToUnion<typeof navigatorKeys>
  | "events"
  | "settings"
>;

export interface OidcAuthContextProps extends Omit<UserManagerContext, 'signinRedirect' | 'signinSilent' | 'signoutRedirect'>, AuthState {
  login: () => Promise<void>;
  logout: () => Promise<void>;
  renewToken: () => void;
  signinRedirect: () => void;
  signoutRedirect: () => void;
  signinSilent: () => Promise<User | null>;
}


const OidcAuthContext = createContext<OidcAuthContextProps>(null as any);
OidcAuthContext.displayName = 'OidcAuthContext';

export { OidcAuthContext }


