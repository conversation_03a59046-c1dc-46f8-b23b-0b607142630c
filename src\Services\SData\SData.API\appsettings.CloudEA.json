{"ConnectionStrings": {"NewsDb": "Server=tcp:cs9ee6r8r4.database.windows.net,1433;Database=shark;User ID=uShark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=SData.API", "SharkDb": "Server=tcp:cs9ee6r8r4.database.windows.net,1433;Database=shark;User ID=uShark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=SData.API", "SharkRealtimeDb": "Server=tcp:eurolandeurope.database.windows.net,1433;Database=shark;User ID=uShark;Password=**********;Trusted_Connection=False;TrustServerCertificate=True;MultipleActiveResultSets=True;Application Name=SData.API", "WebcastDb": "Server=tcp:cs9ee6r8r4.database.windows.net,1433;Database=Webcast;User ID=uShark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=SData.API"}, "Serilog": {"Using": ["Serilog.Sinks.Syslog"], "MinimumLevel": "Warning", "WriteTo": [{"Name": "UdpSyslog", "Args": {"host": "**************", "port": "5141", "appName": "FlipIT.SData.API", "restrictedToMinimumLevel": "Warning", "outputTemplate": "[{Timestamp:HH:mm:ss}] {Message:lj}{NewLine}{Exception}"}}], "Properties": {"Application": "FlipIT.SData.API"}}}