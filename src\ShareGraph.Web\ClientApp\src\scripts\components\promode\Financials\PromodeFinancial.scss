// Font variables - easily changeable
$primary-font: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
$secondary-font: $primary-font;

// Color variables
$primary-color: #1976d2;
$secondary-color: #424242;
$text-primary: #212121;
$text-secondary: #757575;
$background-color: #ffffff;
$border-color: #e0e0e0;
$accent-blue: #2196f3;
$accent-orange: #ff9800;
$accent-teal: #00bcd4;
$accent-pink: #e91e63;
$accent-green: #4caf50;

.dashboard {
  font-family: $primary-font;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: $background-color;
  color: $text-primary;
  line-height: 1.5;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  .headerLeft {
    display: flex;
    align-items: center;
  }

  .logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 16px;

    .heartIcon {
      color: #e91e63;
      width: 20px;
      height: 20px;
    }

    .exchange, .currency {
      color: $text-secondary;
      font-size: 14px;
      font-weight: 400;
    }
  }

  .headerRight {
    display: flex;
    align-items: center;
    gap: 4px;
    color: $text-secondary;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      color: $accent-blue;
    }
  }
}

.title {
  font-size: 24px;
  font-weight: 600;
  margin: 20px 0;
  color: $text-primary;
}

.navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid $border-color;
  margin-bottom: 30px;

  .navLeft {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .searchIcon {
    width: 20px;
    height: 20px;
    color: $text-secondary;
  }

  .navTab {
    background: none;
    border: none;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 500;
    color: $text-secondary;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;

    &:hover {
      color: $text-primary;
    }

    &.active {
      color: $text-primary;
      background-color: #f5f5f5;
      border-radius: 6px;
    }
  }

  .shareIcon {
    width: 20px;
    height: 20px;
    color: $text-secondary;
    cursor: pointer;

    &:hover {
      color: $primary-color;
    }
  }
}

.keyFacts {
  margin-bottom: 30px;

  h2 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: $text-primary;
  }

  .factsGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }

  .factItem {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .factLabel {
      font-size: 13px;
      color: $text-secondary;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .factValue {
      font-size: 15px;
      font-weight: 500;
      color: $text-primary;

      .currency {
        color: $text-secondary;
        font-weight: 400;
        font-size: 13px;
      }
    }
  }
}

.about {
  margin-bottom: 30px;

  h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: $text-primary;
  }

  p {
    font-size: 14px;
    color: $text-secondary;
    line-height: 1.6;
  }

  .showMore {
    background: none;
    border: none;
    color: $accent-blue;
    cursor: pointer;
    font-size: 14px;
    padding: 0;

    &:hover {
      text-decoration: underline;
    }
  }
}

.ownershipSection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-bottom: 40px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.ownership, .capitalStructure {
  h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 20px;
    color: $text-primary;
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.ownershipChart {
  display: flex;
  align-items: center;
  gap: 30px;

  .pieChart {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: conic-gradient(
      $accent-blue 0deg 313deg,
      $accent-orange 313deg 360deg
    );
    display: flex;
    align-items: center;
    justify-content: center;

    &::before {
      content: '';
      position: absolute;
      width: 60px;
      height: 60px;
      background: $background-color;
      border-radius: 50%;
    }

    .centerValue {
      position: relative;
      z-index: 1;
      font-weight: 600;
      font-size: 14px;
      color: $text-primary;
    }
  }

  .ownershipLegend {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
}

.capitalChart {
  .capitalBar {
    height: 40px;
    display: flex;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 20px;

    .barSegment {
      height: 100%;
    }
  }

  .capitalLegend {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}

.legendItem {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;

  .legendColor {
    width: 12px;
    height: 12px;
    border-radius: 2px;
  }

  span:last-child {
    margin-left: auto;
    font-weight: 500;
  }
}

.valuation {
  margin-bottom: 40px;

  h2 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: $text-primary;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  p {
    font-size: 14px;
    color: $text-secondary;
    margin-bottom: 24px;
  }

  .valuationContent {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .valuationLeft {
    h3 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 20px;
      color: $text-primary;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .valuationMetrics {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 24px;

      .metricItem {
        display: flex;
        justify-content: space-between;
        font-size: 14px;

        span:first-child {
          color: $text-secondary;
        }

        span:last-child {
          font-weight: 500;
          color: $text-primary;
        }
      }
    }

    .valuationIndicator {
      text-align: center;
      margin-bottom: 12px;

      span {
        font-size: 18px;
        font-weight: 600;
        color: $text-primary;
      }

      .indicatorBar {
        height: 8px;
        background: linear-gradient(to right, #4caf50, #ff9800, #f44336);
        border-radius: 4px;
        margin: 12px 0;
        position: relative;

        .indicatorPointer {
          position: absolute;
          top: -4px;
          left: 30%;
          width: 16px;
          height: 16px;
          background: $background-color;
          border: 2px solid $text-primary;
          border-radius: 50%;
        }
      }
    }

    .indicatorLabels {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: $text-secondary;
    }
  }

  .valuationRight {
    h3 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 12px;
      color: $text-primary;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .chartTabs {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
      font-size: 14px;

      span {
        color: $text-secondary;
        cursor: pointer;

        &.active {
          color: $text-primary;
          font-weight: 500;
        }

        &:hover {
          color: $text-primary;
        }
      }
    }

    .lineChart {
      .chartArea {
        height: 200px;
        border: 1px solid $border-color;
        border-radius: 4px;
        margin-bottom: 12px;
        padding: 20px;

        .chartSvg {
          width: 100%;
          height: 100%;
        }
      }

      .chartLegend {
        display: flex;
        gap: 20px;
        font-size: 13px;

        span {
          display: flex;
          align-items: center;
          gap: 6px;
        }

        .legendDot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
        }
      }
    }
  }
}

.growthSection {
  margin-bottom: 40px;

  h2 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: $text-primary;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  p {
    font-size: 14px;
    color: $text-secondary;
    margin-bottom: 24px;
  }

  .growthContent {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .performance, .revenueProfit {
    h3 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 20px;
      color: $text-primary;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .performanceChart, .conversionChart {
    height: 200px;
    border: 1px solid $border-color;
    border-radius: 4px;
    padding: 20px;
  }

  .barChart {
    height: 100%;

    .chartBars {
      display: flex;
      justify-content: space-between;
      align-items: end;
      height: 80%;
      gap: 8px;

      .barGroup {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        flex: 1;

        .bar {
          width: 100%;
          max-width: 40px;
          border-radius: 2px;
        }

        span {
          font-size: 11px;
          color: $text-secondary;
        }
      }
    }
  }

  .waterfallChart {
    display: flex;
    justify-content: space-between;
    align-items: end;
    height: 100%;
    gap: 8px;

    .waterfallBar {
      flex: 1;
      border-radius: 2px;
      display: flex;
      align-items: end;
      justify-content: center;
      color: white;
      font-size: 12px;
      font-weight: 500;
      padding: 8px 4px;
    }
  }
}

.revenueBreakdown {
  margin-bottom: 40px;

  h2 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: $text-primary;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  p {
    font-size: 14px;
    color: $text-secondary;
    margin-bottom: 24px;
  }

  .breakdownContent {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .bySource, .byCountry {
    h3 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 8px;
      color: $text-primary;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .period {
      font-size: 13px;
      color: $text-secondary;
      margin-bottom: 20px;
      display: block;
    }

    .donutChart {
      display: flex;
      align-items: center;
      gap: 30px;
      margin-bottom: 20px;

      .donutSvg {
        width: 120px;
        height: 120px;
      }
    }

    .donutLegend {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
  }
}

.estimates {
  margin-bottom: 40px;

  h2 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: $text-primary;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  p {
    font-size: 14px;
    color: $text-secondary;
    margin-bottom: 24px;
  }

  .estimatesContent {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .revenueEstimates, .earningsEstimates {
    h3 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 8px;
      color: $text-primary;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    span {
      font-size: 13px;
      color: $text-secondary;
      margin-bottom: 20px;
      display: block;
    }

    .estimateChart {
      height: 150px;
      border: 1px solid $border-color;
      border-radius: 4px;
      padding: 20px;
    }

    .dotPlot {
      height: 100%;
      display: flex;
      align-items: center;

      .dotRow {
        display: flex;
        gap: 12px;
        align-items: center;

        .dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;
        }

        .dotEmpty {
          width: 12px;
          height: 12px;
          border: 2px solid $border-color;
          border-radius: 50%;
        }
      }
    }
  }
}

.dividends {
  margin-bottom: 40px;

  h2 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: $text-primary;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  p {
    font-size: 14px;
    color: $text-secondary;
    margin-bottom: 24px;
  }

  .noDividends {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    border: 1px solid $border-color;
    border-radius: 8px;

    .dividendIcon {
      width: 48px;
      height: 48px;
      background: #f5f5f5;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      font-weight: 600;
      color: $text-secondary;
    }

    h4 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
      color: $text-primary;
    }

    p {
      font-size: 14px;
      color: $text-secondary;
      margin: 0;
    }
  }
}

.financialHealth {
  margin-bottom: 40px;

  h2 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: $text-primary;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  p {
    font-size: 14px;
    color: $text-secondary;
    margin-bottom: 24px;
  }

  .healthContent {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .debtLevel, .financialPosition {
    h3 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 20px;
      color: $text-primary;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .debtChart, .positionChart {
      height: 200px;
      border: 1px solid $border-color;
      border-radius: 4px;
      padding: 20px;
    }
  }

  .stackedBars {
    display: flex;
    gap: 20px;
    height: 80%;
    align-items: end;

    .stackedBar {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: end;

      .barSegment {
        border-radius: 2px;
        display: flex;
        align-items: end;
        justify-content: center;
        color: white;
        font-size: 12px;
        font-weight: 500;
        padding: 8px 4px;
      }
    }
  }

  .positionLabels {
    display: flex;
    justify-content: space-around;
    margin-top: 12px;
    font-size: 13px;
    color: $text-secondary;
  }
}

// Responsive design
@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
  }

  .navigation {
    .navLeft {
      gap: 12px;
    }

    .navTab {
      padding: 8px 12px;
      font-size: 13px;
    }
  }

  .keyFacts .factsGrid {
    grid-template-columns: 1fr;
  }

  .title {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .dashboard {
    padding: 12px;
  }

  .navigation {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;

    .navLeft {
      justify-content: center;
      flex-wrap: wrap;
    }
  }

  .ownershipChart {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
}
