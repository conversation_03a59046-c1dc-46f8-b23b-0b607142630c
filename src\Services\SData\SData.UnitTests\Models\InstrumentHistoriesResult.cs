﻿using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UnitTest.SData.Application.Models
{
    public class InstrumentHistoriesResult
    {
        public Data Data { get; set; }
    }

    public class Data
    {
        public IEnumerable<InstrumentHistory> InstrumentHistories { get; set; }
    }
}
