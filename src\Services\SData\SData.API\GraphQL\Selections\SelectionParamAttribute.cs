using System;
using HotChocolate;

namespace Euroland.FlipIT.SData.API.GraphQL.Selections;

/// <summary>
/// Supports Local Param Injecting of type <see cref="ISelectionParamContext"/> through the resolver parameter.
/// </summary>
/// <remarks>
///   Usage:
///     <code>
///       public TResult ResolverMethod([SelectionParam] ISelectionParamContext selection) {  }
///     </code>
/// </remarks>
[AttributeUsage(AttributeTargets.Parameter)]
public class SelectionParamAttribute: LocalStateAttribute
{
  public SelectionParamAttribute(): base(nameof(ISelectionParamContext))
  {

  }
}
