﻿using AutoMapper;
using Euroland.FlipIT.SData.API.DTOs.Deprecated;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Mappers.Deprecated.Profiles;
using System.Linq;

namespace Euroland.FlipIT.SData.API.Mappers.Deprecated
{
    public static class EarningEventMappers
    {
        internal static IMapper Mapper { get; }
        static EarningEventMappers()
        {
            Mapper = new MapperConfiguration(cfg => {
                cfg.AddProfile<EarningEventMapperProfile>();
            }).CreateMapper();
        }

        public static IQueryable<EarningEvent> ToModel(this IQueryable<FCEvent> evt, string langCode)
        {
            return Mapper.ProjectTo<EarningEvent>(evt, new { langCode });
        }
    }
}
