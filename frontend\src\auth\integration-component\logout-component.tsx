import { OidcAuthAppRoutes } from "../core";

/**
 * DefaultLogoutIntegrationComponent is a React functional component that integrates with the
 * `window.euroland` object to create a logout component. This component ensures that the
 * `window.euroland` object is available and throws an error if it is not found.
 *
 * The component creates a `LogoutComponent` with specific properties and dimensions, and
 * integrates it into the DOM with the tag `ai-search-signout-component`. The URL for the
 * component is constructed using the base URL and the current location's search parameters.
 *
 * @throws {Error} If the `window.euroland` object is not found.
 *
 * @returns {Object} The created `LogoutComponent` from the `window.euroland` object.
 */
export default function DefaultLogoutIntegrationComponent() {
  if(!window.euroland) {
    throw new Error("Integration window.euroland object not found. Check out whether integration.js is included.");
  }

  let baseUrl = ((document.querySelector('base') || {}) as HTMLBaseElement).href;

  if(/\/$/.test(baseUrl)) {
    baseUrl = baseUrl.replace(/\/$/, '');
  }

  const props = {
    onLogoutSuccess: {
      type: 'function',
      required: true
    },
    onLogoutFail: {
      type: 'function',
      required: true
    }
  };

  return window.euroland.createComponent('LogoutComponent', {
    tag: 'ai-search-signout-component',
    url: `${baseUrl}${OidcAuthAppRoutes.SIGNOUT}${window.location.search}`,
    dimensions: {
      width: '700px',
      height: '600px'
    },
    template: {
      name: 'popup'
    },
    props: props as any
  });
};
