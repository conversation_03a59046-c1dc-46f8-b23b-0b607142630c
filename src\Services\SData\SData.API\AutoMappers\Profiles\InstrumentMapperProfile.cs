using AutoMapper;
using ett = Euroland.FlipIT.SData.API.Infrastructure.Entities;
using System;
using Euroland.FlipIT.SData.API.Dto;

namespace Euroland.FlipIT.SData.API.AutoMapper.Profiles;

public class InstrumentMapperProfile : Profile
{
  static Func<decimal, int, decimal> ToFixed = (decimal x, int decimals) =>
    Math.Round(x, decimals, MidpointRounding.AwayFromZero);

  public InstrumentMapperProfile()
  {
    CreateMap<ett.Instrument, InstrumentDto>()
      .ForMember(d => d.Id,
        cfg => cfg.MapFrom(e => e.InstrumentId))
      .ForMember(d => d.Symbol,
        cfg => cfg.MapFrom(et => et.Ticker))
      .ForMember(d => d.InstrumentType,
        cfg => cfg.MapFrom(e => (InstrumentTypeDto)e.InstrumentType));

    CreateMap<ett.InstrumentPrice, InstrumentPriceDto>()
      .ForMember(d => d.Date,
        cfg => cfg.MapFrom(e => e.LastUpdatedDate))
      .ForMember(d => d.Change,
        cfg => cfg.MapFrom(e => ToFixed(e.Last - e.PrevClose ?? 0 / (decimal)(e.PrevClose != null && e.PrevClose != 0 ? e.PrevClose : 1), 4)))
      .ForMember(d => d.ChangePercentage,
        cfg => cfg.MapFrom(e => ToFixed(((e.Last - e.PrevClose ?? 0) / (decimal)(e.PrevClose != null && e.PrevClose != 0 ? e.PrevClose : 1)) * 100, 4)))
      .ForMember(d => d.MidChange,
        cfg => cfg.MapFrom(e => ToFixed(e.Mid - e.PrevClose ?? 0 / (decimal)(e.PrevClose != null && e.PrevClose != 0 ? e.PrevClose : 1), 4)))
      .ForMember(d => d.MidChangePercentage,
        cfg => cfg.MapFrom(e => ToFixed(((e.Mid - e.PrevClose ?? 0) / (decimal)(e.PrevClose != null && e.PrevClose != 0 ? e.PrevClose : 1)) * 100, 4)));
  }
}
