import { useAuth } from "@/auth";
import { ZoidPopUpConfirm } from "@/components/PopUpConfirm";
import { ZoidPopUpOpenExternalLinkConfirm } from "@/components/PopUpOpenExternalLinkConfirm";
import useZoidEvents from "@/hooks/useZoidEvents";
import { useAppStore } from "@/store/useAppStore";
import { i18n } from "@euroland/libs";
import { useEffect } from "react";
import { AISearchSettings } from "@/services/getAISearchSettings";

const ConfirmPopupControl = ({aiSearchSettings}: {aiSearchSettings: AISearchSettings}) => {
  const auth = useAuth();
  const setProfileMenuOpen = useAppStore((state) => state.setProfileMenuOpen);
  const isLogoutOpen = useAppStore((state) => state.confirm.isLogoutOpen);
  const isExternalLinkOpen = useAppStore(
    (state) => state.confirm.isExternalLinkOpen
  );
  const setExternalLinkOpen = useAppStore(
    (state) => state.confirm.setExternalLinkOpen
  );
  const setLogoutOpen = useAppStore((state) => state.confirm.setLogoutOpen);

  const openEurolandID = () =>
    window.open(import.meta.env.VITE_EUROLAND_ID_URL, "_blank");

  const confirmLogoutWidget = ZoidPopUpConfirm.use(
    {
      content: `<div style="font-size: 24px; font-weight: 600; margin-bottom: 16px;">${i18n.translate(
        "logout.confirmTitle"
      )}</div>
              <div>• ${i18n.translate("logout.localHistoryNote")}</div>
              <div>• ${i18n.translate("logout.alertsNote")}</div>
              <div>• ${i18n.translate("logout.preferencesNote")}</div>
              <div style="font-size: 16px; font-weight: 400; margin-top: 16px;">${i18n.translate(
                "logout.loginReminder"
              )}</div>`,
      onConfirm: async () =>
        auth.logout().then(() => {
          setLogoutOpen(false);
          setProfileMenuOpen(false);
        }),
      onCancel: () => {
        setLogoutOpen(false);
      },
      confirmText: i18n.translate("logout"),
      cancelText: i18n.translate("cancel"),
      aiSearchSettings,
    },
    {
      animate: {
        show: {
          duration: 200,
          easing: "in-out-cubic",
          opacity: [0, 1],
        },
        close: {
          duration: 200,
          easing: "in-out-cubic",
          opacity: [1, 0],
        },
      },
    }
  );

  const confirmOpenExternalLinkWidget = ZoidPopUpOpenExternalLinkConfirm.use(
    {
      content: `<div style="font-size: 24px; font-weight: 600; margin-bottom: 16px;">${i18n.translate(
        "externalLink.confirmTitle"
      )}</div>
                <div>${i18n.translate("externalLink.message", {
                  company: "Nordea",
                })}</div>`,
      onConfirm: () => {
        setExternalLinkOpen(false);
        openEurolandID();
      },
      onCancel: () => {
        setExternalLinkOpen(false);
      },
      confirmText: i18n.translate("ok"),
      cancelText: i18n.translate("cancel"),
      aiSearchSettings,
    },
    {
      animate: {
        show: {
          duration: 200,
          easing: "in-out-cubic",
          opacity: [0, 1],
        },
        close: {
          duration: 200,
          easing: "in-out-cubic",
          opacity: [1, 0],
        },
      },
    }
  );

  useZoidEvents(confirmOpenExternalLinkWidget, {
    BACKDROP_CLICKED: () => setExternalLinkOpen(false)
  })

  useZoidEvents(confirmLogoutWidget, {
    BACKDROP_CLICKED: () => setLogoutOpen(false),
  });

  useEffect(() => {
    if (isLogoutOpen) {
      confirmLogoutWidget.show();
    } else {
      confirmLogoutWidget.hide();
    }
  }, [isLogoutOpen]);

  useEffect(() => {
    if (isExternalLinkOpen) {
      confirmOpenExternalLinkWidget.show();
    } else {
      confirmOpenExternalLinkWidget.hide();
    }
  }, [isExternalLinkOpen]);

  return null;
};

export default ConfirmPopupControl;
