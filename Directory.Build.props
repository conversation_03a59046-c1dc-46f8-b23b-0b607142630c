<?xml version="1.0" encoding="utf-8"?>
<Project>
  <PropertyGroup>
    <Nullable>enable</Nullable>
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <TargetFramework>net6.0</TargetFramework>
    <LangVersion>10.0</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="SonarAnalyzer.CSharp" Version="8.32.0.39516">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Serilog.AspNetCore" Version="4.1.0" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="3.0.1" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="3.1.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="3.1.1" />
    <PackageReference Include="Serilog.Sinks.SyslogMessages" Version="1.0.5" />

    <PackageReference Include="AutoMapper" Version="12.0.0" />
    <PackageReference Include="HotChocolate.Caching" Version="13.9.6" />
    <PackageReference Include="HotChocolate.Stitching" Version="13.9.6" />
    <PackageReference Include="HotChocolate.Data" Version="13.9.6" />
    <PackageReference Include="HotChocolate.AspNetCore" Version="13.9.6" />
    <PackageReference Include="HotChocolate.Data.EntityFramework" Version="13.9.6" />
    <PackageReference Include="HotChocolate.Diagnostics" Version="13.9.6" />

  </ItemGroup>

</Project>
