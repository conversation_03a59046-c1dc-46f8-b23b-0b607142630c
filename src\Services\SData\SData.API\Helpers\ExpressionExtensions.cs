﻿using System;
using System.Linq;
using System.Linq.Expressions;

namespace Euroland.FlipIT.SData.API.Helpers
{
    public static class ExpressionExtensions
    {
        public static Expression<Func<T, bool>> And<T>(this Expression<Func<T, bool>> left, Expression<Func<T, bool>> right)
        {
            if (left == null) return right;
            var and = Expression.AndAlso(left.Body, right.Body);
            return Expression.Lambda<Func<T, bool>>(and, left.Parameters.Single());
        }

        public static Expression<Func<T, bool>> Or<T>(this Expression<Func<T, bool>> left, Expression<Func<T, bool>> right)
        {
            //if (left == null) return right;
            //var and = Expression.OrElse(left.Body, right.Body);
            //return Expression.Lambda<Func<T, bool>>(and, left.Parameters.Single());

            var p = Expression.Parameter(typeof(T));
            return (Expression<Func<T, bool>>)Expression.Lambda(
                Expression.Or(
                    Expression.Invoke(left, p),
                    Expression.Invoke(right, p)
                )
            , p
            );
        }
    }
}
