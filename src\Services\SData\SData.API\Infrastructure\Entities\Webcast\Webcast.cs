using System;
using System.Collections.Generic;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities.Webcast;

public class Webcast : AuditEntity
{
    public long Id { get; set; }
    public int WebcastTypeId { get; set; }
    public virtual WebcastType WebcastType { get; set; } = null!;
    public int WebcastHostId { get; set; }
    public virtual WebcastHost WebcastHost { get; set; } = null!;
    public int WebcastSourceId { get; set; } = WebcastSource.Manual;
    public virtual WebcastSource WebcastSource { get; set; } = null!;
    public virtual ICollection<WebcastUrl> WebcastUrls { get; set; } = new List<WebcastUrl>();
    public virtual ICollection<WebcastTranslation> WebcastTranslations { get; set; } = new List<WebcastTranslation>();
    public string CompanyCode { get; set; } = null!;
    public string Title { get; set; } = null!;
    public DateTime PublishDate { get; set; }
    public string FileType { get; set; } = null!;
    public string? ThumbnailType { get; set; }
    public string? TranscriptType { get; set; }
}
