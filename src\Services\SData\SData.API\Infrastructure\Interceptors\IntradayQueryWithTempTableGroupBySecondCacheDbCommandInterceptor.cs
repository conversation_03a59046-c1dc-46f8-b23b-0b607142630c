using System;
using System.Data.Common;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities;

/// <summary>
/// Intercepts database commands to manipulate the command text for intraday queries
/// by inserting temporary table creation and data manipulation logic.
/// </summary>
internal class IntradayQueryWithTempTableGroupBySecondCacheDbCommandInterceptor : DbCommandInterceptor
{
  /// <summary>
  /// Name of the temporary table for intraday date ranges.
  /// </summary>
  public const string IntradayDateRangesTable = "@IntradayDateRanges";

  public const string TOKEN = "<Intraday-By-Second>";
  const string TAG_TOKEN = $"-- {TOKEN}";
  public const string InterceptorUseRealtimeTableParamName = "@IntradayGroupBySecond_IsRealtime";

  /// <summary>
  /// Intercepts the synchronous execution of a command that returns a <see cref="DbDataReader"/>.
  /// </summary>
  /// <param name="command">The command being executed.</param>
  /// <param name="eventData">The event data associated with the command.</param>
  /// <param name="result">The result of the command execution.</param>
  /// <returns>The result of the command execution, potentially modified.</returns>
  public override InterceptionResult<DbDataReader> ReaderExecuting(
    DbCommand command,
    CommandEventData eventData,
    InterceptionResult<DbDataReader> result)
  {
    ManipulateCommand(eventData.Context!, command);
    return base.ReaderExecuting(command, eventData, result);
  }

  /// <summary>
  /// Manipulates the command text by inserting temporary table creation and data manipulation logic.
  /// </summary>
  /// <param name="command">The command to manipulate.</param>
  public override ValueTask<InterceptionResult<DbDataReader>> ReaderExecutingAsync(
    DbCommand command,
    CommandEventData eventData,
    InterceptionResult<DbDataReader> result,
    CancellationToken cancellationToken = default)
  {
    ManipulateCommand(eventData.Context!, command);
    return base.ReaderExecutingAsync(command, eventData, result, cancellationToken);
  }

  /// <summary>
  /// Manipulates the command text by inserting temporary table creation and data manipulation logic.
  /// </summary>
  /// <param name="command">The command to manipulate.</param>
  private static void ManipulateCommand(DbContext dbContext, DbCommand command)
  {
    var originalCmdText = command.CommandText;

    if (!HasToken(originalCmdText))
    {
      return;
    }

    bool isRealtime = false;
    DbParameter interceptorParamToRemove = null;

    foreach (DbParameter param in command.Parameters)
    {
      if (param.ParameterName == InterceptorUseRealtimeTableParamName)
      {
        interceptorParamToRemove = param;
        if (param.Value != null && param.Value != DBNull.Value)
        {
          isRealtime = Convert.ToBoolean(param.Value);
        }

        break;
      }
    }

    // It's better to get configured table name from Entity instead
    // (dbContext as SharkDbContextBase).Instrument.EntityType.GetSchemaQualifiedTableName();
    var intradayTableName = isRealtime ? "rt_daily_history" : "daily_history";
    var tempTableQuery = CreateTempTableQuery(intradayTableName);

    command.CommandText = originalCmdText.Replace(TAG_TOKEN, tempTableQuery);

    if (interceptorParamToRemove != null)
    {
      command.Parameters.Remove(interceptorParamToRemove);
    }
  }

  private static bool HasToken(string commandText)
  {
    return commandText.IndexOf(TAG_TOKEN, StringComparison.Ordinal) != -1;
  }

  /// <summary>
  /// Creates the query for temporary table creation and data manipulation.
  /// </summary>
  /// <returns>The query string.</returns>
  private static string CreateTempTableQuery(
    string dailyHistoryTbl)
  {
    return @$"
      -- Pre-calculate market open time
      DECLARE @MarketOpenDateTime datetime = CONVERT(datetime, CONVERT(char(10), GETDATE(), 120) + ' ' + @marketOpenTimeLocal, 120);

      -- Create temp table for date calculations to avoid repeated conversions
      DECLARE {IntradayDateRangesTable} TABLE (
          InstrumentId INT,
          GroupedDate DATETIME,
          MinDate DATETIME,
          MaxDate DATETIME,
          [High] MONEY,
          [Low] MONEY,
          Volume BIGINT
      );
      -- Populate date ranges first
      INSERT INTO {IntradayDateRangesTable}
      SELECT
          InstrumentId,
          DATEADD(SECOND,
              ((DATEDIFF(SECOND, @MarketOpenDateTime,
                  DATEADD(SECOND, @minutesDifference, dh.hDate)) / @groupingTimeInterval)
              * @groupingTimeInterval),
              @MarketOpenDateTime) AS GroupedDate,
          MIN(dh.hDate) AS MinDate,
          MAX(dh.hDate) AS MaxDate,
          MAX(dh.hClose) AS [High],
          MIN(dh.hClose) AS [Low],
          SUM(dh.hSize) AS Volume
      FROM [{dailyHistoryTbl}] dh WITH (NOLOCK)
      WHERE dh.InstrumentID = @instrumentId
          AND CAST(dh.hDate AS float) - FLOOR(CAST(dh.hDate AS float)) >= @marketOpenTimeInputTimezone
      GROUP BY
          InstrumentId,
          DATEADD(
            SECOND,
            ((DATEDIFF(SECOND, @MarketOpenDateTime,
                DATEADD(SECOND, @minutesDifference, dh.hDate)) / @groupingTimeInterval)
            * @groupingTimeInterval),
            @MarketOpenDateTime
          );
    ";
  }
}
