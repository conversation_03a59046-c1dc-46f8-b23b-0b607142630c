using System;
using System.Linq;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Infrastructure.Entities.Timescale;
using HotChocolate;
using HotChocolate.Resolvers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Npgsql;
using NpgsqlTypes;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;

internal partial class InstrumentResolvers
{
  public IQueryable<InstrumentHistoryDto> GetIntradayGroupBySecondTimescale(
    IResolverContext resolverContext,
    [Parent] InstrumentDto instrument,
    TimescaleDbContext timescaleDbContext,
    int timeIntervalGrouping
  )
  {
    var hasAdjustedClosePrice = resolverContext.GetAdjClose();

    if (timeIntervalGrouping < 1)
    {
      throw new ArgumentOutOfRangeException(nameof(timeIntervalGrouping),
        "The time interval must be greater than or equal to 1.");
    }

    var dataByPeriod =
      GetDailyHistoryGroupBySecondQueryTimescale(timescaleDbContext, instrument.Id, timeIntervalGrouping,
          hasAdjustedClosePrice)
        .TagWith("<intraday_log>");

    if (!resolverContext.TryGetExchangeCurrency(out var exchangeCurrency) ||
        string.IsNullOrEmpty(instrument.CurrencyCode))
    {
      return dataByPeriod.OrderByArgumentOrDefault(
        resolverContext,
        q => q.OrderByDescending(p => p.DateTime)
      ).WithAutoMapper().ToProjection<InstrumentHistoryDto>();
    }

    var rateView =
      timescaleDbContext.InstrumentCurrencyPairQueryable($"{instrument.CurrencyCode}{exchangeCurrency.ToUpper()}");

    var query = (
      from price in dataByPeriod
      from rate in rateView.Where(r => r.Date.Date == price.DateTime.Date).DefaultIfEmpty()
      select new { price, rate }
    ).Select(grouping => new InstrumentHistory
    {
      InstrumentId = grouping.price.InstrumentId,
      DateTime = grouping.price.DateTime,
      Close = grouping.price.Close * (grouping.rate != null ? grouping.rate.Rate : 1m),
      High = grouping.price.High * (grouping.rate != null ? grouping.rate.Rate : 1m),
      Open = grouping.price.Open * (grouping.rate != null ? grouping.rate.Rate : 1m),
      Low = grouping.price.Low * (grouping.rate != null ? grouping.rate.Rate : 1m),
      Volume = grouping.price.Volume,
    });

    return query.OrderByArgumentOrDefault(
        resolverContext,
        q => q.OrderByDescending(p => p.DateTime)
      )
      .WithAutoMapper().ToProjection<InstrumentHistoryDto>();
  }

  private IQueryable<InstrumentHistoryTimescale> GetDailyHistoryGroupBySecondQueryTimescale(
    TimescaleDbContext timescaleDbContext,
    int instrumentId,
    int groupingTimeIntervalInSecond, bool hasAdjustedClosePrice)
  {
    var query = timescaleDbContext.InstrumentHistoryTimescales
      .FromSqlRaw($@"
               SELECT
                  rdh_grouped.instrument_id,
                  rdh_grouped.second_bucket_grouped,
                  rdh_grouped.open_price * COALESCE(ca.cumulative_adjustment_factor, 1.0) AS open,
                  rdh_grouped.high_price * COALESCE(ca.cumulative_adjustment_factor, 1.0) AS high,
                  rdh_grouped.low_price * COALESCE(ca.cumulative_adjustment_factor, 1.0) AS low,
                  rdh_grouped.close_price * COALESCE(ca.cumulative_adjustment_factor, 1.0) AS close,
                  rdh_grouped.volume_traded AS volume
              FROM (
                  SELECT
                      instrument_id,
                      time_bucket(make_interval(secs => @groupingTimeIntervalInSecond), second_bucket) AS second_bucket_grouped,
                      FIRST(close, second_bucket) AS open_price,
                      MAX(high) AS high_price,
                      MIN(low) AS low_price,
                      LAST(close, second_bucket) AS close_price,
                      SUM(volume) AS volume_traded
                  FROM ohlcv_group_by_second
                  WHERE instrument_id = @instrumentId
                  GROUP BY instrument_id, second_bucket_grouped
              ) AS rdh_grouped
              LEFT JOIN LATERAL (
                  SELECT EXP(SUM(LOG(ca_inner.cumulative_adjustment_factor))) AS cumulative_adjustment_factor
                  FROM cumulative_adjustments AS ca_inner
                  WHERE ca_inner.instrument_id = rdh_grouped.instrument_id
                    AND @adjClose = true AND rdh_grouped.second_bucket_grouped < ca_inner.action_date
              ) AS ca ON TRUE
      ",
        new NpgsqlParameter("adjClose", NpgsqlDbType.Boolean) { Value = hasAdjustedClosePrice },
        new NpgsqlParameter("instrumentId", NpgsqlDbType.Integer) { Value = instrumentId },
        new NpgsqlParameter("groupingTimeIntervalInSecond", NpgsqlDbType.Integer)
          { Value = groupingTimeIntervalInSecond });
    return query;
  }
}
