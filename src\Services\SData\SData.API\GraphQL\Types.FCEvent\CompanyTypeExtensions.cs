using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.Middleware;
using Euroland.FlipIT.SData.API.GraphQL.Types.FinancialEvent;
using HotChocolate.Types;
using HotChocolate.Types.Pagination;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.FCEvent;

/// <summary>
/// Extends financial event fields to type <see cref="Company.CompanyType"/>
/// </summary>
public class CompanyTypeExtensions : ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(Company.CompanyType.Name);

    descriptor.Field("fcEventTypes")
      .Description("Get all FCEvent types for the company")
      .Type<ListType<ObjectType<FCEventTypeDto>>>()
      .UsePaging(
        connectionName: "FCEventTypes",
        options: new PagingOptions
        {
          IncludeTotalCount = true,
          MaxPageSize = int.MaxValue,
          DefaultPageSize = 200
        }
      )
      .UseProjection()
      .UseFiltering()
      .UseSorting()
      .AddCultureNameArgument()
      .ResolveWith<FCEventResolvers>(resolvers => resolvers.GetAllFCEventTypes(default!, default!, default!));

    descriptor.Field("fcEvents")
      .Description("Get all FCEvents for the company")
      .Type<ListType<ObjectType<FCalendarDto>>>()
      .UsePaging(
        connectionName: "FCEvents",
        options: new PagingOptions
        {
          IncludeTotalCount = true,
          MaxPageSize = int.MaxValue,
          DefaultPageSize = 200
        }
      )
      .UseProjection()
      .UseFiltering()
      .UseSorting(typeof(FCEventSortType))
      .AddCultureNameArgument()
      .ResolveWith<FCEventResolvers>(resolvers => resolvers.GetFCEventsByCompany(default!, default!, default!));

    descriptor.Field("fcEventsByTypes")
      .Description("Get FCEvents by FCEvent types name in English")
      .Type<ListType<ObjectType<FCalendarDto>>>()
      .Argument(
        "fcEventTypeNames",
        cfg => cfg.Type<NonNullType<ListType<StringType>>>()
          .Description("List of FCEvent types name in English"))
      .UsePaging(
        connectionName: "FCEventsByTypes",
        options: new PagingOptions
        {
          IncludeTotalCount = true,
          MaxPageSize = int.MaxValue,
          DefaultPageSize = 200
        })
      .UseProjection()
      .UseFiltering()
      .UseSorting(typeof(FCEventSortType))
      .AddCultureNameArgument()
      .ResolveWith<FCEventResolvers>(
        resolvers => resolvers.GetFCEventByTypes(default!, default!, default!, default!)
      );
  }
}
