using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using HotChocolate;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities;

[GraphQLName("MessageType_Deprecated")]
public class MessageType
{
  [Key]
  [Column("mtID")]
  public int? Id { get; set; }

  [Column("mtName")]
  [Required]
  public string Name { get; set; }

  [Column("mtShortName")]
  public string? ShortName { get; set; }

  [Column("mtSourceID")]
  [Required]
  public int SourceId { get; set; }

  [Column("mtMessageGroup")]
  public int? MessageGroupId { get; set; }

  [Column("mtMessageGroupType")]
  public int? MessageGroupTypeId { get; set; }
}
