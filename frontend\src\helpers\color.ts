import {memoize} from "es-toolkit";

/**
 * For colors which fall within the sRGB space, the browser can
 * be used to convert the color string into a rgb /rgba string.
 *
 * For other colors, it will be returned as specified (i.e. for
 * newer formats like display-p3)
 *
 * See: https://www.w3.org/TR/css-color-4/#serializing-sRGB-values
 */
function getRgbStringViaBrowser(color: string): string {
	const element = document.createElement('div');
	element.style.display = 'none';
	// We append to the body as it is the most reliable way to get a color reading
	// appending to the chart container or similar element can result in the following
	// getComputedStyle returning empty strings on each check.
	document.body.appendChild(element);
	element.style.color = color;
	const computed = window.getComputedStyle(element).color;
	document.body.removeChild(element);
	return computed;
}

export const parseColor = memoize((color: string) => {
  const computed = getRgbStringViaBrowser(color);

  const match = computed.match(
    /^rgba?\s*\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d+))?\)$/
  );

  if (!match) {
    throw new Error(`Failed to parse color: ${color}`);
  }

  return [
    parseInt(match[1], 10) as number,
    parseInt(match[2], 10) as number,
    parseInt(match[3], 10) as number,
    (match[4] ? parseFloat(match[4]) : 1) as number,
  ];
})

export class Color {
  /**
  * We fallback to RGBA here since supporting alpha transformations
  * on wider color gamuts would currently be a lot of extra code
  * for very little benefit due to actual usage.
  */
  static applyAlpha(color: string, alpha: number): string {
   // special case optimization
   if (color === 'transparent') {
     return color;
   }
   if(alpha === 1) return color;

   const originRgba = parseColor(color);
   const originAlpha = originRgba[3];
   return `rgba(${originRgba[0]}, ${originRgba[1]}, ${originRgba[2]}, ${
     alpha * originAlpha
   })`;
 }

 static parseColor(color: string) {
  return parseColor(color)
 }

 
}

export class HSLColor {
  constructor(private hsl: number[]) {}

  get() {
    return Array.from(this.hsl);
  }
  static fromString(color: string) {
    const rgba = parseColor(color);
    const r = rgba[0] / 255;
    const g = rgba[1] / 255;
    const b = rgba[2] / 255;
    const a = rgba[3];

    const max = Math.max(r, g, b), min = Math.min(r, g, b);
    const l = (max + min) / 2;
    let h = 0, s;

    if (max == min) {
      h = s = 0; // achromatic
    } else {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }

      h /= 6;
    }

    return [h, s, l, a];
  }

  static parse(color: string) {
    const hslValues = HSLColor.fromString(color);
    // Ensure all values are numbers (not undefined)
    return new HSLColor(hslValues as number[]);
  }

  toRgb() {
    const [h, s, l, a] = this.hsl;
    let r, g, b;

    if (s === 0) {
      r = g = b = l; // achromatic
    } else {
      const hue2rgb = (p: number, q: number, t: number) => {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1 / 6) return p + (q - p) * 6 * t;
        if (t < 1 / 2) return q;
        if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
        return p;
      };

      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      r = hue2rgb(p, q, h + 1 / 3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1 / 3);
    }

    return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255), a];
  }

  adjust(hsl: {h?: number, s?: number, l?: number, a?: number} = {}) {
    let {h = 0, s = 0, l = 0, a = 0} = hsl;
    h += this.hsl[0];
    s += this.hsl[1];
    l += this.hsl[2];
    a += this.hsl[3];

    if(h < 0) h += 360;
    if(h > 360) h -= 360;

    if(s < 0) s = 0;
    if(s > 1) s = 1;

    if(l < 0) l = 0;
    if(l > 1) l = 1;

    return new HSLColor([h, s, l, a]);
  }

  rotateHue(degrees: number) {
    const percent = degrees / 360;
    let h = this.hsl[0] + percent;
    if(h < 0) h += 1;
    if(h > 1) h -= 1;
    return new HSLColor([h, this.hsl[1], this.hsl[2], this.hsl[3]]);
  }

  lighter(amount: number) {
    let l = this.hsl[2] + amount;
    if(l < 0) l = 0;
    if(l > 1) l = 1;
    return new HSLColor([this.hsl[0], this.hsl[1], l, this.hsl[3]]);
  }

  saturate(amount: number) {
    let s = this.hsl[1] + amount;
    if(s < 0) s = 0;
    if(s > 1) s = 1;
    return new HSLColor([this.hsl[0], s, this.hsl[2], this.hsl[3]]);
  }

  toString() {
    const roundNumber = (number: number) => Math.round(number * 1000) / 1000;
    return `hsl(${roundNumber(this.hsl[0] * 360)}deg, ${roundNumber(this.hsl[1] * 100)}%, ${roundNumber(this.hsl[2] * 100)}%)`;
  }
}

export function lightenColor(color: string, percent: number) {
  const num = parseInt(color.slice(1), 16);
  const amt = Math.round(2.55 * percent);
  const r = (num >> 16) + amt;
  const g = (num >> 8 & 0x00FF) + amt;
  const b = (num & 0x0000FF) + amt;
  return `#${(0x1000000 + (r < 255 ? r < 1 ? 0 : r : 255) * 0x10000 + (g < 255 ? g < 1 ? 0 : g : 255) * 0x100 + (b < 255 ? b < 1 ? 0 : b : 255)).toString(16).slice(1).toUpperCase()}`;
}

export function darkenColor(color: string, percent: number) {
  const num = parseInt(color.slice(1), 16);
  const amt = Math.round(2.55 * percent);
  const r = (num >> 16) - amt;
  const g = (num >> 8 & 0x00FF) - amt;
  const b = (num & 0x0000FF) - amt;
  return `#${(0x1000000 + (r > 0 ? r : 0) * 0x10000 + (g > 0 ? g : 0) * 0x100 + (b > 0 ? b : 0)).toString(16).slice(1).toUpperCase()}`;
}
