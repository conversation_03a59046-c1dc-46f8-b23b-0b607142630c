import { FC, useCallback } from "react";
import { XMark } from "../Common";
import { zoidComponentCreator } from "@/zoid-components";
import { XProps } from "@/zoid";
import { appSettings } from "@/config/appSettings";
import { i18n } from "@euroland/libs";
import { useSetupTheme } from "@/hooks/useSetupTheme";
import {AISearchSettings} from "@/services/getAISearchSettings";
interface PopUpOpenExternalLinkConfirmProps {
  content: string;
  onConfirm: () => void;
  onCancel: () => void;
  confirmText?: string;
  cancelText?: string;
  aiSearchSettings: AISearchSettings;
}

const PopUpOpenExternalLinkConfirm: FC<XProps<PopUpOpenExternalLinkConfirmProps>> = ({
  content,
  onConfirm,
  onCancel,
  confirmText = i18n.translate('ok'),
  cancelText = i18n.translate('cancel'),
  aiSearchSettings,
}) => {
  useSetupTheme(aiSearchSettings);
  const handleConfirm = useCallback(() => {
    onConfirm();
  }, [onConfirm]);

  const handleCancel = useCallback(() => {
    onCancel();
  }, [onCancel]);

  return (
    <div className="flex flex-col p-5 pt-8 h-[100vh] overflow-y-hidden gap-4 bg-white  relative">
      <div
        className={`absolute w-[32px] h-[32px] top-3 ${
          appSettings.isRTL ? 'left-4' : 'right-4'
        } flex items-center justify-center hover:bg-gray-100 cursor-pointer rounded-full p-1 group`}
        onClick={handleCancel}
      >
        <XMark className="transition-transform duration-200 group-hover:rotate-90" />
      </div>
      <div className="h-full" dangerouslySetInnerHTML={{ __html: content }}>
      </div>
      <div className="flex justify-end gap-2">
        <button
          className="px-4 py-2 rounded-full border border-gray-300 text-sm hover:bg-gray-50 min-w-[80px]"
          onClick={handleCancel}
        >
          {cancelText}
        </button>
        <button
          className="px-4 py-2 rounded-full bg-primary text-white text-sm hover:bg-primary-dark min-w-[80px]"
          onClick={handleConfirm}
        >
          {confirmText}
        </button>
      </div>
    </div>
  );
};

export const ZoidPopUpOpenExternalLinkConfirm = zoidComponentCreator(PopUpOpenExternalLinkConfirm, {
  dimensions: {
    width: "500px",
    height: "200px",
  },
  autoResize: {
    height: false,
    element: "#root",
  },
  template: {
    name: "dynamic",
    styles: {
      top: "50%",
      left: "50%",
      transform: "translate(-50%, -50%)",
      "z-index": "200003",
      "border-radius": "4px",
      "box-shadow": "0 4px 12px rgba(0,0,0,0.15)",
    },
    backdrop: true,
    backdropBgColor: "transparent",
  },
  props: {
    content: {
      type: "string",
      required: true,
    },
    onConfirm: {
      type: "function",
      required: true,
    },
    onCancel: {
      type: "function",
      required: true,
    },
    confirmText: {
      type: "string",
      required: false,
    },
    cancelText: {
      type: "string",
      required: false,
    },
    aiSearchSettings: {
      type: "object",
      required: true,
    },
  },
}, 'bottom');

export default PopUpOpenExternalLinkConfirm;
