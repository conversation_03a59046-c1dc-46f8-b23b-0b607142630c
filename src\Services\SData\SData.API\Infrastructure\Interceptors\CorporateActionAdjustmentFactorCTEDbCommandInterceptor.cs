using System;
using System.Linq;
using System.Collections.Generic;
using System.Data.Common;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Data.SqlClient;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities;

public class CorporateActionAdjustmentFactorCTEDbCommandInterceptor : DbCommandInterceptor
{
  /// <summary>
  /// Name of the temporary table for cumulative adjustments.
  /// </summary>
  public const string CumulativeAdjustmentsTable = "@CumulativeAdjustments";
  public const string TOKEN = "<CumulativeAdjustments>";
  const string TAG_TOKEN = $"-- {TOKEN}";
  const string SINGLE_INSTRUMENT_ID_PARAMETER_NAME = "instrumentId";
  public const string INSTRUMENT_IDS_PARAMETER_NAME = "instrumentIdArr";
  public const string ADJ_CLOSED_PARAMETER_NAME = "adjClose";
  public override InterceptionResult<DbDataReader> ReaderExecuting(DbCommand command, CommandEventData eventData, InterceptionResult<DbDataReader> result)
  {
    ManipulateCommand(command);
    return base.ReaderExecuting(command, eventData, result);
  }
  public override ValueTask<InterceptionResult<DbDataReader>> ReaderExecutingAsync(DbCommand command, CommandEventData eventData, InterceptionResult<DbDataReader> result, CancellationToken cancellationToken = default)
  {
    ManipulateCommand(command);
    return base.ReaderExecutingAsync(command, eventData, result, cancellationToken);
  }

  private static void ManipulateCommand(DbCommand command)
  {
    var originalCmdText = command.CommandText;

    if (!HasToken(originalCmdText))
    {
      return;
    }

    bool hasAdjClosed = false;
    if(command.Parameters.Contains(ADJ_CLOSED_PARAMETER_NAME)
      && command.Parameters[ADJ_CLOSED_PARAMETER_NAME].Value != DBNull.Value
      && (bool)command.Parameters[ADJ_CLOSED_PARAMETER_NAME].Value) {
        hasAdjClosed = true;
    }

    // COUNT(*) does not need CorporateActionAdjustment involves.
    if(originalCmdText.IndexOf("COUNT(*)", StringComparison.InvariantCultureIgnoreCase) > 0)
    {
      hasAdjClosed = false;
    }

    var cteQuery = CreateCumulativeAdjustmentCTE(
      hasAdjClosed,
      "Instrument",
      "InstrumentHistory",
      "CurrencyRate",
      "CurrencyRateHistory"
    );

    if(hasAdjClosed) {
      AddParameterIfMissing(command);
    }

    command.CommandText = originalCmdText.Replace(TAG_TOKEN, cteQuery);
  }

  private static void AddParameterIfMissing(DbCommand command)
  {
    // if (!command.Parameters.Contains(ADJ_CLOSED_PARAMETER_NAME))
    // {
    //   var adjClosedParameter = command.CreateParameter();
    //   adjClosedParameter.ParameterName = ADJ_CLOSED_PARAMETER_NAME;
    //   adjClosedParameter.DbType = System.Data.DbType.Boolean;
    //   adjClosedParameter.Value = false;
    //   command.Parameters.Add(adjClosedParameter);
    // }

    if (!command.Parameters.Contains(INSTRUMENT_IDS_PARAMETER_NAME))
    {
      if (command.Parameters.Contains(SINGLE_INSTRUMENT_ID_PARAMETER_NAME))
      {
        var insIdParameter = command.Parameters[SINGLE_INSTRUMENT_ID_PARAMETER_NAME];
        if (insIdParameter.Value != null)
        {
          var ids = new List<int> { (int)(insIdParameter.Value) };
          var instrumentIdsParameter = command.CreateParameter();
          instrumentIdsParameter.ParameterName = INSTRUMENT_IDS_PARAMETER_NAME;
          instrumentIdsParameter.DbType = System.Data.DbType.String;
          instrumentIdsParameter.Value = JsonSerializer.Serialize(ids);
          command.Parameters.Add(instrumentIdsParameter);
          return;
        }
      }

      throw new InvalidOperationException($"EFCore Inteceptor CorporateActionAdjustmentFactorCTEDbCommandInterceptor has been invoked but missing parameter @{INSTRUMENT_IDS_PARAMETER_NAME} provided.");
    }
    // Lets SQL throws SqlException caused of missing parameter @instrumentIdArr
  }

  private static bool HasToken(string commandText)
  {
    return commandText.IndexOf(TAG_TOKEN, StringComparison.Ordinal) != -1;
  }

  private static string CreateCumulativeAdjustmentCTE(
    bool hasAdjClosed,
    string instrumentTbl,
    string instrumentHistoryTbl,
    string currencyRateTbl,
    string currencyRateHistoryTbl
  )
  {
    var tempTable = @$"
      -- Create temp table for corporate actions
      DECLARE {CumulativeAdjustmentsTable} TABLE (
          ActionDate datetime,
          InstrumentId int,
          CumulativeAdjustmentFactor float
      );
    ";

    if(!hasAdjClosed) {
      return tempTable;
    }

    return @$"
      {tempTable}
      ;WITH CorporateActions AS (
      SELECT
          exDate AS ActionDate
          ,'Dividend' AS ActionType
          ,GrossDivAdj AS [Value]
          ,TId AS [InstrumentId]
          ,Currency
          FROM Dividends_ExchDataFeed d
      WHERE @adjClose = 1 AND exDate IS NOT NULL
          AND d.currency = (
              SELECT TOP(1)
                  COALESCE(de.DisplayCurrency, d1.[currency])
              FROM [Dividends_ExchDataFeed] d1
              LEFT JOIN [Dividends_ExchDataFeed_DisplayCurrency] de ON de.InstrumentID = d1.TId
              WHERE d1.TId = d.TId
              ORDER BY de.PriorityOrder
              )
          --UNION ALL
          --  SELECT
          --SplitDate AS ActionDate
          --,'Split' AS ActionType
          --,SplitRatio  AS [Value]
          --,TId AS [InstrumentId]
          --  FROM StockSplits
      )
      ,AdjustmentFactors AS (
          SELECT
              ca.ActionDate
          ,ca.InstrumentId
          ,divRate.[Rate]
          ,ip.[CurrencyCode]
          ,ca.[Currency]
          ,CASE
          -- Dividend adjustment factor calculation
          -- e.g. a stock closes at $40.00 on Monday. On Tuesday, it begins tradings
          -- ex-dividend based on a $2.00 dividend. To calculate adjustment factor,
          -- we substract the $2.00 dividend from Monday's closing price ($40.00-$2.00=$38.00).
          -- Then, divide $38.00 by $40.00 to determine the dividend adjustment in percentage terms.
          -- The result is 0.95
          WHEN ca.ActionType = 'Dividend' THEN (sp.[Close]* COALESCE(divRate.[Rate], 1) - ca.[Value]) / NULLIF(sp.[Close]* COALESCE(divRate.[Rate], 1), 0)
          WHEN ca.ActionType = 'Split' THEN ca.[Value]
          END AS AdjustmentFactor
          FROM CorporateActions ca
          INNER JOIN [{instrumentHistoryTbl}] sp ON sp.InstrumentId = ca.InstrumentId
        INNER JOIN [{instrumentTbl}] ip ON ip.Id = ca.InstrumentId
        OUTER APPLY (
            SELECT TOP(1)
            ch.[chRate] AS [Rate]
            FROM [{currencyRateHistoryTbl}] ch
            INNER JOIN [{currencyRateTbl}] cr ON cr.cId = ch.cId
            WHERE ip.[CurrencyCode] IS NOT NULL
          AND ca.[Currency] != ip.[CurrencyCode]
          AND cr.[cCurr] = ca.[Currency] + ip.[CurrencyCode]
              AND CAST(ch.[chDate] AS Date) = CAST(ca.[ActionDate] AS Date)
          ) AS divRate
        WHERE sp.[Date] = DATEADD(day, -1, ca.ActionDate)
      )

      INSERT INTO {CumulativeAdjustmentsTable}
      SELECT
        ActionDate,
        InstrumentId,
        -- with a,b > 0 we have:
          --	log(a*b) = log(a)+log(b)
          -- 	exp(log(a)) = a
          --	=> exp(log(a)+log(b)) = a*b
        EXP(SUM(LOG(AdjustmentFactor))) AS CumulativeAdjustmentFactor
      FROM AdjustmentFactors
      WHERE @adjClose = 1
        AND InstrumentId IN (SELECT [value] FROM OPENJSON(@{INSTRUMENT_IDS_PARAMETER_NAME}))
        AND AdjustmentFactor > 0
      GROUP BY InstrumentId, ActionDate
      OPTION (HASH GROUP);
    ";
  }
}
