using HotChocolate.AspNetCore;
using HotChocolate.Execution;

namespace Euroland.FlipIT.ApiGateway;

/// <summary>
/// An interceptor class that extends the DefaultHttpRequestInterceptor to handle
/// HTTP request creation for introspection queries in the API Gateway.
/// </summary>
/// <remarks>
/// This class overrides the OnCreateAsync method to provide custom logic during
/// the creation of HTTP requests. Currently, it delegates the call to the base
/// implementation.
/// </remarks>
/// <param name="context">The HTTP context of the request.</param>
/// <param name="requestExecutor">The executor responsible for processing the request.</param>
/// <param name="requestBuilder">The builder for constructing the query request.</param>
/// <param name="cancellationToken">Token to monitor for cancellation requests.</param>
/// <returns>A ValueTask representing the asynchronous operation.</returns>
public class InstrospectionInterceptor: DefaultHttpRequestInterceptor
{
  public override ValueTask OnCreateAsync(HttpContext context, IRequestExecutor requestExecutor, IQueryRequestBuilder requestBuilder, CancellationToken cancellationToken)
  {
    if(context.User?.Identity?.IsAuthenticated == true) {
        requestBuilder.AllowIntrospection();
    }

    return base.OnCreateAsync(context, requestExecutor, requestBuilder, cancellationToken);
  }
}
