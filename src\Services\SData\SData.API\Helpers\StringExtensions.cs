﻿using System;
using System.Text;

namespace Euroland.FlipIT.SData.API.Helpers
{
  public static class StringExtensions
  {
    public static string LowercaseFirstLetter(this string input)
    {
      if (!string.IsNullOrEmpty(input) && char.IsUpper(input[0]))
      {
        return char.ToLower(input[0]) + input.AsSpan(1).ToString();
      }

      return input;
    }

    public static uint ToHash(this string input) {
      return BitConverter.ToUInt32(
        System.Security.Cryptography.SHA256.Create().ComputeHash(Encoding.UTF8.GetBytes(input)),
        0
      ) % 1000000;
    }
  }
}
