using System;

namespace Euroland.FlipIT.SData.API.Dto;

public class PerformanceShareDto: IDtoObject
{
  public int InstrumentId { get; set; }
  public PerformancePeriodDto Period { get; set; }
  public DateTime? FromDate { get; set; }
  public decimal? ChangePercentage { get; set; }
  public decimal? High { get; set; }
  public DateTime? HighDate { get; set; }
  public decimal? Low { get; set; }
  public DateTime? LowDate { get; set; }
  public decimal? ClosePrice { get; set; }
  public decimal? CloseRate { get; set; }
}
