import { WatchlistModal } from "./addInstrument/WatchlistModal";
import { useWatchlistQueries } from "../services/watchlistQueries";
import { toast } from "react-toastify";
import { useAuth } from "./watchlist/hooks/useAuth";

function RootIframeWidget() {
  const auth = useAuth();
  const { loadingFetchWatchlist, watchlistsQuery, addInstrumentMutation, createWatchlistMutation } =
    useWatchlistQueries();
  const xpropsInstrumentId = window.xprops?.instrumentId;
  const instrumentId = xpropsInstrumentId || "0";

  const handleAddToWatchlist = async (watchlistId: string) => {
    try {
      await addInstrumentMutation.mutate({
        watchlistId,
        instrumentId: parseInt(instrumentId),
      });

      toast.success("Instrument added to watchlist");
      watchlistsQuery.refetch();
    } catch (error) {
      toast.error("Failed to add instrument");
      console.error("Error adding instrument to watchlist:", error);
    }
  };

  const handleCreateWatchlist = async (name: string) => {
    try {
      await createWatchlistMutation.mutate(name);
      toast.success("Watchlist created successfully");
      watchlistsQuery.refetch();
    } catch (error) {
      toast.error("Failed to create watchlist");
      console.error("Error creating new watchlist:", error);
    }
  };

  if (!auth.isAuthenticated) {
    return null;
  }

  return (
    <div className="root-iframe-widget">
      <WatchlistModal
        watchlists={watchlistsQuery.data?.data || []}
        instrumentId={instrumentId}
        onAddToWatchlist={handleAddToWatchlist}
        onCreateWatchlist={handleCreateWatchlist}
        isLoadingFetch={loadingFetchWatchlist}
        isLoadingAction={addInstrumentMutation.isLoading || createWatchlistMutation.isLoading}
      />
    </div>
  );
}

export default RootIframeWidget;