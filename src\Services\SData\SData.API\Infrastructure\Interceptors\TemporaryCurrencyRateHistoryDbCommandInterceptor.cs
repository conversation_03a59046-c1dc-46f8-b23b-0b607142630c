using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Infrastructure.Configurations;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore.Diagnostics;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities;

public class TemporaryCurrencyRateHistoryDbCommandInterceptor : DbCommandInterceptor
{
  public const string END_TAG_PLACEHOLDER = "//END_TAG//";
  static readonly Regex currencyPairReg = new Regex(@"\<(currencyPair|factor|fixedRate):([^><]*)\>", RegexOptions.IgnoreCase | RegexOptions.Compiled);
  public override InterceptionResult<DbDataReader> ReaderExecuting(DbCommand command, CommandEventData eventData, InterceptionResult<DbDataReader> result)
  {
    ManipulateCommand(command);
    return base.ReaderExecuting(command, eventData, result);
  }
  public override ValueTask<InterceptionResult<DbDataReader>> ReaderExecutingAsync(DbCommand command, CommandEventData eventData, InterceptionResult<DbDataReader> result, CancellationToken cancellationToken = default)
  {
    ManipulateCommand(command);
    return base.ReaderExecutingAsync(command, eventData, result, cancellationToken);
  }

  private static void ManipulateCommand(DbCommand command)
  {
    var originalCmdText = command.CommandText;
    var placeholderIndex = GetPlaceholderIndex(originalCmdText);

    if(placeholderIndex < 0) {
      return;
    }

    var matches = currencyPairReg.Matches(originalCmdText);

    if (matches.Count > 0)
    {
      var parameters = new List<SqlParameter>();

      for (int i = 0; i < matches.Count; i++)
      {
        switch(matches[i].Groups[1].Value)
        {
          case "currencyPair":
            if(matches[i].Groups[2].Value.Length == 6) {
              var currencyPairParam = new SqlParameter("currencyPair", SqlDbType.Char, 6)
              {
                Value = matches[i].Groups[2].Value
              };

              parameters.Add(currencyPairParam);
            }
          break;
        }
      }

      var hasCountClause = originalCmdText.IndexOf("COUNT(*)", StringComparison.InvariantCultureIgnoreCase) > 0;
      var hasCountParam = new SqlParameter("hasCount", SqlDbType.Bit)
      {
        Value = hasCountClause
      };
      parameters.Add(hasCountParam);

      command.Parameters.AddRange(parameters.ToArray());

      var newCommandText = originalCmdText.Insert(placeholderIndex, Environment.NewLine);
      newCommandText = newCommandText.Insert(placeholderIndex + Environment.NewLine.Length, CreateTempTable(hasCountClause));

      newCommandText += @$"
        {Environment.NewLine}
        IF OBJECT_ID('tempdb..{TemporaryCurrencyRateHistoryConfiguration.TemporaryCurrencyRateHistoryTableName}') IS NOT NULL
          DROP TABLE {TemporaryCurrencyRateHistoryConfiguration.TemporaryCurrencyRateHistoryTableName};
      ";

      command.CommandText = newCommandText;
    }
  }

  private static int GetPlaceholderIndex(string commandText) {
    var startIndex = commandText.IndexOf(
      $"-- {TemporaryCurrencyRateHistoryConfiguration.TemporaryCurrencyRateHistoryTableName}",
      StringComparison.Ordinal
    );

    if(startIndex != -1) {
      var newIndex = commandText.IndexOf(END_TAG_PLACEHOLDER, startIndex, StringComparison.Ordinal);
      if(newIndex != -1) {
        newIndex += END_TAG_PLACEHOLDER.Length;
        return newIndex;
      }
    }

    return -1;
  }

  private static string CreateTempTable(bool hasCountClause) {
    var tableName = Configurations.TemporaryCurrencyRateHistoryConfiguration.TemporaryCurrencyRateHistoryTableName;
    if(hasCountClause) {
      return @$"
        CREATE TABLE {tableName}
        (
          cId BIGINT,
          cRate NUMERIC(28,10),
          cDate DATETIME
        )
      ";
    }

    return @$"
      DECLARE @currencyRateId INT = NULL

      SET @currencyRateId = (SELECT TOP(1) cId FROM CurrencyRate WHERE cCurr = @currencyPair)

      IF OBJECT_ID('tempdb..{tableName}') IS NOT NULL DROP TABLE {tableName}

      SELECT
        c.cId,
        c.cRate,
        c.cDate
      INTO {tableName}
      FROM (
        SELECT TOP(1)
          0 AS cId,
          cRate,
          cDate
        FROM CurrencyRate
        WHERE cId = @currencyRateId
        UNION ALL
        SELECT
          ch.chID AS cId,
          ch.chRate AS cRate,
          ch.chDate AS cDate
        FROM CurrencyRateHistory ch
        WHERE ch.cId = @currencyRateId
      ) AS c
    ";
  }
}
