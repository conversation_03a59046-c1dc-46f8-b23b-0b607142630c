using System;
using Euroland.FlipIT.SData.API.Helpers;

namespace Euroland.FlipIT.SData.API.Dto;

public class DividendDto : IDtoObject
{
  // All kind of dates bellow from Database has smalldatetime data type
  // So no need to convert to UTC timezone, but change DateKind only
  private DateTime? _date;
  private DateTime? _recDate;
  private DateTime? _payDate;
  private DateTime? _fyeDate;
  private DateTime _lastRowChange;

  public string? Currency { get; set; }

  public string? ExchangeCurrency { get; set; }

  public DateTime? ExDate
  {
    get
    {
      return _date;
    }
    set
    {
      if(value != null) {
        _date = value.Value.ToUtcKindOnly();
      }
    }
  }
  public DateTime? RecDate
  {
    get
    {
      return _recDate;
    }
    set
    {
      if(value != null) {
        _recDate = value.Value.ToUtcKindOnly();
      }
    }
  }

  public DateTime? PayDate
  {
    get
    {
      return _payDate;
    }
    set
    {
      if(value != null) {
        _payDate = value.Value.ToUtcKindOnly();
      }
    }
  }
  public DateTime? FYEDate
  {
    get
    {
      return _fyeDate;
    }
    set
    {
      if(value != null) {
        _fyeDate = value.Value.ToUtcKindOnly();
      }
    }
  }
  public DateTime LastRowChange
  {
    get
    {
      return _lastRowChange;
    }
    set
    {
      _lastRowChange = value.CestToUtc();
    }
  }
  public string? Period { get; set; }
  public string? PayType { get; set; }
  public decimal? GrossDivAdj { get; set; }
  public decimal? NetDivAdj { get; set; }
  public decimal? GrossDividend { get; set; }
  public decimal? NetDividend { get; set; }
  public decimal? SplitNr { get; set; }
  public long? ShareCapital { get; set; }
  public long? DividendAmount { get; set; }
  public long? BasicEarningsPerShare { get; set; }
  public decimal? PayoutPer { get; set; }
  public bool Enabled { get; set; }
  public int InstrumentId { get; set; }
}
