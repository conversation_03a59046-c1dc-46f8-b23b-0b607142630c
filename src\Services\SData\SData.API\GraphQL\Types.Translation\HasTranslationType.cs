using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Types;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Translation;

public class HasTranslationType: InterfaceType<HasTranslation>
{
  protected override void Configure(IInterfaceTypeDescriptor<HasTranslation> descriptor)
  {
    descriptor.Description("Translation object which contains the translated string into various supported languages.");
    descriptor.Name("HasTranslation");
    descriptor.Field(f => f.TranslationId).Ignore();
    descriptor.Field("translation")
      .Description("")
      .AddCultureNameArgument()
      .Type<ObjectType<TranslationDto>>();
  }
}
