using Euroland.FlipIT.SData.API.Infrastructure.Entities.Timescale;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Euroland.FlipIT.SData.API.Infrastructure.Configurations.Timescale;

public class CurrencyRateHistoryTimescaleConfiguration : IEntityTypeConfiguration<CurrencyRateHistoryTimescale>
{
    public void Configure(EntityTypeBuilder<CurrencyRateHistoryTimescale> builder)
    {
        builder.ToTable("currency_rate_history");

        builder.HasKey(crh => crh.Id);

        builder.Property(crh => crh.Id)
            .HasColumnName("id")
            .IsRequired();

        builder.Property(crh => crh.CurrencyRateId)
            .HasColumnName("currency_rate_id")
            .IsRequired();

        builder.Property(crh => crh.Rate)
            .HasColumnName("rate")
            .HasColumnType("numeric(28,10)")
            .IsRequired();

        builder.Property(crh => crh.Date)
            .HasColumnName("date")
            .IsRequired();
    }
}
