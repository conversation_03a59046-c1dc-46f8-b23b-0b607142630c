using Euroland.FlipIT.SData.API.Dto.Webcast;
using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Webcast;

public class WebcastType: ObjectType<WebcastDto>
{
  public const string TypeName = "Webcast";
  protected override void Configure(IObjectTypeDescriptor<WebcastDto> descriptor)
  {
    descriptor.Name(TypeName);

    descriptor.Field(f => f.Id).IsProjected();
    descriptor.Field(f => f.WebcastTypeId).IsProjected();
    descriptor.Field(f => f.WebcastHostId).IsProjected();
    descriptor.Field(f => f.WebcastSourceId).IsProjected();
  }
}
