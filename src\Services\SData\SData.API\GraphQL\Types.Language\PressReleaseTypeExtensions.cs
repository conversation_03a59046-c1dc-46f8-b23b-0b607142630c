using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Language;

/// <summary>
/// Extends 'language' field to type <see cref="PressRelease.PressReleaseType"/>
/// </summary>
public class PressReleaseTypeExtensions: ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(PressRelease.PressReleaseType.Name);

    descriptor.Field("language")
      .Resolve(async (ctx, ct) => await ctx.DataLoader<LanguageByIdDataLoader>().LoadAsync(ctx.Parent<PressReleaseDto>().LanguageId, ct));
  }
}
