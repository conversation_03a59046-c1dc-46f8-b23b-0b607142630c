using HotChocolate.Execution.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.CustomerType;

public static class CustomerTypeRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddCustomerTypeType(this IRequestExecutorBuilder builder)
  {
    builder.AddType<CustomerTypeType>();
    builder.AddTypeExtension<CompanyTypeExtensions>();
    builder.AddTypeExtension<InstrumentTypeExtensions>();

    return builder;
  }
}
