using System;
using System.Linq;
using System.Collections.Generic;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure;
using GreenDonut;
using Microsoft.EntityFrameworkCore;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Microsoft.Data.SqlClient;
using Euroland.FlipIT.SData.API.Infrastructure.Factories;
using Euroland.FlipIT.Shared.CachingManager;
using System.Text.Json;
using System.Threading;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Microsoft.Extensions.DependencyInjection;
using Euroland.FlipIT.SData.API.GraphQL.Types.CurrencyRate;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;

#pragma warning disable CS8629 // Nullable value type may be null.

public class InstrumentPriceByIdDataLoader : BatchDataLoader<int, InstrumentPriceDto>
{
  private readonly ISharkDbContextAbstractFactory _factory;
  private readonly IGraphQLExecutionContext _queryRequestContext;
  private readonly IConfigurableCacheManager _cache;
  private readonly InstrumentByIdDataLoader _instrumentByIdDataLoader;
  private readonly CurrencyRateByCurrencyPairDataLoader _currencyRateByCurrencyPairDataLoader;

  /// <summary>
  /// The timeout duration in seconds for caching instrument price data.
  /// </summary>
  const short CACHE_TIMEOUT_SECOND = 10; // Instrument Price changed frequently, should not be cached long, 10 seconds in this case

  public InstrumentPriceByIdDataLoader(
    ISharkDbContextAbstractFactory factory,
    IGraphQLExecutionContext queryRequestContext,
    IConfigurableCacheManager configurableCacheManager,
    InstrumentByIdDataLoader instrumentByIdDataLoader,
    CurrencyRateByCurrencyPairDataLoader currencyRateByCurrencyPairDataLoader,
    IBatchScheduler batchScheduler,
    DataLoaderOptions? options = null)
    : base(batchScheduler, options)
  {
    _factory = factory ?? throw new ArgumentNullException(nameof(factory));
    _queryRequestContext = queryRequestContext ?? throw new ArgumentNullException(nameof(queryRequestContext));
    _cache = configurableCacheManager ?? throw new ArgumentNullException(nameof(configurableCacheManager));
    _instrumentByIdDataLoader = instrumentByIdDataLoader ?? throw new ArgumentNullException(nameof(instrumentByIdDataLoader));
    _currencyRateByCurrencyPairDataLoader = currencyRateByCurrencyPairDataLoader ?? throw new ArgumentNullException(nameof(currencyRateByCurrencyPairDataLoader));
  }

  protected override async System.Threading.Tasks.Task<IReadOnlyDictionary<int, InstrumentPriceDto>> LoadBatchAsync(
    IReadOnlyList<int> keys,
    CancellationToken cancellationToken)
  {
    var adjClose = _queryRequestContext.UseAdjClose;
    var useRealtimeData = _queryRequestContext.UseRealtime;
    var cacheManager = _cache;
    var exchangeCurrency = _queryRequestContext.ExchangeCurrency;
    var hasExchangeCurrency = !string.IsNullOrEmpty(exchangeCurrency);

    var (needToFetchIds, dataFromCache) = CheckCache(cacheManager, keys, useRealtimeData, adjClose, exchangeCurrency);

    if (needToFetchIds.Count == 0)
    {
      return dataFromCache.ToDictionary(p => p.InstrumentId);
    }

    using var sharkDbContext = await _factory.CreateDbContextAsync(_queryRequestContext.UseRealtime, _queryRequestContext.UseCloud);

    var pricesQueryable = GetQueryable(sharkDbContext, needToFetchIds, adjClose, useRealtimeData);

    var pricesEnumerable = (IEnumerable<InstrumentPriceDto>?)null;
    if (!hasExchangeCurrency)
    {
      pricesEnumerable = ((await
          pricesQueryable.Join(
            sharkDbContext.Instrument.AsNoTracking(),
            m => m.InstrumentId,
            i => i.InstrumentId,
            (price, instrument) => new { price, instrument }
          )
          .Where(grouping => needToFetchIds.Contains(grouping.price.InstrumentId))
          .Select(grouping => grouping.price)
          .ToListAsync(cancellationToken)
        )
        .WithAutoMapper()
        .Map<InstrumentPriceDto>());
    }
    else
    {
      // Get instrument currencies prepare for exchanging rate
      var instruments = await _instrumentByIdDataLoader.LoadAsync(needToFetchIds, cancellationToken);
      var ids = instruments.Select(c => c.Id).ToList();
      var currencyPairs = instruments
        .Where(c => !string.IsNullOrEmpty(c.CurrencyCode))
        .Select(
          c => $"{c.CurrencyCode?.Trim()}{exchangeCurrency?.Trim()}".ToLower()).ToList();

      var currencyRates = currencyPairs.Any()
        ? await _currencyRateByCurrencyPairDataLoader.LoadAsync(currencyPairs, cancellationToken)
        : Enumerable.Empty<CurrencyRateDto>();

      pricesEnumerable = pricesQueryable.Where(c => ids.Contains(c.InstrumentId)).WithAutoMapper().Map<InstrumentPriceDto>().ToList();

      var getRateFunc = (int insId, DateTime? priceDate) =>
      {
        if (!ids.Contains(insId))
        {
          return 1;
        }
        var insCurr = instruments.FirstOrDefault(p => p.Id == insId)?.CurrencyCode?.Trim();

        if (string.IsNullOrEmpty(insCurr))
        {
          return 1;
        }

        var rate = currencyRates.FirstOrDefault(p => p != null &&
          string.Equals(p.Pair, $"{insCurr}{exchangeCurrency?.Trim()}", StringComparison.InvariantCultureIgnoreCase)
        );

        if (rate != null && priceDate?.Date.CompareTo(rate.Date?.Date) == 0)
        {
          return rate.Value ?? 1;
        }

        return 1;
      };

      // Exchange rate
      foreach (var p in pricesEnumerable)
      {
        var rateValue = getRateFunc(p.InstrumentId, p.Date);
        p.Ask *= rateValue;
        p.Bid *= rateValue;
        p.High *= rateValue;
        p.Low *= rateValue;
        p.Open *= rateValue;
        p.Last *= rateValue;
        p.Mid *= rateValue;
        p.PrevClose *= rateValue;
        p.TodayTurnover *= rateValue;
        p.Vwap *= rateValue;
        p.OfficialClose *= rateValue;
      }
    }

    var results = dataFromCache != null
        ? new List<InstrumentPriceDto>(dataFromCache)
        : new List<InstrumentPriceDto>();

    foreach (var item in pricesEnumerable)
    {
      cacheManager.SetCache(
        item, BuildCacheKey(item.InstrumentId, useRealtimeData, adjClose, exchangeCurrency),
        TimeSpan.FromSeconds(CACHE_TIMEOUT_SECOND)
      );
      results.Add(item);
    }

    return results.ToDictionary(p => p.InstrumentId);
  }

  private static IQueryable<InstrumentPrice> GetQueryable(SharkDbContextBase sharkDbContext, IEnumerable<int> instrumentIds, bool adjClose, bool useRealtime)
  {
    return sharkDbContext.InstrumentPrice.FromSqlRaw(@$"
        SELECT
          p.[InstrumentId]
          ,CAST(p.[Bid] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Bid]
          ,CAST(p.[Ask] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Ask]
          ,CAST(p.[Open] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Open]
          ,CAST(p.[Last] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Last]
          ,CAST(p.[High] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [High]
          ,CAST(p.[Low] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Low]
          ,CAST(p.[Mid] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Mid]
          ,CAST(p.[PrevClose] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [PrevClose]
          ,CAST(p.[TodayTurnover] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [TodayTurnover]
          ,CAST(p.[VWAP] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [VWAP]
          ,CAST(p.[OfficialClose] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [OfficialClose]
          ,p.[Volume]
          ,p.[Date]
          ,p.[Change]
          ,p.[LastRowChange]
          ,p.[ChangePercentage]
          ,p.[BidSize]
          ,p.[AskSize]
          ,p.[OfficialCloseDate]
        FROM (
          SELECT * FROM rt_InstrumentPrice rt WHERE @realtime = 1
          UNION ALL
          SELECT * FROM InstrumentPrice nrt
          WHERE @realtime = 0 OR NOT EXISTS (SELECT 1 FROM rt_InstrumentPrice WHERE @realtime = 1 AND InstrumentId = nrt.InstrumentId)
        ) AS p
        OUTER APPLY (
          SELECT TOP 1 EXP(SUM(LOG(CumulativeAdjustmentFactor))) AS CumulativeAdjustmentFactor
          FROM {CorporateActionAdjustmentFactorCTEDbCommandInterceptor.CumulativeAdjustmentsTable}
          WHERE InstrumentId = p.InstrumentId AND p.[Date] < ActionDate
          GROUP BY InstrumentId
        ) AS ca
      ",
      new SqlParameter("adjClose", System.Data.SqlDbType.Bit) { Value = adjClose },
      new SqlParameter("realtime", System.Data.SqlDbType.Bit) { Value = useRealtime },
      new SqlParameter(
          CorporateActionAdjustmentFactorCTEDbCommandInterceptor.INSTRUMENT_IDS_PARAMETER_NAME,
          System.Data.SqlDbType.VarChar)
      { Value = JsonSerializer.Serialize(instrumentIds) }
      ).TagWith($"{CorporateActionAdjustmentFactorCTEDbCommandInterceptor.TOKEN}");
  }

  /// <summary>
  /// Checks the cache for existing instrument price data.
  /// </summary>
  /// <param name="cacheManager">The cache manager.</param>
  /// <param name="instrumentIds">The list of instrument IDs.</param>
  /// <param name="useRealtime">Indicates whether to use real-time data.</param>
  /// <param name="adjClose">Indicates whether to use adjusted close prices.</param>
  /// <param name="exchangeCurrency">The exchange currency, if any.</param>
  /// <returns>A tuple containing the list of IDs that need to be fetched and the cached instrument price DTOs.</returns>
  private static (IReadOnlyList<int> needToFetchIds, IReadOnlyList<InstrumentPriceDto> cacheDtos) CheckCache(
    IConfigurableCacheManager cacheManager,
    IReadOnlyList<int> instrumentIds,
    bool useRealtime,
    bool adjClose,
    string? exchangeCurrency)
  {

    var dtoFromCache = new List<InstrumentPriceDto>();
    var idNeedToFetchNew = new List<int>();

    foreach (var instrumentId in instrumentIds.Distinct())
    {
      var dto = cacheManager.GetCache<InstrumentPriceDto, string>(BuildCacheKey(instrumentId, useRealtime, adjClose, exchangeCurrency));

      if (dto != null)
      {
        dtoFromCache.Add(dto);
      }
      else
      {
        idNeedToFetchNew.Add(instrumentId);
      }
    }

    return (idNeedToFetchNew.AsReadOnly(), dtoFromCache.AsReadOnly());
  }

  /// <summary>
  /// Builds a cache key for the instrument price data.
  /// </summary>
  /// <param name="instrumentId">The instrument ID.</param>
  /// <param name="useRealtime">Indicates whether to use real-time data.</param>
  /// <param name="adjClose">Indicates whether to use adjusted close prices.</param>
  /// <param name="exchangeCurrency">The exchange currency, if any.</param>
  /// <returns>A string representing the cache key.</returns>
  private static string BuildCacheKey(
    int instrumentId,
    bool useRealtime,
    bool adjClose,
    string? exchangeCurrency
    )
  {
    return $"instPrice_{instrumentId}_{useRealtime}_{adjClose}_{exchangeCurrency ?? ""}";
  }
}
