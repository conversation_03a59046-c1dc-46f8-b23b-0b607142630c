using System;
using System.Linq;
using System.Linq.Expressions;
using HotChocolate.Language;
using HotChocolate.Resolvers;

namespace Microsoft.Extensions.DependencyInjection;

public static class OrderByArgumentIResolverContextExtensions
{
  public static bool HasOrderByArgument(this IResolverContext resolverContext)
  {
    try
    {
        var orderByArgument = resolverContext.ArgumentLiteral<IValueNode>("order");
        if (orderByArgument != NullValueNode.Default && orderByArgument != null)
        {
            return true;
        }
    }
    catch
    {
        return false;
    }

    return false;
  }

  public static IQueryable<T> OrderByArgumentOrDefault<T>(
    this IQueryable<T> query,
    IResolverContext resolverContext,
    Expression<Func<IQueryable<T>, IQueryable<T>>> expression)
    {
      return resolverContext.HasOrderByArgument()
        ? query
        : expression.Compile().Invoke(query);
    }
}
