import { create } from 'zustand';
import { apiClient } from "@/services/clientInstance";
import { API_PATHS } from "@/services/apiEndpoints";
import {
  clearSearchHistory as clearLocalSearchHistory,
  getSearchResultHistory as getLocalSearchResultHistory,
  saveNewSearchResult as saveLocalNewSearchResult,
  deleteSearchHistoryItem as deleteLocalSearchHistoryItem,
} from "@/lib/utils";
import { IAiSearchHistory } from "@/services/ai";

interface SearchHistoryState {
  // State
  searchHistories: IAiSearchHistory[];
  isLoading: boolean;

  // Actions
  setSearchHistories: (histories: IAiSearchHistory[]) => void;
  getSearchHistory: (isLogin: boolean) => Promise<void>;
  clearHistory: (isLogin: boolean) => Promise<void>;
  deleteHistoryItem: (id: string, isLogin: boolean) => Promise<void>;
  saveSearchResult: (result: IAiSearchHistory, isLogin: boolean) => Promise<void>;
}

export const useSearchHistory = create<SearchHistoryState>((set, get) => ({
  // State
  searchHistories: [],
  isLoading: false,

  // Actions
  setSearchHistories: (histories) => set({ searchHistories: histories }),

  getSearchHistory: async (isLogin) => {
    set({ isLoading: true });
    
    if (isLogin) {
      try {
        const response = await apiClient.instance.get<{ data: IAiSearchHistory[] }>(
          API_PATHS.SEARCH_HISTORIES
        );
        set({ searchHistories: response.data });
      } catch (error) {
        console.error("Failed to fetch search histories:", error);
      }
    } else {
      const localHistories = getLocalSearchResultHistory();
      set({ searchHistories: localHistories });
    }
    
    set({ isLoading: false });
  },

  clearHistory: async (isLogin) => {
    set({ isLoading: true });
    
    if (isLogin) {
      try {
        await apiClient.instance.delete(API_PATHS.SEARCH_HISTORIES);
      } catch (error) {
        console.error("Failed to clear search histories:", error);
      }
    } else {
      clearLocalSearchHistory();
    }
    
    set({ searchHistories: [] });
    set({ isLoading: false });
  },

  deleteHistoryItem: async (id, isLogin) => {
    set({ isLoading: true });
    
    if (isLogin) {
      try {
        await apiClient.instance.delete(API_PATHS.SEARCH_HISTORIES + `/${id}`);
        // Refetch the updated history list
        get().getSearchHistory(isLogin);
      } catch (error) {
        console.error(`Failed to delete search history item ${id}:`, error);
        set({ isLoading: false });
      }
    } else {
      deleteLocalSearchHistoryItem(id);
      const currentHistories = get().searchHistories;
      set({ 
        searchHistories: currentHistories.filter(item => item.id !== id),
        isLoading: false 
      });
    }
  },

  saveSearchResult: async (result, isLogin) => {
    set({ isLoading: true });

    if(!isLogin) {
      saveLocalNewSearchResult(result);
    }    
    await get().getSearchHistory(isLogin);
  }
}));

