<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>Euroland.FlipIT.WiseSearch</RootNamespace>
    <AssemblyName>Euroland.FlipIT.WiseSearch</AssemblyName>
    <AspNetCoreHostingModel>OutOfProcess</AspNetCoreHostingModel>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.13" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.13" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.13">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.13" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.13">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="8.6.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Scrutor" Version="6.0.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />

    <PackageReference Include="Serilog.AspNetCore" Version="4.1.0" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="3.0.1" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="3.1.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="3.1.1" />
    <PackageReference Include="Serilog.Sinks.SyslogMessages" Version="1.0.5" />

  </ItemGroup>

  <ItemGroup>
    <Folder Include="Common\Functions\" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="AppSettings\**" />
    <None Remove="AppSettings\**" />
  </ItemGroup>
</Project>
