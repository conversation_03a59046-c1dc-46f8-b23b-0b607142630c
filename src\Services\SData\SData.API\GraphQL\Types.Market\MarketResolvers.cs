using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure;
using HotChocolate;
using System.Linq;
using System.Threading;
using Microsoft.EntityFrameworkCore;
using HotChocolate.Resolvers;
using System.Threading.Tasks;
using System.Reflection;
using Microsoft.Data.SqlClient;
using System;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Market;

public class MarketResolvers
{
#pragma warning disable CS8601 // Possible null reference assignment.
  static MethodInfo marketStatusMethodInfo = typeof(SharkDbContextBase).GetMethod(nameof(SharkDbContextBase.GetMarketStatus));
#pragma warning restore CS8601 // Possible null reference assignment.
  public async Task<MarketDto> GetMarketByCompany(
    IResolverContext resolverContext,
    MarketByIdDataLoader dataLoader,
    [Parent] CompanyDto company,
    CancellationToken cancellationToken = default)
    => await dataLoader.LoadAsync(company.MarketId, cancellationToken);

  public async Task<MarketDto> GetMarketByInstrument(
    IResolverContext resolverContext,
    MarketByIdDataLoader dataLoader,
    [Parent] InstrumentDto instrument,
    CancellationToken cancellationToken = default)
    {

      return await dataLoader.LoadAsync(instrument.MarketID, cancellationToken);
    }

  public async Task<MarketStatusDto?> GetMarketStatus(
    [Parent] MarketDto market,
    SharkDbContextBase sharkDbContext,
    CancellationToken cancellationToken = default
  )
  {
    var functionName = GetMarketStatusFunctionName(marketStatusMethodInfo);
    if (functionName != null)
    {
      var result = await sharkDbContext.Database.SqlQueryRaw<int>(
        $"SELECT {functionName}(@MarketId, @CheckNextDay) as [Value]",
        new SqlParameter("MarketId", System.Data.SqlDbType.SmallInt) { Value = market.Id },
        new SqlParameter("CheckNextDay", System.Data.SqlDbType.Int) { Value = 0 }
      ).FirstOrDefaultAsync(cancellationToken);

      if (result == 0) {
        return null;
      }

      return new MarketStatusDto
      {
        IsOpened = result > 0,
        RemainingTime = Math.Abs(result)
      };
    }
    return null;

  }

  private string GetMarketStatusFunctionName(MethodInfo? methodInfo)
  {
    if (methodInfo == null)
    {
      return null;
    }

    var dbFunctionAttribute = methodInfo.GetCustomAttributes(typeof(DbFunctionAttribute))?.FirstOrDefault() as DbFunctionAttribute;
    if (dbFunctionAttribute != null)
    {
      return $"{dbFunctionAttribute.Schema}.{dbFunctionAttribute.Name}";
    }

    return null;
  }

  public IQueryable<MarketDto> GetMarketByEventType(
    [Parent] FCalendarDto fCalendar,
    SharkDbContextBase sharkDbContext)
  {
    var result = sharkDbContext.Market
      .AsNoTracking()
      .Where(m => m.MarketNumber == fCalendar.MarketId)
      .WithAutoMapper()
      .ToProjection<MarketDto>();
    return result;
  }
}
