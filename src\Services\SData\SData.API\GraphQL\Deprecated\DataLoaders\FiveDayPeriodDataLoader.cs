﻿using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Infrastructure.Repositories;
using GreenDonut;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Euroland.FlipIT.SData.API.GraphQL.Deprecated.DataLoaders
{
    public class FiveDayPeriodDataLoader : GroupedDataLoader<int, IntradayData>
    {
        private readonly IUnitOfWork _uow;

        public FiveDayPeriodDataLoader(
            IBatchScheduler batchScheduler,
            IUnitOfWork uow)
            : base(batchScheduler)
        {
            _uow = uow ?? throw new ArgumentNullException(nameof(uow));
        }

        protected override async Task<ILookup<int, IntradayData>> LoadGroupedBatchAsync(
            IReadOnlyList<int> keys,
            CancellationToken cancellationToken)
        {
            var intradays = _uow.GetRepository<IntradayData>();

            var query = intradays.GetQueryableList<IntradayData>(
                    selector: i => new IntradayData { InstrumentId = i.InstrumentId, Date = i.Date.Date },
                    predicate: i => keys.Contains(i.InstrumentId),
                    orderBy: i => i.OrderByDescending(x => x.Date));

            // show only data from the latest date
            var latestDate = query.Select(i => i.Date).Max(x => x.Date);

            //var fiveDayAgo = query.
            return query.Where(x => x.Date.Date == latestDate.Date)
                              .ToLookup(t => t.InstrumentId);
        }
    }
}
