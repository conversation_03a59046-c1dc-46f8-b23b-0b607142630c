using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Euroland.FlipIT.SData.API.Infrastructure.Configurations;

public class PerformanceShareConfigurationConfiguration : IEntityTypeConfiguration<PerformanceShare>
{
  public const string TableName = nameof(PerformanceShare);
  public void Configure(EntityTypeBuilder<PerformanceShare> builder)
  {
    builder
      .ToView(TableName)
      .HasNoKey();

  }
}
