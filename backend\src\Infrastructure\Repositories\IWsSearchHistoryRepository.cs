﻿using Microsoft.EntityFrameworkCore;
using Euroland.FlipIT.WiseSearch.Infrastructure.Entities.WiseSearch;

namespace Euroland.FlipIT.WiseSearch.Infrastructure.Repositories
{
    public interface IWsSearchHistoryRepository : IRepositoryBase<WiseSearchDbContext, SearchHistoryEntity, Guid>
    {
        Task<List<SearchHistoryEntity>> GetSearchHistoriesByUserAndCompanyAsync(string userName, string companyCode);
        IQueryable<SearchHistoryEntity> GetSearchHistoriesByUser(string userName);
        Task<(List<SearchHistoryEntity> Items, int TotalCount)> GetSearchHistoriesByUserAndCompanyAsync(
    string userName, string companyCode, int pageNumber, int pageSize);
    }

    public class WsSearchHistoryRepository : RepositoryBase<WiseSearchDbContext, SearchHistoryEntity, Guid>, IWsSearchHistoryRepository
    {
        public WsSearchHistoryRepository(WiseSearchDbContext context) : base(context)
        {
        }

        public async Task<(List<SearchHistoryEntity> Items, int TotalCount)> GetSearchHistoriesByUserAndCompanyAsync(
    string userName, string companyCode, int pageNumber, int pageSize)
        {
            var query = _dbContext.SearchHistories
                .Where(h => h.UserName == userName && h.CompanyCode == companyCode)
                .AsNoTracking()
                .OrderByDescending(h => h.SearchTime);

            var totalCount = await query.CountAsync();

            var items = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (items, totalCount);
        }

        public async Task<List<SearchHistoryEntity>> GetSearchHistoriesByUserAndCompanyAsync(string userName, string companyCode)
        {
            return await _dbContext.SearchHistories
                .Where(h => h.UserName == userName && h.CompanyCode == companyCode)
                .AsNoTracking()
                .OrderByDescending(h => h.SearchTime)
                .ToListAsync();
        }

        public IQueryable<SearchHistoryEntity> GetSearchHistoriesByUser(string userName)
        {
            return _dbContext.SearchHistories
                 .AsNoTracking()
                 .Where(h => h.UserName == userName)
                 .OrderByDescending(h => h.SearchTime);
        }
    }
}
