using System;
using Euroland.FlipIT.SData.API.Helpers;

namespace Euroland.FlipIT.SData.API.Dto;

/// <summary>
/// Represents the history exchange rate of a currency exchanged for another currency.
/// </summary>
public class CurrencyRateHistoryDto: IDtoObject
{
  public int CurrencyRateId { get; set; }
  public decimal Rate { get; set; }
  private DateTime _date;
  public DateTime Date
  {
    get
    {
      return _date;
    }
    set
    {

      _date = value.Kind != DateTimeKind.Utc ? value.ToUtcKindOnly() : value;

    }
  }
}
