{"Serilog": {"Using": ["Serilog.Sinks.Syslog"], "MinimumLevel": "Warning", "WriteTo": [{"Name": "UdpSyslog", "Args": {"host": "**********", "port": "514", "appName": "FlipIT.Fundamentals.API", "restrictedToMinimumLevel": "Warning", "outputTemplate": "[{Timestamp:HH:mm:ss}] {Message:lj}{NewLine}{Exception}"}}], "Properties": {"Application": "FlipIT.Fundamentals.API"}}, "ConnectionStrings": {"AnalystEstimatesDb": "Server=tcp:eurolandeurope.database.windows.net,1433;Database=AnalystEstimates;User ID=uShark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=FlipIT.Fundamentals.API", "ReportHubDB": "Server=**********;Database=ARP2018;User ID=ushark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=FlipIT.Fundamentals.API;", "FinCalendarDB": "Server=**********;Database=FinCalendar2;User ID=ushark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=FlipIT.Fundamentals.API;"}, "EarningEstimateApiUrl": "http://**************:3003"}