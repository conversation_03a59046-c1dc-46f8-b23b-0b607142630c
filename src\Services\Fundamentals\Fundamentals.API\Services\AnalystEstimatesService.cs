using Euroland.FlipIT.Fundamental.API.Dtos;
using Euroland.FlipIT.Fundamental.API.Persistance;
using HotChocolate.Resolvers;
using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.Fundamental.API.Services;

public class AnalystEstimatesService : IAsyncDisposable
{
  private readonly AnalystEstimatesDbContext _dbContext;

  public AnalystEstimatesService(IDbContextFactory<AnalystEstimatesDbContext> dbContextFactory)
  {
    _dbContext = dbContextFactory.CreateDbContext();
  }

  public ValueTask DisposeAsync()
  {
    GC.SuppressFinalize(this);

    return _dbContext.DisposeAsync();
  }

  public async Task<AnalystRecommendationDto?> GetAnalystRecommendation(IResolverContext resolverContext, DateTime showDataFrom, CancellationToken cancellationToken = default)
  {
    var companyCode = resolverContext.GetScopedState<string>("companyCode");

    var result =  await _dbContext.AnalystCompany
    .Where(ac => ac.CompanyCode == companyCode
                 && ac.LastRated >= showDataFrom
                 && !ac.Exclude).ToListAsync();

    if (result.Count == 0)
    {
      return null;
    }

    var strongBuy = result.Count(ac => ac.LastRatingValue == 1);
    var buy = result.Count(ac => ac.LastRatingValue == 2);
    var hold = result.Count(ac => ac.LastRatingValue == 3);
    var sell = result.Count(ac => ac.LastRatingValue == 4);
    var strongSell = result.Count(ac => ac.LastRatingValue == 5);
    var date = result.Max(ac => ac.LastRated);
    AnalystRecommendationDto dto = new (){
      AnalystRatingStrongBuy = strongBuy,
      AnalystRatingBuy = buy,
      AnalystRatingHold = hold,
      AnalystRatingSell = sell,
      AnalystRatingStrongSell = strongSell,
      Date = date};

    return dto;
  }
}
