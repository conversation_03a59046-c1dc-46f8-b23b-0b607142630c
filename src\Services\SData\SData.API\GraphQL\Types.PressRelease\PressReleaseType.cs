using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure;
using HotChocolate;
using HotChocolate.Types;
using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.PressRelease;

public class PressReleaseType : ObjectType<PressReleaseDto>
{
  public const string Name = "PressRelease";

  protected override void Configure(IObjectTypeDescriptor<PressReleaseDto> descriptor)
  {
    descriptor.Name(Name);
    descriptor.Field(f => f.Id).IsProjected();
    descriptor.Field(f => f.LanguageId).IsProjected();
    descriptor.Field(f => f.MessageTypeId).IsProjected();
    descriptor.Field(f => f.SourceId).IsProjected();
    descriptor.Field(p => p.DateTime)
      .Description("Published date of the release at the stock exchange timezone, but not UTC although it is in ISO format of UTC.");
    descriptor.Field(f => f.Date).Deprecated(
      $"Use field \"{nameof(PressReleaseDto.DateTime)}\" instead. This deprecated field will be remove in next release"
    );

    descriptor.Field(f => f.CompanyCode).IsProjected();

    descriptor.Field("message")
      .Type<StringType>()
      .ResolveWith<Resolvers>(p => p.GetMessage(default!, default!, default));
  }

  private sealed class Resolvers
  {
    public async Task<string?> GetMessage(
      [Parent] PressReleaseDto pressRelease,
      NewsContext newsContext,
      CancellationToken cancellationToken = default
    )
    {
      return await newsContext.PressReleases
        .AsNoTracking()
        .Where(p => p.Id == pressRelease.Id)
        .Select(p => p.Message)
        .FirstOrDefaultAsync(cancellationToken);
    }
  }
}
