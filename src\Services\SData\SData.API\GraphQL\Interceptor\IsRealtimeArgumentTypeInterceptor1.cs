using HotChocolate.Configuration;
using HotChocolate.Types.Descriptors.Definitions;

namespace Euroland.FlipIT.SData.API.GraphQL.Interceptor;

public class IsRealtimeArgumentTypeInterceptor : TypeInterceptor
{
  public override void OnBeforeCompleteType(ITypeCompletionContext completionContext, DefinitionBase definition)
  {
    if (definition is ObjectTypeDefinition objectTypeDefinition)
    {
      // foreach (var fieldDefinition in objectTypeDefinition.Fields)
      // {
      //   if (//fieldDefinition.HasArguments
      //     !fieldDefinition.Ignore
      //     && !fieldDefinition.IsDeprecated
      //     && !fieldDefinition.IsIntrospectionField
      //   )
      //   {

      //     var useCloudArgumentDefinition = new ArgumentDefinition(
      //       name: "useCloud",
      //       description: "Invoke application to use Cloud database",
      //       type: completionContext.TypeInspector.GetTypeRef(typeof(Boolean?)),
      //       defaultValue: HotChocolate.Language.NullValueNode.Default,
      //       runtimeDefaultValue: null
      //     );
      //     fieldDefinition.Arguments.Add(useCloudArgumentDefinition);
      //   }
      // }
    }
  }
}
