﻿namespace Euroland.FlipIT.WiseSearch.Models
{
    public class AppSettings
    {
        public List<string> AllowedOrigins { get; set; } = new();
        public AzureSettings Azure { get; set; } = new();
        public HttpClientRequestSettings HttpClientRequest { get; set; } = new();
        public AuthenticationSettings Authentication { get; set; } = new();
        public List<ApiKeyAuthOption> ApiKeyAuthentication { get; set; } = new();


        public class AzureSettings
        {
            public TranslatorApiSettings TranslatorApi { get; set; } = new();

            public class TranslatorApiSettings
            {
                public string Endpoint { get; set; } = string.Empty;
                public string SubscriptionKey { get; set; } = string.Empty;
            }
        }

        public class HttpClientRequestSettings
        {
            public OpenAISettings OpenAI { get; set; } = new();
            public WiseApiSettings WiseApi { get; set; } = new();
            public OidcSettings Oidc { get; set; } = new();

            public class OpenAISettings
            {
                public string BaseUrl { get; set; } = string.Empty;
                public OpenAIEndpoints Endpoints { get; set; } = new();
                public string Model { get; set; } = string.Empty;
                public double Temperature { get; set; }
                public string OpenAIKey { get; set; } = string.Empty;

                public class OpenAIEndpoints
                {
                    public string ChatCompletions { get; set; } = string.Empty;
                }
            }

            public class WiseApiSettings
            {
                public string BaseUrl { get; set; } = string.Empty;
                public string DefaultLanguageToTranslate { get; set; } = string.Empty;
                public WiseApiEndpoints Endpoints { get; set; } = new();
                public WiseApiQuerySettings Query { get; set; } = new();

                public class WiseApiEndpoints
                {
                    public string Query { get; set; } = string.Empty;
                }

                public class WiseApiQuerySettings
                {
                    public string ResponseModel { get; set; } = string.Empty;
                    public int SemanticCount { get; set; }
                }
            }

            public class OidcSettings
            {
                public string BaseUrl { get; set; } = string.Empty;
                public OidcEndpoints Endpoints { get; set; } = new();
                public string ClientId { get; set; } = string.Empty;
                public string ClientSecret { get; set; } = string.Empty;
                public string GrantType { get; set; } = string.Empty;

                public class OidcEndpoints
                {
                    public string TokenUrl { get; set; } = string.Empty;
                }

            }
        }

        public class AuthenticationSettings
        {
            public JwtBearerSettings JwtBearer { get; set; } = new();

            public class JwtBearerSettings
            {
                public bool RequireHttpsMetadata { get; set; }
                public string Authority { get; set; } = string.Empty;
                public string ValidIssuer { get; set; } = string.Empty;
                public string ValidAudience { get; set; } = string.Empty;
                public int ClockSkewSeconds { get; set; }
            }
        }

        public class ApiKeyAuthOption
        {
            public string Name { get; set; } = string.Empty;
            public string ApiKey { get; set; } = string.Empty;
            public string Secret { get; set; } = string.Empty;
            public int AllowedTimeSkewSeconds { get; set; } = 300;
        }
    }
}
