using HotChocolate.Execution.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.FCEvent;

public static class FCEventRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddFCEventType(this IRequestExecutorBuilder builder)
  {
    builder.AddType<FCEventType>();
    builder.AddType<FCEventTypeType>();
    builder.AddTypeExtension<CompanyTypeExtensions>();

    return builder;
  }
}
