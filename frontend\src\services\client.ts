import { appSettings } from "@/config/appSettings";
import { ensureNotNull } from "@/helpers";

const DEFAULT_BASE_URL = import.meta.env.VITE_API_URL;

export class ApiClient {
  private baseUrl: string;

  constructor(
    private getToken: () => Promise<string | undefined>,
    baseUrl?: string
  ) {
    this.baseUrl = baseUrl || DEFAULT_BASE_URL;
  }

  private async getHeaders() {
    const token = await this.getToken();
    const headers: Record<string, string> = {
      // Explicit type declaration
      "Content-Type": "application/json",
    };
    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
    }
    return headers as HeadersInit;
  }

  private prepareQueryParams(params?: Record<string, string | number>) {
    const query = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        query.append(key, value.toString());
      });
    }

    query.append("language", appSettings.language);
    query.append("companyCode", ensureNotNull(appSettings.companyCode));
    query.append("version", appSettings.version);
    return query;
  }

  async get<T>(
    url: string,
    params?: Record<string, string | number>,
    options?: RequestInit
  ): Promise<T> {
    const query = this.prepareQueryParams(params);

    const response = await fetch(`${this.baseUrl}${url}?${query.toString()}`, {
      ...options,
      method: "GET",
      headers: await this.getHeaders(),
    });
    return this.handleResponse<T>(response);
  }

  async post<T>(
    url: string,
    data?: object,
    params?: Record<string, string | number>,
    options?: RequestInit
  ): Promise<T> {
    const response = await fetch(
      `${this.baseUrl}${url}?${this.prepareQueryParams(params).toString()}`,
      {
        ...options,
        method: "POST",
        headers: await this.getHeaders(),
        body: data && JSON.stringify(data),
      }
    );
    return this.handleResponse<T>(response);
  }

  async delete<T>(url: string): Promise<T> {
    const response = await fetch(
      `${this.baseUrl}${url}?${this.prepareQueryParams().toString()}`,
      {
        method: "DELETE",
        headers: await this.getHeaders(),
      }
    );
    return this.handleResponse<T>(response);
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const error = await response.text();
      throw new Error(error || "Request failed");
    }
    return response.json();
  }

  async* sse<T>(
    url: string,
    data?: object,
    params?: Record<string, string | number>,
    options?: RequestInit
  ): AsyncGenerator<T> {
    const response = await fetch(
      `${this.baseUrl}${url}?${this.prepareQueryParams(params).toString()}`,
      {
        ...options,
        method: "POST",
        headers: await this.getHeaders(),
        body: data && JSON.stringify(data),
      }
    );

    if(!response.body) throw new Error("No body");

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    while (true) {
      const { done, value } = await reader.read();
      if (done) return;
      const text = decoder.decode(value, { stream: true });
      yield JSON.parse(text);
    }
  }
}

// Example usage:
// const apiClient = new ApiClient(() => localStorage.getItem('token'));
// apiClient.post<boolean>(API_PATHS.SEARCH, searchData)
//   .then(data => console.log(data))
//   .catch(error => console.error(error));
