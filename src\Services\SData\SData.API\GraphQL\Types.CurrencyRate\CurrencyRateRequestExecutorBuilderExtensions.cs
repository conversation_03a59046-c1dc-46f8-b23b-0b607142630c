using Euroland.FlipIT.SData.API.GraphQL.Types.CurrencyRate;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class CurrencyRateRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddCurrencyRateType(this IRequestExecutorBuilder builder)
  {
    builder.AddType<CurrencyRateType>();
    builder.AddTypeExtension<CurrencyRateHistoryTypeExtensions>();

    builder.AddDataLoader<CurrencyRateByCurrencyPairDataLoader>();
    builder.AddDataLoader<CurrencyRateByIdDataLoader>();

    return builder;
  }
}
