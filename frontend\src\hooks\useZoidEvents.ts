import {Euroland} from "@/zoid";
import {ComponentZoidInstance} from "@/zoid-components/zoid-instance";
import {useEffect, useRef} from "react";

export default function useZoidEvents<P extends object>(instance: ComponentZoidInstance<P>, events: Partial<Record<keyof Euroland['EVENT'], () => void>>) {
  const eventsRef = useRef(events);
  eventsRef.current = events;

  useEffect(() => {
    const clearup: (() => void)[] = []
    for(const [key, listener] of Object.entries(events)) {
      const {cancel} = instance.component.event.on(window.euroland.EVENT[key as keyof Euroland['EVENT']], listener)
      clearup.push(cancel)
    }

    return () => clearup.forEach(fn => fn())
  }, []) 
}