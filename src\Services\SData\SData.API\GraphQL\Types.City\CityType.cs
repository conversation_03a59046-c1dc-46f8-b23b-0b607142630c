using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Types;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.City;

public class CityType: ObjectType<CityDto>
{
  public const string Name = "City";
  protected override void Configure(IObjectTypeDescriptor<CityDto> descriptor)
  {
    descriptor.Name(Name);

    descriptor.Field(f => f.Id);
    descriptor.Field(f => f.CountryId).IsProjected();

    descriptor.TranslationField();
  }
}
