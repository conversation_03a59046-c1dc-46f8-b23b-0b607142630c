
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

public class InstrumentConfiguration: IEntityTypeConfiguration<Instrument>
{
    public void Configure(EntityTypeBuilder<Instrument> builder)
    {
        // builder
        //   .HasOne(e => e.InstrumentPrice)
        //   .WithOne(e => e.Instrument)
        //   .HasPrincipal<PERSON>ey("InstrumentId");
    }
}

public class InstrumentHistoryConfiguration: IEntityTypeConfiguration<InstrumentHistory>
{
    public void Configure(EntityTypeBuilder<InstrumentHistory> builder)
    {
        builder.ToTable(nameof(InstrumentHistory));
    }
}

public class AdjustedInstrumentHistoryConfiguration: IEntityTypeConfiguration<AdjustedInstrumentHistory>
{
    public void Configure(EntityTypeBuilder<AdjustedInstrumentHistory> builder)
    {
        builder.ToView(nameof(AdjustedInstrumentHistory));
    }
}

public class InstrumentPriceConfiguration: IEntityTypeConfiguration<InstrumentPrice>
{
    public void Configure(EntityTypeBuilder<InstrumentPrice> builder)
    {
        // builder
        //   .HasOne(e => e.Instrument)
        //   .WithOne(e => e.InstrumentPrice)
        //   .HasPrincipalKey("InstrumentId");
    }
}
