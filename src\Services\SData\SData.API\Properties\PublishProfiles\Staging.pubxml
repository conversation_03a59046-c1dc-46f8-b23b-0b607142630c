<?xml version="1.0" encoding="utf-8"?>
<!--
https://go.microsoft.com/fwlink/?LinkID=208121.
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>AnyCPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>https://staging.euroland.com/tools/sdata-api/graphql/?companycode=dk-cbg</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>False</LaunchSiteAfterPublish>
    <ExcludeApp_Data>False</ExcludeApp_Data>
    <MSDeployServiceURL>https://ee-v-webcat15.euroland.com:8172/msdeploy.axd</MSDeployServiceURL>
    <DeployIisAppPath>staging-site/tools/sdata-api</DeployIisAppPath>
    <UserName>EE-V-WEBCAT15\WDeployAdmin2</UserName>
    <RemoteSitePhysicalPath />
    <SkipExtraFilesOnServer>False</SkipExtraFilesOnServer>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <EnableMSDeployBackup>True</EnableMSDeployBackup>
    <_SavePWD>True</_SavePWD>
    <AllowUntrustedCertificate>True</AllowUntrustedCertificate>
    <DeployEnv>Staging</DeployEnv>
    <EnvironmentName>Staging</EnvironmentName>
  </PropertyGroup>
  <ItemGroup>
    <MsDeploySkipRules Include="CustomSkipFolder">
      <ObjectName>dirPath</ObjectName>
      <AbsolutePath>Config\\Company</AbsolutePath>
    </MsDeploySkipRules>
    <MsDeploySkipRules Include="CustomSkipFolder">
      <ObjectName>dirPath</ObjectName>
      <AbsolutePath>logs</AbsolutePath>
    </MsDeploySkipRules>
  </ItemGroup>
</Project>
