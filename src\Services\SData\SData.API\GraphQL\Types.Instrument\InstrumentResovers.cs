using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using HotChocolate;
using HotChocolate.Data.Projections.Context;
using HotChocolate.Resolvers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;


namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;

internal partial class InstrumentResolvers
{
  public async Task<InstrumentPriceDto?> GetInstrumentPrice(
    [Parent] InstrumentDto instrument,
    InstrumentPriceByIdDataLoader dataLoader,
    IResolverContext resolverContext,
    CancellationToken cancellationToken = default
  ) => await dataLoader.LoadAsync(instrument.Id, cancellationToken);

  public async Task<InstrumentDto?> GetInstrumentById(
      int id,
      InstrumentByIdDataLoader dataLoader,
      IResolverContext resolverContext,
      CancellationToken cancellationToken = default
    ) => await dataLoader.LoadAsync(id, cancellationToken);

  public async Task<IEnumerable<InstrumentDto?>> GetInstruments(
    [Parent] CompanyDto company,
    IResolverContext resolverContext,
    SharkDbContextBase sharkDbContext,
    InstrumentTypeDto type = InstrumentTypeDto.ALL,
    CancellationToken cancellationToken = default)
  {
    var cache = resolverContext.GetConfigurableCacheManager();

    var cacheKey =
      CacheKeyHelper.GenerateKeyByPropOfDtoObject<InstrumentDto, int?>(company.Id.ToString(),
        i => i.CompanyID);

    var cachedInstrumentsByCompanyId = cache.GetCache<List<InstrumentDto>?, string>(cacheKey);

    if (cachedInstrumentsByCompanyId != null)
    {
      return cachedInstrumentsByCompanyId.Where(i => type == InstrumentTypeDto.ALL || i.InstrumentType == type);
    }

    var instrumentsByCompanyId = (
        await sharkDbContext.Instrument
          .AsNoTracking()
          .Where(i =>
            i.CompanyID == company.Id
            //&& i.Customer == 1 /* Euroland Customer only */
            && (type == InstrumentTypeDto.ALL || i.InstrumentType == (byte)type))
          .ToListAsync(cancellationToken)
      )
      .WithAutoMapper()
      .Map<InstrumentDto>();

    cache.SetCache(instrumentsByCompanyId, cacheKey);

    return instrumentsByCompanyId;
  }

  public bool HasAdjustedClosePriceField(IResolverContext resolverContext)
  {
    List<string> list = new List<string>();
    TraverseField(list, resolverContext.GetSelectedField());

    return list.Count > 0;
  }
  private void TraverseField(List<string> list, ISelectedField? selectedField = null)
  {
    if(selectedField == null)
    {
      return;
    }

    if(selectedField.Field.Name == InstrumentHistoryType.ADJ_CLOSE_FIELD) {
      list.Add(selectedField.Field.Name);
      return;
    }

    foreach (var field in selectedField.GetFields())
    {
      TraverseField(list, field);
    }
  }
}
