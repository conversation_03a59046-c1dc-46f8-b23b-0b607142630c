using AutoMapper;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;

namespace Euroland.FlipIT.SData.API.AutoMapper.Profiles;

public class CurrencyRateHistoryMapperProfile : Profile
{
  public CurrencyRateHistoryMapperProfile()
  {
    CreateMap<CurrencyRateHistory, CurrencyRateHistoryDto>()
      .ForMember(d => d.Date, cfg => cfg.MapFrom(e => e.Date));
  }
}
