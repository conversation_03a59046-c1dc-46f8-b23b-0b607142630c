using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Euroland.FlipIT.WiseSearch;
using Euroland.FlipIT.WiseSearch.Extensions;
using Euroland.FlipIT.WiseSearch.Infrastructure;
using Euroland.FlipIT.WiseSearch.Middlewares;
using Euroland.FlipIT.WiseSearch.Models;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

builder.Host.UseSerilog((context, configuration) =>
    configuration.ReadFrom.Configuration(context.Configuration));

// Add services to the container.
builder.Services.AddDbContext<WiseSearchDbContext>(options =>
                                                   options.UseSqlServer(builder.Configuration.GetConnectionString("WiseSearchDbConnection")));
builder.Services.AddDbContext<SharkDbContext>(options =>
                                                   options.UseSqlServer(builder.Configuration.GetConnectionString("SharkDbConnection")));

// Add API service here
builder.Services.RegisterAPIServices();
builder.Services.RegisterRepositories();
builder.Services.AddUnitOfWorks();
builder.Services.RegisterDI();
builder.Services.AddControllers();

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerService();

builder.Services.AddHttpContextAccessor();

builder.Services.AddAutoMapper(typeof(AutoMapperProfile).Assembly);

builder.Services.AddHealthChecks();

builder.Services.Configure<AppSettings>(
    builder.Configuration.GetSection("AppSettings"));

var appSettings = builder.Configuration.GetSection("AppSettings").Get<AppSettings>()
    ?? throw new ArgumentNullException("Missing \"AppSettings\" block in appsettings.json file");
builder.Services.AddHttpClientService(appSettings);


var configuration = builder.Configuration;

var jwtSettings = appSettings.Authentication.JwtBearer;

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.IncludeErrorDetails = true;
    options.RefreshOnIssuerKeyNotFound = true;
    options.SaveToken = true;
    options.RequireHttpsMetadata = jwtSettings.RequireHttpsMetadata;
    options.Authority = jwtSettings.Authority;

    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidIssuer = jwtSettings.ValidIssuer,
        ValidateAudience = true,
        ValidAudience = jwtSettings.ValidAudience,
        ValidateIssuerSigningKey = true,
        ValidateLifetime = true,
        ClockSkew = TimeSpan.FromSeconds(jwtSettings.ClockSkewSeconds),
    };
});

builder.Services.Configure<SecurityStampValidatorOptions>(options =>
{
    options.ValidationInterval = TimeSpan.FromMinutes(1);
});

// Add CORS services
builder.Services.AddCors(options =>
{
    options.AddPolicy("CorsPolicy", policyBuilder =>
    {
        policyBuilder
            .WithOrigins([.. appSettings.AllowedOrigins])
            .AllowAnyMethod()
            .AllowAnyHeader();
    });
});


var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseCors("CorsPolicy");
app.UseStaticFiles();
//Add support to logging request with SERILOG
//app.UseSerilogRequestLogging();

app.UseRouting();

app.UseMiddleware<ApiKeyAuthenticationMiddleware>();
app.UseAuthentication();
app.UseAuthorization();
app.UseMiddleware<GlobalResponseMiddleware>();

app.MapControllers();
app.MapHealthChecks("/health");

if(!app.Environment.IsDevelopment()) {
    app.MapFallbackToFile("index.html");
}

TokenExts.Configure(app.Services.GetRequiredService<IHttpContextAccessor>());

app.Run();
