using Euroland.FlipIT.SData.API.GraphQL.Types.PressRelease;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class PressReleaseRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddPressReleaseType(this IRequestExecutorBuilder builder)
  {
    builder
      .AddType<PressReleaseType>();

    builder.AddTypeExtension<CompanyTypeExtensions>();
    builder.AddTypeExtension<AttachmentTypeExtensions>();

    return builder;
  }
}
