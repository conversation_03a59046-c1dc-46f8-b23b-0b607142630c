using Euroland.FlipIT.Fundamental.API.Extensions;
using Serilog;
using Euroland.FlipIT.Fundamental.API.GraphQL;
using Euroland.FlipIT.Fundamental.API.Services;
using Euroland.FlipIT.Fundamental.API.Persistance;
using Microsoft.EntityFrameworkCore;
using Euroland.FlipIT.Fundamental.API.GraphQL.Types;
using Euroland.FlipIT.Fundamental.API.AutoMappers.Profiles;

var builder = WebApplication.CreateBuilder(args);

var logger = new LoggerConfiguration()
  .WriteTo.Console()
  .ReadFrom.Configuration(builder.Configuration)
  .CreateLogger();

builder.Logging.ClearProviders();
builder.Logging.AddSerilog(logger);

builder.Services.AddHttpClient<FundamentalService>(
  httpClient => httpClient.BaseAddress = new Uri(builder.Configuration["EarningEstimateApiUrl"]));

builder.Services.AddTransient<AnalystEstimatesService>();
builder.Services.AddTransient<ReportHubService>();

builder.Services.AddPooledDbContextFactory<AnalystEstimatesDbContext>(
    options => options.UseSqlServer(builder.Configuration.GetConnectionString("AnalystEstimatesDb")));

builder.Services.AddPooledDbContextFactory<ReportHubDbContext>(
    options => options.UseSqlServer(builder.Configuration.GetConnectionString("ReportHubDB")));

builder.Services.AddPooledDbContextFactory<FinCalendarDbContext>(
    options => options.UseSqlServer(builder.Configuration.GetConnectionString("FinCalendarDB")));

builder.Services.AddEurolandTranslation(builder.Configuration);
builder.Services.AddAutoMapper(typeof(SegmentsMapperProfile));
builder.Services.AddGraphQLServer()
  .AddDiagnosticEventListener<ErrorLoggingExecutionEventListener>()
  .AddQueryType<QueryType>(d => d.Name(OperationTypeNames.Query))
  .AddType<FundamentalType>()
  .AddReportType()
  .AddFinCalenderType()
  .AddDocumentFromFile("./Schema.graphql")
  .AddNewtonJsonSupport()
  .RegisterDbContext<ReportHubDbContext>(DbContextKind.Pooled)
  .RegisterDbContext<AnalystEstimatesDbContext>(DbContextKind.Pooled)
  .RegisterDbContext<FinCalendarDbContext>(DbContextKind.Pooled)
  .PublishSchemaDefinition(d =>
    d.SetName("fundamental")
      .IgnoreRootTypes()
      .AddTypeExtensionsFromFile("./Stitching.graphql")
  )
  .AddTypeConverter<DateTimeOffset, DateTime>(t => t.UtcDateTime)
  .AddTypeConverter<DateTime, DateTimeOffset>(
    t => t.Kind is DateTimeKind.Unspecified
      ? DateTime.SpecifyKind(t, DateTimeKind.Utc)
      : t
  )
  .AddProjections()
  .AddFiltering()
  .AddSorting()
  .InitializeOnStartup();

var app = builder.Build();

app.UseRouting();

app.UseEndpoints(endpoints =>
{
  endpoints.MapGraphQL();
});

app.Run();

// For test mocking
public partial class Program { }
