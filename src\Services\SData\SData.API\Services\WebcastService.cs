using Euroland.FlipIT.SData.API.DTOs.Deprecated.Webcasts;
using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Euroland.FlipIT.SData.API.Services
{
  public class WebcastService : IWebcastService
    {
        private readonly IConfiguration _configuration;

        public WebcastService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public ValueTask DisposeAsync()
        {
            throw new System.NotImplementedException();
        }

        public async Task<IQueryable<WebcastDetailOutputDto>> GetWebcast(WebcastContext webcastContext, string companyCode, CancellationToken cancellationToken = default)
        {
            var pdfUrlTempalte = _configuration.GetSection("ShareGraph:Webcast:PdfUrlTemplate").Value;
            var query = webcastContext.Webcasts.AsQueryable()
                                               .Include(s => s.WebcastUrls)
                                               .Include(s => s.WebcastType)
                                               .Where(s => s.CompanyCode == companyCode)
                                               .Select(wc => new WebcastDetailOutputDto
                                               {
                                                   //Date = wc.PublishDate,
                                                   //TranscriptUrl = string.Format(pdfUrlTempalte, wc.Id),
                                                   //Title = wc.Title,
                                                   //Urls = wc.WebcastUrls
                                                   //           .Where(s => wc.DefaultHost != WebcastUrl.HostTypeVimeo
                                                   //                       && s.UploadStatus == WebcastUrl.UploadStatusSuccess)
                                                   //           .Select(s => new WebcastVideo
                                                   //           {
                                                   //               Url = s.Url
                                                   //           }),
                                                   //VideoType = wc.WebcastType.Name,
                                                   //Vimeo = wc.WebcastUrls
                                                   //           .Where(s => wc.DefaultHost == WebcastUrl.HostTypeVimeo
                                                   //                        && s.UploadStatus == WebcastUrl.UploadStatusSuccess)
                                                   //           .Select(s => new WebcastVimeo
                                                   //           {
                                                   //               VideoId = s.Url,
                                                   //               Url = s.Url
                                                   //           })
                                               });

            return query;

        }
    }
}
