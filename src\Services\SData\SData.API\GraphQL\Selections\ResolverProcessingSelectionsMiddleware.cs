using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using HotChocolate;
using HotChocolate.Data.Projections.Context;
using HotChocolate.Resolvers;

namespace Euroland.FlipIT.SData.API.GraphQL.Selections;

public class ResolverProcessingSelectionsMiddleware
{
  private static readonly ConcurrentDictionary<MethodInfo, Lazy<bool>> _paramsContextResolverRegistry = new ConcurrentDictionary<MethodInfo, Lazy<bool>>();
  private readonly FieldDelegate _next;

  public ResolverProcessingSelectionsMiddleware(FieldDelegate next)
  {
    _next = next;
  }

  public async Task InvokeAsync(IMiddlewareContext context)
  {
    ISelectionParamContext selectionParamContext = null;

    if(context.GetSelectedField().Field.ResolverMember is MethodInfo resolverMethod && ResolverHasSelectionParamContext(resolverMethod))
    {
      selectionParamContext = GetOrSetSelectionResolverParamContext(context);
    }
    var parent = context.Parent<object>();
    await _next(context).ConfigureAwait(false);
    parent = context.Parent<object>();
    var result = context.Result;
    var s = "";
  }

  /// <summary>
  /// Check if resolver method requires <see cref="ISelectionParamContext"/> parameter.
  /// </summary>
  /// <param name="resolverMethod">The resolver method</param>
  /// <returns>True if <see cref="ISelectionParamContext"/> is required. Otherwise, False.</returns>
  private bool ResolverHasSelectionParamContext(MethodInfo resolverMethod) {
    var lazyCheck = _paramsContextResolverRegistry.GetOrAdd(
      resolverMethod,
      new Lazy<bool>(() => {
          return resolverMethod.GetParameters().Any(p => p.ParameterType.IsAssignableFrom(typeof(ISelectionParamContext)));
      }));

    return lazyCheck.Value;
  }

  public ISelectionParamContext GetOrSetSelectionResolverParamContext(IResolverContext resolverContext) {
    return resolverContext.GetOrSetLocalState(nameof(ISelectionParamContext), (_) => new DefaultSelectionParamContext(resolverContext));
  }
}
