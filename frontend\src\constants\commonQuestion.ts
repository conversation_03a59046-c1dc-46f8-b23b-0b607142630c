import {
  faChartLine,
  faCoins,
  faPercentage,
  faChartPie,
  faCalculator,
  faPiggyBank,
  faShieldAlt,
  faChartBar,
  faTachometerAlt,
  faBullseye,
  faBuilding,
  faUsers,
  faLaptop,
  faLeaf,
  faHandHoldingUsd,
  faChartArea,
  faGlobe,
  faChess,
  faTemperatureHigh,
  faTasks,
  faRobot,
  faUserTie,
} from '@fortawesome/free-solid-svg-icons';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';

export interface IQuestion {
  icon: string;
  text: string;
  category: string;
}

export const ICON_MAP: { [key: string]: IconDefinition } = {
  'fa-chart-line': faChartLine,
  'fa-coins': faCoins,
  'fa-percentage': faPercentage,
  'fa-chart-pie': faChartPie,
  'fa-calculator': faCalculator,
  'fa-piggy-bank': faPiggyBank,
  'fa-shield-alt': faShieldAlt,
  'fa-chart-bar': faChartBar,
  'fa-tachometer-alt': faTachometerAlt,
  'fa-bullseye': faBullseye,
  'fa-building': faBuilding,
  'fa-users': faUsers,
  'fa-laptop': faLaptop,
  'fa-leaf': faLeaf,
  'fa-hand-holding-usd': faHandHoldingUsd,
  'fa-chart-area': faChartArea,
  'fa-globe': faGlobe,
  'fa-chess': faChess,
  'fa-temperature-high': faTemperatureHigh,
  'fa-tasks': faTasks,
  'fa-robot': faRobot,
  'fa-user-tie': faUserTie,
};

const COMMON_QUESTIONS = [
  // Financial Performance
  {
    icon: 'fa-chart-line',
    text: 'commonQuestions.financialHighlights',
    category: 'performance',
  },
  {
    icon: 'fa-coins',
    text: 'commonQuestions.netInterestIncomeDevelopment',
    category: 'performance',
  },
  {
    icon: 'fa-percentage',
    text: 'commonQuestions.costToIncomeRatioTrend',
    category: 'performance',
  },
  {
    icon: 'fa-chart-pie',
    text: 'commonQuestions.feeCommissionIncomeDevelopment',
    category: 'performance',
  },
  {
    icon: 'fa-calculator',
    text: 'commonQuestions.returnOnEquity',
    category: 'performance',
  },

  // Capital & Risk
  {
    icon: 'fa-piggy-bank',
    text: 'commonQuestions.capitalAndLiquidityPosition',
    category: 'capital',
  },
  {
    icon: 'fa-shield-alt',
    text: 'commonQuestions.creditQualityLoanLossProvisions',
    category: 'capital',
  },
  {
    icon: 'fa-chart-bar',
    text: 'commonQuestions.cet1RatioTrend',
    category: 'capital',
  },
  {
    icon: 'fa-tachometer-alt',
    text: 'commonQuestions.liquidityCoverageRatioDevelopment',
    category: 'capital',
  },

  // Strategy & Business
  {
    icon: 'fa-bullseye',
    text: 'commonQuestions.strategicPrioritiesProgress',
    category: 'strategy',
  },
  {
    icon: 'fa-building',
    text: 'commonQuestions.businessAreasPerformance',
    category: 'strategy',
  },
  {
    icon: 'fa-users',
    text: 'commonQuestions.customerSatisfactionAndActivity',
    category: 'strategy',
  },
  {
    icon: 'fa-laptop',
    text: 'commonQuestions.digitalTransformationProgress',
    category: 'strategy',
  },
  {
    icon: 'fa-leaf',
    text: 'commonQuestions.sustainabilityESGDevelopments',
    category: 'strategy',
  },

  // Shareholder Returns
  {
    icon: 'fa-hand-holding-usd',
    text: 'commonQuestions.dividendPolicyAndCapitalDistribution',
    category: 'returns',
  },
  {
    icon: 'fa-coins',
    text: 'commonQuestions.epsDevelopment',
    category: 'returns',
  },
  {
    icon: 'fa-chart-area',
    text: 'commonQuestions.netAssetValuePerShareDevelopment',
    category: 'returns',
  },

  // Market & Competition
  {
    icon: 'fa-globe',
    text: 'commonQuestions.marketConditionsImpact',
    category: 'market',
  },
  {
    icon: 'fa-chess',
    text: 'commonQuestions.competitivePosition',
    category: 'market',
  },
  {
    icon: 'fa-temperature-high',
    text: 'commonQuestions.interestRateEnvironmentImpact',
    category: 'market',
  },

  // Costs & Efficiency
  {
    icon: 'fa-tasks',
    text: 'commonQuestions.costDriversAndEfficiencyMeasures',
    category: 'costs',
  },
  {
    icon: 'fa-robot',
    text: 'commonQuestions.automationProgress',
    category: 'costs',
  },
  {
    icon: 'fa-user-tie',
    text: 'commonQuestions.personnelCostsAndFteDevelopment',
    category: 'costs',
  },
];

export function getRandomQuestions(count: number = 8): IQuestion[] {
  // Ensure not to get more questions than available
  count = Math.min(count, COMMON_QUESTIONS.length);

  // Group questions by category
  const categorizedQuestions: Record<string, IQuestion[]> = COMMON_QUESTIONS.reduce((acc, q) => {
    if (!acc[q.category]) {
      acc[q.category] = [];
    }
    acc[q.category].push(q);
    return acc;
  }, {} as Record<string, IQuestion[]>);

  // Calculate number of questions needed from each category
  const categories: string[] = Object.keys(categorizedQuestions);
  const questionsPerCategory = Math.max(1, Math.floor(count / categories.length));

  const selectedQuestions: IQuestion[] = [];

  // First, select at least 1 question from each category
  categories.forEach((category) => {
    const categoryQuestions: IQuestion[] = categorizedQuestions[category];
    const randomIndices: number[] = [];

    for (let i = 0; i < questionsPerCategory && categoryQuestions.length > 0; i++) {
      let randomIndex: number;
      do {
        randomIndex = Math.floor(Math.random() * categoryQuestions.length);
      } while (randomIndices.includes(randomIndex));
      randomIndices.push(randomIndex);
      selectedQuestions.push(categoryQuestions[randomIndex]);
    }
  });

  // If the number of selected questions is not enough, randomly select from all questions
  while (selectedQuestions.length < count) {
    const remainingQuestions: IQuestion[] = COMMON_QUESTIONS.filter(q => !selectedQuestions.includes(q));
    if (remainingQuestions.length === 0) break;

    const randomQuestion = remainingQuestions[Math.floor(Math.random() * remainingQuestions.length)];
    selectedQuestions.push(randomQuestion);
  }

  // Shuffle the final result
  return selectedQuestions.sort(() => Math.random() - 0.5);
}
