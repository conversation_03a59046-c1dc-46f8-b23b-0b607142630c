/* Instruments Section */
.instruments-section {
  flex: 1;
  padding: 10px 0 0 0;
  overflow-y: auto;
  max-height: calc(100vh - 430px);
  display: flex;
  flex-direction: column;
}

.instruments-table-container {
  width: 100%;
  overflow-y: auto;
  max-height: calc(100vh - 430px);
  border: 1px solid #e1e3e6;
  border-radius: 6px;
}

.instruments-table {
  width: 100%;
  border-collapse: collapse;
  background: #ffffff;
  table-layout: fixed;
}

.instruments-table thead {
  position: sticky;
  top: 0;
  z-index: 1;
}

.table-header {
  background: #f8f9fa;
  padding: 8px 12px;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #6c757d;
  border-bottom: 1px solid #e1e3e6;
  text-align: left;
}

.table-row {
  display: table-row;
  position: relative;
  cursor: pointer;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s;
}

.table-row td {
  display: table-cell;
  padding: 8px 12px;
  vertical-align: middle;
}

.header-cell {
  display: flex;
  align-items: center;
}

.symbol {
  flex-direction: column;
  align-items: flex-start;
}

.symbol-text {
  font-weight: 600;
}

.market-text {
  font-size: 11px;
  color: #6c757d;
}

.positive {
  color: #28a745;
}

.negative {
  color: #dc3545;
}

.remove-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #dc3545;
  padding: 0;
}

.table-row:hover .remove-btn {
  visibility: visible;
}

.table-row.selected {
  background: #e3f2fd;
}

.table-row:last-child {
  border-bottom: none;
}

.cell {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
}

.cell.symbol {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}


.symbol-text {
  font-weight: 600;
  color: #131722;
}

.market-text {
  font-size: 11px;
  color: #6c757d;
  font-weight: 400;
}

.cell.name {
  color: #6c757d;
  font-weight: 400;
}

.cell.price {
  color: #131722;
  font-weight: 600;
}

.cell.change {
  font-weight: 500;
}

.cell.change-percent {
  text-align: end;
  font-weight: 500;
}

.cell.change.positive {
  color: #26a69a;
}

.cell.change.negative {
  color: #f44336;
}

.cell.change-percent.positive {
  color: #26a69a;
}

.cell.change-percent.negative {
  color: #f44336;
}

.cell.high {
  color: #131722;
  font-weight: 600;
}

.cell.low {
  color: #131722;
  font-weight: 600;
}

.cell.week-high {
  color: #131722;
  font-weight: 600;
}

.remove-btn {
  background: none;
  visibility: hidden;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background: white;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  color: #6c757d;
  transition: all 0.2s;
}

.remove-btn:hover {
  background: rgba(243, 147, 140);
  color: #f44336;
}

.delete-confirm {
  display: flex;
  gap: 4px;
}

.confirm-delete-btn,
.cancel-delete-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.confirm-delete-btn {
  color: #00c853;
}

.confirm-delete-btn:hover {
  background: rgba(0, 200, 83, 0.1);
}

.cancel-delete-btn {
  color: #6c757d;
}

.cancel-delete-btn:hover {
  background: rgba(108, 117, 125, 0.1);
}

/* Skeleton Loading Animations */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Skeleton Rows */
.skeleton-row {
  border-bottom: 1px solid #f1f3f4;
  animation: fadeIn 0.3s ease-out;
}

.skeleton-row:hover {
  background: transparent;
}

.skeleton-cell {
  padding: 12px;
  vertical-align: middle;
}

.skeleton-line {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
  height: 16px;
}

.skeleton-symbol {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.skeleton-symbol-text {
  width: 80px;
  height: 18px;
}

.skeleton-market-text {
  width: 60px;
  height: 12px;
}

.skeleton-price {
  width: 70px;
}

.skeleton-change {
  width: 50px;
}

.skeleton-change-percent {
  width: 60px;
  margin-left: auto;
}

/* Enhanced Loading and Empty States */
.loading-state,
.empty-state,
.error-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
  text-align: center;
  animation: fadeIn 0.5s ease-out;
}

.loading-state {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  margin: 20px;
  border: 1px solid #e9ecef;
}

.empty-state {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  margin: 20px;
  border: 1px solid #e9ecef;
}

.error-state {
  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
  border-radius: 12px;
  margin: 20px;
  border: 1px solid #fed7d7;
}

/* Icons */
.loading-icon,
.empty-icon,
.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
  animation: pulse 2s infinite;
}

.loading-icon {
  animation: pulse 1.5s infinite;
}

.empty-icon {
  opacity: 0.7;
}

.error-icon {
  color: #f44336;
}

/* Text Styling */
.loading-title,
.empty-title,
.error-title {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.loading-subtitle,
.empty-subtitle,
.error-subtitle {
  font-size: 14px;
  color: #718096;
  margin: 0;
  line-height: 1.5;
  max-width: 300px;
}

.error-title {
  color: #e53e3e;
}

.error-subtitle {
  color: #c53030;
}

/* Legacy support for existing p tags */
.empty-state p,
.loading-state p,
.error-state p {
  margin: 4px 0;
  font-size: 14px;
}

.error-state {
  color: #f44336;
}

/* Responsive Design for States */
@media (max-width: 768px) {
  .loading-state,
  .empty-state,
  .error-state {
    padding: 40px 16px;
    margin: 16px;
  }

  .loading-icon,
  .empty-icon,
  .error-icon {
    font-size: 36px;
    margin-bottom: 12px;
  }

  .loading-title,
  .empty-title,
  .error-title {
    font-size: 16px;
  }

  .loading-subtitle,
  .empty-subtitle,
  .error-subtitle {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .skeleton-symbol-text {
    width: 60px;
  }

  .skeleton-market-text {
    width: 45px;
  }

  .skeleton-price {
    width: 50px;
  }

  .skeleton-change {
    width: 40px;
  }

  .skeleton-change-percent {
    width: 45px;
  }

  .loading-state,
  .empty-state,
  .error-state {
    padding: 30px 12px;
    margin: 12px;
  }

  .loading-icon,
  .empty-icon,
  .error-icon {
    font-size: 32px;
    margin-bottom: 10px;
  }

  .loading-title,
  .empty-title,
  .error-title {
    font-size: 15px;
  }

  .loading-subtitle,
  .empty-subtitle,
  .error-subtitle {
    font-size: 12px;
  }
}