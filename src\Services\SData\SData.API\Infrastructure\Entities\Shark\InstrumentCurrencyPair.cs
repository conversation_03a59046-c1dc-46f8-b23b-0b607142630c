using System;
using System.Text.Json.Serialization;
//using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities;

/// <summary>
/// Temp table for helping convert currency for instrument price.
/// </summary>
// public class InstrumentCurrencyPair
// {
//   [JsonIgnore]
//   public decimal? Rate { get; set;}
//   [JsonIgnore]
//   public DateTime? Date { get; set;}
//   public int? InstrumentId { get; set; }
//   public string? CurrencyPair { get; set; }
//   public float? Factor { get; set; }
//   public decimal? FixedRate { get; set; }
// }
