using HotChocolate.Data.Filters;

namespace Euroland.FlipIT.Fundamental.API.Dtos.Filters;

public class EstimatesFilterInputType : FilterInputType<EstimatesFilterInput>
{
  protected override void Configure(IFilterInputTypeDescriptor<EstimatesFilterInput> descriptor)
  {
    descriptor.AllowAnd(false).AllowOr(false);
    descriptor.Field(x => x.Year).Type<TimeIntFieldFilterInputType>();
    descriptor.Field(x => x.Period).Type<TimeStringFieldFilterInputType>();
    descriptor.Field(x => x.Calculation).Type<EstimatesStringFieldFilterInputType>();
  }
}

public class EstimatesStringFieldFilterInputType : StringOperationFilterInputType
{
  protected override void Configure(IFilterInputTypeDescriptor descriptor)
  {
    descriptor.AllowAnd(false).AllowOr(false);
    descriptor.Operation(DefaultFilterOperations.Equals).Type<StringType>();
  }
}
