using Microsoft.EntityFrameworkCore;
using Euroland.FlipIT.WiseSearch.Infrastructure.Entities.Shark;
using Euroland.FlipIT.WiseSearch.Infrastructure.Entities.Shark.Views;

namespace Euroland.FlipIT.WiseSearch.Infrastructure;

public partial class SharkDbContext : DbContext
{
  public SharkDbContext()
  {
  }

  public SharkDbContext(DbContextOptions<SharkDbContext> options)
      : base(options)
  {
  }

  public virtual DbSet<CompanySettingsLanguage> CompanySettingLanguages { get; set; }
  public virtual DbSet<CompanySetting> CompanySettings { get; set; }
  public virtual DbSet<CompanyNameView> CompanyNames { get; set; }
}
