﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
  [Table("CompanyName")]
    public class CompanyNames
    {
        [Column("cCode", Order = 0), ForeignKey("Company")]
        public string? CompanyCode { get; set; }
        [Column("cLang")]
        public string? CompanyLang { get; set; }
        [Column("cName")]
        public string? CompanyName { get; set; }

        [Column("cAdr1")]
        public string? CompanyAdr1 { get; set; }

        [Column("cAdr2")]
        public string? CompanyAdr2 { get; set; }

        [Column("cZip")]
        public string? CompanyZip { get; set; }

        [Column("cTown")]
        public string? CompanyTown { get; set; }

        [Column("cCountry")]
        public string? CompanyCountry { get; set; }

        [Column("cTel")]
        public string? CompanyTel{ get; set; }

        [Column("cFax")]
        public string? CompanyFax { get; set; }

        [Column("cEMail")]
        public string? CompanyEmail { get; set; }

        public Company Company { get; set; }
    }
}
