import { appSettings } from "@/config/appSettings";
import { memo, useEffect, useState, useRef } from "react";
import {useSearchWidgetPropsContext} from "../context/propContext";
import {getTickers, TickerResponse} from "@/services/apiSData";
import clsx from "clsx";

const REFRESH_TIME = 60; // seconds;

const PriceFeedTime = () => {
  const [time, setTime] = useState(() => new Date());
  const companyInfo = useSearchWidgetPropsContext(state => state.companyInfo);

  useEffect(() => {
    const interval = setInterval(() => {
      setTime(new Date());
    }, 1000 * REFRESH_TIME);

    return () => clearInterval(interval);
  }, []);

  const formatDate = (date: Date) => {
    const day = date.toLocaleDateString(appSettings.language, {
      weekday: "short",
      month: "short",
      day: "2-digit",
      year: "numeric",
      timeZone: companyInfo.timezone
    });

    const time = date.toLocaleTimeString(appSettings.language, {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
      timeZone: companyInfo.timezone
    });

    return `${day} ${time}`;
  };

  return <div className="text-xs">{formatDate(time)}</div>;
};

const PriceFeed = () => {
  const [data, setData] = useState<TickerResponse['instrumentByIds']>([]);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  
  const aiSearchSettings = useSearchWidgetPropsContext(item => item.aiSearchSettings)

  useEffect(() => {
    const fetchPriceFeedData = async () => {
      const tickerIds = Object.entries(aiSearchSettings.tickers).filter(([, active]) => active).map(([instrumentId]) => instrumentId);
      if(tickerIds.length === 0) {
        return;
      }
      const response = await getTickers(Array.from(tickerIds).map(item => parseInt(item)));

      setData(response.instrumentByIds);

      if (response.instrumentByIds.length === 0) {
        clearIntervalRef();
      }
    };

    fetchPriceFeedData();

    if (!intervalRef.current) {
      intervalRef.current = setInterval(() => {
        fetchPriceFeedData();
      }, 1000 * REFRESH_TIME);
    }

    return () => {
      clearIntervalRef();
    };
  }, []);

  const clearIntervalRef = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }

  if (data.length === 0) {
    return null;
  }

  return (
    <div className="flex border-b">
      <div className="overflow-hidden flex flex-wrap flex-1">
        {data.map((item, index) => (
            <div key={index} style={{margin: "-0.5px",}} className={clsx('py-2 px-4 w-1/2 sm:w-1/3 md:w-auto border', {
              "flex-1": index === data.length - 1
            })}>
              <div className="flex items-center whitespace-nowrap">
                <span className="text-xs font-medium mr-1">
                  {item?.market?.abbreviation}
                </span>
                <span className="text-xs font-medium mr-1">
                  {item?.currentPrice?.last} {item?.currencyCode}
                </span>
                <span
                  className={`text-xs ${
                    item?.currentPrice?.changePercentage > 0 ? "text-green-500" : "text-red-500"
                  }`}
                >
                  {item?.currentPrice?.changePercentage > 0 ? "+" : "-"}
                  {Number(Math.abs(item?.currentPrice?.changePercentage ?? 0)).toFixed(2)}%
                </span>
              </div>
            </div>
        ))}
      </div>
      <div className="py-2 px-4 items-center border-l hidden md:flex">
        <PriceFeedTime />
      </div>
    </div>
  );
};

export default memo(PriceFeed);
