USE [shark]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


-- =============================================
-- Author:		ThinhTD
-- Create date: 19.12.2022
-- Description:	select dbo.fn_GetStartingDate(32864)
-- =============================================
CREATE FUNCTION [dbo].[fnGetStartingDate]
(
	@instrumentId int
)
RETURNS datetime
AS
BEGIN
  -- Declare the return variable here
	DECLARE @Result nvarchar(100)
  -- Add the T-SQL statements to compute the return value
	SET @Result =  (SELECT MIN([Date]) FROM InstrumentHistory WHERE InstrumentID = @instrumentId)
	-- Return the result of the function
	RETURN @Result
END


