import { ensureDefined } from "./helpers";
import { useEffect, useMemo, useRef, useState } from "react";
import { merge, snakeCase } from "es-toolkit";
import { searchParams } from "./config/appSettings";
import { ComponentZoidInstance } from "./zoid-instance";
import { baseUrl } from "./lib/base-url";
import { joinPath } from "./lib/join-path";

const __globalZoidInstances = window;
const globalZoidInstances =
  __globalZoidInstances.__globalZoidInstances ?? new Map();
__globalZoidInstances.__globalZoidInstances = globalZoidInstances;

function validateZoidOptions(
  options,
  instanceOptions
) {
  const opacity = options.template?.styles?.opacity;
  if (opacity && parseFloat(opacity) == 0) {
    const animate = instanceOptions.animate;
    if (animate?.show && !animate.show?.opacity) {
      throw new Error("opacity is 0, but no opacity animation is defined");
    }
  }
}

export function zoidComponentCreator(
  Component,
  config,
  layoutPosition
) {
  const displayName = snakeCase(
    ensureDefined(Component.displayName ?? Component.name)
  );

  const url = new URL(joinPath(baseUrl, displayName));
  Array.from(searchParams.entries()).forEach(([key, value]) =>
    url.searchParams.append(key, value)
  );

  const options = merge(
    {
      tag: displayName,
      url: url.toString(),
      autoResize: {
        width: false,
        height: false,
        element: "body"
      },
      dimensions: {
        width: "50vw",
        height: "100vh"
      },
      template: {
        name: "dynamic",
        styles: {
          position: "fixed",
          bottom: "0px",
          right: "0px",
          "z-index": "20001",
          opacity: "0"
        }
      }
    },
    config
  );

  window.euroland.createComponent(displayName, options);

  function ComponentWrapper() {
    const forceRender = useState([])[1];

    useEffect(() => {
      window.xprops.onProps(() => forceRender([]));
    }, []);
    return <Component {...(window.xprops ?? {})} />;
  }

  const init = (
    props,
    instanceOptions = {}
  ) => {
    const { defaultHidden = true } = instanceOptions;
    let instance = globalZoidInstances.get(displayName);
    if (instance) return instance;
    const component = window.euroland.components[displayName](props);
    validateZoidOptions(options, instanceOptions);
    instance = new ComponentZoidInstance(
      component,
      props,
      layoutPosition ? { ...instanceOptions, layoutPosition } : instanceOptions
    );
    instance.render();
    if (defaultHidden) {
      instance.hide();
    } else {
      instance.show();
    }
    globalZoidInstances.set(displayName, instance);
    return instance;
  };

  const useComponent = (
    props,
    instanceOptions = {}
  ) => {

    const propsRef = useRef(props);

    propsRef.current = props;

    const instance = useMemo(() => {
      // Ensure that any function prop uses the latest callback reference from propsRef,
      // avoiding issues with stale closures.
      const forwardProps = Object.fromEntries(
        Array.from(Object.entries(props)).map(([key, value]) => {
          if (typeof value === "function")
            return [
              key,
              (...args) =>
                propsRef.current[key](...args)
            ];

          return [key, value];
        })
      );
      return init(forwardProps, instanceOptions);
    }, []);

    return instance;
  };

  return {
    ComponentWrapper,
    init,
    use: useComponent,
    path: displayName
  };
}
