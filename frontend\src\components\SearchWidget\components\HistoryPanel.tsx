import { FC, useEffect } from "react";
import { Info, TrashCan } from "../../Common";
import { i18n } from "@euroland/libs";
import { SearchHistoryItem } from "./SearchHistoryItem";
import { useSearchWidgetPropsContext } from "../context/propContext";
import { useSearchHistory } from "../context/useSearchHistory";
import {AUTH_ENABLED} from "@/helpers/constants";

export const HistoryPanel: FC = () => {
  const isLogin = useSearchWidgetPropsContext(state => state.isLogin);
  const { 
    searchHistories, 
    clearHistory, 
    deleteHistoryItem,
    getSearchHistory 
  } = useSearchHistory();

  useEffect(() => {
    getSearchHistory(isLogin);
  }, [isLogin, getSearchHistory]);

  return (
  <div className="wise-widget-history">
    <div className="wise-widget-history-header">
      <h3>
        {i18n.translate(
          isLogin ? "searchHistory" : "localHistory"
        )}
      </h3>
      <button
        className="wise-widget-clear-history"
        onClick={() => clearHistory(isLogin)}
      >
        <TrashCan />
        {i18n.translate("clearHistory")}
      </button>
    </div>
    {!isLogin && AUTH_ENABLED && (
      <div className="wise-widget-local-warning">
        <Info />
        <span>{i18n.translate("localHistoryTooltip")}</span>
      </div>
    )}
    {searchHistories?.length > 0 ? (
      searchHistories.map((searchHistory) => (
        <SearchHistoryItem
          key={searchHistory.id}
          className="mt-5"
          data={searchHistory}
          onDelete={(id) => deleteHistoryItem(id, isLogin)}
        />
      ))
    ) : (
      <div className="wise-widget-history-items">
        <div className="wise-widget-no-history">
          {i18n.translate("noHistory")}
        </div>
      </div>
    )}
    </div>
  );
};
