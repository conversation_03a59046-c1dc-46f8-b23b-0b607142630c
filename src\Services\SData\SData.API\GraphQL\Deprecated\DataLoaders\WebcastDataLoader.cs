﻿using Euroland.FlipIT.SData.API.DTOs.Deprecated.Webcasts;
using Euroland.FlipIT.SData.API.Infrastructure;
using GreenDonut;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using Euroland.FlipIT.SData.API.Infrastructure.Entities.Webcast;
using Microsoft.EntityFrameworkCore;
using Euroland.FlipIT.SData.API.Helpers;
using System.Globalization;

namespace Euroland.FlipIT.SData.API.GraphQL.Deprecated.DataLoaders;

public class WebcastDataLoader : BatchDataLoader<long, WebcastDetailOutputDto>, IAsyncDisposable
{
    private readonly WebcastContext _dbContext;

    public WebcastDataLoader(
        IDbContextFactory<WebcastContext> ctx,
        IBatchScheduler batchScheduler,
        DataLoaderOptions? options = null)
        : base(batchScheduler, options)
    {
        _dbContext = ctx.CreateDbContext();
    }

    protected override async Task<IReadOnlyDictionary<long, WebcastDetailOutputDto>> LoadBatchAsync(IReadOnlyList<long> keys, CancellationToken cancellationToken)
    {
        var query = _dbContext.Webcasts.Where(s => keys.Contains(s.Id)).Select(wc => new WebcastDetailOutputDto
        {
            Id = wc.Id,
            Date = wc.PublishDate,
            TranscriptUrl = $"{wc.CompanyCode}/{wc.Id}/transcript{wc.TranscriptType}",
            Title = wc.Title,
            Urls = wc.WebcastUrls
                                                           .Where(a => a.WebcastHost.Name != WebcastHost.Vimeo)
                                                           .Select(a => new WebcastVideo
                                                           {
                                                               VideoId = a.Id.ToString(),
                                                               Url = $"{wc.CompanyCode}/{wc.Id}/webcast{wc.FileType}"
                                                           }),
            VideoType = wc.WebcastType.Name,
            Vimeo = wc.WebcastUrls
                                                           .Where(a => a.WebcastHost.Name == WebcastHost.Vimeo
                                                                        && a.UploadStatus.Name == UploadStatus.Success)
                                                           .Select(a => new WebcastVimeo
                                                           {
                                                               VideoId = a.Id.ToString(),
                                                               VimeoId = a.OriginalId,

                                                           })
                                                           .FirstOrDefault(),
            ThumbnailUrl = $"{wc.CompanyCode}/{wc.Id}/thumbnail{wc.ThumbnailType}"
        }
        );

        return query.ToDictionary(x => x.Id, s => s);
    }

    public async Task<IQueryable<WebcastDetailOutputDto>> LoadByCompanyCode(string companyCode, DateTime from, DateTime? to, CancellationToken cancellationToken)
    {
        var LangCode = CultureInfo.CurrentCulture.Name;
        var webcastsByCompany = _dbContext.Webcasts
                                           .Include(s => s.WebcastUrls)
                                           .Include(s => s.WebcastType)
                                           .Include(s => s.WebcastTranslations)
                                           .Where(s => s.CompanyCode == companyCode && s.PublishDate >= from)
                                           .Where(s => !to.HasValue || s.PublishDate <= to)
                                           .Select(wc => new WebcastDetailOutputDto
                                           {
                                               Id = wc.Id,
                                               Date = wc.PublishDate.ToUtc(),
                                               TranscriptUrl = $"{companyCode}/{wc.Id}/transcript{wc.TranscriptType}",
                                               Title = wc.WebcastTranslations.Any(s => s.LanguageCode == LangCode) ? wc.WebcastTranslations.First(s => s.LanguageCode == LangCode).Title : wc.Title,
                                               DefaultHost = wc.WebcastHost.Name,
                                               ThumbnailUrl = $"{companyCode}/{wc.Id}/thumbnail{wc.ThumbnailType}",
                                               Urls = wc.WebcastUrls
                                                          .Where(a => a.WebcastHost.Name != WebcastHost.Vimeo)
                                                          .Select(a => new WebcastVideo
                                                          {
                                                              Url = $"{companyCode}/{wc.Id}/webcast{wc.FileType}",
                                                              HostType = a.WebcastHost.Name
                                                          }),
                                               VideoType = wc.WebcastType.Name,
                                               Vimeo = wc.WebcastUrls
                                                          .Where(a => a.WebcastHost.Name == WebcastHost.Vimeo
                                                                       && a.UploadStatus.Name == UploadStatus.Success)
                                                          .Select(a => new WebcastVimeo
                                                          {
                                                              VideoId = a.Id.ToString(),
                                                              VimeoId = a.OriginalId,
                                                              HostType = a.WebcastHost.Name
                                                          })
                                                          .FirstOrDefault()
                                           });

        return webcastsByCompany;
    }

    public ValueTask DisposeAsync()
    {
        return _dbContext.DisposeAsync();
    }
}

