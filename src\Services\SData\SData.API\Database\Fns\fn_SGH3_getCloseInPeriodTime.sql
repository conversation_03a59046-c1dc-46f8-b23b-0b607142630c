USE [shark]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- =============================================
-- Author:		<AnhNT>
-- Create date: <19/12/2023>
-- Description:	<Get close price from date>
-- =============================================
CREATE FUNCTION [dbo].[fn_SGH3_getCloseInPeriodTime] ( @instrumentId AS INT,
	@dateFrom AS datetime2,
	@dateTo AS datetime2 ) RETURNS money AS BEGIN
	DECLARE
		@result money;
	SELECT TOP
		( 1 ) @result = [Close] 
	FROM
		InstrumentHistory 
	WHERE
		InstrumentId = @instrumentId 
		AND [Date] >= @dateFrom 
		AND [Date] <= @dateTo 
	ORDER BY
		[Date] ASC;
RETURN @result;
END;