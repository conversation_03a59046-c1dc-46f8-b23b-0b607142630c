using AISearch.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Euroland.FlipIT.WiseSearch.Extensions;
using Euroland.FlipIT.WiseSearch.Models;
using Euroland.FlipIT.WiseSearch.Models.Search;
using Euroland.FlipIT.WiseSearch.Models.Authentication;

namespace Euroland.FlipIT.WiseSearch.Controllers
{
    [Route("/api/[controller]")]
    [ApiController]
    [Authorize]
    public class SearchController : ControllerBase
    {
        private readonly ISearchService _searchService;

        public SearchController(ISearchService searchService)
        {
            _searchService = searchService;
        }

        [HttpGet("histories")]
        public async Task<ActionResult> GetSearchHistoryAsync(
            string companyCode,
            string language,
            int? pageNumber,
            int? pageSize)
        {
            var userName = TokenExts.GetUserName();

            if (pageNumber == null && pageSize == null)
            {
                var result = await _searchService.GetSearchHistoryAsync(companyCode, userName, language);
                return Ok(result);
            }
            else
            {
                PagedResult<SearchHistoryResponse> result = await _searchService.GetSearchHistoryPagingAsync(companyCode, userName, language, pageNumber ?? 1, pageSize ?? 10);
                return Ok(result);
            }
        }

        [HttpGet("profile-search-histories")]
        public async Task<ActionResult<List<ProfileSearchHistoryOutput>>> GetProfileSearchHistoryAsync(string language)
        {
            var userName = TokenExts.GetUserName();
            var result = await _searchService.GetProfileSearchHistoryAsync(userName, language);
            return Ok(result);
        }

        [HttpPost("stream")]
        [AllowAnonymous]
        public async Task StreamPostAsync([FromBody] SearchInput input,
                                   [FromQuery] string companyCode,
                                   [FromQuery] string language)
        {
            input.CompanyCode = companyCode;
            input.Language = language;

            var token = new TokenInfoDto
            {
                IPAddress = TokenExts.GetClientIp(),
                UserName = TokenExts.GetUserName(),
                FullName = $"{TokenExts.GetFamilyName() ?? string.Empty} {TokenExts.GetGivenName() ?? string.Empty}".Trim(),
                Email = TokenExts.GetEmail(),
                UserAgent = Request.Headers.UserAgent,
            };

            Response.ContentType = "application/x-ndjson";

            await foreach (var step in _searchService.StreamSearchSteps(input, token))
            {
                var json = JsonConvert.SerializeObject(step);
                await Response.WriteAsync(json + "\n");
                await Response.Body.FlushAsync();
            }
        }

        [HttpDelete("profile-histories")]
        public async Task<IActionResult> ProfileClearSearchHistoryAsync(CancellationToken ct)
        {
            var userName = TokenExts.GetUserName();
            var result = await _searchService.ProfileClearSearchHistoryAsync(userName, ct);
            return Ok(result);
        }

        [HttpDelete("histories")]
        public async Task<IActionResult> ClearSearchHistoryAsync(string companyCode)
        {
            var userName = TokenExts.GetUserName();
            var result = await _searchService.ClearSearchHistoryAsync(companyCode, userName);
            return Ok(result);
        }

        [HttpDelete("histories/{historyId}")]
        public async Task<IActionResult> ClearSearchHistoryAsync([FromRoute] Guid historyId)
        {
            var userName = TokenExts.GetUserName();
            var result = await _searchService.DeleteSearchHistoryAsync(userName, historyId);
            return Ok(result);
        }
    }
}
