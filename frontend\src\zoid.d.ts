import type React from "react";

 
export type DimensionType = `${number}${'px' | '%' | 'vh' | 'vw'}`;

export interface Dimensions {
  width: DimensionType;
  height: DimensionType;
}

export interface AutoResize {
  width?: boolean;
  height?: boolean;
  element?: string; // not support lazy element, ensure element is in the DOM
}


export type PropType = 'string' | 'number' | 'boolean' | 'object' | 'function' | 'array';
export type SerializationType = 'json' | 'dotify' | 'base64';

export interface PropDefinitionContext<P = any> {
  props: P;
  state: Record<string, any>;
  close: () => Promise<undefined>;
  focus: () => Promise<undefined>;
  onError: (error: any) => Promise<undefined>;
  container: HTMLElement | undefined;
  event: Event;
}

export interface PropValidateOptions<P = any> {
  value: any;
  props: P;
}

export interface PropDefinition<P = any> {
  type: PropType;
  required?: boolean;
  default?: (context: PropDefinitionContext<P>) => any;
  validate?: (options: PropValidateOptions<P>) => void;
  queryParam?: boolean | string | ((value: any) => string);
  value?: (context: PropDefinitionContext<P>) => any;
  decorate?: (context: PropDefinitionContext<P> & { value: any }) => any;
  serialization?: SerializationType;
  alias?: string;
}

export  interface TemplateOptions {
  uid: string;
  props: Record<string, any>;
  doc: Document;
  container: HTMLElement;
  dimensions: Dimensions;
  tag: string;
  context: 'iframe' | 'popup' | 'dynamic';
  frame?: HTMLElement;
  prerenderFrame?: HTMLElement;
  close: () => void;
  focus: () => void;
  event: {
    on: (eventName: string, callback: () => void) => void;
  };
}

export type DomainType = string | RegExp;

export interface ZoidComponentOptions<P = any, E = any> {
  tag: string;
  url: string | ((opts: { props: P }) => string);
  dimensions?: Dimensions;
  props?: Record<string, PropDefinition>;
  autoResize?: AutoResize;
  allowedParentDomains?: string | Array<DomainType>;
  domain?: string;
  defaultContext?: 'iframe' | 'popup';
  validate?: (opts: { props: P }) => void;
  eligible?: (opts: { props: P }) => boolean;
  template?: {
    name: 'dynamic' | 'iframe' | 'popup';
    styles?: Record<string, string>;
    backdrop?: boolean;
    backdropBgColor?: string;
  };
}

export type Easings = 
  | 'linear'
  | 'in-cubic'
  | 'in-quartic'
  | 'in-quintic'
  | 'in-exponential'
  | 'in-circular'
  | 'in-elastic'
  | 'out-cubic'
  | 'out-quartic'
  | 'out-quintic'
  | 'out-exponential'
  | 'out-circular'
  | 'out-elastic'
  | 'in-out-cubic'
  | 'in-out-quartic'
  | 'in-out-quintic'
  | 'in-out-exponential'
  | 'in-out-circular'
  | 'in-out-elastic';

export type AnimateValue = [string | number, string | number];

export type AnimateOptions = {
  easing?: Easings;
  duration: number;
} & {
  [K in keyof React.CSSProperties]?: AnimateValue;
}

export type XProps<P = object> = {
  // Core functionality
  close: () => Promise<void>;
  focus: () => Promise<void>;
  resize: (dimensions: { width: number; height: number }) => Promise<void>;

  // Component identification
  uid: string;
  tag: string;

  // Parent window interactions
  getParent: () => Window;
  getParentDomain: () => string;

  // Visibility controls
  show: () => Promise<void>;
  hide: () => Promise<void>;

  // Data communication
  export: (exports: E) => Promise<void>;
  onError: (error: Error) => Promise<void>;
  onProps: (handler: (props: P) => void) => void;

  // Add new methods
  animate: (options: AnimateOptions) => Promise<void>;
  css: (styles: Record<string, string>) => void;
  layout: {
    middle: string;
    top: string;
    bottom: string;
  };
} & P;


export interface ZoidComponentInstance<P = any, E = any> extends XProps<P, E> {
  rendered: boolean
  renderTo: (w: Window, layout: LayoutPosition, type?: 'popup' | 'iframe') => void;
  event: {
    on: (eventName: string, handler: (...args: any[]) => void) => ({cancel: () => void});
    once: (eventName: string, handler: (...args: any[]) => void) => ({cancel: () => void});
    trigger: (eventName: string, args?: object) => Promise<void>;
    triggerOnce: (eventName: string, args?: object) => Promise<void>;
  };
  updateProps: (props: Partial<P>) => void;
  hide: () => Promise<void>;
  show: () => Promise<void>;
  close: () => Promise<void>;
  focus: () => Promise<void>;
}

// Add Euroland interface at the bottom
export interface Euroland {
  components: Record<string, (props: any) => ZoidComponentInstance>;
  createComponent: (
    componentName: string,
    config: ZoidComponentOptions
  ) => ZoidComponentInstance;
  integrate: (p: { toolName: string; companyCode?: string; lang?: string }) => Promise<void>;
  EVENT: {
    ANIMATE: string;
    RENDER: string;
    RENDERED: string;
    DISPLAY: string;
    ERROR: string;
    CLOSE: string;
    PROPS: string;
    RESIZE: string;
    BACKDROP_CLICKED: string;
  };
}
