using System.Text.RegularExpressions;
using System.Threading.Tasks;
using HotChocolate;
using HotChocolate.Data.Projections.Context;
using HotChocolate.Resolvers;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Middleware;

public class ExchangeCurrencyValidationMiddleware
{
  private readonly FieldDelegate _next;
  private readonly Regex _regex = new Regex("^[a-zA-Z]{3}$");

  public ExchangeCurrencyValidationMiddleware(FieldDelegate next)
  {
    _next = next;
  }

  public async ValueTask InvokeAsync(IMiddlewareContext context)
  {
    try {
      var exachangeCurrency = context.ArgumentValue<string>(HasExchangeCurrencyArgumentIResolverContextExtensions.EXCHANGE_CURRENCY_ARGUMENT);
      if(!string.IsNullOrEmpty(exachangeCurrency) && !_regex.IsMatch(exachangeCurrency)) {
        context.ReportError("Invalid value of exchange currency");
        return;
      }

      // Add exchangecurrency to the hierarchy, so that other middlewares and resolvers
      // can access to it.
      context.ScopedContextData = context.ScopedContextData.Add(HasExchangeCurrencyArgumentIResolverContextExtensions.EXCHANGE_CURRENCY_ARGUMENT, exachangeCurrency);
    }
    catch(GraphQLException) {}

    await _next(context).ConfigureAwait(false);
  }
}
