using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities;

/// <summary>
/// Class to map with Performance Share query only
/// </summary>
public class PerformanceShare
{
  public int InstrumentId { get; set; }
  public int Period { get; set; }

  /// <summary>
  /// The request date from client to evaluate price from.
  /// </summary>
  /// <value></value>
  [Column(TypeName = "SMALLDATETIME")]
  public DateTime? FromDate { get; set; }

  [Column(TypeName = "DECIMAL(18,4)")]
  public decimal? ChangePercentage { get; set; }

  [Column(TypeName = "MONEY")]
  public decimal? High { get; set; }

  [Column(TypeName = "DATETIME")]
  public DateTime? HighDate { get; set; }

  [Column(TypeName = "MONEY")]
  public decimal? Low { get; set; }

  [Column(TypeName = "DATETIME")]
  public DateTime? LowDate { get; set; }

  [Column(TypeName = "MONEY")]
  public decimal? ClosePrice { get; set; }

  [Column(TypeName = "DECIMAL(28,10)")]
  public decimal? CloseRate { get; set; }
}
