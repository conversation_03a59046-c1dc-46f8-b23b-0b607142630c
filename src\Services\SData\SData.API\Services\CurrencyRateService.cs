using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using Euroland.FlipIT.SData.API.Services.Interfaces;
using Euroland.FlipIT.SData.API.Helpers;
using System;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.DTOs.Deprecated.Currencies;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.Services
{
  public class CurrencyRateService : ICurrencyRateService, ICurrencyRateTransformationStrategy
  {
    readonly DateTime epoch = DateTime.UnixEpoch;
    private readonly IDistributedCache _cache;
    private readonly DistributedCachingConfiguration _cacheSettings;
    private readonly IServiceScopeFactory _serviceScope;
    private readonly IDbContextFactory<DefaultSharkDbContext> _defaultContextFactory;
    private readonly IDbContextFactory<RealtimeSharkDbContext> _realtimeContextFactory;
    private SharkDbContextBase _localSharkDbContext;
    private SharkDbContextBase _realtimeSharkDbContext;

    public DateTime CESTNow
    {
      get
      {
        return DateTime.UtcNow.ToCEST();
      }
    }


    public CurrencyRateService(
        IDbContextFactory<DefaultSharkDbContext> defaultContextFactory,
        IDbContextFactory<RealtimeSharkDbContext> realtimeContextFactory,
        IDistributedCache cache,
        IOptions<DistributedCachingConfiguration> cacheSettings,
        IServiceScopeFactory serviceScope = null)
    {
      _defaultContextFactory = defaultContextFactory ?? throw new ArgumentNullException(nameof(defaultContextFactory));
      _realtimeContextFactory = realtimeContextFactory ?? throw new ArgumentNullException(nameof(realtimeContextFactory));
      _cache = cache ?? throw new ArgumentNullException(nameof(cache));
      _cacheSettings = cacheSettings?.Value;
      _serviceScope = serviceScope;
    }

    private ISharkDbContext CreateSharkDbContext(bool useRealtimeDatabase)
    {
      if (useRealtimeDatabase)
      {
        if (_realtimeSharkDbContext == null)
        {
          _realtimeSharkDbContext = _realtimeContextFactory.CreateDbContext();
        }

        return _realtimeSharkDbContext;
      }
      else
      {
        if (_localSharkDbContext == null)
        {
          _localSharkDbContext = _defaultContextFactory.CreateDbContext();
        }

        return _localSharkDbContext;
      }
    }

    /// <inheritdoc/>
    public async Task<CurrencyRate> GetCurrentRateAsync(string baseCurrency, string quoteCurrency, bool isRT = false)
    {
      if (string.IsNullOrEmpty(baseCurrency))
      {
        throw new ArgumentNullException(nameof(baseCurrency));
      }

      if (string.IsNullOrEmpty(quoteCurrency))
      {
        throw new ArgumentNullException(nameof(quoteCurrency));
      }

      if (baseCurrency.Length != 3)
      {
        throw new ArgumentOutOfRangeException(nameof(baseCurrency), "Invalid currency length. The length of currency must be 3 characters length");
      }
      if (quoteCurrency.Length != 3)
      {
        throw new ArgumentOutOfRangeException(nameof(quoteCurrency), "Invalid currency length. The length of currency must be 3 characters length");
      }

      var ctx = CreateSharkDbContext(isRT);

      var BaseQuoteCurrency = $"{baseCurrency}{quoteCurrency}".ToUpper();

      var list = await ctx.CurrencyRates.AsNoTracking().ToListAsync();
#pragma warning disable CS8603 // Possible null reference return.
      return await ctx.CurrencyRates.AsNoTracking()
              .Where(curr => curr.Currencies == BaseQuoteCurrency)
              .Select(curr => new CurrencyRate
              {
                Date = curr.Date.ToKindUtc(),
                Value = curr.Rate ?? 0
              })
              .FirstOrDefaultAsync();
#pragma warning restore CS8603 // Possible null reference return.
    }

    public async Task<IEnumerable<ConvertCurrencyRate>> GetCurrentRatesAsync(List<string> baseCurrencies,
        string quoteCurrency, bool isRT = false)
    {
      if (string.IsNullOrEmpty(quoteCurrency))
      {
        throw new ArgumentNullException(nameof(quoteCurrency));
      }

      if (quoteCurrency.Length != 3)
      {
        throw new ArgumentOutOfRangeException(nameof(quoteCurrency),
            "Invalid currency length. The length of currency must be 3 characters length");
      }

      var baseQuoteCurrencies = new List<string>();

      foreach (var baseCurrency in baseCurrencies)
      {
        if (string.IsNullOrEmpty(baseCurrency))
        {
          throw new ArgumentNullException(nameof(baseCurrencies), "All currency in baseCurrencies list must not be null or empty.");
        }

        if (baseCurrency.Length != 3)
        {
          throw new ArgumentOutOfRangeException(nameof(baseCurrencies), "All currency in baseCurrencies list must be 3 characters length.");
        }

        baseQuoteCurrencies.Add($"{baseCurrency}{quoteCurrency}".ToUpper());
      }

      var ctx = CreateSharkDbContext(isRT);

      var result = await ctx.CurrencyRates.AsNoTracking()
          .Where(curr => baseQuoteCurrencies.Contains(curr.Currencies))
          .Select(curr => new ConvertCurrencyRate()
          {
            BaseCurrency = !string.IsNullOrEmpty(curr.Currencies)
                  ? curr.Currencies.Substring(0, 3)
                  : string.Empty,
            QuoteCurrency = quoteCurrency.ToUpper(),
            Date = curr.Date.ToKindUtc(),
            Value = curr.Rate ?? 0
          }).ToListAsync();

      return result;
    }

    public async Task<decimal> GetClosestRateAsync(string baseCurrency, string quoteCurrency, DateTime date, bool isRT = false)
    {
      if (baseCurrency.Length != 3)
      {
        throw new ArgumentOutOfRangeException(nameof(baseCurrency), "Invalid currency length. The length of currency must be 3 characters length");
      }
      if (quoteCurrency.Length != 3)
      {
        throw new ArgumentOutOfRangeException(nameof(quoteCurrency), "Invalid currency length. The length of currency must be 3 characters length");
      }

      if (date < epoch)
      {
        throw new ArgumentException($"`{nameof(date)}` value is invalid.", nameof(date));
      }

      var (baseQuoteCurrency, factor, customRate) = await GetTransformedCurrencyRateAsync(baseCurrency, quoteCurrency);
      if (customRate.HasValue)
      {
        return customRate.Value;
      }

      var ctx = CreateSharkDbContext(isRT);

      var currencyRate = await ctx.CurrencyRates
        .AsNoTracking()
        .FirstOrDefaultAsync(curr => curr.Currencies == baseQuoteCurrency);

      if (currencyRate is { Date: not null, Rate: not null } && currencyRate.Date.Value.Date == date.Date)
      {
        return (decimal)currencyRate.Rate * (decimal)factor;
      }

      var historyRate = await ctx.CurrencyRateHistories
        .AsNoTracking()
        .OrderByDescending(h => h.Date)
        .FirstOrDefaultAsync(h =>
          currencyRate != null && h.CurrencyRateID == currencyRate.ID && h.Date.Date <= date.Date);
      if (historyRate != null)
      {
        return historyRate.Rate * (decimal)factor;
      }

      return 1;
    }

    public async Task<IEnumerable<CurrencyRate>> GetRatesAsync(string baseCurrency, string quoteCurrency,
      DateTime fromUTC, DateTime? toUTC = null, bool isRT = false)
    {
      var currencyRates = await GetRateHistoryAsync(baseCurrency, quoteCurrency, fromUTC, toUTC, isRT);
      if (!toUTC.HasValue || toUTC.Value.Date == DateTime.UtcNow.Date)
      {
        var currentRate = await GetCurrentRateAsync(baseCurrency, quoteCurrency, isRT);
        if (currentRate != null)
        {
          currencyRates = currencyRates.Append(new CurrencyRate()
          {
            Date = DateTime.UtcNow.Date,
            Value = currentRate?.Value ?? 1
          });
        }
      }

      return currencyRates;
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<CurrencyRate>> GetRateHistoryAsync(string baseCurrency, string quoteCurrency, DateTime fromUTC, DateTime? toUTC = null, bool isRT = false)
    {
      if (string.IsNullOrEmpty(baseCurrency))
      {
        throw new ArgumentNullException(nameof(baseCurrency));
      }

      if (string.IsNullOrEmpty(quoteCurrency))
      {
        throw new ArgumentNullException(nameof(quoteCurrency));
      }

      if (baseCurrency.Length != 3)
      {
        throw new ArgumentOutOfRangeException(nameof(baseCurrency), "Invalid currency length. The length of currency must be 3 characters length");
      }
      if (quoteCurrency.Length != 3)
      {
        throw new ArgumentOutOfRangeException(nameof(quoteCurrency), "Invalid currency length. The length of currency must be 3 characters length");
      }

      if (toUTC != null && (toUTC <= epoch))
      {
        throw new ArgumentException($"`{nameof(toUTC)}` value is invalid.", nameof(toUTC));
      }

      if (fromUTC < epoch)
      {
        throw new ArgumentException($"`{nameof(fromUTC)}` value is invalid.", nameof(fromUTC));
      }

      if (toUTC == null)
      {
        toUTC = DateTime.UtcNow;
      }

      if (fromUTC > toUTC.Value)
      {
        throw new ArgumentException($"{nameof(fromUTC)} must be less than or equal {nameof(toUTC)}");
      }

      var fromDateCEST = fromUTC.Date;
      var toDateCEST = toUTC.Value.Date;

      var (baseQuoteCurrency, factor, customRate) = await GetTransformedCurrencyRateAsync(baseCurrency, quoteCurrency);
      if (customRate.HasValue)
      {
        return new[] {
                    new CurrencyRate{
                        Date = fromDateCEST,
                        Value = customRate.Value
                    }
                };
      }

      var ctx = CreateSharkDbContext(isRT);

      var cRates = ctx.CurrencyRates.AsNoTracking();
      var cRatesH = ctx.CurrencyRateHistories.AsNoTracking();

      var closestFromDateHasData = await cRatesH
          .Join(
              cRates,
              h => h.CurrencyRateID,
              c => c.ID,
              (h, c) => new { RateHistory = h, CurrencyRate = c })
          .Where(
              joined => joined.CurrencyRate.Currencies == baseQuoteCurrency && fromDateCEST >= joined.RateHistory.Date
          // System.Data.Entity.SqlServer.SqlFunctions.DateDiff() throws exception in Unit Test when InMemoryDB is being used.
          //&& System.Data.Entity.SqlServer.SqlFunctions.DateDiff("day", joined.RateHistory.Date, fromDateCEST) >= 0
          )
          .Select(joined => joined.RateHistory.Date)
          .MaxAsync(x => (DateTime?)x.Date);

      if (!closestFromDateHasData.HasValue)
      {
        closestFromDateHasData = fromDateCEST;
      }

      return await cRatesH.Join(
          cRates,
          h => h.CurrencyRateID,
          c => c.ID,
          (h, c) => new { RateHistory = h, CurrencyRate = c })
      .Where(
          joined => joined.CurrencyRate.Currencies == baseQuoteCurrency
          && joined.RateHistory.Date >= closestFromDateHasData
          && joined.RateHistory.Date <= toDateCEST
      )
      .Select(joined => new CurrencyRate
      {
        Date = DateTime.SpecifyKind(joined.RateHistory.Date, DateTimeKind.Utc),
        Value = joined.RateHistory.Rate * (decimal)factor
      })
      .ToListAsync();
    }

    private async Task<(string baseQuoteCurrency, float factor, decimal? customRate)> GetTransformedCurrencyRateAsync(string baseCurrency, string quoteCurrency)
    {
      var baseQuoteCurrency = $"{baseCurrency}{quoteCurrency}".ToUpper();
      var factor = 1f;

      var strategy = GetTrasformationStrategy();
      if (strategy != null)
      {
        var transform = await strategy.GetTransformAsync(baseCurrency, quoteCurrency);
        if (transform != null)
        {
          switch (transform)
          {
            case CustomRateTransformation transformation:
              return (baseQuoteCurrency, factor, (decimal)transformation.CustomRate);
            case QuoteCurrencyTransformation quoted:
              baseQuoteCurrency = $"{baseCurrency}{quoted.TransformTo}".ToUpper();
              factor = quoted.Factor;
              break;
            case BaseCurrencyTransformation based:
              baseQuoteCurrency = $"{based.TransformTo}{quoteCurrency}".ToUpper();
              factor = based.Factor;
              break;
          }
        }
      }

      return (baseQuoteCurrency, factor, null);
    }


    public Task<CurrencyTransformationBase> GetTransformAsync(string baseCurrency, string quoteCurrency)
    {
      using (var scope = _serviceScope.CreateScope())
      {
        var _currencyRateTransformationSettings = scope.ServiceProvider.GetService<IOptions<CurrencyTransformationConfiguration>>()?.Value;

        if (_currencyRateTransformationSettings == null
        || !_currencyRateTransformationSettings.Enabled
        || _currencyRateTransformationSettings.Transforms == null
        || !_currencyRateTransformationSettings.Transforms.Any())
        {
          return Task.FromResult<CurrencyTransformationBase>(null);
        }

        return Task.FromResult(_currencyRateTransformationSettings.Transforms.Where(trs => FindTransformation(trs, baseCurrency, quoteCurrency)).Select(trs => trs).FirstOrDefault());
      }
    }


    private bool FindTransformation(CurrencyTransformationBase transform, string baseCurrency, string quoteCurrency)
    {
      if (transform is CustomRateTransformation)
      {
        var customRate = transform as CustomRateTransformation;
        if (
            string.Equals(customRate.BaseCurrency, baseCurrency, StringComparison.InvariantCultureIgnoreCase)
            && string.Equals(customRate.QuoteCurrency, quoteCurrency, StringComparison.InvariantCultureIgnoreCase)
        )
        {
          return true;
        }
      }

      if (transform is QuoteCurrencyTransformation)
      {
        var quote = transform as QuoteCurrencyTransformation;
        if (string.Equals(quote.QuoteCurrency, quoteCurrency, StringComparison.InvariantCultureIgnoreCase))
        {
          return true;
        }
      }

      if (transform is BaseCurrencyTransformation)
      {
        var _base = transform as BaseCurrencyTransformation;
        if (string.Equals(_base.BaseCurrency, baseCurrency, StringComparison.InvariantCultureIgnoreCase))
        {
          return true;
        }
      }

      return false;
    }

    private ICurrencyRateTransformationStrategy GetTrasformationStrategy()
    {
      var srv = (ICurrencyRateTransformationStrategy)null;
      if (_serviceScope != null)
      {
        using (var scope = _serviceScope.CreateScope())
        {
          srv = scope.ServiceProvider.GetService<ICurrencyRateTransformationStrategy>();
        }
      }

      return srv;
    }

    public async ValueTask DisposeAsync()
    {
      if (_localSharkDbContext != null)
      {
        await _localSharkDbContext.DisposeAsync();
      }
      if (_realtimeSharkDbContext != null)
      {
        await _realtimeSharkDbContext.DisposeAsync();
      }
    }

    public void Dispose()
    {
      DisposeAsync().ConfigureAwait(false).GetAwaiter().GetResult();
    }
  }
}
