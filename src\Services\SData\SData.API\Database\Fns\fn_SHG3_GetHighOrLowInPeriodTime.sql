USE [shark]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


-- =============================================
-- Author:		<AnhNT>
-- Create date: <19/12/2023>
-- Description:	<Get highest or lowest price from date>
CREATE FUNCTION [dbo].[fn_SHG3_GetHighOrLowInPeriodTime] ( @instrumentId AS INT,
  @dateFrom AS datetime2,
  @dateTo AS datetime2,
  @isGetHigh AS bit = 1 ) RETURNS money AS BEGIN
	DECLARE
		@result money;
	IF ( @isGetHigh = 1 ) 
			SELECT
			@result = MAX ( [High] ) 
			FROM
				InstrumentHistory 
			WHERE
				InstrumentId = @instrumentId 
				AND [Date] <= @dateTo 
				AND [Date] >= @dateFrom 
	ELSE 
			SELECT
				@result = MIN ( [Low] )
			FROM
				InstrumentHistory 
			WHERE
				InstrumentId = @instrumentId 
				AND [Date] <= @dateTo 
			AND [Date] >= @dateFrom 
	
	RETURN @result 
END