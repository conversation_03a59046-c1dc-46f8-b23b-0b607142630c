// using System.Linq;
// using System.Threading.Tasks;
// using Euroland.FlipIT.SData.API.Dto;
// using Euroland.FlipIT.SData.API.Infrastructure;
// using entity = Euroland.FlipIT.SData.API.Infrastructure.Entities;
// using GreenDonut;
// using HotChocolate.Resolvers;
// using HotChocolate.Types;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.Extensions.DependencyInjection;
// using System.Collections.Generic;
// using System.Threading;
// using System;

// namespace Euroland.FlipIT.SData.API.GraphQL.Types.PressRelease;

// public class PressReleaseByIdDataLoader : BatchDataLoader<long, PressReleaseDto>
// {
//   private readonly IDbContextFactory<NewsContext> _newDbContextFactory;
//   public PressReleaseByIdDataLoader(
//     IDbContextFactory<NewsContext> newDbContextFactory,
//     IBatchScheduler batchScheduler,
//     DataLoaderOptions? options = null)
//     : base(batchScheduler, options)
//   {
//     _newDbContextFactory = newDbContextFactory ?? throw new ArgumentNullException(nameof(newDbContextFactory));
//   }

//   // public static IDataLoader<long, PressReleaseDto> PressReleaseByIdDataLoader(
//   //   this IResolverContext resolverContext,
//   //   bool? includeHidden = true,
//   //   bool? groupByMessageType = false,
//   //   string? companyCode = null
//   //   ) => resolverContext.BatchDataLoader<long, PressReleaseDto>(async (keys, ct) =>
//   protected override async Task<IReadOnlyDictionary<long, PressReleaseDto>> LoadBatchAsync(IReadOnlyList<long> keys, CancellationToken cancellationToken)
//   {

//     using var newsContext = await _newDbContextFactory.CreateDbContextAsync();

//     var ungroupGroup = !(groupByMessageType ?? false);

//     var pressReleases = newsContext.PressReleases.AsNoTracking();
//     var messageTypes = newsContext.MessageTypes.AsNoTracking();

// #pragma warning disable S3358 // Ternary operators should not be nested
// #pragma warning disable S3358 // Ternary operators should not be nested
//     var resultQuery = (
//       from p in pressReleases
//       where keys.Contains(p.Id) && (companyCode == null || companyCode == p.CompanyCode)
//       join m1 in messageTypes
//         on new { mtTypeId = p.MessageTypeId, p.SourceId } equals new { mtTypeId = m1.Id, m1.SourceId } into mtTypeGrouping
//       from g1 in mtTypeGrouping.DefaultIfEmpty()
//       join m2 in messageTypes
//         on new { messageTypeGroup = g1.MessageGroupId, g1.SourceId } equals new { messageTypeGroup = m2.MessageGroupId, m2.SourceId } into mtTypeGroupGrouping
//       from g2 in mtTypeGroupGrouping.Where(t => t.MessageGroupTypeId == null).DefaultIfEmpty()
//       select new entity.PressRelease
//       {
//         Id = p.Id,
//         CompanyCode = p.CompanyCode,
//         DateTime = p.DateTime,
//         InsertedDate = p.InsertedDate,
//         IsHidden = p.IsHidden,
//         // We instantiate a new PressRelease object here to remove [Message] out of SELECT clause
//         // because of [Message] has large content and not good performance to select multiple records with [Message].
//         Message = null,
//         HasAttachment = p.HasAttachment,
//         LanguageId = p.LanguageId,
//         MessageGroupId = p.MessageGroupId,
//         MessageTypeId = p.MessageTypeId == null ? 0 : ungroupGroup ? g1.Id : g2.Id,
//         SourceId = p.SourceId,
//         Title = p.Title,
//         MessageType = p.MessageTypeId == null ? null : ungroupGroup ? g1 : g2
//       }
//     ).Distinct();
// #pragma warning restore S3358 // Ternary operators should not be nested
// #pragma warning restore S3358 // Ternary operators should not be nested

//     var pressreleases = await resultQuery
//       .WithAutoMapper()
//       .ToProjection<PressReleaseDto>()
//       .ToListAsync();

//     return await Task.FromResult(pressreleases.ToDictionary(x => x.Id));
//   });
// }
