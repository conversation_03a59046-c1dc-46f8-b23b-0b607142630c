async function query<T>(query: string, variables: any): Promise<T> {
    const response = await fetch(import.meta.env.VITE_API_SDATA_URL, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({ query, variables }),
    });

    return (await response.json()).data;
}

export type TickerResponse = {
  instrumentByIds: {
    currencyCode: string;
    currentPrice: { last: number; changePercentage: number; date: string };
    market: { abbreviation: string };
  }[];
};

export async function getTickers(ids: number[]): Promise<TickerResponse> {
  return query(`query Tickers($ids: [Int!]!) {
    instrumentByIds(ids: $ids) {
      currencyCode
      currentPrice {
        last
        changePercentage
        date
      }
      market {
        abbreviation
      }
    }
  }
  `, { ids });
}
