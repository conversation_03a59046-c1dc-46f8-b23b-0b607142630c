using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;

internal static class TimezoneOffset
{
  /// <summary>
  /// Retrieves the timezone offsets for Central European Time (CET) and the market's timezone
  /// relative to UTC for a given instrument.
  /// By using DATEADD(...) to convert between timezones instead of AT TIME ZONE in order to reduce
  /// the CPU resource consumsion as it is expensive on SQL Azure.
  /// </summary>
  /// <param name="sharkDbContext">The database context used to execute the SQL query.</param>
  /// <param name="instrumentId">The ID of the instrument for which to retrieve the timezone offsets.</param>
  /// <returns>
  /// A tuple containing two integers:
  /// <list type="number">
  /// <item>
  /// <description>The CET timezone offset in minutes.</description>
  /// </item>
  /// <item>
  /// <description>The market's timezone offset in minutes relative to CET.</description>
  /// </item>
  /// </list>
  /// </returns>
  public static (int, int) GetTimezoneOffset(DbContext sharkDbContext, int instrumentId)
  {
    var instrumentIdParam = new SqlParameter("instrumentId", System.Data.SqlDbType.Int) { Value = instrumentId };

    var cetTimezoneOffsetParam = new SqlParameter{
      ParameterName = "cet_offset",
      Value = 0,
      SqlDbType = System.Data.SqlDbType.Int,
      Direction = System.Data.ParameterDirection.Output
    };

    var marketTimezoneOffsetParam = new SqlParameter {
      ParameterName = "market_offset",
      Value = 0,
      SqlDbType = System.Data.SqlDbType.Int,
      Direction = System.Data.ParameterDirection.Output
    };

    /*
    In SQL Server, the DATEPART(TZOFFSET, ...) function specifically returns timezone offsets
    as integer values measured in minutes, not as floating point values.
    This is by design because all standard time zones have offsets in whole minutes from UTC.
    The TZOFFSET datepart returns the offset in minutes, so even if a time zone were to have
    a fractional hour offset (like some historical time zones that had 30-minute or 45-minute offsets from UTC),
    the result would still be an integer number of minutes.
    */
    sharkDbContext.Database.ExecuteSqlRaw(@$"
      DECLARE @reference_date DATETIME2 = SYSUTCDATETIME()
      DECLARE @cet DATETIMEOFFSET = @reference_date AT TIME ZONE 'Central European Standard Time';

      SET @{cetTimezoneOffsetParam.ParameterName} = DATEPART(TZOFFSET, @cet);

      SELECT @{marketTimezoneOffsetParam.ParameterName} = (DATEPART(TZOFFSET, @reference_date AT TIME ZONE m.Timezone) - @cet_offset)
      FROM Market m
      INNER JOIN Instrument i
      ON m.MarketNumber = i.MarketID
      WHERE i.Id = @instrumentId;
    ", instrumentIdParam,
      cetTimezoneOffsetParam,
      marketTimezoneOffsetParam);

    return ((int)(cetTimezoneOffsetParam.Value ?? 0), (int)(marketTimezoneOffsetParam.Value ?? 0));
  }
}
