using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.Fundamental.API.Persistance.FinCalender;

[Table("EventDetail")]
public class FinancialEventDetail
{
  [Key]
  public int Id { get; set; }

  [Column("EventID"), ForeignKey("FinancialEvent")]
  public int FinancialEventId { get; set; }
  public FinancialEvent FinancialEvent { get; set; }

  public int LanguageId { get; set; }
  public string Title { get; set; }
  public string? Description { get; set; }
  public string? Location { get; set; }
  public string? LinkUrl { get; set; }
  public string? LinkDescription { get; set; }
}
