using Euroland.FlipIT.Fundamental.API.AutoMappers;
using Euroland.FlipIT.Fundamental.API.Dtos;
using Euroland.FlipIT.Fundamental.API.Dtos.FinCalender;
using Euroland.FlipIT.Fundamental.API.Persistance;
using Euroland.NetCore.ToolsFramework.Localization;
using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.Fundamental.API.GraphQL.Types.FinCalender;

public class FincalenderResolver
{
  public IQueryable<FinancialEventDetailDto> GetFinancialEventsByCompanyCode(
    [Parent] FundamentalDto fundamental,
    [Service] ILanguageToCultureProvider languageToCultureProvider,
    string cultureName,
    FinCalendarDbContext finCalendarDbContext
  )
  {
    var language = languageToCultureProvider.GetLanguage(cultureName);
    return finCalendarDbContext.FinancialEventDetails
      .AsNoTracking()
      .Include(fed => fed.FinancialEvent)
      .Where(fed => fed.FinancialEvent.CompanyCode.ToLower().Equals(fundamental.CompanyCode.ToLower()) &&
                    language != null && fed.LanguageId == language.DbId)
      .WithAutoMapper()
      .ToProjection<FinancialEventDetailDto>();
  }

  public IQueryable<FinancialEventTypeDetailDto> GetFinancialEventTypesByCompanyCode(
    [Parent] FundamentalDto fundamental,
    [Service] ILanguageToCultureProvider languageToCultureProvider,
    string cultureName,
    FinCalendarDbContext finCalendarDbContext
  )
  {
    var language = languageToCultureProvider.GetLanguage(cultureName);
    return finCalendarDbContext.FinancialEventTypeDetails
      .AsNoTracking()
      .Include(fetd => fetd.FinancialEventType)
      .Where(fetd => fetd.FinancialEventType.CompanyCode.ToLower().Equals(fundamental.CompanyCode.ToLower()) &&
                     language != null && fetd.LanguageId == language.DbId)
      .WithAutoMapper()
      .ToProjection<FinancialEventTypeDetailDto>();
  }

  public IQueryable<FinancialEventAttachmentDto> GetAttachmentsByFinancialEvent(
    [Parent] FinancialEventDetailDto financialEvent,
    FinCalendarDbContext finCalendarDbContext)
  {
    return finCalendarDbContext.FinancialEventAttachments
      .AsNoTracking()
      .Where(fea => fea.EventDetailId == financialEvent.EventDetailId)
      .WithAutoMapper()
      .ToProjection<FinancialEventAttachmentDto>();
  }

  public IQueryable<FinancialEventStatisticsDto> GetStatisticsByFinancialEvent(
    [Parent] FinancialEventDetailDto financialEvent,
    FinCalendarDbContext finCalendarDbContext)
  {
    return finCalendarDbContext.FinancialEventStatistics
      .AsNoTracking()
      .Where(fes => fes.EventId == financialEvent.EventId)
      .WithAutoMapper()
      .ToProjection<FinancialEventStatisticsDto>();
  }
}
