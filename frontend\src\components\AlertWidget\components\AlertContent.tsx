import { FC, useEffect, useRef } from "react";
import { v4 as uuidv4 } from "uuid";
import { toast } from "sonner";
import { i18n } from "@euroland/libs";
import clsx from "clsx";
import { ToastContent } from "./ToastContent";

export const AlertContent: FC = () => {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

useEffect(() => {
    const randomNotify = () => {
      const newNotification = {
        key: uuidv4(),
        type: (Math.random() < 0.5 ? "positive" : "negative") as
          | "positive"
          | "negative"
          | "info",
        text: i18n.translate(
          `notification.price${Math.random() < 0.5 ? "Up" : "Down"}`,
          {
            percent: (Math.random() * 10).toFixed(1),
          }
        ),
      };
      const toastId = toast(
        <ToastContent
          type={newNotification.type}
          text={newNotification.text}
          handleClose={() => toast.dismiss(toastId)}
        />,
        {
          className: clsx("wise-notification-bubble", {
            "wise-notification-positive": newNotification.type === "positive",
            "wise-notification-negative": newNotification.type === "negative",
            "wise-notification-info": newNotification.type === "info",
          }),
          duration: 50000,
        }
      );
    }

    randomNotify()
    intervalRef.current = setInterval(randomNotify, 60000);

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, []);

  return null;
}; 