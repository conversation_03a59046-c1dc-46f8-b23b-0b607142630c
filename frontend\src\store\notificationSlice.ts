import {StateCreator} from "zustand";
import type {IStoreState} from "./useAppStore";
import {produce} from "immer";

export interface INotificationSlice {
  isNotificationOpen: boolean
  setNotificationOpen: (isOpen: boolean) => void
} 

export const notificationSlice: StateCreator<IStoreState, [], [], INotificationSlice> = (set) => ({
  isNotificationOpen: true,
  setNotificationOpen: (isOpen: boolean) => set(produce(draft => {
    draft.isNotificationOpen = isOpen
  })),
})