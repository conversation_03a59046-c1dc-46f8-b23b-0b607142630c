using Euroland.FlipIT.SData.API.Infrastructure.Configurations;
using Euroland.FlipIT.SData.API.Infrastructure.Configurations.Timescale;
using Euroland.FlipIT.SData.API.Infrastructure.Entities.Timescale;
using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.SData.API.Infrastructure;

public class TimescaleDbContext : DbContext
{
  public TimescaleDbContext(DbContextOptions<TimescaleDbContext> options) : base(options)
  {
  }

  public DbSet<InstrumentHistoryTimescale> InstrumentHistoryTimescales { get; set; }

  protected override void OnModelCreating(ModelBuilder modelBuilder)
  {
    base.OnModelCreating(modelBuilder);
    modelBuilder.ApplyConfiguration(new InstrumentHistoryTimescaleConfiguration());
    modelBuilder.ApplyConfiguration(new CurrencyRateTimescaleConfiguration());
    modelBuilder.ApplyConfiguration(new CurrencyRateHistoryTimescaleConfiguration());
    modelBuilder.ApplyConfiguration(new TemporaryCurrencyRateHistoryTimescaleConfiguration());
  }
}
