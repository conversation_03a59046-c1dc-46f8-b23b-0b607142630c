﻿using Euroland.FlipIT.SData.API.DTOs.Deprecated.Webcasts;
using Euroland.FlipIT.SData.API.GraphQL.Deprecated.DataLoaders;
using HotChocolate;
using HotChocolate.Data;
using HotChocolate.Types;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Euroland.FlipIT.SData.API.GraphQL.Deprecated.Webcast
{
  [ExtendObjectType(OperationTypeNames.Query)]
    public class WebcastQueries
    {
        [GraphQLDeprecated("This field may be removed in the next releas. Use company->webcasts instead.")]
        [UseProjection]
        public async Task<IQueryable<WebcastDetailOutputDto>> GetWebcasts(WebcastDataLoader dataloader,
                                                        string companyCode,
                                                        DateTime from,
                                                        DateTime? to = null,
                                                        CancellationToken cancellationToken = default)
        {
            return await dataloader.LoadByCompanyCode(companyCode, from, to, cancellationToken);
        }

        [GraphQLDeprecated("This field may be removed in the next releas. Use company->webcast instead.")]
        [UseProjection]
        public async Task<WebcastDetailOutputDto> GetWebcast(WebcastDataLoader dataloader, long id)
        {
            return await dataloader.LoadAsync(id);
        }
    }
}
