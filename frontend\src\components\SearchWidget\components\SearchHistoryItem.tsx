import { FC, useLayoutEffect, useRef, useState } from "react";
import { IAiSearchHistory } from "@/services/ai";
import { ChevronDown, Trash } from "../../Common";
import { formatDate, i18n } from "@euroland/libs";
import clsx from "clsx";
import { appSettings } from "@/config/appSettings";
import AiResponse from "./AiResponse";

interface SearchHistoryItemProps {
  data: IAiSearchHistory;
  className?: string;
  onDelete: (id: string) => void;
}

const HistoryContent = ({children}: {children: React.ReactNode}) => {
  const innerRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    const container = containerRef.current;
    const inner = innerRef.current;
    if(!container || !inner) return;
    let innerHeight = inner.clientHeight;
    const handleResize = () => {
      if(inner.clientHeight !== innerHeight) {
        innerHeight = inner.clientHeight;
        container.style.setProperty("--history-content-height", `${innerHeight}px`);
      }
    }
    handleResize();
    const resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(inner);
    return () => resizeObserver.disconnect();
  }, []);
  
  return (<div className="history-content relative" ref={containerRef}> 
    <div className="history-content-inner" ref={innerRef}>
      {children}
    </div>
  </div>)
}

export const SearchHistoryItem: FC<SearchHistoryItemProps> = ({
  data,
  className,
  onDelete
}) => {
  const [isExpand, setIsExpand] = useState<boolean>(false);

  return (
    <div className={clsx("wise-widget-history-items", className)}>
      <div className={clsx("wise-widget-history-item", { expanded: isExpand })}>
        <div
          className="history-header"
          onClick={() => setIsExpand((isExpand) => !isExpand)}
        >
          <div className="history-summary">
            <span className="history-query">{data.question}</span>
            <span className="history-time">
              {formatDate(data.searchTime, "YYYY-MM-DD HH:mm:ss")}
            </span>
          </div>
          <ChevronDown />
        </div>
        <HistoryContent>
          <button
            className={clsx(
              "absolute top-3 p-1 text-gray-400 hover:text-red-500",
              {
                "right-3": !appSettings.isRTL,
                "left-3": appSettings.isRTL,
              }
            )}
            aria-label={i18n.translate("deleteSearchHistory")}
            onClick={() => onDelete(data.id)}
          >
            <Trash />
          </button>
          <h4 className="text-lg font-semibold mb-4">
            {i18n.translate("answer")}
          </h4>
          <div className="overflow-y-auto">
            <AiResponse
              qaLanguage={data.qaLanguage}
              aiResultLanguage={data.aiResultLanguage}
              answer={data.resultShow}
            />
          </div>
        </HistoryContent>
      </div>
    </div>
  );
};
