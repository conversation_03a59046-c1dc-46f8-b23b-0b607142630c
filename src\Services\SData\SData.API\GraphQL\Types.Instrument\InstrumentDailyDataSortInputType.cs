using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Data.Sorting;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;

/// <summary>
/// Custom sort type to enable ordering on <see cref="InstrumentDailyDataDto"/> fields.
/// </summary>
public class InstrumentDailyDataSortInputType: SortInputType<InstrumentDailyDataDto>
{
  protected override void Configure(ISortInputTypeDescriptor<InstrumentDailyDataDto> descriptor)
  {
    descriptor.BindFieldsExplicitly();
    descriptor.Field(f => f.Date).Type<DefaultSortEnumType>();
    descriptor.Field(f => f.Close).Type<DefaultSortEnumType>();
    descriptor.Field(f => f.Volume).Type<DefaultSortEnumType>();
  }
}
