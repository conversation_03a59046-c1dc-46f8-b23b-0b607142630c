﻿namespace Euroland.FlipIT.SData.API.Helpers
{
  public static class InstrumentHelper
    {
        public static string? TruncateMarketAbbreviation(string shareName, string? marketAbbreviation)
        {
            if (string.IsNullOrEmpty(marketAbbreviation) || string.IsNullOrEmpty(shareName))
            {
                return shareName;
            }
            var shareNameTrunctated = shareName.Replace($"({marketAbbreviation})", "");
            return shareNameTrunctated == null ? shareNameTrunctated : shareNameTrunctated.Trim();
        }
    }
}
