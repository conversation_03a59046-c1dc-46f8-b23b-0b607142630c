using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Euroland.FlipIT.SData.API.Infrastructure.Configurations;

// public class EverageSharePriceConfiguration: IEntityTypeConfiguration<EverageSharePrice>
// {
//     public void Configure(EntityTypeBuilder<EverageSharePrice> builder)
//     {
//         builder.ToView(nameof(EverageSharePrice));
//     }
// }

public class TotalDividendPerShareConfiguration: IEntityTypeConfiguration<TotalDividendPerShare>
{
    public void Configure(EntityTypeBuilder<TotalDividendPerShare> builder)
    {
        builder.ToView(nameof(TotalDividendPerShare)).HasNoKey();
    }
}
