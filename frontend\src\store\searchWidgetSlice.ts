import {StateCreator} from "zustand";
import {IStoreState} from "./useAppStore";
import {produce} from "immer";

export interface ISearchWidgetSlice {
  searchWidget: {
    isOpen: boolean,
    setOpen: (isOpen: boolean) => void
  }
}

const searchWidgetSlice: StateCreator<IStoreState, [], [], ISearchWidgetSlice> = set => ({
  searchWidget: {
    isOpen: false,
    setOpen: isOpen => set(state => produce(state, draft => {
      draft.searchWidget.isOpen = isOpen
    }))
  }
});

export default searchWidgetSlice;