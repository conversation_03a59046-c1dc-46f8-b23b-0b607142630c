using System.Text.Json;
using System.Text.Json.Nodes;
using System.Web;
using HotChocolate.Resolvers;
using Newtonsoft.Json.Linq;

namespace Euroland.FlipIT.Fundamental.API.Services;

public class FundamentalService
{
  private readonly HttpClient _httpClient;

  public FundamentalService(HttpClient httpClient)
  {
    _httpClient = httpClient;
  }

  public async Task<JToken> GetTableData(IResolverContext resolverContext, string tableName, CancellationToken cancellationToken)
  {
    var companyCode = resolverContext.GetScopedState<string>("companyCode");
    var source = resolverContext.GetScopedState<string>("source");
    var year = resolverContext.GetScopedStateOrDefault<List<int?>?>("year");
    var period = resolverContext.GetScopedStateOrDefault<List<string>?>("period");

    var query = HttpUtility.ParseQueryString(string.Empty);
    query["company_code"] = companyCode;
    query["source"] = source;
    query["table_name"] = tableName;
    if (year is not null)
    {
      query["year"] = string.Join(',', year);
    }
    if (period is not null)
    {
      query["period"] = string.Join(',', period);
    }

    var content = await _httpClient.GetStringAsync($"/wcb/financial-data?{query}", cancellationToken);
    return JToken.Parse(content);
  }
}
