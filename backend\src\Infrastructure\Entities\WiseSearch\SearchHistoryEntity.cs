﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.WiseSearch.Infrastructure.Entities.WiseSearch
{
    [Table("WiseSearch_SearchHistories")]
    public class SearchHistoryEntity
    {
        [Key]
        public Guid Id { get; set; }
        [Column("UserName", TypeName = "varchar(255)")]
        public string UserName { get; set; }
        [Column("Question", TypeName = "narchar(400)")]
        public string Question { get; set; }
        public string AIResult { get; set; }
        public DateTime SearchTime { get; set; }
        [Column("CompanyCode", TypeName = "varchar(15)")]
        public string CompanyCode { get; set; }
        [Column("IpAddress", TypeName = "varchar(50)")]
        public string? IpAddress { get; set; }
        public string ResultShow { get; set; }
        [Column("QALanguage", TypeName = "varchar(25)")]
        public string QALanguage { get; set; }
        [Column("AIResultLanguage", TypeName = "varchar(25)")]
        public string AIResultLanguage { get; set; }
    }
}
