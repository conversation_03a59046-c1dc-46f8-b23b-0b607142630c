using AutoMapper;
using Euroland.FlipIT.Fundamental.API.Dtos;
using Newtonsoft.Json.Linq;

namespace Euroland.FlipIT.Fundamental.API.AutoMappers.Profiles
{
  public class SegmentsMapperProfile : Profile
  {
    public SegmentsMapperProfile() {
      CreateMap<JToken, SegmentsDto>()
            .ForMember(dest => dest.cik, opt => opt.MapFrom(src => src.Value<string>("cik")))
            .ForMember(dest => dest.fye, opt => opt.MapFrom(src => src.Value<DateTime?>("fye")))
            .ForMember(dest => dest.fys, opt => opt.MapFrom(src => src.Value<DateTime?>("fys")))
            .ForMember(dest => dest.date, opt => opt.MapFrom(src => src.Value<DateTime?>("date")))
            .ForMember(dest => dest.link, opt => opt.MapFrom(src => src.Value<string>("link")))
            .ForMember(dest => dest.year, opt => opt.MapFrom(src => src.Value<string>("year")))
            .ForMember(dest => dest.period, opt => opt.MapFrom(src => src.Value<string>("period")))
            .ForMember(dest => dest.symbol, opt => opt.MapFrom(src => src.Value<string>("symbol")))
            .ForMember(dest => dest.currency, opt => opt.MapFrom(src => src.Value<string>("currency")))
            .ForMember(dest => dest.finalLink, opt => opt.MapFrom(src => src.Value<string>("finalLink")))
            .ForMember(dest => dest.periodEnd, opt => opt.MapFrom(src => src.Value<DateTime?>("periodEnd")))
            .ForMember(dest => dest.fillingDate, opt => opt.MapFrom(src => src.Value<DateTime?>("fillingDate")))
            .ForMember(dest => dest.periodStart, opt => opt.MapFrom(src => src.Value<DateTime?>("periodStart")))
            .ForMember(dest => dest.acceptedDate, opt => opt.MapFrom(src => src.Value<DateTime?>("acceptedDate")))
            .ForMember(dest => dest.calendarYear, opt => opt.MapFrom(src => src.Value<string>("calendarYear")))
            .ForMember(dest => dest.cumulativeType, opt => opt.MapFrom(src => src.Value<string>("cumulativeType")))
            .ForMember(dest => dest.periodEndMonth, opt => opt.MapFrom(src => src.Value<string>("periodEndMonth")))
            .ForMember(dest => dest.updateTimestamp, opt => opt.MapFrom(src => src.Value<DateTime?>("updateTimestamp")))
            .ForMember(dest => dest.reportedCurrency, opt => opt.MapFrom(src => src.Value<string>("reportedCurrency")))
            .ForMember(dest => dest.Segments, opt => opt.Ignore());
    }
  }
}
