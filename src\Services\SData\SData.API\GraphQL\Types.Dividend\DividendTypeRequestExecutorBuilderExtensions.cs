using HotChocolate.Execution.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Dividend;

public static class DividendTypeRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddDividendType(this IRequestExecutorBuilder builder)
  {
    builder.AddType<DividendType>();
    builder.AddType<AnnualDividendYieldType>();
    builder.AddTypeExtension<InstrumentTypeExtensions>();

    return builder;
  }
}
