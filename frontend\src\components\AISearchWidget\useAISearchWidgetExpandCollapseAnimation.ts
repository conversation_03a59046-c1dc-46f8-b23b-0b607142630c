import {XP<PERSON>} from "@/zoid";
import {useState,useRef,useEffect} from "react";

/**
 * Custom hook to handle the expand/collapse animation of the AI Search Widget.
 *
 * This hook listens to hover state changes to determine whether to animate the widget
 * into an "open" (expanded) or "close" (collapsed) state by invoking the provided animate function.
 *
 * Key points:
 * - Uses a hover state to trigger the appropriate animation.
 * - Prevents overlapping animations by checking isAnimatingRef.
 * - Forces a component re-render using a dummy state update after the animation completes.
 * - Remembers the previous animation state to avoid redundant animations.
 *
 * @param {XProps['animate']} animate - The animation function used to control widget transitions.
 * @returns {object} An object containing:
 *   - events: { onMouseEnter, onMouseLeave } event handlers for the widget.
 *   - hover: A boolean indicating the current hover state.
 */
const useAISearchWidgetExpandCollapseAnimation = (animate: XProps['animate']) => {
  const [hover, setHover] = useState(false);
  const rerender = useState([])[1]; // Trick to force re-render after animation
  const isAnimatingRef = useRef(false); // Prevents multiple animations from running simultaneously
  const prevAnimateRef = useRef<'open' | 'close'>('close'); // Stores the previous animation state

  useEffect(() => {
    // If an animation is already in progress, skip starting a new one
    if (isAnimatingRef.current) return;
    const action = hover ? 'open' : 'close';
    // Skip animation if already in the desired state
    if (prevAnimateRef.current === action) return;
    
    isAnimatingRef.current = true;
    switch (action) {
      case 'open':
        animate({
          width: ["74px", "260px"],
          easing: "linear",
          duration: 200,
        }).then(() => {
          isAnimatingRef.current = false;
          prevAnimateRef.current = 'open';
          rerender([]);
        });
        break;
      case 'close':
        animate({
          width: ["260px", "74px"],
          easing: "linear",
          duration: 200,
        }).then(() => {
          isAnimatingRef.current = false;
          prevAnimateRef.current = 'close';
          rerender([]);
        });
        break;
      default:
        break;
    }
  }, [hover, isAnimatingRef.current]);

  // Set hover state to true when mouse enters
  const onMouseEnter: React.MouseEventHandler<HTMLElement> = () => setHover(true);
  // Set hover state to false when mouse leaves
  const onMouseLeave: React.MouseEventHandler<HTMLElement> = () => setHover(false);

  return { 
    events: {
      onMouseEnter,
      onMouseLeave
    },
    hover
  };
};

export default useAISearchWidgetExpandCollapseAnimation