﻿<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\..\..\Version.props" />
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>

    <IsPackable>false</IsPackable>
    <IsPublishable>false</IsPublishable>

    <RootNamespace>UnitTest.SData.Application</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Database\**" />
    <EmbeddedResource Remove="Database\**" />
    <None Remove="Database\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="7.0.17" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.4.1" />
    <PackageReference Include="Moq" Version="4.18.4" />
    <PackageReference Include="Snapshooter.Xunit" Version="0.12.0" />
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="1.3.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SData.API\SData.API.csproj" />
  </ItemGroup>

</Project>
