namespace Euroland.FlipIT.SData.API.Dto;

public enum PerformancePeriodDto
{
  /// <summary>
  /// One week period
  /// </summary>
  ONE_WEEK = 1,
  /// <summary>
  /// Two weeks period
  /// </summary>
  TWO_WEEKS = 2,
  /// <summary>
  /// 52 weeks period
  /// </summary>
  FIFTY_TWO_WEEKS = 3,
  /// <summary>
  /// One month period
  /// </summary>
  ONE_MONTH = 4,
  /// <summary>
  /// 3 months period
  /// </summary>
  THREE_MONTHS = 5,
  /// <summary>
  /// 6 months period
  /// </summary>
  SIX_MONTHS = 6,
  /// <summary>
  /// 1 year period
  /// </summary>
  YTD = 7,
  /// <summary>
  /// 1 year period
  /// </summary>
  ONE_YEAR = 8,
  /// <summary>
  /// 2 years period
  /// </summary>
  TWO_YEARS = 9,
  /// <summary>
  /// 3 years period
  /// </summary>
  THREE_YEARS = 10,
  /// <summary>
  /// 5 years period
  /// </summary>
  FIVE_YEARS = 11,
  /// <summary>
  /// 10 years period
  /// </summary>
  TEN_YEARS = 12,
  ALL_TIME = 13,
  CUSTOM = 0
}
