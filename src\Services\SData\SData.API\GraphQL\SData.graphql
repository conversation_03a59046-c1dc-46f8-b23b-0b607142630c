"""
The `Short` scalar type represents non-fractional signed whole 16-bit numeric values. Short can represent values between -(2^15) and 2^15 - 1.
"""
scalar Short

"""
The `Date` scalar represents an ISO-8601 compliant date type.
"""
scalar Date

"""
The `DateTime` scalar represents an ISO-8601 compliant date time type.
"""
scalar DateTime

"""
The `Long` scalar type represents non-fractional signed whole 64-bit numeric values. Long can represent values between -(2^63) and 2^63 - 1.
"""
scalar Long


"""
The built-in `Decimal` scalar type.
"""
scalar Decimal

"""
The `Byte` scalar type represents non-fractional whole numeric values. Byte can represent values between 0 and 255.
"""
scalar Byte

type Query {
  company(id: ID!): Company
  instrument(id: ID!): Instrument
  currency(id: ID!): Currency
  currencyRate(currencyPair: String!): CurrencyRate!
  pressrelease(id: ID!): Pressrelease
}

type Currency {
  code: ID!
  name: String
  isRegionMajor: Boolean
  regionId: Byte
  decimalPlace: Int
  symbol: String
  translationName(cultureCode: String!): String
}

type CurrencyRate {
  id: ID!
  currencyPair: String!
  value: Decimal!
  date: DateTime!
  historicals: [CurrencyRateHistory!]! # Filterable
}

type CurrencyRateHistory {
  id: ID!
  value: Decimal!
  date: DateTime!
  currencyRate: CurrencyRate!
}

type Pressrelease {
  id: ID!
  title: String
  date: DateTime!
  hasAttachment: Boolean!
  insertedDate: DateTime!
  isHidden: Boolean
  languageId: Int!
  message: String
  groupId: Int
  typeId: Int
  sourceId: Int!

  attachments: [Attachment!]!
  company: Company!
}

type Attachment {
  id: ID!
  fileName: String
  isInBlob: Boolean!
  languageId: Int!
  location: String
  mime: String!
  order: Byte

  pressrelease: Pressrelease!
}

type CustomerType {
  id: ID!
  type: String
}

type CompanyName {
  language: String!
  name: String
  adr1: String
  adr2: String
  zip: String
  town: String
  country: String
  tel: String
  fax: String
  email: String
}

type Company {
  id: ID!
  code: String!
  marketId: Short!
  name: String
  homePage: String
  country: String
  town: String
  tel: String
  fax: String
  email: String

  primaryMarket: Market!
  customerType: CustomerType!
  companyName(language: String!): CompanyName
  instruments: [Instrument!] #queryable
  pressreleases: [Pressrelease!] #queryable
  customer: CustomerType!
}

type TimeZone {
  id: ID!
  name: String!
  hour: Float!
  diff: Int!
  diffFromServerTime: Int

  translationName(twoLetterISOLanguageName: String!): String
}

type Market {
  id: ID! #Market Number
  regionId: Int
  parentId: Int
  openTimeLocal: String!
  closeTimeLocal: String!
  timeDiff: Int!
  dataSource: String
  delay: String!
  timezoneName: String!
  businessDaysStoT: Boolean
  name: String!
  Abbreviation: String
  translationName(twoLetterISOLanguageName: String!): String

  city: City!
  timezone: TimeZone!
}

type City {
  id: ID!
  name: String
  country: Country!
}

type Country {
  id: ID!
  name: String
  isEnabled: Boolean!
  callingCode: Byte!

  translationName(twoLetterISOLanguageName: String!): String
}

type Instrument {
  id: ID!
  ticker: String!
  isin: String!
  shareName: String!
  marCat: Int
  eurCat: Int
  yTD: Decimal
  week: Decimal
  twoWeek: Decimal
  month: Decimal
  noShares: Decimal
  agentupdate: DateTime
  marketSpecSign: String
  high52W: Decimal
  low52W: Decimal
  percent52W: Decimal
  volatility: Decimal
  correlation: Decimal
  betaFactor: Decimal
  tokyoEurope: Short
  primaryMarket: Byte
  threeMonthHigh: Decimal
  threeMonthLow: Decimal
  threeMonthChange: Decimal
  fiveYearsChange: Decimal
  numSharesDate: DateTime
  splitRatio: Float
  ePS: Decimal
  sPS: Float
  dPS: Decimal
  payoutRatio: Float
  turnover: Decimal
  netIncome: Decimal
  turnoverGrowth: Float
  netInComeGrowth: Float
  bookValueOfShare: Float
  lotSize: Int
  allTimeHigh: Decimal
  allTimeLow: Decimal
  listedFrom: DateTime
  totalMarketCap: Decimal
  prevMid: Decimal
  highest52w: Decimal
  lowest52w: Decimal
  lastRowChange: DateTime
  visible: Boolean!
  mainIndex: Boolean
  yahooSymbol: String
  companyID: Int
  highYTD: Decimal
  lowYTD: Decimal
  regionID: Int
  marketCapEUR: Decimal
  numberOfUnlistedShares: Long
  volumeTurnover: Decimal
  volumeTurnoverUSD: Decimal
  dataSourceID: Int!
  wKN: String
  ricCode: String

  # extensions types
  company: Company!
  market: Market!
  instrumentType: InstrumentType!
  list: List
  customer: CustomerType
  currency: Currency
  historicals: [InstrumentHistory!]! # Filterable
  historicals_rt: [InstrumentHistory!]! # Filterable
  currentPrice: InstrumentPrice
  historicalsIntraday: [InstrumentDailyHistory!]! # Filterable
  historicalsIntradayForChart: [InstrumentDailyHistoryChart!]! # Filterable
}

type List {
  id: ID!
  name: String!
  translationName(cultureName: String!): String
}

type InstrumentPrice {
  id: ID!
  bid: Decimal
  ask: Decimal
  open: Decimal
  last: Decimal
  high: Decimal
  low: Decimal
  volume: Long!
  mid: Decimal
  date: DateTime
  prevClose: Decimal
  change: Decimal
  lastRowChange: DateTime
  changePercentage: Decimal
  todayTurnover: Decimal
  vwap: Decimal
  bidSize: Long
  askSize: Long
  officialClose: Decimal
  officialCloseDate: DateTime

  instrument: Instrument!
}

type InstrumentHistory {
  id: ID!
  date: DateTime!
  close: Decimal!
  volume: Long
  high: Decimal
  low: Decimal
  open: Decimal
  mid: Decimal
  lastRowChange: DateTime
  instrument: Instrument!
}

type InstrumentDailyHistory {
  date: DateTime!
  close: Decimal!
  size: Int
  tradeLastSellerBuyer: String
  instrument: Instrument!
}

type InstrumentDailyHistoryChart {
  date: DateTime!
  close: Decimal!
  diff: Int

  instrument: Instrument!
}

type InstrumentType {
  Id: ID!
  Name: String!
  instruments: [Instrument!]
}
