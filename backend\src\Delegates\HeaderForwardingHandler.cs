﻿
namespace Euroland.FlipIT.WiseSearch.Delegates
{
    public class HeaderForwardingHandler : DelegatingHandler
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public HeaderForwardingHandler(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            var context = _httpContextAccessor.HttpContext;
            if (context != null)
            {
                var ignoreHeaderForward = new string[] { "Content-Type", "Content-Length" };

                foreach (var header in context.Request.Headers)
                {
                    if (ignoreHeaderForward.Any(s => s.Equals(header.Key, StringComparison.OrdinalIgnoreCase)))
                        continue;

                    if (!request.Headers.Contains(header.Key))
                    {
                        request.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray());
                    }
                }
            }

            return await base.SendAsync(request, cancellationToken);
        }
    }

}
