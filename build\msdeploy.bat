@ECHO OFF

::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
:: usage:
:: msdeploy.bat <[options]>
::
:: options:
::  --source <value>  -> relative path (project directory as a root) to the publish content directory. The path should not start/end with back-slash "\" or slash "/" characters. Regularly path will be bin/Release/publish
::  --iisAppPath <value>  -> IIS application path
::  --env <value>     -> The deploy environment. Which will set as a name of <EnvironmentName/> to web.config. E.g. QA, Production, Gamma, Staging, CloudNE, CloudKSA, CloudUAE, CloudCN, CloudEA
::  --usr <value>     -> The user-name who has previlege to deploy app to remote server.
::  --pwd <value>     -> The password of user who has previlege to deploy app to remote server.
::  --url <value>     -> The absolute url of MSDeployService of destination web server.
::
:: remarks:
::  List of web deploy providers:
::    - https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-server-2008-r2-and-2008/dd569040(v=ws.10)
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::

SET ROOT=%CD%
ECHO Current working DIR: %ROOT%


setlocal

:parse
    if "%~1"=="" GOTO endparse
    if "%~1"=="--source" ( set "_Source=%~2" )
    if "%~1"=="--iisAppPath" ( set "_IisAppPath=%~2" )
    if "%~1"=="--env" ( set "_EnvironmentName=%~2" )
    if "%~1"=="--usr" ( set "_PublishUserName=%~2" )
    if "%~1"=="--pwd" ( set "_PublishUserPassword=%~2" )
    if "%~1"=="--url" ( set "_DeployServiceUrl=%~2" )
    ::if "%~1"=="--excludes" ( set "_Excludes=%~2" )
    shift
    GOTO parse
:endparse

set "MSDeploy=%ProgramFiles%\IIS\Microsoft Web Deploy V3\msdeploy.exe"
set "_Source=%ROOT%\%_Source%"
set "app_offline=%ROOT%\build\app_offline-template.htm"
:: parameterize msdeploy to set netcore EnvironmentName value via command argument.
set "paramsFile=%ROOT%\build\msdeployparams.xml"
set "appSettings=appSettings.%_EnvironmentName%.json"
set "appSettingsTempDir=%_Source%\..\appSettingsTmp"

ECHO ========================================================================================
ECHO IIS Web Application Path: %_IisAppPath%...
ECHO Local publish package DIR: %_Source%
ECHO ServiceUrl: %_DeployServiceUrl%
ECHO EnvironmentName: %_EnvironmentName%
ECHO ========================================================================================

:: Move appSettings.{EnvironmentName}.json temp folder, we can copy back later
:: to prepare deleting all other environments of appSettings.json file.
:: specify /XF file [file] to exclude specific files which is matched with appSettings.*.json also.
ECHO Moving appSettings.*.json(s) to temp folder...
robocopy "%_Source%" "%appSettingsTempDir%" appSettings.*.json /MOV
copy "%app_offline%" "%_Source%\app_offline.htm"

:: Rename back for the deploying environment.
IF EXIST "%appSettingsTempDir%\%appSettings%" (
  robocopy "%appSettingsTempDir%" "%_Source%" "%appSettings%"
) ELSE (
  ECHO Not found file %appSettings% for publishing
)

:: Starting msdeploy.exe processes.
ECHO Bringing application offline...

:: Create app_offline.htm file at the root of application to stop it down.
"%MSDeploy%"^
  -verb:sync^
  -disableLink:AppPoolExtension^
  -disableLink:ContentExtension^
  -disableLink:CertificateExtension^
  -allowUntrusted^
  -retryAttempts=10^
  -retryInterval=2000^
  -userAgent="VSCmdLine"^
  -source:contentPath="%app_offline%"^
  -dest:contentPath="%_IisAppPath%/app_offline.htm",UserName='%_PublishUserName%',Password=%_PublishUserPassword%,AuthType='Basic',IncludeAcls='False',ComputerName="%_DeployServiceUrl%"

ECHO Application offline!

ECHO Deploying application...

"%MSDeploy%"^
  -verb:sync^
  -enableRule:AppOffline^
  -disableLink:AppPoolExtension^
  -disableLink:ContentExtension^
  -disableLink:CertificateExtension^
  -skip:objectname='dirPath',absolutepath='Config\\Company'^
  -skip:objectname='dirPath',absolutepath='logs'^
  -allowUntrusted^
  -retryAttempts=10^
  -retryInterval=2000^
  -userAgent="VSCmdLine"^
  -setParamFile:"%paramsFile%"^
  -setParam:name="EnvironmentName",value="%_EnvironmentName%"^
  -source:IisApp="%_Source%"^
  -dest:IisApp="%_IisAppPath%",UserName='%_PublishUserName%',Password=%_PublishUserPassword%,AuthType='Basic',IncludeAcls='False',ComputerName="%_DeployServiceUrl%"
ECHO Deployed successful!

ECHO Bringing application online...

"%MSDeploy%"^
  -verb:delete^
  -disableLink:AppPoolExtension^
  -disableLink:ContentExtension^
  -disableLink:CertificateExtension^
  -allowUntrusted^
  -retryAttempts=10^
  -retryInterval=2000^
  -userAgent="VSCmdLine"^
  -dest:contentPath="%_IisAppPath%/app_offline.htm",UserName='%_PublishUserName%',Password=%_PublishUserPassword%,AuthType='Basic',IncludeAcls='False',ComputerName="%_DeployServiceUrl%"

ECHO Deploy done!

:errorcheck
IF %ERRORLEVEL% NEQ 0 (
  EXIT /b 1
)

endlocal

:EOF
