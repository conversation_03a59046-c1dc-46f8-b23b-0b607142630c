using System;
using Euroland.FlipIT.SData.API.Helpers;

namespace Euroland.FlipIT.SData.API.Dto;

public class InstrumentDailyDataDto: IDtoObject
{
  public long Id { get; set; }

  public int InstrumentId { get; set; }

  private DateTime _date;

  public DateTime Date
  {
    get
    {
      return _date;
    }
    set
    {
      _date = value.Kind != DateTimeKind.Utc ? value.ToUtcKindOnly() : value;
    }
  }

  public decimal Close { get; set; }

  public long? Volume { get; set; }
}
