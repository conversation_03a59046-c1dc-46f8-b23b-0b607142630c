using System;
using System.Linq.Expressions;
using System.Reflection;
using Euroland.FlipIT.SData.API.DTOs.Deprecated;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Infrastructure.Entities.Shark;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query.SqlExpressions;
using Microsoft.EntityFrameworkCore.Storage;

namespace Euroland.FlipIT.SData.API.Infrastructure;

public abstract class SharkDbContextBase : DbContext, ISharkDbContext
{
  public SharkDbContextBase(DbContextOptions options) : base(options)
  {

  }

  protected override void OnModelCreating(ModelBuilder modelBuilder)
  {
    modelBuilder.Entity<Market>()
        .HasOne(i => i.Instrument)
        .WithOne(m => m.Market)
        .HasForeignKey<Entities.Instrument>(i => i.MarketID);

    modelBuilder.Entity<Entities.Currency>()
        .HasOne(i => i.Instrument)
        .WithOne(m => m.Currency)
        .IsRequired(false)
        .HasForeignKey<Entities.Instrument>(i => i.CurrencyCode);

    modelBuilder.Entity<Translation>()
        .HasOne(i => i.Market)
        .WithOne(m => m.Translation)
        .HasForeignKey<Market>(i => i.TranslationId);

    modelBuilder.Entity<Translation>()
        .HasOne(i => i.Currency)
        .WithOne(m => m.Translation)
        .IsRequired(false)
        .HasForeignKey<Entities.Currency>(i => i.TranslationId);

    modelBuilder.Entity<CompanyNames>()
        .HasKey(c => new { c.CompanyCode, c.CompanyLang });

    ConfigureModel(modelBuilder);
    base.OnModelCreating(modelBuilder);
    modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
  }

  protected abstract void ConfigureModel(ModelBuilder modelBuilder);

  public DbSet<ActiveFnTradesHistory_RT> ActiveFnTradesHistories { get; set; }
  public DbSet<IceTradesHistory_RT> IceTradesHistories { get; set; }
  public DbSet<LatestShareTradesDto> LatestShareTrades { get; set; }
  public DbSet<Entities.Instrument> Instrument { get; set; }
  public DbSet<InstrumentPrice> InstrumentPrice { get; set; }
  public DbSet<Market> Market { get; set; }
  public DbSet<Timezone> Timezone { get; set; }
  public DbSet<City> City { get; set; }
  public DbSet<Country> Country { get; set; }
  public DbSet<MarketDepth> MarketDepth { get; set; }
  public DbSet<Translation> Translation { get; set; }
  public DbSet<List> List { get; set; }
  public DbSet<SubSector> SubSector { get; set; }
  public DbSet<InstrumentHistory> InstrumentHistory { get; set; }
  public DbSet<AdjustedInstrumentHistory> AdjustedInstrumentHistories { get; set; }
  public DbSet<Company> Company { get; set; }
  public DbSet<CompanyNames> CompanyNames { get; set; }
  public DbSet<Dividend> Dividends { get; set; }
  public DbSet<DividendDisplayCurrency> DividendDisplayCurrencies { get; set; }
  public DbSet<InstrumentDailyData> InstrumentDailyData { get; set; }
  public DbSet<DailyHistory> DailyHistory { get; set; }
  public DbSet<DividendEvt> DividendEvts { get; set; }
  public DbSet<FCEvent> FCEvents { get; set; }
  public DbSet<FCalendar> FCalendars { get; set; }
  public DbSet<FCEventType> FcEventTypes { get; set; }
  public DbSet<Entities.Currency> Currency { get; set; }
  public DbSet<Splits> Splits { get; set; }
  public DbSet<Entities.CurrencyRate> CurrencyRates { get; set; }
  public DbSet<CurrencyRateHistory> CurrencyRateHistories { get; set; }
  public DbSet<CustomerType> CustomerType { get; set; }
  public DbSet<TotalDividendPerShare> TotalDividendPerShares { get; set; }
  //public DbSet<EverageSharePrice> EverageSharePrices { get; set; }
  public DbSet<Language> Languages { get; set; }

  [DbFunction("fn_shgGetMarketStatus", "dbo")] // new-scalarFunc
  public static int GetMarketStatus(short marketId, int checkNextDay)
      => throw new NotSupportedException();

  [DbFunction("fnGetRelativeVolume", "dbo")] // new-scalarFunc
  public static decimal? GetVolumeChange(int instrumentId, int dayPeriod)
      => throw new NotSupportedException();

  [DbFunction("fnGetTotalTrades", "dbo")] // new-scalarFunc
  public static int? GetTotalTrades(int instrumentId, DateTime ipDateCet)
      => throw new NotSupportedException();

  [DbFunction("fnSSSelectSubSector", "dbo")]
  public static string GetIndustry(int marCat, short market, int lang)
      => throw new NotSupportedException();

  [DbFunction("fnSSSelectSectorNewSubSectors", "dbo")]
  public static int? GetIndustryTranslationID(int eurCat, int lang)
      => throw new NotSupportedException();

  [DbFunction("fn_shgGetRateForCurrencyExchange", "dbo")]
  public static decimal? GetRateForCurrencyExchange(string currencyBase, string currencyQuote, DateTime date)
      => throw new NotSupportedException();

  [DbFunction("fn_shgGetPeriodChange", "dbo")]
  public static decimal GetPeriodChange(int instrumentId, DateTime startDate)
      => throw new NotSupportedException();

  [DbFunction("fnGetStartingDate", "dbo")]
  public static DateTime? GetStartingDate(int instrumentId)
      => throw new NotSupportedException();

  [DbFunction("fnGetLatestDate", "dbo")]
  public static DateTime? GetLatestDate(int instrumentId)
     => throw new NotSupportedException();

  [DbFunction("fn_shgGetHighestPrice", "dbo")]
  public static decimal GetHighPeriod(int id, DateTime? dateFrom)
      => throw new NotSupportedException();

  [DbFunction("fn_shgGetLowestPrice", "dbo")]
  public static decimal GetLowPeriod(int id, DateTime? dateFrom)
      => throw new NotSupportedException();

  [DbFunction("fn_SHG3_GetHighOrLowInPeriodTime", "dbo")]
  public static decimal GetHighOrLowInPeriodTime(int instrumentId, DateTime dateFrom, DateTime dateTo, bool isGetHigh = true)
      => throw new NotSupportedException();

  [DbFunction("fn_SGH3_getCloseInPeriodTime", "dbo")]
  public static decimal GetCloseInPeriodTime(int instrumentId, DateTime dateFrom, DateTime dateTo)
      => throw new NotSupportedException();

  [DbFunction("fn_SGH3_GetCurrentCurrencyRate", "dbo")]
  public static decimal GetCurrentCurrencyRate(string currencyBase, string currencyQuote)
      => throw new NotSupportedException();

  [DbFunction("fn_SGH3_GetPeriodChangeByCurrencyRate", "dbo")]
  public static decimal GetPeriodChangeByCurrencyRate(int instrumentId, string currencyBase, string currencyQuote, DateTime startDate)
      => throw new NotSupportedException();

  [DbFunction("fn_SGH3_GetPeriodChangeByCurrencyRate_RealTime", "dbo")]
  public static decimal GetPeriodChangeByCurrencyRateRealTime(int instrumentId, string currencyBase, string currencyQuote, DateTime startDate)
      => throw new NotSupportedException();

  [DbFunction("fn_SGH3_GetPeriodHighestOfHighByCurrencyRate", "dbo")]
  public static decimal GetPeriodHighestOfHighByCurrencyRate(int instrumentId, string currencyBase, string currencyQuote, DateTime? startDate)
      => throw new NotSupportedException();

  [DbFunction("fn_SGH3_GetPeriodHighestOfHighByCurrencyRate_RealTime", "dbo")]
  public static decimal GetPeriodHighestOfHighByCurrencyRateRealTime(int instrumentId, string currencyBase, string currencyQuote, DateTime? startDate)
      => throw new NotSupportedException();

  [DbFunction("fn_SGH3_GetPeriodLowestOfLowByCurrencyRate", "dbo")]
  public static decimal GetPeriodLowestOfLowByCurrencyRate(int instrumentId, string currencyBase, string currencyQuote, DateTime? startDate)
      => throw new NotSupportedException();

  [DbFunction("fn_SGH3_GetPeriodLowestOfLowByCurrencyRate_RealTime", "dbo")]
  public static decimal GetPeriodLowestOfLowByCurrencyRateRealTime(int instrumentId, string currencyBase, string currencyQuote, DateTime? startDate)
      => throw new NotSupportedException();

  [DbFunction("fn_SGH3_getCloseInPeriodTimeByCurrencyRate", "dbo")]
  public static decimal GetCloseInPeriodTimeByCurrencyRate(int instrumentId, string currencyBase, string currencyQuote, DateTime startDate, DateTime endDate)
      => throw new NotSupportedException();

  [DbFunction("fn_SGH3_GetCurrencyRateByDate", "dbo")]
  public static decimal GetCurrencyRateByDate(string currencyBase, string currencyQuote, DateTime? date)
      => throw new NotSupportedException();

  [DbFunction("fn_SGH3_GetPrevCloseOfInstrumentByCurrencyRate", "dbo")]
  public static decimal GetPrevCloseOfInstrumentByCurrencyRate(int instrumentId, string currencyBase, string currencyQuote)
      => throw new NotSupportedException();

  [DbFunction("fn_SGH3_GetPrevCloseOfInstrumentByCurrencyRate_RealTime", "dbo")]
  public static decimal GetPrevCloseOfInstrumentByCurrencyRateRealTime(int instrumentId, string currencyBase, string currencyQuote)
    => throw new NotSupportedException();

  [DbFunction("fn_SGH3_GetLatestPriceOfInstrumentByCurrencyRate", "dbo")]
  public static decimal GetLatestPriceOfInstrumentByCurrencyRate(int instrumentId, string currencyBase, string currencyQuote)
      => throw new NotSupportedException();

  [DbFunction("fn_SGH3_GetLatestPriceOfInstrumentByCurrencyRate_RealTime", "dbo")]
  public static decimal GetLatestPriceOfInstrumentByCurrencyRateRealTime(int instrumentId, string currencyBase, string currencyQuote)
    => throw new NotSupportedException();
}
