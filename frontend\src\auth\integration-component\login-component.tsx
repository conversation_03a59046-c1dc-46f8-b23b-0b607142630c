import { OidcAuthAppRoutes } from "../core";

/**
 * LoginIntegrationComponent is a React functional component that integrates with the Euroland system
 * to create a login component.
 *
 * @throws {Error} Throws an error if the `window.euroland` object is not found, indicating that the integration script is missing.
 *
 * @returns {Object} Returns the created integration login component.
 *
 */
export default function DefaultLoginIntegrationComponent() {
  if(!window.euroland) {
    throw new Error("Integration window.euroland object not found. Check out whether integration.js is included.");
  }

  let baseUrl = ((document.querySelector('base') || {}) as HTMLBaseElement).href;

  if(/\/$/.test(baseUrl)) {
    baseUrl = baseUrl.replace(/\/$/, '');
  }

  const props = {
    returnUrl: {
      type: 'string',
      required: false,
      default: ''
    },
    onLoginSuccess: {
      type: 'function',
      required: true
    },
    onLoginFail: {
      type: 'function',
      required: true
    }
  };

  const component = window.euroland.createComponent('LoginComponent', {
    tag: 'ai-search-signin-component',
    url: `${baseUrl}${OidcAuthAppRoutes.SIGNIN}${window.location.search}`,
    dimensions: {
      width: '850px',
      height: '600px'
    },
    template: {
      name: 'popup'
    },
    props: props as any
  });

  return component;
};
