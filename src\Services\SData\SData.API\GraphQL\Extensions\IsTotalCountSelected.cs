using HotChocolate.Resolvers;
using HotChocolate.Types;

namespace Microsoft.Extensions.DependencyInjection;

internal static class IsTotalCountSelectedIResolverContextExtensions
{
  const string CONNECTION_TOTAL_COUNT_NAME = "totalCount";
  public static bool IsTotalCountSelected(this IResolverContext context)
    {
        // TotalCount is one of the heaviest operations. It is only necessary to load totalCount
        // when it is enabled (IncludeTotalCount) and when it is contained in the selection set.
        if (context.Selection.Type is ObjectType objectType &&
            context.Selection.SyntaxNode.SelectionSet is not null)
        {
            var selections = context.GetSelections(objectType, null, true);

            for (var i = 0; i < selections.Count; i++)
            {
                if (selections[i].Field.Name == CONNECTION_TOTAL_COUNT_NAME)
                {
                    return true;
                }
            }
        }

        return false;
    }
}
