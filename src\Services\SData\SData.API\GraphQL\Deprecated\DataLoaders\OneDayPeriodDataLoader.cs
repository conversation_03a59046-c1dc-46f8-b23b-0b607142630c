﻿using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Infrastructure.Repositories;
using GreenDonut;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Euroland.FlipIT.SData.API.GraphQL.Deprecated.DataLoaders
{
    public class OneDayPeriodDataLoader : BatchDataLoader<int, IntradayData>
    {
        private readonly IUnitOfWork _uow;

        public OneDayPeriodDataLoader(
            IBatchScheduler batchScheduler,
            IUnitOfWork uow)
            : base(batchScheduler)
        {
            _uow = uow ?? throw new ArgumentNullException(nameof(uow));
        }

        /// <summary>
        /// With data loaders we can now centralise the data fetching
        /// and reduce the number of round trips to our data source.
        /// </summary>
        /// <param name="keys"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        protected override async Task<IReadOnlyDictionary<int, IntradayData>> LoadBatchAsync(
            IReadOnlyList<int> keys,
            CancellationToken cancellationToken)
        {
            var intradays = _uow.GetRepository<IntradayData>();

            var query = intradays.GetQueryableList<IntradayData>(
                    selector: i => new IntradayData { InstrumentId = i.InstrumentId, Date = i.Date.Date },
                    predicate: i => keys.Contains(i.InstrumentId),
                    orderBy: i => i.OrderByDescending(x => x.Date));

            // show only data from the latest date
            var latestDate = query.Max(x => x.Date);
            return await query.Where(x => x.Date.Date == latestDate.Date)
                              .ToDictionaryAsync(t => t.InstrumentId, cancellationToken);

        }
    }
}
