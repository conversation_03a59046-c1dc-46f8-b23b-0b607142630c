﻿using AutoMapper;
using Euroland.FlipIT.SData.API.DTOs.Deprecated;
using Euroland.FlipIT.SData.API.Helpers;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using System;
#pragma warning disable S3358 // Ternary operators should not be nested
namespace Euroland.FlipIT.SData.API.Mappers.Deprecated.Profiles
{
    public class EarningEventMapperProfile : Profile
    {
        public EarningEventMapperProfile()
        {
            var langCode = "en-gb";



            CreateMap<FCEvent, EarningEvent>(MemberList.Destination)

                .ForMember(dto => dto.DateTime, conf =>
                    conf.MapFrom(ol => ol.FCalendar.DateTime.HasValue
                      ? DateTime.SpecifyKind(ol.FCalendar.DateTime.Value, DateTimeKind.Utc)
                      : (DateTime?)null))

                .ForMember(dto => dto.Date, conf =>
                  conf.MapFrom(ol => ol.FCalendar.DateTime.HasValue
                    ? ol.FCalendar.DateTime.Value.ToString("yyyy-MM-dd")
                    : null))

                .ForMember(dto => dto.Heading, conf =>
                    conf.MapFrom(ol => (langCode.IsCulture("en-us") ? ol.English :
                                       langCode.IsCulture("fr-fr") ? ol.French :
                                       langCode.IsCulture("ar-sa") ? ol.Arabic :
                                       langCode.IsCulture("ar-ae") ? ol.Arabic :
                                       langCode.IsCulture("fi-fi") ? ol.Finnish :
                                       langCode.IsCulture("sv-se") ? ol.Swedish :
                                       langCode.IsCulture("de-de") ? ol.German :
                                       langCode.IsCulture("es-mx") ? ol.Spanish :
                                       langCode.IsCulture("it-it") ? ol.Italian :
                                       langCode.IsCulture("nl-nl") ? ol.Dutch :
                                       langCode.IsCulture("ru-ru") ? ol.Russian :
                                       langCode.IsCulture("pl-pl") ? ol.Polish :
                                       langCode.IsCulture("zh-tw") ? ol.Taiwanese :
                                       langCode.IsCulture("zh-cn") ? ol.Chinese :
                                       langCode.IsCulture("ko-kr") ? ol.Korean :
                                       langCode.IsCulture("da-dk") ? ol.Danish :
                                       langCode.IsCulture("is-is") ? ol.Icelandic :
                                       langCode.IsCulture("vi-vn") ? ol.Vietnamese :
                                       langCode.IsCulture("ja-jp") ? ol.Japanese :
                                       ol.English) ?? ol.English));


        }
    }
}


#pragma warning restore S3358 // Ternary operators should not be nested
