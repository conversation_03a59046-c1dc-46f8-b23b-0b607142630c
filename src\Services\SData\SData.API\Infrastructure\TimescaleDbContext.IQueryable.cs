using System.Linq;
using Euroland.FlipIT.SData.API.Infrastructure.Configurations.Timescale;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Infrastructure.Entities.Timescale;
using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.SData.API.Infrastructure;

public static class TimescaleDbContextIQueryableExtensions
{
  public static IQueryable<TemporaryCurrencyRateHistoryTimescale> InstrumentCurrencyPairQueryable(
    this TimescaleDbContext sharkDbContext, string baseQuoteCurrencyPair)
  {
    return sharkDbContext.Set<TemporaryCurrencyRateHistoryTimescale>()
      .TagWith(
        $"{TemporaryCurrencyRateHistoryTimescaleConfiguration.TemporaryCurrencyRateHistoryTableName} <currencyPair:{baseQuoteCurrencyPair}><factor:0><fixedRate:0> {TemporaryCurrencyRateHistoryDbCommandInterceptorTimescale.END_TAG_PLACEHOLDER}");
  }
}
