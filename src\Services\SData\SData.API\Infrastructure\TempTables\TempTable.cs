namespace Euroland.FlipIT.SData.API.Infrastructure.TempTables;

public class TempTable<TColumn>
{
  public TempTable()
  {
  }

  public TempTable(TColumn column)
  {
    Column = column;
  }
  public TColumn Column { get; set; }
}

public class TempTable<TColumn1, TColumn2>
{
  public TColumn1 Column1 { get; set; }


  public TColumn2 Column2 { get; set; }

  public TempTable()
  {
  }


  public TempTable(TColumn1 column1, TColumn2 column2)
  {
    Column1 = column1;
    Column2 = column2;
  }
}
