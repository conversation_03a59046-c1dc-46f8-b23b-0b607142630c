using System.Linq;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Dto.Webcast;
using Euroland.FlipIT.SData.API.Infrastructure;
using HotChocolate;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Webcast;

public class WebcastResolver
{
  public IQueryable<WebcastDto> GetCompanyWebcasts([Parent] CompanyDto company, WebcastContext webcastContext)
  {
    var result = webcastContext.Webcasts
      .AsNoTracking()
      .Include(wc=> wc.WebcastType)
      .Include(wc=> wc.WebcastHost)
      .Include(wc=> wc.WebcastSource)
      .Include(wc=> wc.WebcastUrls)
      .Include(wc=> wc.WebcastTranslations)
      .Where(wc => wc.CompanyCode == company.Code)
      .WithAutoMapper()
      .ToProjection<WebcastDto>();

    return result;
  }
}
