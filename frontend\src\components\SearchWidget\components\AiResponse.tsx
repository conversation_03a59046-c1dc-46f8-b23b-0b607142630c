import { faLanguage } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Markdown } from "@/components/Markdown";
import { capitalize } from 'es-toolkit'

interface AiResponseProps {
  qaLanguage?: string;
    aiResultLanguage?: string;
    answer: string;
}

const AiResponse = ({ qaLanguage, aiResultLanguage, answer }: AiResponseProps) => {
  return (
    <>
      {qaLanguage && aiResultLanguage && qaLanguage !== aiResultLanguage && (
        <div className="flex items-center gap-2 text-[#0066cc] text-sm">
          <span className="translation-icon">
            <FontAwesomeIcon icon={faLanguage} />
          </span>{" "}
          <span className="translation-text">
            {capitalize(aiResultLanguage ?? '')} → {capitalize(qaLanguage ?? '')}
          </span>
        </div>
      )}
      <Markdown>{answer as string}</Markdown>
    </>
  );
};

export default AiResponse;
