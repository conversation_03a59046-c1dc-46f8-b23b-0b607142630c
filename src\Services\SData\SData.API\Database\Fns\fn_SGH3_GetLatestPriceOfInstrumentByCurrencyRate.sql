USE [shark]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		HuyDQ
-- Create date: July 17 2024
-- Description:	Get latest price of instrument by currency rate
-- =============================================
CREATE FUNCTION [dbo].[fn_SGH3_GetLatestPriceOfInstrumentByCurrencyRate]
(
	@InstrumentId int,
	@CurrencyBase	AS VARCHAR(3),
	@CurrencyQuote	AS VARCHAR(3)
)
RETURNS numeric(21,7)
AS
BEGIN
	DECLARE @ipCloseDate date
	DECLARE @ipLast money
	DECLARE @LasthCloseDate date
	DECLARE @LasthClose money
	DECLARE @MaxHistoryDate date
	DECLARE @Result numeric(21,7)

	SELECT
	@ipCloseDate = [Date],
	@ipLast = [Last]
    FROM dbo.InstrumentPrice
    WHERE [InstrumentId] = @InstrumentId

	IF(LEN(@CurrencyQuote) <> 3
		OR	(@CurrencyBase = @CurrencyQuote)
		OR	(@CurrencyQuote = '')
		OR  (@CurrencyQuote IS NULL))
		SET @Result = @ipLast 
	Else
		Set @Result = @ipLast * dbo.fn_SGH3_GetCurrencyRateByDate(@CurrencyBase, @CurrencyQuote, @ipCloseDate)

	SELECT @MaxHistoryDate = MAX([Date])
	FROM InstrumentHistory 
	WHERE InstrumentID = @InstrumentId

	IF (@MaxHistoryDate >= Cast(@ipCloseDate AS Date))
		Begin
			SELECT TOP 1 
			@LasthClose = [Close],
			@LasthCloseDate = [Date]
			FROM dbo.InstrumentHistory
			WHERE [InstrumentId] = @InstrumentId
			ORDER BY [Date] DESC

			IF(LEN(@CurrencyQuote) <> 3
				OR	(@CurrencyBase = @CurrencyQuote)
				OR	(@CurrencyQuote = '')
				OR  (@CurrencyQuote IS NULL))
				SET @Result = @LasthClose 
			Else
				Set @Result = @LasthClose * dbo.fn_SGH3_GetCurrencyRateByDate(@CurrencyBase, @CurrencyQuote, @LasthCloseDate)
		End
	RETURN @Result
END
GO

