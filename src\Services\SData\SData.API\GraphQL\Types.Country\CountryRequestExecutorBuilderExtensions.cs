using Euroland.FlipIT.SData.API.GraphQL.Types.Country;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class CountryRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddCountryType(this IRequestExecutorBuilder builder)
  {
    builder.AddType<CountryType>();
    builder.AddType<CityTypeExtensions>();

    builder.AddDataLoader<CountryByIdDataLoader>();

    return builder;
  }
}
