using Euroland.FlipIT.SData.API.GraphQL.Types.OrderDepth;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class OrderDepthRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddOrderDepthType(this IRequestExecutorBuilder builder)
  {
    builder
      .AddType<OrderDepthType>()
      .AddType<MarketDepthType>();

    builder.AddTypeExtension<InstrumentTypeExtensions>();

    builder.AddDataLoader<OrderDepthByInstrumentIdDataLoader>();

    return builder;
  }
}
