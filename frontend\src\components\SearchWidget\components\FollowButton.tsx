import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCheck, faBell } from "@fortawesome/free-solid-svg-icons";
import { FOLLOWING_STATUS } from "@/helpers/constants";
import { i18n } from "@euroland/libs";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";
import {useSearchWidgetPropsContext} from "../context/propContext";
const UnFollowConfirm = ({
  onConfirm,
  onCancel,
}: {
  onConfirm: () => void;
  onCancel: () => void;
}) => {
  return (
    <div className="flex flex-col gap-3">
      <p className="text-sm">{i18n.translate("unfollow_confirmation")}</p>
      <div className="flex justify-end gap-2">
        <Button className="px-1.5 py-0.5 h-auto leading-tight text-sm" variant="ghost" size="sm" onClick={onCancel}>
          {i18n.translate("cancel")}
        </Button>
        <Button className="px-1.5 py-0.5 h-auto leading-tight text-sm" variant="destructive" size="sm" onClick={onConfirm}>
          {i18n.translate("unfollow")}
        </Button>
      </div>
    </div>
  );
};

class AutoShowFollow {
  private static readonly KEY = "followButtonShown";
  private static readonly TIMEOUT = 5000;

  public isFollowButtonShown = Boolean(localStorage.getItem(AutoShowFollow.KEY));
  public setFollowButtonShown = (followStatus: boolean) => {
    if (followStatus) return localStorage.setItem(AutoShowFollow.KEY, "true");
    localStorage.removeItem(AutoShowFollow.KEY);

    // isFollowButtonShown just switch value when followStatus = false, when followStatus = true, we dont update isFollowButtonShown 
    this.isFollowButtonShown = followStatus;
  }

  public autoFollowCallback = (callback: () => void) => {
    if (!this.isFollowButtonShown) return;
    const timer = setTimeout(() => {
      callback();
    }, AutoShowFollow.TIMEOUT);
    return () => clearTimeout(timer);
  }
}

const autoShowFollow = new AutoShowFollow();

const AutoShowFollowButton = ({ onClick }: { onClick: () => void }) => {
  const [isUnfollowConfirmOpen, setIsUnfollowConfirmOpen] = useState(false);
  const isOpen = useSearchWidgetPropsContext(item => item.isOpen)

  useEffect(() => {
    if(!isOpen) return;
    return autoShowFollow.autoFollowCallback(() => {
      setIsUnfollowConfirmOpen(true);
    });
  }, [isOpen]);

  const onOpenChange = (isOpen: boolean) => {
    setIsUnfollowConfirmOpen(isOpen);
    // if(!isOpen) autoShowFollow.setFollowButtonShown(false);
  }

  return (
    <Popover open={isUnfollowConfirmOpen} onOpenChange={onOpenChange}>
      <PopoverTrigger asChild>
        <button
          className="py-1.5 px-3 gap-1.5 items-center flex text-xs font-medium border border-white border-opacity-30 bg-white bg-opacity-15 hover:bg-opacity-20 rounded-md leading-none"
          onClick={onClick}
        >
          <FontAwesomeIcon icon={faBell} />
          {i18n.translate("follow")}
        </button>
      </PopoverTrigger>
      <PopoverContent className="w-64 py-2 px-3">
        <div className="text-sm">
          <p className="mb-0">
            {i18n.translate("followButton.popup.title")}{" "}
            <button className="text-primary px-1.5 py-0.5 rounded-md" onClick={onClick}>{i18n.translate("follow")}</button>
          </p>
        </div>
      </PopoverContent>
    </Popover>
  );
};

const FollowButton = ({
  isFollowed,
  followAndUnfollowCompany,
}: {
  isFollowed: boolean;
  followAndUnfollowCompany: (status: FOLLOWING_STATUS) => void;
}) => {
  const [isUnfollowConfirmOpen, setIsUnfollowConfirmOpen] = useState(false);
  useEffect(() => {
    autoShowFollow.setFollowButtonShown(!isFollowed);
  }, [isFollowed]);
  if (isFollowed)
    return (
      <Popover
        key="unfollow-confirm"
        open={isUnfollowConfirmOpen}
        onOpenChange={setIsUnfollowConfirmOpen}
      >
        <PopoverTrigger asChild>
          <button className="py-1.5 gap-1.5 items-center flex text-xs font-medium leading-none">
            <FontAwesomeIcon icon={faCheck} />
            {i18n.translate("following")}
          </button>
        </PopoverTrigger>
        <PopoverContent className="w-64 py-2 px-3">
          <UnFollowConfirm
            onConfirm={() => {
              followAndUnfollowCompany(FOLLOWING_STATUS.UN_FOLLOW);
              setIsUnfollowConfirmOpen(false);
            }}
            onCancel={() => setIsUnfollowConfirmOpen(false)}
          />
        </PopoverContent>
      </Popover>
    );

  return (
    <AutoShowFollowButton
      key="follow-button"
      onClick={() => followAndUnfollowCompany(FOLLOWING_STATUS.FOLLOWING)}
    />
  );
};

export default FollowButton;
