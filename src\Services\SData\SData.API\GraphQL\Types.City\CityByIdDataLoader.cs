using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using Euroland.FlipIT.SData.API.Infrastructure.Factories;
using Euroland.FlipIT.Shared.CachingManager;
using GreenDonut;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.City;

public class CityByIdDataLoader : BatchDataLoader<int, CityDto>
{
  private readonly ISharkDbContextAbstractFactory _factory;
  private readonly IGraphQLExecutionContext _queryRequestContext;
  private readonly IConfigurableCacheManager _configurableCacheManager;

  public CityByIdDataLoader(
    ISharkDbContextAbstractFactory factory,
    IGraphQLExecutionContext queryRequestContext,
    IConfigurableCacheManager configurableCacheManager,
    IBatchScheduler batchScheduler,
    DataLoaderOptions? options = null)
    : base(batchScheduler, options)
  {
    _factory = factory ?? throw new ArgumentNullException(nameof(factory));
    _queryRequestContext = queryRequestContext ?? throw new ArgumentNullException(nameof(queryRequestContext));
    _configurableCacheManager = configurableCacheManager ?? throw new ArgumentNullException(nameof(configurableCacheManager));
  }

  protected override async Task<IReadOnlyDictionary<int, CityDto>> LoadBatchAsync(IReadOnlyList<int> keys, CancellationToken cancellationToken)
  {
    // Retreive Market DTO from the cache with provided Market IDs
    var dtoFromCache = new List<CityDto>();
    var idNeedToFetchNew = new List<int>();

    foreach (var mid in keys.Distinct())
    {
      var dto = _configurableCacheManager.GetCache<CityDto, string>(
        CacheKeyHelper.GenerateKeyByPropOfDtoObject<CityDto, int>(mid.ToString(), m => m.Id)
      );

      if (dto != null)
      {
        dtoFromCache.Add(dto);
      }
      else
      {
        idNeedToFetchNew.Add(mid);
      }
    }

    await using var sharkDbContext = await _factory.CreateDbContextAsync(_queryRequestContext.UseRealtime, _queryRequestContext.UseCloud);

    var data = idNeedToFetchNew.Count > 0
      ? await sharkDbContext.City
        .AsNoTracking()
        .Where(c => idNeedToFetchNew.Contains(c.Id))
        .WithAutoMapper()
        .ToProjection<CityDto>()
        .ToListAsync(cancellationToken)
      : Enumerable.Empty<CityDto>();

    foreach (var city in data)
    {
      _configurableCacheManager.SetCache(
        city,
        CacheKeyHelper.GenerateKeyByPropOfDtoObject<CityDto, int>(city.Id.ToString(), c => c.Id)
      );

      dtoFromCache.Add(city);
    }

    return dtoFromCache.ToDictionary(x => x.Id);
  }
}
