using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Dividend;

/// <summary>
/// Extends <see cref="DividendType"/> to the <see cref="Instrument.InstrumentType"/>
/// </summary>
public class InstrumentTypeExtensions: ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(Instrument.InstrumentType.Name);

    descriptor
      .Field("dividends")
      .UsePaging(options: new HotChocolate.Types.Pagination.PagingOptions{
        IncludeTotalCount = true,
        MaxPageSize = int.MaxValue,
        DefaultPageSize = 100
      })
      .UseProjection()
      .UseFiltering()
      .UseSorting(typeof(DividendSortInputType))
      .ResolveWith<DividendResolvers>(p => p.GetDividends(default!, default!, default!, default!, default));

    descriptor
      .Field("annualDividends")
      .UseProjection()
      .UseFiltering()
      .UseSorting()
      .ResolveWith<DividendResolvers>(p => p.GetAnnualDividends(default!, default!, default!, default!, default));
  }
}
