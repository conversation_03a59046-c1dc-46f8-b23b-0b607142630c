import { useState } from 'react';
import './Tabs.scss';
import React from 'react'; // Added missing import for React

const Tabs = ({ tabs = [], activeTab, onTabChange, children }) => {


  const tabList = tabs;
  const [internalActiveTab, setInternalActiveTab] = useState(activeTab || tabList[0].id);

  const handleTabClick = (tabId) => {
    if (onTabChange) {
      onTabChange(tabId);
    } else {
      setInternalActiveTab(tabId);
    }
  };

  const currentActiveTab = activeTab || internalActiveTab;

  // Find the active tab content
  const activeTabContent = React.Children.toArray(children).find(
    child => child.props.tabId === currentActiveTab
  );

  return (
    <div className="Tabs">
      <div className="Tabs__container">
        {tabList.map((tab) => (
          <button
            key={tab.id}
            className={`Tabs__tab ${currentActiveTab === tab.id ? 'Tabs__tab--active' : ''}`}
            onClick={() => handleTabClick(tab.id)}
            type="button"
          >
            {tab.label}
          </button>
        ))}
      </div>
      {children && (
        <div className="Tabs__content">
          {activeTabContent}
        </div>
      )}
    </div>
  );
};

// TabContent component for individual tab content
const TabContent = ({ tabId, children }) => {
  return <div className="Tabs__tab-content">{children}</div>;
};

Tabs.TabContent = TabContent;

export default Tabs;
