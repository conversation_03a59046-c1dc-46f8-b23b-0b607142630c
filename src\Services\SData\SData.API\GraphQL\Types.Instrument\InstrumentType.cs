using System;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.Middleware;
using Euroland.FlipIT.SData.API.Infrastructure;
using HotChocolate.Internal;
using HotChocolate.Types;
using HotChocolate.Types.Pagination;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;

public class InstrumentType : ObjectType<InstrumentDto>
{
  public const string Name = "Instrument";
  protected override void Configure(IObjectTypeDescriptor<InstrumentDto> descriptor)
  {
    descriptor.Name(Name);

    descriptor.Field(f => f.Id).IsProjected(true);
    descriptor.Field(f => f.CompanyID).IsProjected(true);
    descriptor.Field(f => f.CompanyCode).IsProjected(true);
    descriptor.Field(f => f.CurrencyCode).IsProjected(true);
    descriptor.Field(f => f.MarketID).IsProjected(true);

    // CurrencyRate is used to calculate the price, not used for client.
    descriptor.Field(f => f.CurrencyRate).Ignore(ignore: true);

    descriptor
      .Field("currentPrice")
      .AddUseCloudArgument()
      .ResolveWith<InstrumentResolvers>(p => p.GetInstrumentPrice(default!, default!, default!, default));

    descriptor
      .Field("historicals")
      .AddUseCloudArgument()
      .AddAdjCloseArgument()
      .UsePaging(
        connectionName: "InstrumentHistory",
        options: new PagingOptions { IncludeTotalCount = true, MaxPageSize = int.MaxValue, DefaultPageSize = 200 })
      .UseProjection()
      .UseFiltering()
      .UseSorting(typeof(InstrumentHistorySortInputType))
      .ResolveWith<InstrumentResolvers>(p => p.GetInstrumentHistory(default!, default!, default!, default!));

    descriptor
      .Field("intraday")
      .AddUseCloudArgument()
      .Argument(
        "timeIntervalGrouping",
        cfg =>
          cfg.Type<IntType>()
            .DefaultValue(1)
            .Description("The time interval in minutes to group the data, default is 1 minute")
      )
      .AddAdjCloseArgument()
      .UsePaging(
        connectionName: "InstrumentIntraday",
        options: new PagingOptions { IncludeTotalCount = true, MaxPageSize = int.MaxValue, DefaultPageSize = 200 })
      .UseProjection()
      .UseFiltering()
      .UseSorting(typeof(InstrumentHistorySortInputType))
      .ResolveWith<InstrumentResolvers>(p => p.GetIntraday(default!, default!, default!, default!, default));

    descriptor
      .Field("intradayGroupBySecond")
      .AddUseCloudArgument()
      .Argument(
        "timeIntervalGrouping",
        cfg =>
          cfg.Type<IntType>()
            .DefaultValue(1)
            .Description("The time interval in seconds to group the data, default is 1 second")
      )
      .AddAdjCloseArgument()
      .UsePaging(
        connectionName: "InstrumentIntradayGroupBySecond",
        options: new PagingOptions { IncludeTotalCount = true, MaxPageSize = int.MaxValue, DefaultPageSize = 200 })
      .UseProjection()
      .UseFiltering()
      .UseSorting(typeof(InstrumentHistorySortInputType))
      .ResolveWith<InstrumentResolvers>(p =>
        p.GetIntradayGroupBySecondTimescale(default!, default!, default!, default));

    descriptor
      .Field("lastDayIntraday")
      .Description("Get all trades within the last day which has daily data.")
      .UsePaging(
        connectionName: "InstrumentLastDayIntraday",
        options: new PagingOptions { IncludeTotalCount = true, MaxPageSize = int.MaxValue, DefaultPageSize = 200 }
        /*resolvePagingProvider: ResolvePagingProvider*/)
      .UseProjection()
      .UseFiltering()
      .UseSorting(typeof(InstrumentDailyDataSortInputType))
      .ResolveWith<InstrumentResolvers>(p => p.GetLastDayIntraday(default!, default!, default!, default!));
  }

  private static CursorPagingProvider ResolvePagingProvider(
        IServiceProvider services,
        IExtendedType source,
        string? providerName)
    {
      return new LastDayIntradayQueryableCursorPagingProvider();
    }
}
