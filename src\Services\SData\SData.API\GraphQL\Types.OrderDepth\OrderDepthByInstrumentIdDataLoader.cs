using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.SData.API.GraphQL.Types.CurrencyRate;
using Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;
using Euroland.FlipIT.SData.API.Helpers;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Infrastructure.Factories;
using Euroland.FlipIT.Shared.CachingManager;
using GreenDonut;
using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.OrderDepth;

public class OrderDepthByInstrumentIdDataLoader: BatchDataLoader<int, OrderDepthDto>
{
  private readonly ISharkDbContextAbstractFactory _factory;
  private readonly IGraphQLExecutionContext _queryRequestContext;
  private readonly IConfigurableCacheManager _configurableCacheManager;
  private readonly InstrumentByIdDataLoader _instrumentByIdDataLoader;
  private readonly CurrencyRateByCurrencyPairDataLoader _currencyRateByCurrencyPairDataLoader;

  public OrderDepthByInstrumentIdDataLoader(
    ISharkDbContextAbstractFactory factory,
    IGraphQLExecutionContext queryRequestContext,
    IConfigurableCacheManager configurableCacheManager,
    InstrumentByIdDataLoader instrumentByIdDataLoader,
    CurrencyRateByCurrencyPairDataLoader currencyRateByCurrencyPairDataLoader,
    IBatchScheduler batchScheduler,
    DataLoaderOptions? options = null)
    : base(batchScheduler, options)
  {
    _factory = factory ?? throw new ArgumentNullException(nameof(factory));
    _queryRequestContext = queryRequestContext ?? throw new ArgumentNullException(nameof(queryRequestContext));
    _configurableCacheManager = configurableCacheManager ?? throw new ArgumentNullException(nameof(configurableCacheManager));
    _instrumentByIdDataLoader = instrumentByIdDataLoader ?? throw new ArgumentNullException(nameof(instrumentByIdDataLoader));
    _currencyRateByCurrencyPairDataLoader = currencyRateByCurrencyPairDataLoader ?? throw new ArgumentNullException(nameof(currencyRateByCurrencyPairDataLoader));
  }


  protected override async Task<IReadOnlyDictionary<int, OrderDepthDto>> LoadBatchAsync(IReadOnlyList<int> keys, CancellationToken cancellationToken)
  {
    var exchangeCurrency = _queryRequestContext.ExchangeCurrency;
    var hasExchangeCurrency = !string.IsNullOrEmpty(exchangeCurrency);

    var (needToFetchIds, dataFromCache) = CheckCache(_configurableCacheManager, keys, exchangeCurrency);

    if (needToFetchIds.Count == 0)
    {
      return dataFromCache.ToDictionary(p => p.InstrumentId);
    }

    using var sharkDbContext = await _factory.CreateDbContextAsync(_queryRequestContext.UseRealtime, _queryRequestContext.UseCloud);

    var marketDepthsQuery = sharkDbContext.MarketDepth.AsNoTracking();
    List<OrderDepthDto> result;

    if (!hasExchangeCurrency)
    {
      result = await marketDepthsQuery.Join(
          sharkDbContext.Instrument.AsNoTracking(),
          m => m.InstrumentId,
          i => i.InstrumentId,
          (market, instrument) => new { market, instrument }
        )
        .Where(p => needToFetchIds.Contains(p.market.InstrumentId))
        .Select(m => ToOrderDepthDto(m.market))
        .ToListAsync(cancellationToken);
    }
    else
    {
      // Get instrument currencies prepare for exchanging rate
      var instruments = await _instrumentByIdDataLoader.LoadAsync(needToFetchIds, cancellationToken);
      var ids = instruments.Select(c => c.Id).ToList();
      var currencyPairs = instruments
        .Where(c => !string.IsNullOrEmpty(c.CurrencyCode))
        .Select(c => $"{c.CurrencyCode?.Trim()}{exchangeCurrency?.Trim()}".ToLower()).ToList();

      var currencyRates = currencyPairs.Any()
        ? await _currencyRateByCurrencyPairDataLoader.LoadAsync(currencyPairs, cancellationToken)
        : Enumerable.Empty<CurrencyRateDto>();

      var getRateFunc = (int insId, DateTime? priceDate) =>
      {
        if (!ids.Contains(insId))
        {
          return 1;
        }
        var insCurr = instruments.FirstOrDefault(p => p.Id == insId)?.CurrencyCode?.Trim();

        if (string.IsNullOrEmpty(insCurr))
        {
          return 1;
        }

        var rate = currencyRates.FirstOrDefault(p => p != null &&
          string.Equals(p.Pair, $"{insCurr}{exchangeCurrency?.Trim()}", StringComparison.InvariantCultureIgnoreCase)
        );

        if (rate != null && priceDate?.Date.CompareTo(rate.Date?.Date) == 0)
        {
          return rate.Value ?? 1;
        }

        return 1;
      };

      var orderDepthList = await marketDepthsQuery.Join(
          sharkDbContext.Instrument.AsNoTracking(),
          m => m.InstrumentId,
          i => i.InstrumentId,
          (market, instrument) => new { market, instrument }
        )
        .Where(grouping => ids.Contains(grouping.market.InstrumentId))
        .Select(grouping => grouping.market)
        .ToListAsync(cancellationToken);

      // Exchange rates
      foreach (var p in orderDepthList)
      {
        var rateValue = getRateFunc(p.InstrumentId, p.FiRowUpdated);

        p.Fid_Best_Ask_1 *= rateValue;
        p.Fid_Best_Ask_2 *= rateValue;
        p.Fid_Best_Ask_3 *= rateValue;
        p.Fid_Best_Ask_4 *= rateValue;
        p.Fid_Best_Ask_5 *= rateValue;
        p.Fid_Best_Ask_6 *= rateValue;
        p.Fid_Best_Ask_7 *= rateValue;
        p.Fid_Best_Ask_8 *= rateValue;
        p.Fid_Best_Ask_9 *= rateValue;
        p.Fid_Best_Ask_10 *= rateValue;

        p.Fid_Best_Bid_1 *= rateValue;
        p.Fid_Best_Bid_2 *= rateValue;
        p.Fid_Best_Bid_3 *= rateValue;
        p.Fid_Best_Bid_4 *= rateValue;
        p.Fid_Best_Bid_5 *= rateValue;
        p.Fid_Best_Bid_6 *= rateValue;
        p.Fid_Best_Bid_7 *= rateValue;
        p.Fid_Best_Bid_8 *= rateValue;
        p.Fid_Best_Bid_9 *= rateValue;
        p.Fid_Best_Bid_10 *= rateValue;

      }

      result = orderDepthList.Select(m => ToOrderDepthDto(m)).ToList();

    }

    return result.ToDictionary(x => x.InstrumentId);
  }

  private OrderDepthDto ToOrderDepthDto(MarketDepth marketDepth)
  {
    var orderDepth = new OrderDepthDto
    {
      RowUpdated = marketDepth.FiRowUpdated.CestToUtc(),
      InstrumentId = marketDepth.InstrumentId
    };

    var askPrices = new[]
    {
          marketDepth.Fid_Best_Ask_1, marketDepth.Fid_Best_Ask_2, marketDepth.Fid_Best_Ask_3,
          marketDepth.Fid_Best_Ask_4, marketDepth.Fid_Best_Ask_5, marketDepth.Fid_Best_Ask_6,
          marketDepth.Fid_Best_Ask_7, marketDepth.Fid_Best_Ask_8, marketDepth.Fid_Best_Ask_9,
          marketDepth.Fid_Best_Ask_10
        };

    var askSizes = new[]
    {
          marketDepth.Fid_Best_Ask_1_Size, marketDepth.Fid_Best_Ask_2_Size, marketDepth.Fid_Best_Ask_3_Size,
          marketDepth.Fid_Best_Ask_4_Size, marketDepth.Fid_Best_Ask_5_Size, marketDepth.Fid_Best_Ask_6_Size,
          marketDepth.Fid_Best_Ask_7_Size, marketDepth.Fid_Best_Ask_8_Size, marketDepth.Fid_Best_Ask_9_Size,
          marketDepth.Fid_Best_Ask_10_Size
        };

    var bidPrices = new[]
    {
          marketDepth.Fid_Best_Bid_1, marketDepth.Fid_Best_Bid_2, marketDepth.Fid_Best_Bid_3,
          marketDepth.Fid_Best_Bid_4, marketDepth.Fid_Best_Bid_5, marketDepth.Fid_Best_Bid_6,
          marketDepth.Fid_Best_Bid_7, marketDepth.Fid_Best_Bid_8, marketDepth.Fid_Best_Bid_9,
          marketDepth.Fid_Best_Bid_10
        };

    var bidSizes = new[]
    {
          marketDepth.Fid_Best_Bid_1_Size, marketDepth.Fid_Best_Bid_2_Size, marketDepth.Fid_Best_Bid_3_Size,
          marketDepth.Fid_Best_Bid_4_Size, marketDepth.Fid_Best_Bid_5_Size, marketDepth.Fid_Best_Bid_6_Size,
          marketDepth.Fid_Best_Bid_7_Size, marketDepth.Fid_Best_Bid_8_Size, marketDepth.Fid_Best_Bid_9_Size,
          marketDepth.Fid_Best_Bid_10_Size
        };


    for (var i = 0; i < 10; i++)
    {
#pragma warning disable CS8629 // Nullable value type may be null.
      if (askPrices[i].HasValue && askPrices[i].Value > 0 &&
          askSizes[i].HasValue && askSizes[i].Value > 0 &&
          bidPrices[i].HasValue && bidPrices[i].Value > 0 &&
          bidSizes[i].HasValue && bidSizes[i].Value > 0)
      {
        orderDepth.MarketDepths.Add(new MarketDepthDto()
        {
          BuyPrice = bidPrices[i].Value,
          BuyVolume = bidSizes[i].Value,
          SellPrice = askPrices[i].Value,
          SellVolume = askSizes[i].Value
        });
      }
#pragma warning restore CS8629 // Nullable value type may be null.
    }

    return orderDepth;
  }

  /// <summary>
  /// Checks the cache for existing instrument price data.
  /// </summary>
  /// <param name="cacheManager">The cache manager.</param>
  /// <param name="instrumentIds">The list of instrument IDs.</param>
  /// <param name="useRealtime">Indicates whether to use real-time data.</param>
  /// <param name="adjClose">Indicates whether to use adjusted close prices.</param>
  /// <param name="exchangeCurrency">The exchange currency, if any.</param>
  /// <returns>A tuple containing the list of IDs that need to be fetched and the cached instrument price DTOs.</returns>
  private (IReadOnlyList<int> needToFetchIds, IReadOnlyList<OrderDepthDto> cacheDtos) CheckCache(
    IConfigurableCacheManager cacheManager,
    IReadOnlyList<int> instrumentIds,
    string? exchangeCurrency)
  {

    var dtoFromCache = new List<OrderDepthDto>();
    var idNeedToFetchNew = new List<int>();

    foreach (var instrumentId in instrumentIds.Distinct())
    {
      var dto = cacheManager.GetCache<OrderDepthDto, string>(BuildCacheKey(instrumentId, exchangeCurrency));

      if (dto != null)
      {
        dtoFromCache.Add(dto);
      }
      else
      {
        idNeedToFetchNew.Add(instrumentId);
      }
    }

    return (idNeedToFetchNew.AsReadOnly(), dtoFromCache.AsReadOnly());
  }

  /// <summary>
  /// Builds a cache key for the instrument price data.
  /// </summary>
  /// <param name="instrumentId">The instrument ID.</param>
  /// <param name="useRealtime">Indicates whether to use real-time data.</param>
  /// <param name="adjClose">Indicates whether to use adjusted close prices.</param>
  /// <param name="exchangeCurrency">The exchange currency, if any.</param>
  /// <returns>A string representing the cache key.</returns>
  private static string BuildCacheKey(
    int instrumentId,
    string? exchangeCurrency
    )
  {
    return $"instPrice_{instrumentId}_{exchangeCurrency ?? ""}";
  }


}
