﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Euroland.FlipIT.SData.API.Infrastructure.Migrations
{
  /// <inheritdoc />
  public partial class AddStoreProcedureSelectCurrencyRateHistoryWithDates : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                CREATE TYPE CurrencyHistoryByDatesUDT AS TABLE
                (
                    cpair CHAR(6) NOT NULL,
                    cdate DATE NOT NULL
                )
            ");

            migrationBuilder.Sql(@"
                CREATE PROC spCurrencyRateHistorySelectByDates
                (
                    @queryTable CurrencyHistoryByDatesUDT READONLY
                )
                AS
                BEGIN
                    select c.cCurr, c.chID, c.cId, c.chRate, c.chDate, c.chRate2, q.cdate as qDate from (
                        select distinct q1.* from @queryTable q1
                    ) as q
                    --inner join (
                    --	select
                    --	min(q.cdate) as mindate,
                    --	max(q.cdate) as maxdate,
                    --	cpair
                    --		from @queryTable q
                    --		group by cpair
                    --) as q2
                    --on q.cpair = q2.cpair
                    cross apply (
                        select distinct c.cCurr, h.chID, h.cId, h.chRate, h.chDate, h.chRate2
                        from CurrencyRateHistory h
                        inner join CurrencyRate c
                        on h.cId = c.cId
                        where c.cCurr = q.cpair
                        and h.chDate = (select max(chDate) from CurrencyRateHistory h2 where h2.chDate <= q.cdate and h2.cId = c.cId)
                    ) as c

                END
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DROP PROC spCurrencyRateHistorySelectByDates");
            migrationBuilder.Sql("DROP TYPE CurrencyHistoryByDatesUDT");
        }
    }
}
