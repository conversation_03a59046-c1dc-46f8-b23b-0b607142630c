import {ensureDefined} from "../helpers";

export const searchParams = new URLSearchParams(window.location.search);

// Array of RTL language codes
const RTL_LANGUAGES = ['ar-ae', 'ar', 'he', 'fa'];

const language = ensureDefined(searchParams.get("lang") || "en-gb").toLowerCase();
const version = ensureDefined(searchParams.get("version") || "default");

export const appSettings = {
  companyCode: ensureDefined(searchParams.get("companyCode")),
  language,
  isRTL: RTL_LANGUAGES.includes(language),
  version
};

export const LocalStorageSearchResultKey = `wiseSearchHistory-${appSettings.companyCode || ""}`;
