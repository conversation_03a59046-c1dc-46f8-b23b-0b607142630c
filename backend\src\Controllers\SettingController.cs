﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Euroland.FlipIT.WiseSearch.Infrastructure.Entities;
using Euroland.FlipIT.WiseSearch.Infrastructure.UnitOfWorks;
using Euroland.FlipIT.WiseSearch.Models.Setting;
using Euroland.FlipIT.WiseSearch.Services;

namespace Euroland.FlipIT.WiseSearch.Controllers
{
    [ApiController]
    [Route("/api/[controller]")]
    public class SettingController : Controller
    {
        private readonly ISettingService _settingService;
        private readonly IWiseSearchUoW _wiseSearchUoW;

        public SettingController(ISettingService settingService,
                                 IWiseSearchUoW wiseSearchUoW)
        {
            _settingService = settingService;
            _wiseSearchUoW = wiseSearchUoW;
        }

        [HttpGet("company-configuration")]
        public async Task<ActionResult<CompanyConfiguration>> GetCompanyConfigurationAsync(string companyCode, string language)
        {
            CompanyConfiguration? result = await _settingService.GetCompanyConfigurationAsync(companyCode, language);
            return Ok(result);
        }

        [AllowAnonymous]
        [HttpGet("wise-search")]
        public async Task<ActionResult<SettingResponse>> GetSettingByCodeAndVersion(string companyCode, string? version = null)
        {
            var setting = await _wiseSearchUoW.WsSettingRepository.GetSettingByCodeAndVersion(companyCode, version);

            if (setting is null)
            {
                return NotFound($"Not found company setting by code {companyCode}, version {version}");
            }

            return Ok(setting);
        }
    }
}
