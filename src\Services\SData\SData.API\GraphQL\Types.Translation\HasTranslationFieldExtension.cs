using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.Types.Translation;
using HotChocolate.Types;

namespace Microsoft.Extensions.DependencyInjection;

internal static class HasTranslationFieldExtension
{
  public static IObjectFieldDescriptor TranslationField<T>(this IObjectTypeDescriptor<T> descriptor)
    where T : HasTranslation
  {
    descriptor.Field(f => f.TranslationId).IsProjected();

    return descriptor
      .Field("translation")
      .AddCultureNameArgument()
      .Type<ObjectType<TranslationDto>>()
      .ResolveWith<TranslationResolvers>(resolvers =>
        resolvers.GetTranslation(default!, default!, default!, default!, default!));
  }

  public static IObjectFieldDescriptor AddCultureNameArgument(this IObjectFieldDescriptor descriptor)
  {
    return descriptor.Argument("cultureName",
      d => d.Type<StringType>()
        .DefaultValue(null)
        .Description("Culture name for translation in IETF BCP 47 Language Tag (e.g. 'en-gb', 'ar-ae', 'de-de')."));
  }

  public static IInterfaceFieldDescriptor AddCultureNameArgument(this IInterfaceFieldDescriptor descriptor)
  {
    return descriptor.Argument("cultureName",
      d => d.Type<StringType>()
        .DefaultValue(null)
        .Description("Culture name for translation in IETF BCP 47 Language Tag (e.g. 'en-gb', 'ar-ae', 'de-de')."));
  }
}
