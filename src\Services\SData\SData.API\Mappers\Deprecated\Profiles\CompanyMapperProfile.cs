﻿using AutoMapper;
using Euroland.FlipIT.SData.API.DTOs.Deprecated;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;
using System.Linq;

namespace Euroland.FlipIT.SData.API.Mappers.Deprecated.Profiles
{
    public class CompanyMapperProfile : Profile
    {
        /// <summary>
        /// AutoMapper Profile: <see cref="CompanyInfo"/>
        /// </summary>
        public CompanyMapperProfile()
        {
            string lang = "";
            CreateMap<Company, CompanyInfo>(MemberList.Destination)
                .ForMember(dest => dest.CompanyId, conf => conf.MapFrom(ol => ol.CompanyId))
                .ForMember(dest => dest.CompanyName, conf =>
                    conf.MapFrom(ol => ol.CompanyNames == null || string.IsNullOrEmpty(ol.CompanyNames.First(c => c.CompanyLang == lang).CompanyName)
                    ? EF.Functions.Collate(ol.CompanyName, "SQL_Latin1_General_CP1_CS_AS")
                    : EF.Functions.Collate(ol.CompanyNames.First(c => c.CompanyLang == lang).CompanyName, "SQL_Latin1_General_CP1_CS_AS")
                    ));
        }
    }
}
