﻿using HotChocolate;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Euroland.FlipIT.SData.API.DTOs.Deprecated.Webcasts
{
  [GraphQLName("WebcastDetail_Deprecated")]
    public class WebcastDetailOutputDto
    {
        [JsonProperty("date")]
        private DateTime? date;

        [JsonProperty("videoType")]
        private string? videoType;

        [JsonProperty("title")]
        private string? title;

        [JsonProperty("videos")]
        private IEnumerable<WebcastVideo>? videos;

        [JsonProperty("vimeos")]
        private WebcastVimeo vimeos;

        [JsonProperty("pdf_URL")]
        private string? pdfURL;

        public IEnumerable<WebcastVideo>? Urls { get => videos; set => videos = value; }
        public WebcastVimeo? Vimeo { get => vimeos; set => vimeos = value; }
        public string? TranscriptUrl { get => pdfURL; set => pdfURL = value; }
        public string? Title { get => title; set => title = value; }
        public string? VideoType { get => videoType; set => videoType = value; }
        public DateTime? Date { get => date; set => date = value; }
        public long Id { get; set; }
        public string? ThumbnailUrl { get; set; }
        public string? DefaultHost { get; set; }
    }

    [GraphQLName("VideoGeneral_Deprecated")]
    public class VideoGeneral
    {
        [JsonProperty("videoId")]
        private string? videoId;

        public string? VideoId { get => videoId; set => videoId = value; }
        public string? Url { get; set; }
        public string? HostType { get; set; }
    }

    [GraphQLName("WebcastVideo_Deprecated")]
    public class WebcastVideo : VideoGeneral
    {
    }

    [GraphQLName("WebcastVimeo_Deprecated")]
    public class WebcastVimeo : VideoGeneral
    {
        public string? VimeoId { get; set; }
    }

    [GraphQLName("Webcast_Deprecated")]
    public class WebcastOutputDto
    {
        public string CompanyCode { get; set; }
        public IQueryable<WebcastDetailOutputDto>? Webcasts { get; set; }
    }
}
