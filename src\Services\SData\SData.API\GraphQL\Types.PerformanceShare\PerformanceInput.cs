using System;
using Euroland.FlipIT.SData.API.Dto;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.PerformanceShare;

internal class PerformanceInput
{
  public bool AdjClose { get; set; }
  public int InstrumentId { get; set; }
  public string? InstrumentCurrencyCode { get; set; }
  public int? CurrencyRateId { get; set; }
  public PerformancePeriodDto Period { get; set; }
  public DateTime? FromDate { get; set; }
}
