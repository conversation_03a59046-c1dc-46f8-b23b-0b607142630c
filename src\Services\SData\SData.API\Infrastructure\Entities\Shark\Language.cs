using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities.Shark;

public class Language
{
  [Key]
  [Column("laID")]
  public int Id { get; set; }

  [Column("laCultureCode")]
  [Required]
  public string CultureCode { get; set; }

  [Column("laName")]
  [Required]
  public string Name { get; set; }

  [Column("laISO")]
  public string? ISO { get; set; }

  [Column("laIDHugin")]
  public int? IDHugin { get; set; }

  [Column("laIDOMX")]
  public int? IDOMX { get; set; }

  [Column("laNameEnglish")]
  public string? NameEnglish { get; set; }

  [Column("date_format")]
  public string? DateFormat { get; set; }
}
