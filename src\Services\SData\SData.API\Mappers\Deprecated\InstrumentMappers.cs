﻿using AutoMapper;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Mappers.Deprecated.Profiles;
using System.Collections.Generic;
using System.Linq;

namespace Euroland.FlipIT.SData.API.Mappers.Deprecated
{
    public static class InstrumentMappers
    {
        internal static IMapper Mapper { get; }
        static InstrumentMappers()
        {
            Mapper = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<InstrumentMapperProfile>();
            }).CreateMapper();
        }

        public static IQueryable<DTOs.Deprecated.Instrument> ToInstrumentDTO(this IQueryable<Instrument> instrument, int dayPeriod, string langCode, bool isRT, string? toCurrency)
        {
          toCurrency ??= string.Empty;

          return Mapper.ProjectTo<DTOs.Deprecated.Instrument>(instrument,
            new Dictionary<string, object> { { "dayPeriod", dayPeriod }, { "langCode", langCode }, { "isRT", isRT }, { "toCurrency", toCurrency } });
        }

    }
}
