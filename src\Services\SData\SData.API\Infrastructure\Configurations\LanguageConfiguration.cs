using Euroland.FlipIT.SData.API.Infrastructure.Entities.Shark;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Euroland.FlipIT.SData.API.Infrastructure.Configurations;

public class LanguageConfiguration: IEntityTypeConfiguration<Language>
{
  private const string ViewName = "Language";
  public void Configure(EntityTypeBuilder<Language> builder)
  {
    builder.ToView(ViewName);
  }
}
