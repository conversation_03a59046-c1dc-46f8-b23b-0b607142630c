using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;
using HotChocolate;
using HotChocolate.Resolvers;
using HotChocolate.Types;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.PerformanceShare;

/// <summary>
/// Extends type of <see cref="PerformanceShareType"/> to type <see cref="Instrument.InstrumentType"/>
/// </summary>
public class InstrumentTypeExtensions: ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(InstrumentType.Name);

    descriptor.Field("performance")
      .Type<PerformanceShareType>()
      .Argument(
        "period",
        argDescriptor =>
          argDescriptor.Type<PerformancePeriodType>()
            .DefaultValue(PerformancePeriodDto.YTD)
            .Description("Predefined period to calculate performance of share. Otherwise, specify `CUSTOM` period combines with argument `fromDate` to input manually the start of date in the past to calculate")
      )
      .Argument(
        "fromDate",
        argDescriptor =>
          argDescriptor.Type<DateTimeType>()
            .DefaultValue(null)
            .Description("The UTC date in the past to calculate performance from. The input date must be at least 1 week earlier than present")
      )
      .ResolveWith<Resolvers>(
        q => q.GetCalculatePerformanceShareAsync(default!, default!, default!, default!, default, default, default)
      );

  }

  private sealed class Resolvers {
    public async Task<PerformanceShareDto?> GetCalculatePerformanceShareAsync(
      [Service] IGraphQLExecutionContext executionContext,
      [Parent] InstrumentDto instrument,
      PerformanceShareBatchDataLoader performanceShareBatchDataLoader,
      IResolverContext resolverContext,
      PerformancePeriodDto period,
      DateTime? fromDate = default,
      CancellationToken cancellationToken = default
    )
    {
      if(period == PerformancePeriodDto.CUSTOM)
      {
        if(fromDate == null || DateTime.UtcNow.AddDays(-7) < fromDate.Value) {
          resolverContext.ReportError("Must provide a valid date for argument `fromDate` when `CUSTOM` period is specified. Input date must be at least 1 week earlier than present");
          return null;
        }
      }
      else
      {
        fromDate = PerformanceShareGroupDataLoader.GetStartDateFromPeriod(period);
      }

      var periods = resolverContext.ScopedContextData[nameof(PerformanceShareType)] as Dictionary<int, List<PerformanceInput>>;

      if(!periods.ContainsKey(instrument.Id)) {
        periods.Add(instrument.Id, new List<PerformanceInput>());
      }

      var inputs = periods[instrument.Id];
      if(!inputs.Any(p => p.Period == period))
      {
        inputs.Add(new PerformanceInput {
          InstrumentId = instrument.Id,
          InstrumentCurrencyCode = instrument.CurrencyCode,
          FromDate = fromDate,
          Period = period,
          AdjClose = executionContext.UseAdjClose
        });
      }
      resolverContext.ScopedContextData = resolverContext.ScopedContextData.SetItem(nameof(PerformanceShareType), periods);
      executionContext.ScopedContextData[nameof(PerformanceShareType)] = periods;
      return await performanceShareBatchDataLoader.LoadAsync(PerformanceShareGroupDataLoader.ComputeCacheKey(instrument.Id, period), cancellationToken);
    }
  }
}
