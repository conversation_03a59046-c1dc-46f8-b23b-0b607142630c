using Euroland.FlipIT.SData.API.GraphQL.Types.Webcast;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class WebcastRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddWebcastType(this IRequestExecutorBuilder builder)
  {
    builder.AddType<WebcastType>();
    builder.AddTypeExtension<CompanyTypeExtensions>();

    return builder;
  }
}
