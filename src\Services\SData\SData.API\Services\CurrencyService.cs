using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.DTOs.Deprecated.Currencies;

namespace Euroland.FlipIT.SData.API.Services
{
  public class CurrencyService : ICurrencyService
    {
        private readonly SharkDbContextBase _sharkDbContext;

        public CurrencyService(IDbContextFactory<DefaultSharkDbContext> sharkContextFactory)
        {
            _sharkDbContext = sharkContextFactory?.CreateDbContext() ?? throw new ArgumentNullException(nameof(sharkContextFactory));
        }

        public void Dispose()
        {
            _sharkDbContext.Dispose();
        }

        public async ValueTask DisposeAsync()
        {
            await _sharkDbContext.DisposeAsync();
        }

        public async Task<IEnumerable<Currency>> GetCurrencies()
        {
            return await _sharkDbContext.Currency.AsNoTracking()
                    .Select(curr => new Currency
                    {
                        Code = curr.Code,
                        Name = curr.Name,
                        IsRegionMajor = curr.IsRegionMajor,
                        DecimalPlace = curr.DecimalPlace,
                        TranslationId = curr.TranslationId
                    }).ToListAsync();
        }

    public async Task<IEnumerable<Currency>> GetCurrenciesByCodes(List<String> codes)
    {
      return await _sharkDbContext.Currency.AsNoTracking()
              .Where(curr => codes.Contains(curr.Code))
              .Select(curr => new Currency
              {
                Code = curr.Code,
                Name = curr.Name,
                IsRegionMajor = curr.IsRegionMajor,
                DecimalPlace = curr.DecimalPlace,
                TranslationId = curr.TranslationId
              }).ToListAsync();
    }
  }
}
