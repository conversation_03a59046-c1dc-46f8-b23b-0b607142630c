using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using HotChocolate;
using HotChocolate.Resolvers;
using HotChocolate.Types;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Currency;
public class InstrumentTypeExtensions : ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(Instrument.InstrumentType.Name);

    descriptor
      .Field("currency")
      .UseFirstOrDefault()
      .UseProjection()
      .ResolveWith<Resolvers>(p => p.GetCurrencyFromInstrument(default!, default!, default!, default!));
  }

  private class Resolvers {
    public async Task<CurrencyDto?> GetCurrencyFromInstrument(
      [Parent] InstrumentDto instrument,
      CurrencyByCodeDataLoader dataLoader,
      IResolverContext resolverContext,
      CancellationToken cancellationToken = default
    )
    {
      if(string.IsNullOrEmpty(instrument.CurrencyCode)) {
        return null;
      }

      return await dataLoader.LoadAsync(instrument.CurrencyCode.ToLower()!, cancellationToken);
    }
  }
}
