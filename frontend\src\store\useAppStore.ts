import '@/lib/ensureRunOnRoot';
import {create, StateCreator} from 'zustand';
import {IUserProfile, userProfile} from './userProfile';
import {INotificationSlice, notificationSlice} from './notificationSlice';
import profileMenuSlice from './profileMenuSlice';
import confirmPopupSlice from './logoutConfirmSlice';

import type {IProfileMenuSlice} from './profileMenuSlice';
import type {IConfirmPopupSlice} from './logoutConfirmSlice';
import type {ISearchWidgetSlice} from './searchWidgetSlice';
import searchWidgetSlice from './searchWidgetSlice';

export type IStoreState = IUserProfile &
  INotificationSlice &
  IProfileMenuSlice &
  IConfirmPopupSlice &
  ISearchWidgetSlice;

const createRootSlice: StateCreator<IStoreState> = (...a) => ({
  ...userProfile(...a),
  ...notificationSlice(...a),
  ...profileMenuSlice(...a),
  ...confirmPopupSlice(...a),
  ...searchWidgetSlice(...a)
})

export const useAppStore = create(createRootSlice)