import { LocalStorageSearchResultKey } from '@/config/appSettings';

import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { i18n } from "@euroland/libs";
import {IAiSearchHistory} from '@/services/ai';
import {joinPath} from './join-path';


export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function saveNewSearchResult(result: IAiSearchHistory) {
  const currentResults = localStorage.getItem(LocalStorageSearchResultKey);
  let newResults = [];
  try {
    newResults = currentResults ? JSON.parse(currentResults) || [] : [];
    if (!Array.isArray(newResults)) {
      newResults = [currentResults];
    }
    newResults.unshift(result);
  } catch (error) {
    console.log('Parse JSON error:', error);
  }
  localStorage.setItem(LocalStorageSearchResultKey, JSON.stringify(newResults));
}

export function getSearchResultHistory(): Array<IAiSearchHistory> {
  const searchResults = localStorage.getItem(LocalStorageSearchResultKey);
  let searchHistories = [];
  try {
    searchHistories = searchResults ? JSON.parse(searchResults) || [] : [];
  } catch (error) {
    console.log('Parse JSON error:', error);
  }

  return searchHistories;
}

export function clearSearchHistory() {
  localStorage.setItem(LocalStorageSearchResultKey, JSON.stringify([]));
}

async function getTranslation(locale: string) {
  const response = await fetch(joinPath(import.meta.env.BASE_URL, `translations/${locale}.json?v=${import.meta.env.VITE_BUILD_TIME}`), {
    cache: 'force-cache'
  });
  const translations = await response.json();
  return translations;
}

export const updateTranslation = async (languageCode: string) => {
  const locale = parseLanguageCode(languageCode);
  try {
    const translations = await getTranslation(locale);
    await i18n.load(locale, translations, () => {});
  } catch (error) {
    const translations = await getTranslation('en');
    await i18n.load(locale, translations, () => {});
    console.warn(`Failed to load translation for ${locale}, fallback to en`);
    return;
  }
};

export function parseLanguageCode(fullCode: string): string {
  return fullCode.split('-')[0].toLowerCase();
}

export function deleteSearchHistoryItem(id: string) {
  const searchHistories = getSearchResultHistory();
  const filteredHistories = searchHistories.filter((history) => history.id !== id);
  localStorage.setItem(LocalStorageSearchResultKey, JSON.stringify(filteredHistories));
}
