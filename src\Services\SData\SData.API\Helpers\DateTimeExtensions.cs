﻿using System;
using TimeZoneConverter;

namespace Euroland.FlipIT.SData.API.Helpers
{
    public static class DateTimeExtensions
    {
        // Works on Windows only
        //static TimeZoneInfo _cetTZ = TimeZoneInfo.FindSystemTimeZoneById("Central European Standard Time");

        // Works on Linux only
        //static TimeZoneInfo _cetTZ = TimeZoneInfo.FindSystemTimeZoneById("Europe/Budapest");

        // Retrieves a System.TimeZoneInfo object given a valid Windows or IANA time zone
        // identifier, regardless of which platform the application is running on.
        static readonly TimeZoneInfo _cetTZ = TZConvert.GetTimeZoneInfo("Central European Standard Time");
        public static DateTime? ToKindUtc(this DateTime? cetDate)
        {
            if (cetDate.HasValue)
            {
                var utcDate = TimeZoneInfo.ConvertTimeToUtc(cetDate.Value, _cetTZ);
                return DateTime.SpecifyKind(utcDate, DateTimeKind.Utc);
            }
            return cetDate;
        }

        [Obsolete($"Use {nameof(ToUtcKindOnly)} instead")]
        public static DateTime ToUtc(this DateTime date)
        {
            return DateTime.SpecifyKind(date, DateTimeKind.Utc);
        }

        [Obsolete($"Use {nameof(ToUtcKindOnly)} instead")]
        public static DateTime ToUtc(this DateTime? date)
        {
            if (date.HasValue) return DateTime.SpecifyKind(date.Value, DateTimeKind.Utc);
            return DateTime.Now;
        }

        [Obsolete("Will be remove in the next release")]
        public static DateTime ToKindUtc(this DateTime cetDate)
        {
            if (cetDate.Kind != DateTimeKind.Unspecified)
            {
                cetDate = DateTime.SpecifyKind(cetDate, DateTimeKind.Unspecified);
            }
            var utcDate = TimeZoneInfo.ConvertTimeToUtc(cetDate, _cetTZ);
            return DateTime.SpecifyKind(utcDate, DateTimeKind.Utc);
        }

        [Obsolete("Will be remove in the next release")]
        public static DateTime? ToCEST(this DateTime? date)
        {
            if (date.HasValue)
            {
                return TimeZoneInfo.ConvertTimeFromUtc(date.Value, _cetTZ);
            }
            return null;
        }

        [Obsolete("Will be remove in the next release")]
        public static DateTime ToCEST(this DateTime date)
        {
            var result = TimeZoneInfo.ConvertTimeFromUtc(date, _cetTZ);
            return result;
        }

        public static DateTime UtcToCest(this DateTime date)
        {
          if(date.Kind != DateTimeKind.Utc) {
            throw new ArgumentException("The input date must be a kind of UTC", nameof(date));
          }
          var result = TimeZoneInfo.ConvertTimeFromUtc(date, _cetTZ);
          return result;
        }

        /// <summary>
        /// Converts a <see cref="DateTime"/> from CEST to UTC
        /// and convert kind of output <see cref="DateTime"/> to <see cref="DateTimeKind.Utc"/>.
        /// </summary>
        /// <param name="cestDateTime">DateTime in CEST timezone</param>
        /// <returns></returns>
        public static DateTime CestToUtc(this DateTime cestDateTime)
        {
          if (cestDateTime.Kind != DateTimeKind.Unspecified)
            {
                cestDateTime = DateTime.SpecifyKind(cestDateTime, DateTimeKind.Unspecified);
            }
            var utcDate = TimeZoneInfo.ConvertTimeToUtc(cestDateTime, _cetTZ);
            return DateTime.SpecifyKind(utcDate, DateTimeKind.Utc);
        }

        /// <summary>
        /// Convert kind of a given <see cref="DateTime"/> to <see cref="DateTimeKind.Utc"/> only,
        /// but without converting timezone.
        /// </summary>
        /// <param name="dateTime">DateTime in CEST timezone</param>
        /// <returns></returns>
        public static DateTime ToUtcKindOnly(this DateTime dateTime)
        {
          if(dateTime.Kind != DateTimeKind.Utc)
          {
            return DateTime.SpecifyKind(dateTime, DateTimeKind.Utc);
          }
          else
          {
            return dateTime;
          }
        }

        public static int GetWeekNumberFromNow()
        {
            var now = DateTime.UtcNow.Date;
            int dayOfYear = now.DayOfYear;
            int weekNumber = (dayOfYear - 1) / 7 + 1;

            return weekNumber;
        }

        public static DateTime GetFirstDateOfYear(int year)
        {
            return new DateTime(year, 1, 1);
        }

        public static DateTime GetFirstDateOfYear(this DateTime currentDate)
        {
            return new DateTime(currentDate.Year, 1, 1);
        }

        public static DateTime GetLastDateOfYear(int year)
        {
            return new DateTime(year, 12, 31);
        }

        public static DateTime GetLastDateOfYear(this DateTime currentDate)
        {
            return new DateTime(currentDate.Year, 12, 31);
        }

        public static DateTime GetDateWeeksAgo(this DateTime currentDate, int weeksAgo)
        {
            return currentDate.AddDays(-7 * weeksAgo);
        }

        public static DateTime GetDateMonthsAgo(this DateTime currentDate, int monthsAgo)
        {
            return currentDate.AddMonths(-monthsAgo);
        }

        public static DateTime GetDateYearsAgo(this DateTime currentDate, int yearsAgo)
        {
            return currentDate.AddYears(-yearsAgo);
        }

        public static DateTimeOffset DateTimeOffsetToCest(this DateTimeOffset utcDateTime)
        {
          return TimeZoneInfo.ConvertTime(utcDateTime, _cetTZ);
        }

        /// <summary>
        /// Retrieves the timezone offset for Central European Time (CET/CEST) relative to UTC.
        /// Automatically handles seasonal changes between CET and CEST.
        /// </summary>
        /// <param name="referenceDate">Optional reference date to calculate the offset for. Defaults to current UTC time.</param>
        /// <returns>The CET/CEST timezone offset in minutes relative to UTC.</returns>
        public static int GetCetTimezoneOffset(DateTime? referenceDate = null)
        {
          var dateToUse = referenceDate?.ToUniversalTime() ?? DateTime.UtcNow;
          var offset = _cetTZ.GetUtcOffset(dateToUse);
          return (int)offset.TotalMinutes;
        }
    }
}
