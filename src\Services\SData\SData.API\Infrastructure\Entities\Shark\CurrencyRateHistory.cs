using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using HotChocolate;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
    [Table("CurrencyRateHistory")]
    public class CurrencyRateHistory
    {
        [Key]
        [Column("chID")]
        public long ID { get; set; }

        [Column("cId")]
        public int CurrencyRateID { get; set; }

        [Column(name: "chRate", TypeName = "numeric(28,10)")]
        public decimal Rate { get; set; }

        [Column("chDate")]
        public DateTime Date { get; set; }

        [GraphQLIgnore]
        public virtual CurrencyRate CurrencyRate { get; set; }
    }
}
