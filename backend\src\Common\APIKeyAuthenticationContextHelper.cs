﻿using Euroland.FlipIT.WiseSearch.Models.Authentication;

namespace Euroland.FlipIT.WiseSearch.Common
{
    public class APIKeyAuthenticationContextHelper
    {
        public static APIKeyAuthenticationContext FromHeaders(IHeaderDictionary headers)
        {
            return new APIKeyAuthenticationContext
            {
                Username = headers["X-User-Username"].FirstOrDefault() ?? "",

                APIKey = headers["X-API-Key"].FirstOrDefault(),
                Timestamp = headers["X-Timestamp"].FirstOrDefault(),
                Signature = headers["X-Signature"].FirstOrDefault()
            };
        }
    }
}
