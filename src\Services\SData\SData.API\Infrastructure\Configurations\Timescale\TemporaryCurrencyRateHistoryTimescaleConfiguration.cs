using Euroland.FlipIT.SData.API.Infrastructure.Entities.Timescale;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Euroland.FlipIT.SData.API.Infrastructure.Configurations.Timescale;

public class
  TemporaryCurrencyRateHistoryTimescaleConfiguration : IEntityTypeConfiguration<TemporaryCurrencyRateHistoryTimescale>
{
  public const string TemporaryCurrencyRateHistoryTableName = "temporary_currency_rate_history";

  public void Configure(EntityTypeBuilder<TemporaryCurrencyRateHistoryTimescale> builder)
  {
    // Configure temporary table as a View to be ignored during migration.
    // Also the entity TemporaryCurrencyRateHistory is created as keyless
    builder
      .ToView(TemporaryCurrencyRateHistoryTableName)
      .HasAlternateKey(x => x.Id);

    builder.Property(x => x.Id)
      .HasColumnName("id")
      .HasColumnType("bigint");

    builder.Property(x => x.Date)
      .HasColumnName("date")
      .IsRequired();

    builder.Property(x => x.Rate)
      .HasColumnName("rate")
      .HasColumnType("numeric(21,7)")
      .IsRequired();
  }
}
