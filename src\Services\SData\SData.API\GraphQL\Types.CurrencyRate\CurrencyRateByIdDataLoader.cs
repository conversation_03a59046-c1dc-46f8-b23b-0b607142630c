using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using Euroland.FlipIT.SData.API.Infrastructure.Factories;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.Shared.CachingManager;
using GreenDonut;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.CurrencyRate;

public class CurrencyRateByIdDataLoader : BatchDataLoader<int, CurrencyRateDto>
{
  private readonly ISharkDbContextAbstractFactory _factory;
  private readonly IGraphQLExecutionContext _queryRequestContext;
  private readonly IConfigurableCacheManager _cache;

  public CurrencyRateByIdDataLoader(
    ISharkDbContextAbstractFactory factory,
    IGraphQLExecutionContext queryRequestContext,
    IConfigurableCacheManager configurableCacheManager,
    IBatchScheduler batchScheduler,
    DataLoaderOptions? options = null)
    : base(batchScheduler, options)
  {
    _factory = factory ?? throw new ArgumentNullException(nameof(factory));
    _queryRequestContext = queryRequestContext ?? throw new ArgumentNullException(nameof(queryRequestContext));
    _cache = configurableCacheManager ?? throw new ArgumentNullException(nameof(configurableCacheManager));
  }

  protected override async Task<IReadOnlyDictionary<int, CurrencyRateDto>> LoadBatchAsync(IReadOnlyList<int> keys, CancellationToken cancellationToken)
  {
    var filterKeys = keys.Where(k =>
        _cache.GetCache<CurrencyRateDto, string>(
          CacheKeyHelper.GenerateKeyByPropOfDtoObject<CurrencyRateDto, int>(k.ToString(), cr => cr.Id)) == null)
      .ToList();

    await using var sharkDbContext = await _factory.CreateDbContextAsync(_queryRequestContext.UseRealtime, _queryRequestContext.UseCloud);

    var data = filterKeys.Count > 0
      ? await sharkDbContext.CurrencyRates
        .AsNoTracking()
        .Where(cr => filterKeys.Contains(cr.ID))
        .WithAutoMapper()
        .ToProjection<CurrencyRateDto>()
        .ToListAsync(cancellationToken)
      : Enumerable.Empty<CurrencyRateDto>();

    foreach (var currencyRate in data)
    {
      _cache.SetCache(
        currencyRate,
        CacheKeyHelper.GenerateKeyByPropOfDtoObject<CurrencyRateDto, int>(currencyRate.Id.ToString(), cr => cr.Id)
      );
    }

    var result = keys.Select(k =>
        _cache.GetCache<CurrencyRateDto, string>(
          CacheKeyHelper.GenerateKeyByPropOfDtoObject<CurrencyRateDto, int>(k.ToString(), cr => cr.Id)))
      .ToList();

    return result.ToDictionary(cr => cr.Id);
  }
}
