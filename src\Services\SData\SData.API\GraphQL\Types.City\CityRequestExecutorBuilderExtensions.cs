using Euroland.FlipIT.SData.API.GraphQL.Types.City;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class CityRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddCityType(this IRequestExecutorBuilder builder)
  {
    builder.AddType<CityType>();
    builder.AddTypeExtension<MarketTypeExtensions>();

    builder.AddDataLoader<CityByIdDataLoader>();

    return builder;
  }
}
