using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;
using HotChocolate;
using HotChocolate.Resolvers;
using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.OrderDepth;

public class OrderDepthType: ObjectType<OrderDepthDto>
{
  protected override void Configure(IObjectTypeDescriptor<OrderDepthDto> descriptor)
  {
    descriptor.Name("OrderDepth");
    descriptor.Field(p => p.InstrumentId).IsProjected();
    descriptor
      .Field("instrument")
      .ResolveWith<Resolvers>(r => r.GetInstrument(default!, default!, default!, default));
  }

  private class Resolvers {
    public async Task<InstrumentDto> GetInstrument(
      [Parent] OrderDepthDto orderDepth,
      IResolverContext resolverContext,
      InstrumentByIdDataLoader instrumentByIdDataLoader,
      CancellationToken cancellationToken = default
    ) => await instrumentByIdDataLoader.LoadAsync(orderDepth.InstrumentId, cancellationToken);
  }
}
