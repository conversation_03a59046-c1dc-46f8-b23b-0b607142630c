using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Types;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Timezone;

public class TimezoneType: ObjectType<TimezoneDto>
{
  public const string Name = "Timezone";
  protected override void Configure(IObjectTypeDescriptor<TimezoneDto> descriptor)
  {
    descriptor.Name(Name);

    descriptor.Field(f => f.Id);

    descriptor.TranslationField();
    descriptor.Field("ianaByCountry")
      .Description("Get IANA time zone name with ISO country name.")
      .Argument("countryName",
        arg => arg
          .Type<StringType>()
          .DefaultValue(null)
          .Description("ISO-3166 Country Name")
      )
      .Resolve(t =>
        WindowsTimezoneToIANA.Convert(
          t.Parent<TimezoneDto>().Name,
          t.ArgumentValue<string>("countryName"),
          null
        )
      );
  }
}
