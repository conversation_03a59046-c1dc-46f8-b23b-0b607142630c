
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

public class MarketConfiguration: IEntityTypeConfiguration<Market>
{
    public void Configure(EntityTypeBuilder<Market> builder)
    {

    }
}

public class MarketStatusConfiguration: IEntityTypeConfiguration<MarketStatus>
{
    public void Configure(EntityTypeBuilder<MarketStatus> builder)
    {
      builder
        .ToView("MarketStatus")
        .HasNoKey();
    }
}
