using Euroland.FlipIT.SData.API;
using Euroland.FlipIT.SData.API.Services.Interfaces;
using HotChocolate.Resolvers;

namespace Microsoft.Extensions.DependencyInjection;

public static class CurrencyRateTransformationExtensions
{
  public static (string? currencyPair, float factor, decimal? customRate) GetCustomRate(
    this IResolverContext resolverContext,
    string baseCurrency,
    string? quoteCurrency)
  {
    var strategy = resolverContext.RequestServices.GetService<ICurrencyRateTransformationStrategy>();

    if (string.IsNullOrEmpty(baseCurrency) || string.IsNullOrEmpty(quoteCurrency))
    {
      return (null, 1, null);
    }
    var baseQuoteCurrency = $"{baseCurrency}{quoteCurrency}".ToUpper();
    var factor = 1f;

    if (strategy != null)
    {
      var transform = strategy.GetTransformAsync(baseCurrency, quoteCurrency).ConfigureAwait(false).GetAwaiter().GetResult();
      if (transform != null)
      {
        switch (transform)
        {
          case CustomRateTransformation transformation:
            return (baseQuoteCurrency, factor, (decimal)transformation.CustomRate);
          case QuoteCurrencyTransformation quoted:
            baseQuoteCurrency = $"{baseCurrency}{quoted.TransformTo}".ToUpper();
            factor = quoted.Factor;
            break;
          case BaseCurrencyTransformation based:
            baseQuoteCurrency = $"{based.TransformTo}{quoteCurrency}".ToUpper();
            factor = based.Factor;
            break;
        }
      }
    }

    return (baseQuoteCurrency, factor, null);
  }
}
