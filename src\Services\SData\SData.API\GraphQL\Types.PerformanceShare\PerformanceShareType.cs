using HotChocolate.Types;
using Euroland.FlipIT.SData.API.Dto;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.PerformanceShare;

public class PerformanceShareType: ObjectType<PerformanceShareDto>
{
  protected override void Configure(IObjectTypeDescriptor<PerformanceShareDto> descriptor)
  {
    descriptor.Name("PerformanceShare");
    descriptor.Field(p => p.Period).Ignore();
    descriptor.Field(p => p.FromDate).Ignore();
    descriptor.Field(p => p.High).Name("highest");
    descriptor.Field(p => p.HighDate).Name("highestDate");
    descriptor.Field(p => p.Low).Name("lowest");
    descriptor.Field(p => p.LowDate).Name("lowestDate");
  }
}
