using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.Fundamental.API.Persistance.FinCalender;

[Table("Event")]
public class FinancialEvent
{
  [Key]
  public int Id { get; set; }
  public string CompanyCode { get; set; }
  public int EventTypeId { get; set; }

  [Column("DateType")]
  public byte DateTypeId { get; set; }

  public DateTime StartDate { get; set; }
  public DateTime? EndDate { get; set; }
  public bool IsAllDayEvent { get; set; }

  [Column("Status")]
  public byte? StatusId { get; set; }

  public bool IsDeleted { get; set; }

  [Column("OLD_ID")]
  public int? OldId { get; set; }

  public DateTime? LastUpdated { get; set; }
  public bool IsHighlighted { get; set; }
}
