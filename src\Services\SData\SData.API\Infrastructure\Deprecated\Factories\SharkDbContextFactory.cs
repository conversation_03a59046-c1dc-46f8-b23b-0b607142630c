﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace Euroland.FlipIT.SData.API.Infrastructure.Factories
{
  public class SharkDbContextFactory : ISharkDbContextFactory
    {
        private readonly IEnumerable<ISharkDbContext> _sharkContexts;
        public SharkDbContextFactory(IEnumerable<ISharkDbContext> sharkDbContexts)
        {
            _sharkContexts = sharkDbContexts;
        }
        public ISharkDbContext CreateDbContext(bool useRealtimeDb)
        {
            if(_sharkContexts == null || !_sharkContexts.Any()) {
                throw new InvalidOperationException($"Cannot resolve any instane of {nameof(ISharkDbContext)}. Check out the DI system to ensure there is at least one registration in the DI system.");
            }

            return !useRealtimeDb
                ? _sharkContexts.OfType<DefaultSharkDbContext>().Single()
                : _sharkContexts.OfType<RealtimeSharkDbContext>().Single();
        }
    }
}
