@use "../_rtl-mixins"as *;

.wise-widget {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 260px;
  height: 84px;
}

/* Widget icon */
.wise-widget-icon {
  position: static;
  width: 250px;
  height: 64px;
  background: #0066ff;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  overflow: hidden;

  // LTR styles
  border-radius: 32px 0 0 32px;
  box-shadow: -2px 2px 10px rgba(0, 0, 0, 0.2);

  // RTL styles
  @include rtl {
    border-radius: 0 32px 32px 0;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.2);
  }
}

.wise-widget-icon-container {
  display: flex;
  align-items: center;
  padding: 0 16px;
  min-width: 250px;
}

.wise-widget-icon-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.wise-widget-icon-item:hover {
  transform: scale(1.05);
}

.wise-widget-icon .divider {
  width: 1px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
}

.wise-widget-icon svg {
  width: 32px;
  height: 32px;
  color: white;
  fill: white;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  &#notification-toggle {
    width: 20px;
    height: 20px;
  }

  &#login-icon {
    height: 16px;
    width: 16px;
  }
}


.wise-widget-initials {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

.wise-widget-login {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 13px;
  font-weight: 500;
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.wise-widget-login:hover {
  transform: scale(1.05);
  background: rgba(255, 255, 255, 0.3);
}

.wise-gradient-button {
  @apply overflow-hidden;
  @apply before:absolute before:inset-0 before:bg-primary before:opacity-100 before:transition-opacity before:duration-300 before:z-[-1];
  @apply after:absolute after:inset-0 after:bg-[image:var(--primary-linear-gradient)] after:opacity-0 after:transition-opacity after:duration-300 after:hover:opacity-100 after:z-[-1];
}
