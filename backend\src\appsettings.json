{"ConnectionStrings": {"WiseSearchDbConnection": "Server=BUFFALO;Database=Wise;uid=uShark;pwd=**********;Trusted_Connection=false; Application Name=Euroland.FlipIT.WiseSearch; TrustServerCertificate=True;", "SharkDbConnection": "Server=BUFFALO;Database=shark;uid=uShark;pwd=**********;Trusted_Connection=false; Application Name=Euroland.FlipIT.WiseSearch; TrustServerCertificate=True;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"Using": ["Serilog.Sinks.Syslog"], "MinimumLevel": "Warning", "WriteTo": [{"Name": "UdpSyslog", "Args": {"host": "*************", "port": "514", "appName": "Euroland.FlipIT.WiseSearch", "restrictedToMinimumLevel": "Warning", "outputTemplate": "[{Timestamp:HH:mm:ss}] {Message:lj}{NewLine}{Exception}"}}], "Properties": {"Application": "Euroland.FlipIT.WiseSearch"}}, "AllowedHosts": "*", "AppSettings": {"AllowedOrigins": ["http://localhost"], "Azure": {"TranslatorApi": {"Endpoint": "https://api.cognitive.microsofttranslator.com/", "SubscriptionKey": ""}}, "HttpClientRequest": {"OpenAI": {"BaseUrl": "https://api.openai.com", "Endpoints": {"ChatCompletions": "https://api.openai.com/v1/chat/completions"}, "Model": "gpt-4o-mini-2024-07-18", "Temperature": 0, "OpenAIKey": "********************************************************************************************************************************************************************"}, "WiseApi": {"BaseUrl": "https://cloud-wise-api.euroland.com", "DefaultLanguageToTranslate": "English", "Endpoints": {"Query": "/api/v1/query"}, "Query": {"ResponseModel": "openai", "SemanticCount": 3}}, "Oidc": {"BaseUrl": "https://dev.vn.euroland.com", "Endpoints": {"TokenUrl": "/auth/realms/irservices/protocol/openid-connect/token"}, "ClientId": "aisearchfeapi-local", "ClientSecret": "cLJPNEiUth8ESVcIt2tGT5XkiuTuYVIL", "GrantType": "client_credentials"}}, "Authentication": {"JwtBearer": {"RequireHttpsMetadata": true, "Authority": "https://dev.vn.euroland.com/auth/realms/irservices", "ValidIssuer": "https://dev.vn.euroland.com/auth/realms/irservices", "ValidAudience": "account", "ClockSkewSeconds": 0}}, "ApiKeyAuthentication": [{"Name": "MobileClient", "ApiKey": "qvR+yXhR6iA8iyd5VRH1z4J8RIckf5O3", "Secret": "1U3y0i+Vq2A4CkExWz4ZgxKZnpblEszC6lB0T3vTjN1P6YBrEae9F+Kx2NpS/fTz", "AllowedTimeSkewSeconds": 300}]}}