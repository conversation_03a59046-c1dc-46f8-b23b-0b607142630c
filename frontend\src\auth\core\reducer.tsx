import { User } from "oidc-client-ts";
import { AuthState, AuthActionType } from "./interfaces.ts";

export const authActionType = {
  INITIALISED: 'INITIALISED',
  USER_LOADED: 'USER_LOADED',
  USER_UNLOADED: 'USER_UNLOADED',
  NAVIGATOR_INIT: 'NAVIGATOR_INIT',
  NAVIGATOR_CLOSE: 'NAVIGATOR_CLOSE',
  ERROR: 'ERROR'
};

/**
 * A collection of action creators for authentication-related actions.
 */
export const authAction = {
  /**
   * Creates an action to indicate that the user has been initialised.
   * @param user - The user manager instance.
   * @returns An action object with type `INITIALISED` and the user payload.
   */
  initialised: (user: User) => {
    return {
      type: authActionType.INITIALISED as AuthActionType,
      payload: { user }
    };
  },

  /**
   * Creates an action to indicate that the user has been loaded.
   * @param user - The user manager instance.
   * @returns An action object with type `USER_LOADED` and the user payload.
   */
  userLoaded: (user: User) => {
    return {
      type: authActionType.USER_LOADED as AuthActionType,
      payload: { user }
    };
  },

  /**
   * Creates an action to indicate that the user has been unloaded.
   * @returns An action object with type `USER_UNLOADED`.
   */
  userUnloaded: () => {
    return {
      type: authActionType.USER_UNLOADED as AuthActionType
    };
  },

  /**
   * Creates an action to initialise the navigator with a specific method.
   * @param method - The navigation method.
   * @returns An action object with type `NAVIGATOR_INIT` and the method payload.
   */
  navigatorInit: (method: string) => {
    return {
      type: authActionType.NAVIGATOR_INIT as AuthActionType,
      payload: { method }
    };
  },

  /**
   * Creates an action to close the navigator.
   * @returns An action object with type `NAVIGATOR_CLOSE`.
   */
  navigatorClose: () => {
    return {
      type: authActionType.NAVIGATOR_CLOSE as AuthActionType
    };
  },

  /**
   * Creates an action to handle an error.
   * @param error - The error object.
   * @returns An action object with type `ERROR` and the error payload.
   */
  error: (error: Error) => {
    return {
      type: authActionType.ERROR as AuthActionType,
      payload: { error }
    };
  }
};


/**
 * Reducer function for handling authentication-related state changes.
 *
 * @param state - The current authentication state.
 * @param param1 - An object containing the action type and payload.
 * @param param1.type - The type of action to be handled.
 * @param param1.payload - The payload containing data for the action.
 * @returns The updated authentication state.
 *
 * The reducer handles the following action types:
 * - `authActionType.INITIALISED`: Sets the user, loading state, authentication status, and clears any errors.
 * - `authActionType.USER_LOADED`: Sets the user, loading state, authentication status, and clears any errors.
 * - `authActionType.USER_UNLOADED`: Clears the user and sets the authentication status to false.
 * - `authActionType.NAVIGATOR_INIT`: Sets the loading state and active navigator method.
 * - `authActionType.NAVIGATOR_CLOSE`: Clears the loading state and active navigator.
 * - `authActionType.ERROR`: Sets the loading state to false and updates the error.
 * - `default`: Returns the current state with an error indicating an unknown action type.
 */
 
export const authReducer = (state: AuthState, { type, payload = null }: { type: AuthActionType; payload?: any | null; }): AuthState => {
  switch (type) {
    case authActionType.INITIALISED:
    case authActionType.USER_LOADED:
      return {
        ...state,
        user: payload.user,
        isLoading: false,
        isAuthenticated: payload.user ? !payload.user.expired : false,
        error: null
      };
    case authActionType.USER_UNLOADED:
      return {
        ...state,
        user: null,
        isAuthenticated: false
      };
    case authActionType.NAVIGATOR_INIT:
      return {
        ...state,
        isLoading: true,
        activeNavigator: payload.method
      };
    case authActionType.NAVIGATOR_CLOSE:
      // we intentionally don't handle cases where multiple concurrent navigators are open
      return {
        ...state,
        isLoading: false,
        activeNavigator: null
      };
    case authActionType.ERROR:
      return {
        ...state,
        isLoading: false,
        error: payload.error
      };
    default:
      return {
        ...state,
        isLoading: false,
        error: new Error(`unknown type ${type}`)
      };
  }
};
