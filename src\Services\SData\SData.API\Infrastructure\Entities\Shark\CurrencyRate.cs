using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
    [Table("CurrencyRate")]
    public class CurrencyRate
    {
        [Key]
        [Column("cId")]
        public int ID { get; set; }

        [Column(name: "cCurr", TypeName = "char(6)")]
        public string Currencies { get; set; }

        [Column(name: "cRate", TypeName = "numeric(21,7)")]
        public decimal? Rate { get; set; }

        [Column("cDate")]
        public DateTime? Date { get; set; }

        public CurrencyRateHistory Histories { get; set; }
    }
}
