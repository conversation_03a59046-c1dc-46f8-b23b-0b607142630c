using Euroland.FlipIT.Fundamental.API.Persistance.ReportHub;
using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.Fundamental.API.Persistance;

public class ReportHubDbContext : DbContext
{
  public ReportHubDbContext(DbContextOptions<ReportHubDbContext> options)
    : base(options)
  {
  }

  public DbSet<Company> Company { get; set; } = null!;
  public DbSet<Report> Report { get; set; } = null!;
  public DbSet<ReportType> ReportType { get; set; } = null!;
  public DbSet<ReportParentType> ReportParentType { get; set; } = null!; 
}
