﻿using Microsoft.EntityFrameworkCore;
using Euroland.FlipIT.WiseSearch.Infrastructure.Entities.WiseSearch;

namespace Euroland.FlipIT.WiseSearch.Infrastructure
{
    public class WiseSearchDbContext : DbContext
    {
        public WiseSearchDbContext(DbContextOptions<WiseSearchDbContext> options) : base(options)
        {

        }

        public virtual DbSet<SearchHistoryEntity> SearchHistories { get; set; }
        public virtual DbSet<SettingEntity> Settings { get; set; }
        public virtual DbSet<IngestedActivityEntity> IngestedActivities { get; set; }
    }
}
