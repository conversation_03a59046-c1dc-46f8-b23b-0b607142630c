# API Gateway

A single endpoint gateway for the underlying services which implements GraphQL solution.
This gateway itself is an GraphQL server implementing Federated Schema with Pull request.

All of underlying services (except SData) need to publish their schema with root type ignorance.

```CSharp
builder.Services.AddGraphQLServer()
  .AddQueryType<QueryType>(d => d.Name(OperationTypeNames.Query))
  .PublishSchemaDefinition(c =>
    c.SetName("fundamental")
      .IgnoreRootTypes()
      .AddTypeExtensionsFromFile("./Stitching.graphql")
  );
```

REMARKS: Alternatively, to have a distributed schema solution using Redis for Schema Federation.
