USE [shark]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		HuyDQ
-- Create date: July 22 2024
-- Description:	Get previous close of instrument by currency rate realtime
-- =============================================
CREATE FUNCTION [dbo].[fn_SGH3_GetPrevCloseOfInstrumentByCurrencyRate_RealTime]
(
	@InstrumentId int,
	@CurrencyBase	AS VARCHAR(3),
	@CurrencyQuote	AS VARCHAR(3)
)
RETURNS numeric(21,7)
AS
BEGIN
	DECLARE @hCloseDate date
	DECLARE @LasthClose money
	DECLARE @LasthCloseDate date
	DECLARE @MaxHistoryDate date
	DECLARE @Result numeric(21,7)

	SELECT
	@hCloseDate = Date
    FROM dbo.rt_InstrumentPrice
    WHERE [InstrumentId] = @InstrumentId

	SELECT @MaxHistoryDate = MAX([Date])
	FROM InstrumentHistory 
	WHERE InstrumentID = @InstrumentId

	IF (@MaxHistoryDate >= Cast(@hCloseDate AS Date)) 
	SET @hCloseDate = @MaxHistoryDate

	SELECT TOP 1 
    @LasthClose = [Close],
    @LasthCloseDate = [Date]
    FROM dbo.InstrumentHistory
    WHERE [InstrumentId] = @InstrumentId AND [DATE] < @hCloseDate
    ORDER BY [Date] DESC

	IF(LEN(@CurrencyQuote) <> 3
			OR	(@CurrencyBase = @CurrencyQuote)
			OR	(@CurrencyQuote = '')
			OR  (@CurrencyQuote IS NULL))
		SET @Result = @LasthClose
  ELSE
    SET @Result = @LasthClose * dbo.fn_SGH3_GetCurrencyRateByDate(@CurrencyBase, @CurrencyQuote, @LasthCloseDate)
	RETURN @Result
END
