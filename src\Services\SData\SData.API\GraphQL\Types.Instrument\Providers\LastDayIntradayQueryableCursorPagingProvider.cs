using System;
using System.Reflection;
using HotChocolate.Internal;
using HotChocolate.Types.Pagination;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;

/// <summary>
/// Provides cursor-based paging for intraday data, specifically for the last day.
/// Inherits from <see cref="QueryableCursorPagingProvider"/>.
/// </summary>
/// <remarks>
/// Query pagination with totalCount is heaviest operation. This customized provider is to
/// simplify count query without ordering clause to improve query performance.
/// </remarks>
internal class LastDayIntradayQueryableCursorPagingProvider : QueryableCursorPagingProvider
{
  private static readonly MethodInfo _createHandler =
        typeof(LastDayIntradayQueryableCursorPagingProvider).GetMethod(
            nameof(CreateHandlerInternal),
            BindingFlags.Static | BindingFlags.NonPublic)!;
  /// <summary>
  /// Creates a cursor paging handler for the given source and paging options.
  /// </summary>
  /// <param name="source">The source type for which the handler is created.</param>
  /// <param name="options">The paging options to be used.</param>
  /// <returns>A <see cref="CursorPagingHandler"/> instance.</returns>
  protected override CursorPagingHandler CreateHandler(IExtendedType source, PagingOptions options)
  {
    //return base.CreateHandler(source, options);
    if (source is null)
    {
      throw new ArgumentNullException(nameof(source));
    }

    return (CursorPagingHandler)_createHandler
            .MakeGenericMethod(source.ElementType?.Source ?? source.Source)
            .Invoke(null, new object[] { options })!;
  }

  private static LastDayIntradayQueryableCursorPagingHandler<TEntity> CreateHandlerInternal<TEntity>(
        PagingOptions options) =>
        new(options);
}
