using static Euroland.FlipIT.Fundamental.API.Dtos.FundamentalDto;

namespace Euroland.FlipIT.Fundamental.API.Dtos
{
  public class SegmentsDto
  {
    public string? cik { get; set; }
    public DateTime? fye { get; set; }
    public DateTime? fys { get; set; }
    public DateTime? date { get; set; }
    public string? link { get; set; }
    public string? year { get; set; }
    public string? period { get; set; }
    public string? symbol { get; set; }
    public string? currency { get; set; }
    public string? finalLink { get; set; }
    public DateTime? periodEnd { get; set; }
    public DateTime? fillingDate { get; set; }
    public DateTime? periodStart { get; set; }
    public DateTime? acceptedDate { get; set; }
    public string? calendarYear { get; set; }
    public string? cumulativeType { get; set; }
    public string? periodEndMonth { get; set; }
    public DateTime? updateTimestamp { get; set; }
    public string? reportedCurrency { get; set; }
    public List<SegmentDetail>? Segments { get; set; } 
  }

  public class SegmentDetail
  {
    public string? Name { get; set; }  // Represents the "geo" key in your JSON
    public List<Segment>? Detail { get; set; } // Represents the "detail" array in your JSON
  }

  public class Segment
  {
    public string? Name { get; set; }  // Represents the "TotalAssets" key in your JSON
    public List<SegmentValue>? Value { get; set; } // List of values for each key
  }

  public class SegmentValue
  {
    public string? Name { get; set; }  // Represents the "name" field in your JSON (e.g., "Other")
    public string? Value { get; set; } // Represents the "value" field in your JSON (e.g., "15000000000")
  }
}
