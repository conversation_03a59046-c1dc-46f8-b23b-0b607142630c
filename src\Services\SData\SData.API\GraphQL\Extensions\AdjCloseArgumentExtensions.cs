using HotChocolate.Resolvers;
using HotChocolate.Types;

namespace Microsoft.Extensions.DependencyInjection;

public static class AdjCloseArgumentExtensions
{
  public const string ADJ_CLOSE_FIELD = "adjClose";
  public static IObjectFieldDescriptor AddAdjCloseArgument(this IObjectFieldDescriptor descriptor)
  {
    return descriptor.Argument(ADJ_CLOSE_FIELD, desc =>
      desc
        .Type<BooleanType>()
        .DefaultValue(null)
        .Description("Adjust data for dividends, stock splits. Default `false`.")
    );
  }

  public static bool GetAdjClose(this IResolverContext resolverContext)
  {
    var hasAdjClose = resolverContext.ScopedContextData.ContainsKey(ADJ_CLOSE_FIELD);

    var result = hasAdjClose && ((resolverContext.ScopedContextData[ADJ_CLOSE_FIELD] as bool?) ?? false);

    return result;
  }
}
