using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Market;

/// <summary>
/// Extends 'market' field to type <see cref="FCEvent.FCEventType"/>
/// </summary>
public class EventTypeExtensions: ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(FCEvent.FCEventType.TypeName);

    descriptor.Field("market")
      .UseFirstOrDefault()
      .UseProjection()
      .ResolveWith<MarketResolvers>(
        resolvers => resolvers.GetMarketByEventType(default!, default!)
      );
  }
}
