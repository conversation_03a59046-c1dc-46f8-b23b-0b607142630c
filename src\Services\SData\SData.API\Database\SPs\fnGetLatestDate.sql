USE [shark]
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetStartingDate]    Script Date: 5/22/2024 10:27:30 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


-- =============================================
-- Author:		ThinhTD
-- Create date: 22/05/2024
-- Description:	select dbo.fnGetLatestDate(32864)
-- =============================================
CREATE FUNCTION [dbo].[fnGetLatestDate]
(
	@instrumentId int
)
RETURNS datetime
AS
BEGIN
  -- Declare the return variable here
	DECLARE @Result nvarchar(100)
  -- Add the T-SQL statements to compute the return value
	SET @Result =  (SELECT MAX([Date]) FROM InstrumentHistory WHERE InstrumentID = @instrumentId)
	-- Return the result of the function
	RETURN @Result
END


