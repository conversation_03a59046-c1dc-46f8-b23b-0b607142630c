!function(){"use strict";try{if("undefined"!=typeof document){var e=document.createElement("style");e.appendChild(document.createTextNode(".tab-bar{border-bottom:1px solid #e1e3e6;padding:0;overflow-x:auto;overflow-y:hidden}.tabs-container{display:flex;align-items:flex-end;gap:2px;min-width:min-content}.tab{display:flex;align-items:center;min-width:120px;max-width:240px;height:30px;background:#e9ecef;border:1px solid #d1d4dc;border-bottom:none;border-radius:4px 4px 0 0;cursor:pointer;transition:all .2s;position:relative;padding:0 8px;margin-bottom:1px}.tab:hover{background:#dee2e6}.tab.active{background:#fff;border-color:#e1e3e6;margin-bottom:0;z-index:1}.tab-content{display:flex;align-items:center;gap:4px;flex:1;min-width:0}.tab-name{font-size:14px;font-weight:500;color:#131722;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;flex:1}.tab-star{color:#ffc107;flex-shrink:0}.tab-count{font-size:12px;color:#6c757d;flex-shrink:0}.tab-actions{display:flex;align-items:center;gap:2px;opacity:0;transition:opacity .2s;margin-left:16px}.tab:hover .tab-actions,.tab.active .tab-actions{opacity:1}.tab-action-btn{display:flex;align-items:center;justify-content:center;border:none;border-radius:3px;background:transparent;color:#6c757d;cursor:pointer;transition:all .2s}button.tab-action-btn{width:20px;height:20px;padding:0}.tab-action-btn:hover{background:#0000001a;color:#131722}.close-tab-btn:hover{background:#f443361a;color:#f44336}.tab-action-btn:disabled{opacity:.3;cursor:not-allowed}.tab-action-btn:disabled:hover{background:transparent;color:#6c757d}.tab-edit-form{flex:1;padding:2px}.tab-edit-input{width:100%;padding:4px 6px;border:1px solid #2962ff;border-radius:3px;background:#fff;color:#131722;font-size:14px;font-weight:500}.tab-edit-input:focus{outline:none;border-color:#2962ff}.delete-confirm{display:flex;align-items:center;gap:2px}.confirm-delete-btn{display:flex;align-items:center;justify-content:center;border-radius:3px;border:1px solid #f44336;color:#f44336;cursor:pointer;transition:background-color .2s}button.confirm-delete-btn{width:20px;height:20px;padding:0}.cancel-delete-btn{display:flex;align-items:center;justify-content:center;border-radius:3px;border:1px solid #6c757d;color:#6c757d;cursor:pointer;transition:background-color .2s}button.cancel-delete-btn{width:20px;height:20px;padding:0}.add-tab-btn{display:flex;align-items:center;justify-content:center;background:#e9ecef;border:1px solid #d1d4dc;border-bottom:none;border-radius:4px 4px 0 0;cursor:pointer;transition:all .2s;color:#6c757d;margin-bottom:1px;flex-shrink:0}button.add-tab-btn{width:30px;height:30px;padding:0}.add-tab-btn:hover{background:#dee2e6;color:#131722}.add-tab-form{display:flex;align-items:center;gap:4px;min-width:200px;height:30px;background:#fff;border:1px solid #2962ff;border-bottom:none;border-radius:4px 4px 0 0;padding:0 8px;margin-bottom:1px;flex-shrink:0}.add-tab-input{flex:1;padding:4px 6px;border:none;background:transparent;color:#131722;font-size:14px;font-weight:500;min-width:100px}.add-tab-input:focus{outline:none}.add-tab-input::placeholder{color:#6c757d}.add-tab-actions{display:flex;align-items:center;gap:2px;flex-shrink:0}.confirm-add-btn{display:flex;align-items:center;justify-content:center;border:1px solid #00c853;border-radius:3px;color:#00c853;cursor:pointer;transition:background-color .2s;flex-shrink:0}button.confirm-add-btn{width:20px;height:20px;padding:0}.cancel-add-btn{display:flex;align-items:center;justify-content:center;border:1px solid #6c757d;border-radius:3px;color:#6c757d;cursor:pointer;transition:background-color .2s;flex-shrink:0}button.cancel-add-btn{width:20px;height:20px;padding:0}@media (max-width: 768px){.tab{min-width:120px;max-width:180px}}@media (max-width: 480px){.tab{min-width:120px;max-width:120px;padding:0 4px}.tab-name{font-size:12px}.tab-count{display:none}}.add-instrument-section{position:relative;width:100%;padding:8px 0;border-top:1px solid #e1e3e6}.add-section-title{margin:0 0 8px;font-size:16px;font-weight:600;color:#131722}.search-container{position:relative}.search-icon{position:absolute;left:10px;top:50%;transform:translateY(-50%);color:#6c757d}.search-input{width:100%;padding:10px 8px 10px 32px;border:1px solid #d1d4dc;border-radius:6px;background:#fff;color:#131722;font-size:14px}.search-input:focus{outline:none;border-color:#2962ff}.search-results{position:absolute;top:84px;left:0;right:0;border:1px solid #e1e3e6;border-radius:4%;background:#fff;margin-bottom:16px;z-index:999;box-shadow:0 2px 4px #0000001a}.search-results-empty{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:24px;color:var(--text-secondary);text-align:center;min-height:120px}.search-results-empty-icon{font-size:24px;margin-bottom:8px;color:#d1d4dc}.search-results-empty-text{font-size:14px;color:#6c757d}.search-results .rc-virtual-list,.search-results .rc-virtual-list-holder,.search-results .rc-virtual-list-holder-inner{border-radius:6px}.search-result-item{display:flex;align-items:center;justify-content:space-between;cursor:pointer;padding:12px 16px;gap:4px;border-bottom:1px solid #f1f3f4;transition:background-color .2s;height:60px;box-sizing:border-box}.search-result-item:hover{background:#f8f9fa}.search-result-item:last-child{border-bottom:none}.instrument-info{display:flex;align-items:center;justify-content:space-between;gap:16px;flex:1}.instrument-info .symbol{font-weight:600;color:#131722;min-width:60px}.symbol-info{display:flex;align-items:left;flex-direction:column}.instrument-info .name{font-size:12px;color:#6c757d;flex:1}.instrument-info .abbreviation{font-weight:600;color:#131722;min-width:80px}.instrument-info .change{font-weight:500;min-width:100px}.instrument-info .change.positive{color:#00c853}.instrument-info .change.negative{color:#f44336}.add-instrument-btn{display:flex;align-items:center;justify-content:center;color:#a4a6ac;border:none;border-radius:4px;padding-left:16px;cursor:pointer;transition:background-color .2s}button.add-instrument-btn{width:36px;height:36px;padding:0}button.add-instrument-btn:hover{background:#d8dbe2;color:#fff}button.add-instrument-btn:disabled{background:#d1d4dc;cursor:not-allowed}@media (max-width: 768px){.instrument-info{flex-direction:column;align-items:flex-start;gap:4px}.instrument-info .symbol,.instrument-info .price,.instrument-info .change{min-width:auto}}.instruments-section{flex:1;padding:10px 0 0;overflow-y:auto;max-height:calc(100vh - 430px);display:flex;flex-direction:column}.instruments-table-container{width:100%;overflow-y:auto;max-height:calc(100vh - 430px);border:1px solid #e1e3e6;border-radius:6px}.instruments-table{width:100%;border-collapse:collapse;background:#fff;table-layout:fixed}.instruments-table thead{position:sticky;top:0;z-index:1}.table-header{background:#f8f9fa;padding:8px 12px;font-weight:600;font-size:12px;text-transform:uppercase;letter-spacing:.5px;color:#6c757d;border-bottom:1px solid #e1e3e6;text-align:left}.table-row{display:table-row;position:relative;cursor:pointer;border-bottom:1px solid #f1f3f4;transition:background-color .2s}.table-row td{display:table-cell;padding:8px 12px;vertical-align:middle}.header-cell{display:flex;align-items:center}.symbol{flex-direction:column;align-items:flex-start}.symbol-text{font-weight:600}.market-text{font-size:11px;color:#6c757d}.positive{color:#28a745}.negative{color:#dc3545}.remove-btn{background:none;border:none;cursor:pointer;color:#dc3545;padding:0}.table-row:hover .remove-btn{visibility:visible}.table-row.selected{background:#e3f2fd}.table-row:last-child{border-bottom:none}.cell{display:flex;align-items:center;font-size:14px;font-weight:500}.cell.symbol{display:flex;flex-direction:column;align-items:flex-start;gap:2px}.symbol-text{font-weight:600;color:#131722}.market-text{font-size:11px;color:#6c757d;font-weight:400}.cell.name{color:#6c757d;font-weight:400}.cell.price{color:#131722;font-weight:600}.cell.change{font-weight:500}.cell.change-percent{text-align:end;font-weight:500}.cell.change.positive{color:#26a69a}.cell.change.negative{color:#f44336}.cell.change-percent.positive{color:#26a69a}.cell.change-percent.negative{color:#f44336}.cell.high,.cell.low,.cell.week-high{color:#131722;font-weight:600}.remove-btn{background:none;visibility:hidden;position:absolute;right:0;top:50%;transform:translateY(-50%);background:#fff;border:none;cursor:pointer;padding:6px;border-radius:4px;color:#6c757d;transition:all .2s}.remove-btn:hover{background:#f3938c;color:#f44336}.delete-confirm{display:flex;gap:4px}.confirm-delete-btn,.cancel-delete-btn{background:none;border:none;cursor:pointer;padding:6px;border-radius:4px;display:flex;align-items:center;justify-content:center;transition:all .2s}.confirm-delete-btn{color:#00c853}.confirm-delete-btn:hover{background:#00c8531a}.cancel-delete-btn{color:#6c757d}.cancel-delete-btn:hover{background:#6c757d1a}@keyframes shimmer{0%{background-position:-200px 0}to{background-position:calc(200px + 100%) 0}}@keyframes pulse{0%,to{opacity:1}50%{opacity:.5}}@keyframes fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.skeleton-row{border-bottom:1px solid #f1f3f4;animation:fadeIn .3s ease-out}.skeleton-row:hover{background:transparent}.skeleton-cell{padding:12px;vertical-align:middle}.skeleton-line{background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200px 100%;animation:shimmer 1.5s infinite;border-radius:4px;height:16px}.skeleton-symbol{display:flex;flex-direction:column;gap:6px}.skeleton-symbol-text{width:80px;height:18px}.skeleton-market-text{width:60px;height:12px}.skeleton-price{width:70px}.skeleton-change{width:50px}.skeleton-change-percent{width:60px;margin-left:auto}.loading-state,.empty-state,.error-state{display:flex;flex-direction:column;justify-content:center;align-items:center;padding:60px 20px;text-align:center;animation:fadeIn .5s ease-out}.loading-state,.empty-state{background:linear-gradient(135deg,#f8f9fa,#fff);border-radius:12px;margin:20px;border:1px solid #e9ecef}.error-state{background:linear-gradient(135deg,#fff5f5,#fff);border-radius:12px;margin:20px;border:1px solid #fed7d7}.loading-icon,.empty-icon,.error-icon{font-size:48px;margin-bottom:16px;animation:pulse 2s infinite}.loading-icon{animation:pulse 1.5s infinite}.empty-icon{opacity:.7}.error-icon{color:#f44336}.loading-title,.empty-title,.error-title{font-size:18px;font-weight:600;color:#2d3748;margin:0 0 8px;line-height:1.4}.loading-subtitle,.empty-subtitle,.error-subtitle{font-size:14px;color:#718096;margin:0;line-height:1.5;max-width:300px}.error-title{color:#e53e3e}.error-subtitle{color:#c53030}.empty-state p,.loading-state p,.error-state p{margin:4px 0;font-size:14px}.error-state{color:#f44336}@media (max-width: 768px){.loading-state,.empty-state,.error-state{padding:40px 16px;margin:16px}.loading-icon,.empty-icon,.error-icon{font-size:36px;margin-bottom:12px}.loading-title,.empty-title,.error-title{font-size:16px}.loading-subtitle,.empty-subtitle,.error-subtitle{font-size:13px}}@media (max-width: 480px){.skeleton-symbol-text{width:60px}.skeleton-market-text{width:45px}.skeleton-price{width:50px}.skeleton-change{width:40px}.skeleton-change-percent{width:45px}.loading-state,.empty-state,.error-state{padding:30px 12px;margin:12px}.loading-icon,.empty-icon,.error-icon{font-size:32px;margin-bottom:10px}.loading-title,.empty-title,.error-title{font-size:15px}.loading-subtitle,.empty-subtitle,.error-subtitle{font-size:12px}}")),document.head.appendChild(e)}}catch(t){console.error("vite-plugin-css-injected-by-js",t)}}();
var n$2, l$4, u$5, t$3, i$4, r$3, o$3, e$4, f$5, c$4, s$2, a$3, h$4, p$4 = {}, v$4 = [], y$5 = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i, w$4 = Array.isArray;
function d$6(n2, l2) {
  for (var u2 in l2) n2[u2] = l2[u2];
  return n2;
}
function g$4(n2) {
  n2 && n2.parentNode && n2.parentNode.removeChild(n2);
}
function _$3(l2, u2, t2) {
  var i2, r2, o2, e2 = {};
  for (o2 in u2) "key" == o2 ? i2 = u2[o2] : "ref" == o2 ? r2 = u2[o2] : e2[o2] = u2[o2];
  if (arguments.length > 2 && (e2.children = arguments.length > 3 ? n$2.call(arguments, 2) : t2), "function" == typeof l2 && null != l2.defaultProps) for (o2 in l2.defaultProps) void 0 === e2[o2] && (e2[o2] = l2.defaultProps[o2]);
  return m$2(l2, e2, i2, r2, null);
}
function m$2(n2, t2, i2, r2, o2) {
  var e2 = { type: n2, props: t2, key: i2, ref: r2, __k: null, __: null, __b: 0, __e: null, __c: null, constructor: void 0, __v: null == o2 ? ++u$5 : o2, __i: -1, __u: 0 };
  return null == o2 && null != l$4.vnode && l$4.vnode(e2), e2;
}
function b$2() {
  return { current: null };
}
function k$3(n2) {
  return n2.children;
}
function x$2(n2, l2) {
  this.props = n2, this.context = l2;
}
function S(n2, l2) {
  if (null == l2) return n2.__ ? S(n2.__, n2.__i + 1) : null;
  for (var u2; l2 < n2.__k.length; l2++) if (null != (u2 = n2.__k[l2]) && null != u2.__e) return u2.__e;
  return "function" == typeof n2.type ? S(n2) : null;
}
function C$3(n2) {
  var l2, u2;
  if (null != (n2 = n2.__) && null != n2.__c) {
    for (n2.__e = n2.__c.base = null, l2 = 0; l2 < n2.__k.length; l2++) if (null != (u2 = n2.__k[l2]) && null != u2.__e) {
      n2.__e = n2.__c.base = u2.__e;
      break;
    }
    return C$3(n2);
  }
}
function M$1(n2) {
  (!n2.__d && (n2.__d = true) && i$4.push(n2) && !$$2.__r++ || r$3 != l$4.debounceRendering) && ((r$3 = l$4.debounceRendering) || o$3)($$2);
}
function $$2() {
  for (var n2, u2, t2, r2, o2, f2, c2, s2 = 1; i$4.length; ) i$4.length > s2 && i$4.sort(e$4), n2 = i$4.shift(), s2 = i$4.length, n2.__d && (t2 = void 0, o2 = (r2 = (u2 = n2).__v).__e, f2 = [], c2 = [], u2.__P && ((t2 = d$6({}, r2)).__v = r2.__v + 1, l$4.vnode && l$4.vnode(t2), O$1(u2.__P, t2, r2, u2.__n, u2.__P.namespaceURI, 32 & r2.__u ? [o2] : null, f2, null == o2 ? S(r2) : o2, !!(32 & r2.__u), c2), t2.__v = r2.__v, t2.__.__k[t2.__i] = t2, z$3(f2, t2, c2), t2.__e != o2 && C$3(t2)));
  $$2.__r = 0;
}
function I$3(n2, l2, u2, t2, i2, r2, o2, e2, f2, c2, s2) {
  var a2, h2, y2, w2, d2, g2, _2 = t2 && t2.__k || v$4, m2 = l2.length;
  for (f2 = P$3(u2, l2, _2, f2, m2), a2 = 0; a2 < m2; a2++) null != (y2 = u2.__k[a2]) && (h2 = -1 == y2.__i ? p$4 : _2[y2.__i] || p$4, y2.__i = a2, g2 = O$1(n2, y2, h2, i2, r2, o2, e2, f2, c2, s2), w2 = y2.__e, y2.ref && h2.ref != y2.ref && (h2.ref && q$3(h2.ref, null, y2), s2.push(y2.ref, y2.__c || w2, y2)), null == d2 && null != w2 && (d2 = w2), 4 & y2.__u || h2.__k === y2.__k ? f2 = A$3(y2, f2, n2) : "function" == typeof y2.type && void 0 !== g2 ? f2 = g2 : w2 && (f2 = w2.nextSibling), y2.__u &= -7);
  return u2.__e = d2, f2;
}
function P$3(n2, l2, u2, t2, i2) {
  var r2, o2, e2, f2, c2, s2 = u2.length, a2 = s2, h2 = 0;
  for (n2.__k = new Array(i2), r2 = 0; r2 < i2; r2++) null != (o2 = l2[r2]) && "boolean" != typeof o2 && "function" != typeof o2 ? (f2 = r2 + h2, (o2 = n2.__k[r2] = "string" == typeof o2 || "number" == typeof o2 || "bigint" == typeof o2 || o2.constructor == String ? m$2(null, o2, null, null, null) : w$4(o2) ? m$2(k$3, { children: o2 }, null, null, null) : null == o2.constructor && o2.__b > 0 ? m$2(o2.type, o2.props, o2.key, o2.ref ? o2.ref : null, o2.__v) : o2).__ = n2, o2.__b = n2.__b + 1, e2 = null, -1 != (c2 = o2.__i = L$2(o2, u2, f2, a2)) && (a2--, (e2 = u2[c2]) && (e2.__u |= 2)), null == e2 || null == e2.__v ? (-1 == c2 && (i2 > s2 ? h2-- : i2 < s2 && h2++), "function" != typeof o2.type && (o2.__u |= 4)) : c2 != f2 && (c2 == f2 - 1 ? h2-- : c2 == f2 + 1 ? h2++ : (c2 > f2 ? h2-- : h2++, o2.__u |= 4))) : n2.__k[r2] = null;
  if (a2) for (r2 = 0; r2 < s2; r2++) null != (e2 = u2[r2]) && 0 == (2 & e2.__u) && (e2.__e == t2 && (t2 = S(e2)), B$2(e2, e2));
  return t2;
}
function A$3(n2, l2, u2) {
  var t2, i2;
  if ("function" == typeof n2.type) {
    for (t2 = n2.__k, i2 = 0; t2 && i2 < t2.length; i2++) t2[i2] && (t2[i2].__ = n2, l2 = A$3(t2[i2], l2, u2));
    return l2;
  }
  n2.__e != l2 && (l2 && n2.type && !u2.contains(l2) && (l2 = S(n2)), u2.insertBefore(n2.__e, l2 || null), l2 = n2.__e);
  do {
    l2 = l2 && l2.nextSibling;
  } while (null != l2 && 8 == l2.nodeType);
  return l2;
}
function H$1(n2, l2) {
  return l2 = l2 || [], null == n2 || "boolean" == typeof n2 || (w$4(n2) ? n2.some(function(n3) {
    H$1(n3, l2);
  }) : l2.push(n2)), l2;
}
function L$2(n2, l2, u2, t2) {
  var i2, r2, o2 = n2.key, e2 = n2.type, f2 = l2[u2];
  if (null === f2 && null == n2.key || f2 && o2 == f2.key && e2 == f2.type && 0 == (2 & f2.__u)) return u2;
  if (t2 > (null != f2 && 0 == (2 & f2.__u) ? 1 : 0)) for (i2 = u2 - 1, r2 = u2 + 1; i2 >= 0 || r2 < l2.length; ) {
    if (i2 >= 0) {
      if ((f2 = l2[i2]) && 0 == (2 & f2.__u) && o2 == f2.key && e2 == f2.type) return i2;
      i2--;
    }
    if (r2 < l2.length) {
      if ((f2 = l2[r2]) && 0 == (2 & f2.__u) && o2 == f2.key && e2 == f2.type) return r2;
      r2++;
    }
  }
  return -1;
}
function T$3(n2, l2, u2) {
  "-" == l2[0] ? n2.setProperty(l2, null == u2 ? "" : u2) : n2[l2] = null == u2 ? "" : "number" != typeof u2 || y$5.test(l2) ? u2 : u2 + "px";
}
function j$2(n2, l2, u2, t2, i2) {
  var r2, o2;
  n: if ("style" == l2) if ("string" == typeof u2) n2.style.cssText = u2;
  else {
    if ("string" == typeof t2 && (n2.style.cssText = t2 = ""), t2) for (l2 in t2) u2 && l2 in u2 || T$3(n2.style, l2, "");
    if (u2) for (l2 in u2) t2 && u2[l2] == t2[l2] || T$3(n2.style, l2, u2[l2]);
  }
  else if ("o" == l2[0] && "n" == l2[1]) r2 = l2 != (l2 = l2.replace(f$5, "$1")), o2 = l2.toLowerCase(), l2 = o2 in n2 || "onFocusOut" == l2 || "onFocusIn" == l2 ? o2.slice(2) : l2.slice(2), n2.l || (n2.l = {}), n2.l[l2 + r2] = u2, u2 ? t2 ? u2.u = t2.u : (u2.u = c$4, n2.addEventListener(l2, r2 ? a$3 : s$2, r2)) : n2.removeEventListener(l2, r2 ? a$3 : s$2, r2);
  else {
    if ("http://www.w3.org/2000/svg" == i2) l2 = l2.replace(/xlink(H|:h)/, "h").replace(/sName$/, "s");
    else if ("width" != l2 && "height" != l2 && "href" != l2 && "list" != l2 && "form" != l2 && "tabIndex" != l2 && "download" != l2 && "rowSpan" != l2 && "colSpan" != l2 && "role" != l2 && "popover" != l2 && l2 in n2) try {
      n2[l2] = null == u2 ? "" : u2;
      break n;
    } catch (n3) {
    }
    "function" == typeof u2 || (null == u2 || false === u2 && "-" != l2[4] ? n2.removeAttribute(l2) : n2.setAttribute(l2, "popover" == l2 && 1 == u2 ? "" : u2));
  }
}
function F$4(n2) {
  return function(u2) {
    if (this.l) {
      var t2 = this.l[u2.type + n2];
      if (null == u2.t) u2.t = c$4++;
      else if (u2.t < t2.u) return;
      return t2(l$4.event ? l$4.event(u2) : u2);
    }
  };
}
function O$1(n2, u2, t2, i2, r2, o2, e2, f2, c2, s2) {
  var a2, h2, p2, v2, y2, _2, m2, b2, S2, C2, M2, $2, P2, A2, H2, L2, T2, j2 = u2.type;
  if (null != u2.constructor) return null;
  128 & t2.__u && (c2 = !!(32 & t2.__u), o2 = [f2 = u2.__e = t2.__e]), (a2 = l$4.__b) && a2(u2);
  n: if ("function" == typeof j2) try {
    if (b2 = u2.props, S2 = "prototype" in j2 && j2.prototype.render, C2 = (a2 = j2.contextType) && i2[a2.__c], M2 = a2 ? C2 ? C2.props.value : a2.__ : i2, t2.__c ? m2 = (h2 = u2.__c = t2.__c).__ = h2.__E : (S2 ? u2.__c = h2 = new j2(b2, M2) : (u2.__c = h2 = new x$2(b2, M2), h2.constructor = j2, h2.render = D$2), C2 && C2.sub(h2), h2.props = b2, h2.state || (h2.state = {}), h2.context = M2, h2.__n = i2, p2 = h2.__d = true, h2.__h = [], h2._sb = []), S2 && null == h2.__s && (h2.__s = h2.state), S2 && null != j2.getDerivedStateFromProps && (h2.__s == h2.state && (h2.__s = d$6({}, h2.__s)), d$6(h2.__s, j2.getDerivedStateFromProps(b2, h2.__s))), v2 = h2.props, y2 = h2.state, h2.__v = u2, p2) S2 && null == j2.getDerivedStateFromProps && null != h2.componentWillMount && h2.componentWillMount(), S2 && null != h2.componentDidMount && h2.__h.push(h2.componentDidMount);
    else {
      if (S2 && null == j2.getDerivedStateFromProps && b2 !== v2 && null != h2.componentWillReceiveProps && h2.componentWillReceiveProps(b2, M2), !h2.__e && null != h2.shouldComponentUpdate && false === h2.shouldComponentUpdate(b2, h2.__s, M2) || u2.__v == t2.__v) {
        for (u2.__v != t2.__v && (h2.props = b2, h2.state = h2.__s, h2.__d = false), u2.__e = t2.__e, u2.__k = t2.__k, u2.__k.some(function(n3) {
          n3 && (n3.__ = u2);
        }), $2 = 0; $2 < h2._sb.length; $2++) h2.__h.push(h2._sb[$2]);
        h2._sb = [], h2.__h.length && e2.push(h2);
        break n;
      }
      null != h2.componentWillUpdate && h2.componentWillUpdate(b2, h2.__s, M2), S2 && null != h2.componentDidUpdate && h2.__h.push(function() {
        h2.componentDidUpdate(v2, y2, _2);
      });
    }
    if (h2.context = M2, h2.props = b2, h2.__P = n2, h2.__e = false, P2 = l$4.__r, A2 = 0, S2) {
      for (h2.state = h2.__s, h2.__d = false, P2 && P2(u2), a2 = h2.render(h2.props, h2.state, h2.context), H2 = 0; H2 < h2._sb.length; H2++) h2.__h.push(h2._sb[H2]);
      h2._sb = [];
    } else do {
      h2.__d = false, P2 && P2(u2), a2 = h2.render(h2.props, h2.state, h2.context), h2.state = h2.__s;
    } while (h2.__d && ++A2 < 25);
    h2.state = h2.__s, null != h2.getChildContext && (i2 = d$6(d$6({}, i2), h2.getChildContext())), S2 && !p2 && null != h2.getSnapshotBeforeUpdate && (_2 = h2.getSnapshotBeforeUpdate(v2, y2)), L2 = a2, null != a2 && a2.type === k$3 && null == a2.key && (L2 = N$2(a2.props.children)), f2 = I$3(n2, w$4(L2) ? L2 : [L2], u2, t2, i2, r2, o2, e2, f2, c2, s2), h2.base = u2.__e, u2.__u &= -161, h2.__h.length && e2.push(h2), m2 && (h2.__E = h2.__ = null);
  } catch (n3) {
    if (u2.__v = null, c2 || null != o2) if (n3.then) {
      for (u2.__u |= c2 ? 160 : 128; f2 && 8 == f2.nodeType && f2.nextSibling; ) f2 = f2.nextSibling;
      o2[o2.indexOf(f2)] = null, u2.__e = f2;
    } else for (T2 = o2.length; T2--; ) g$4(o2[T2]);
    else u2.__e = t2.__e, u2.__k = t2.__k;
    l$4.__e(n3, u2, t2);
  }
  else null == o2 && u2.__v == t2.__v ? (u2.__k = t2.__k, u2.__e = t2.__e) : f2 = u2.__e = V$2(t2.__e, u2, t2, i2, r2, o2, e2, c2, s2);
  return (a2 = l$4.diffed) && a2(u2), 128 & u2.__u ? void 0 : f2;
}
function z$3(n2, u2, t2) {
  for (var i2 = 0; i2 < t2.length; i2++) q$3(t2[i2], t2[++i2], t2[++i2]);
  l$4.__c && l$4.__c(u2, n2), n2.some(function(u3) {
    try {
      n2 = u3.__h, u3.__h = [], n2.some(function(n3) {
        n3.call(u3);
      });
    } catch (n3) {
      l$4.__e(n3, u3.__v);
    }
  });
}
function N$2(n2) {
  return "object" != typeof n2 || null == n2 || n2.__b && n2.__b > 0 ? n2 : w$4(n2) ? n2.map(N$2) : d$6({}, n2);
}
function V$2(u2, t2, i2, r2, o2, e2, f2, c2, s2) {
  var a2, h2, v2, y2, d2, _2, m2, b2 = i2.props, k2 = t2.props, x2 = t2.type;
  if ("svg" == x2 ? o2 = "http://www.w3.org/2000/svg" : "math" == x2 ? o2 = "http://www.w3.org/1998/Math/MathML" : o2 || (o2 = "http://www.w3.org/1999/xhtml"), null != e2) {
    for (a2 = 0; a2 < e2.length; a2++) if ((d2 = e2[a2]) && "setAttribute" in d2 == !!x2 && (x2 ? d2.localName == x2 : 3 == d2.nodeType)) {
      u2 = d2, e2[a2] = null;
      break;
    }
  }
  if (null == u2) {
    if (null == x2) return document.createTextNode(k2);
    u2 = document.createElementNS(o2, x2, k2.is && k2), c2 && (l$4.__m && l$4.__m(t2, e2), c2 = false), e2 = null;
  }
  if (null == x2) b2 === k2 || c2 && u2.data == k2 || (u2.data = k2);
  else {
    if (e2 = e2 && n$2.call(u2.childNodes), b2 = i2.props || p$4, !c2 && null != e2) for (b2 = {}, a2 = 0; a2 < u2.attributes.length; a2++) b2[(d2 = u2.attributes[a2]).name] = d2.value;
    for (a2 in b2) if (d2 = b2[a2], "children" == a2) ;
    else if ("dangerouslySetInnerHTML" == a2) v2 = d2;
    else if (!(a2 in k2)) {
      if ("value" == a2 && "defaultValue" in k2 || "checked" == a2 && "defaultChecked" in k2) continue;
      j$2(u2, a2, null, d2, o2);
    }
    for (a2 in k2) d2 = k2[a2], "children" == a2 ? y2 = d2 : "dangerouslySetInnerHTML" == a2 ? h2 = d2 : "value" == a2 ? _2 = d2 : "checked" == a2 ? m2 = d2 : c2 && "function" != typeof d2 || b2[a2] === d2 || j$2(u2, a2, d2, b2[a2], o2);
    if (h2) c2 || v2 && (h2.__html == v2.__html || h2.__html == u2.innerHTML) || (u2.innerHTML = h2.__html), t2.__k = [];
    else if (v2 && (u2.innerHTML = ""), I$3("template" == t2.type ? u2.content : u2, w$4(y2) ? y2 : [y2], t2, i2, r2, "foreignObject" == x2 ? "http://www.w3.org/1999/xhtml" : o2, e2, f2, e2 ? e2[0] : i2.__k && S(i2, 0), c2, s2), null != e2) for (a2 = e2.length; a2--; ) g$4(e2[a2]);
    c2 || (a2 = "value", "progress" == x2 && null == _2 ? u2.removeAttribute("value") : null != _2 && (_2 !== u2[a2] || "progress" == x2 && !_2 || "option" == x2 && _2 != b2[a2]) && j$2(u2, a2, _2, b2[a2], o2), a2 = "checked", null != m2 && m2 != u2[a2] && j$2(u2, a2, m2, b2[a2], o2));
  }
  return u2;
}
function q$3(n2, u2, t2) {
  try {
    if ("function" == typeof n2) {
      var i2 = "function" == typeof n2.__u;
      i2 && n2.__u(), i2 && null == u2 || (n2.__u = n2(u2));
    } else n2.current = u2;
  } catch (n3) {
    l$4.__e(n3, t2);
  }
}
function B$2(n2, u2, t2) {
  var i2, r2;
  if (l$4.unmount && l$4.unmount(n2), (i2 = n2.ref) && (i2.current && i2.current != n2.__e || q$3(i2, null, u2)), null != (i2 = n2.__c)) {
    if (i2.componentWillUnmount) try {
      i2.componentWillUnmount();
    } catch (n3) {
      l$4.__e(n3, u2);
    }
    i2.base = i2.__P = null;
  }
  if (i2 = n2.__k) for (r2 = 0; r2 < i2.length; r2++) i2[r2] && B$2(i2[r2], u2, t2 || "function" != typeof n2.type);
  t2 || g$4(n2.__e), n2.__c = n2.__ = n2.__e = void 0;
}
function D$2(n2, l2, u2) {
  return this.constructor(n2, u2);
}
function E$2(u2, t2, i2) {
  var r2, o2, e2, f2;
  t2 == document && (t2 = document.documentElement), l$4.__ && l$4.__(u2, t2), o2 = (r2 = "function" == typeof i2) ? null : i2 && i2.__k || t2.__k, e2 = [], f2 = [], O$1(t2, u2 = (!r2 && i2 || t2).__k = _$3(k$3, null, [u2]), o2 || p$4, p$4, t2.namespaceURI, !r2 && i2 ? [i2] : o2 ? null : t2.firstChild ? n$2.call(t2.childNodes) : null, e2, !r2 && i2 ? i2 : o2 ? o2.__e : t2.firstChild, r2, f2), z$3(e2, u2, f2);
}
function G$1(n2, l2) {
  E$2(n2, l2, G$1);
}
function J$1(l2, u2, t2) {
  var i2, r2, o2, e2, f2 = d$6({}, l2.props);
  for (o2 in l2.type && l2.type.defaultProps && (e2 = l2.type.defaultProps), u2) "key" == o2 ? i2 = u2[o2] : "ref" == o2 ? r2 = u2[o2] : f2[o2] = void 0 === u2[o2] && null != e2 ? e2[o2] : u2[o2];
  return arguments.length > 2 && (f2.children = arguments.length > 3 ? n$2.call(arguments, 2) : t2), m$2(l2.type, f2, i2 || l2.key, r2 || l2.ref, null);
}
function K$1(n2) {
  function l2(n3) {
    var u2, t2;
    return this.getChildContext || (u2 = /* @__PURE__ */ new Set(), (t2 = {})[l2.__c] = this, this.getChildContext = function() {
      return t2;
    }, this.componentWillUnmount = function() {
      u2 = null;
    }, this.shouldComponentUpdate = function(n4) {
      this.props.value != n4.value && u2.forEach(function(n5) {
        n5.__e = true, M$1(n5);
      });
    }, this.sub = function(n4) {
      u2.add(n4);
      var l3 = n4.componentWillUnmount;
      n4.componentWillUnmount = function() {
        u2 && u2.delete(n4), l3 && l3.call(n4);
      };
    }), n3.children;
  }
  return l2.__c = "__cC" + h$4++, l2.__ = n2, l2.Provider = l2.__l = (l2.Consumer = function(n3, l3) {
    return n3.children(l3);
  }).contextType = l2, l2;
}
n$2 = v$4.slice, l$4 = { __e: function(n2, l2, u2, t2) {
  for (var i2, r2, o2; l2 = l2.__; ) if ((i2 = l2.__c) && !i2.__) try {
    if ((r2 = i2.constructor) && null != r2.getDerivedStateFromError && (i2.setState(r2.getDerivedStateFromError(n2)), o2 = i2.__d), null != i2.componentDidCatch && (i2.componentDidCatch(n2, t2 || {}), o2 = i2.__d), o2) return i2.__E = i2;
  } catch (l3) {
    n2 = l3;
  }
  throw n2;
} }, u$5 = 0, t$3 = function(n2) {
  return null != n2 && null == n2.constructor;
}, x$2.prototype.setState = function(n2, l2) {
  var u2;
  u2 = null != this.__s && this.__s != this.state ? this.__s : this.__s = d$6({}, this.state), "function" == typeof n2 && (n2 = n2(d$6({}, u2), this.props)), n2 && d$6(u2, n2), null != n2 && this.__v && (l2 && this._sb.push(l2), M$1(this));
}, x$2.prototype.forceUpdate = function(n2) {
  this.__v && (this.__e = true, n2 && this.__h.push(n2), M$1(this));
}, x$2.prototype.render = k$3, i$4 = [], o$3 = "function" == typeof Promise ? Promise.prototype.then.bind(Promise.resolve()) : setTimeout, e$4 = function(n2, l2) {
  return n2.__v.__b - l2.__v.__b;
}, $$2.__r = 0, f$5 = /(PointerCapture)$|Capture$/i, c$4 = 0, s$2 = F$4(false), a$3 = F$4(true), h$4 = 0;
var f$4 = 0;
function u$4(e2, t2, n2, o2, i2, u2) {
  t2 || (t2 = {});
  var a2, c2, p2 = t2;
  if ("ref" in p2) for (c2 in p2 = {}, t2) "ref" == c2 ? a2 = t2[c2] : p2[c2] = t2[c2];
  var l2 = { type: e2, props: p2, key: n2, ref: a2, __k: null, __: null, __b: 0, __e: null, __c: null, constructor: void 0, __v: --f$4, __i: -1, __u: 0, __source: i2, __self: u2 };
  if ("function" == typeof e2 && (a2 = e2.defaultProps)) for (c2 in a2) void 0 === p2[c2] && (p2[c2] = a2[c2]);
  return l$4.vnode && l$4.vnode(l2), l2;
}
var e$3 = {
  NAME: "Name",
  DOCUMENT: "Document",
  OPERATION_DEFINITION: "OperationDefinition",
  FIELD: "Field",
  FRAGMENT_DEFINITION: "FragmentDefinition"
};
class GraphQLError extends Error {
  constructor(e2, r2, i2, n2, t2, a2, o2) {
    if (super(e2), this.name = "GraphQLError", this.message = e2, t2) {
      this.path = t2;
    }
    if (r2) {
      this.nodes = Array.isArray(r2) ? r2 : [r2];
    }
    if (i2) {
      this.source = i2;
    }
    if (n2) {
      this.positions = n2;
    }
    if (a2) {
      this.originalError = a2;
    }
    var l2 = o2;
    if (!l2 && a2) {
      var d2 = a2.extensions;
      if (d2 && "object" == typeof d2) {
        l2 = d2;
      }
    }
    this.extensions = l2 || {};
  }
  toJSON() {
    return {
      ...this,
      message: this.message
    };
  }
  toString() {
    return this.message;
  }
  get [Symbol.toStringTag]() {
    return "GraphQLError";
  }
}
var i$3;
var n$1;
function error(e2) {
  return new GraphQLError(`Syntax Error: Unexpected token at ${n$1} in ${e2}`);
}
function advance(e2) {
  if (e2.lastIndex = n$1, e2.test(i$3)) {
    return i$3.slice(n$1, n$1 = e2.lastIndex);
  }
}
var t$2 = / +(?=[^\s])/y;
function blockString(e2) {
  var r2 = e2.split("\n");
  var i2 = "";
  var n2 = 0;
  var a2 = 0;
  var o2 = r2.length - 1;
  for (var l2 = 0; l2 < r2.length; l2++) {
    if (t$2.lastIndex = 0, t$2.test(r2[l2])) {
      if (l2 && (!n2 || t$2.lastIndex < n2)) {
        n2 = t$2.lastIndex;
      }
      a2 = a2 || l2, o2 = l2;
    }
  }
  for (var d2 = a2; d2 <= o2; d2++) {
    if (d2 !== a2) {
      i2 += "\n";
    }
    i2 += r2[d2].slice(n2).replace(/\\"""/g, '"""');
  }
  return i2;
}
function ignored() {
  for (var e2 = 0 | i$3.charCodeAt(n$1++); 9 === e2 || 10 === e2 || 13 === e2 || 32 === e2 || 35 === e2 || 44 === e2 || 65279 === e2; e2 = 0 | i$3.charCodeAt(n$1++)) {
    if (35 === e2) {
      for (; 10 !== (e2 = i$3.charCodeAt(n$1++)) && 13 !== e2; ) {
      }
    }
  }
  n$1--;
}
function name() {
  var e2 = n$1;
  for (var r2 = 0 | i$3.charCodeAt(n$1++); r2 >= 48 && r2 <= 57 || r2 >= 65 && r2 <= 90 || 95 === r2 || r2 >= 97 && r2 <= 122; r2 = 0 | i$3.charCodeAt(n$1++)) {
  }
  if (e2 === n$1 - 1) {
    throw error("Name");
  }
  var t2 = i$3.slice(e2, --n$1);
  return ignored(), t2;
}
function nameNode() {
  return {
    kind: "Name",
    value: name()
  };
}
var a$2 = /(?:"""|(?:[\s\S]*?[^\\])""")/y;
var o$2 = /(?:(?:\.\d+)?[eE][+-]?\d+|\.\d+)/y;
function value(e2) {
  var r2;
  switch (i$3.charCodeAt(n$1)) {
    case 91:
      n$1++, ignored();
      var t2 = [];
      for (; 93 !== i$3.charCodeAt(n$1); ) {
        t2.push(value(e2));
      }
      return n$1++, ignored(), {
        kind: "ListValue",
        values: t2
      };
    case 123:
      n$1++, ignored();
      var l2 = [];
      for (; 125 !== i$3.charCodeAt(n$1); ) {
        var d2 = nameNode();
        if (58 !== i$3.charCodeAt(n$1++)) {
          throw error("ObjectField");
        }
        ignored(), l2.push({
          kind: "ObjectField",
          name: d2,
          value: value(e2)
        });
      }
      return n$1++, ignored(), {
        kind: "ObjectValue",
        fields: l2
      };
    case 36:
      if (e2) {
        throw error("Variable");
      }
      return n$1++, {
        kind: "Variable",
        name: nameNode()
      };
    case 34:
      if (34 === i$3.charCodeAt(n$1 + 1) && 34 === i$3.charCodeAt(n$1 + 2)) {
        if (n$1 += 3, null == (r2 = advance(a$2))) {
          throw error("StringValue");
        }
        return ignored(), {
          kind: "StringValue",
          value: blockString(r2.slice(0, -3)),
          block: true
        };
      } else {
        var u2 = n$1;
        var s2;
        n$1++;
        var c2 = false;
        for (s2 = 0 | i$3.charCodeAt(n$1++); 92 === s2 && (n$1++, c2 = true) || 10 !== s2 && 13 !== s2 && 34 !== s2 && s2; s2 = 0 | i$3.charCodeAt(n$1++)) {
        }
        if (34 !== s2) {
          throw error("StringValue");
        }
        return r2 = i$3.slice(u2, n$1), ignored(), {
          kind: "StringValue",
          value: c2 ? JSON.parse(r2) : r2.slice(1, -1),
          block: false
        };
      }
    case 45:
    case 48:
    case 49:
    case 50:
    case 51:
    case 52:
    case 53:
    case 54:
    case 55:
    case 56:
    case 57:
      var v2 = n$1++;
      var f2;
      for (; (f2 = 0 | i$3.charCodeAt(n$1++)) >= 48 && f2 <= 57; ) {
      }
      var m2 = i$3.slice(v2, --n$1);
      if (46 === (f2 = i$3.charCodeAt(n$1)) || 69 === f2 || 101 === f2) {
        if (null == (r2 = advance(o$2))) {
          throw error("FloatValue");
        }
        return ignored(), {
          kind: "FloatValue",
          value: m2 + r2
        };
      } else {
        return ignored(), {
          kind: "IntValue",
          value: m2
        };
      }
    case 110:
      if (117 === i$3.charCodeAt(n$1 + 1) && 108 === i$3.charCodeAt(n$1 + 2) && 108 === i$3.charCodeAt(n$1 + 3)) {
        return n$1 += 4, ignored(), {
          kind: "NullValue"
        };
      } else {
        break;
      }
    case 116:
      if (114 === i$3.charCodeAt(n$1 + 1) && 117 === i$3.charCodeAt(n$1 + 2) && 101 === i$3.charCodeAt(n$1 + 3)) {
        return n$1 += 4, ignored(), {
          kind: "BooleanValue",
          value: true
        };
      } else {
        break;
      }
    case 102:
      if (97 === i$3.charCodeAt(n$1 + 1) && 108 === i$3.charCodeAt(n$1 + 2) && 115 === i$3.charCodeAt(n$1 + 3) && 101 === i$3.charCodeAt(n$1 + 4)) {
        return n$1 += 5, ignored(), {
          kind: "BooleanValue",
          value: false
        };
      } else {
        break;
      }
  }
  return {
    kind: "EnumValue",
    value: name()
  };
}
function arguments_(e2) {
  if (40 === i$3.charCodeAt(n$1)) {
    var r2 = [];
    n$1++, ignored();
    do {
      var t2 = nameNode();
      if (58 !== i$3.charCodeAt(n$1++)) {
        throw error("Argument");
      }
      ignored(), r2.push({
        kind: "Argument",
        name: t2,
        value: value(e2)
      });
    } while (41 !== i$3.charCodeAt(n$1));
    return n$1++, ignored(), r2;
  }
}
function directives(e2) {
  if (64 === i$3.charCodeAt(n$1)) {
    var r2 = [];
    do {
      n$1++, r2.push({
        kind: "Directive",
        name: nameNode(),
        arguments: arguments_(e2)
      });
    } while (64 === i$3.charCodeAt(n$1));
    return r2;
  }
}
function type() {
  var e2 = 0;
  for (; 91 === i$3.charCodeAt(n$1); ) {
    e2++, n$1++, ignored();
  }
  var r2 = {
    kind: "NamedType",
    name: nameNode()
  };
  do {
    if (33 === i$3.charCodeAt(n$1)) {
      n$1++, ignored(), r2 = {
        kind: "NonNullType",
        type: r2
      };
    }
    if (e2) {
      if (93 !== i$3.charCodeAt(n$1++)) {
        throw error("NamedType");
      }
      ignored(), r2 = {
        kind: "ListType",
        type: r2
      };
    }
  } while (e2--);
  return r2;
}
function selectionSetStart() {
  if (123 !== i$3.charCodeAt(n$1++)) {
    throw error("SelectionSet");
  }
  return ignored(), selectionSet();
}
function selectionSet() {
  var e2 = [];
  do {
    if (46 === i$3.charCodeAt(n$1)) {
      if (46 !== i$3.charCodeAt(++n$1) || 46 !== i$3.charCodeAt(++n$1)) {
        throw error("SelectionSet");
      }
      switch (n$1++, ignored(), i$3.charCodeAt(n$1)) {
        case 64:
          e2.push({
            kind: "InlineFragment",
            typeCondition: void 0,
            directives: directives(false),
            selectionSet: selectionSetStart()
          });
          break;
        case 111:
          if (110 === i$3.charCodeAt(n$1 + 1)) {
            n$1 += 2, ignored(), e2.push({
              kind: "InlineFragment",
              typeCondition: {
                kind: "NamedType",
                name: nameNode()
              },
              directives: directives(false),
              selectionSet: selectionSetStart()
            });
          } else {
            e2.push({
              kind: "FragmentSpread",
              name: nameNode(),
              directives: directives(false)
            });
          }
          break;
        case 123:
          n$1++, ignored(), e2.push({
            kind: "InlineFragment",
            typeCondition: void 0,
            directives: void 0,
            selectionSet: selectionSet()
          });
          break;
        default:
          e2.push({
            kind: "FragmentSpread",
            name: nameNode(),
            directives: directives(false)
          });
      }
    } else {
      var r2 = nameNode();
      var t2 = void 0;
      if (58 === i$3.charCodeAt(n$1)) {
        n$1++, ignored(), t2 = r2, r2 = nameNode();
      }
      var a2 = arguments_(false);
      var o2 = directives(false);
      var l2 = void 0;
      if (123 === i$3.charCodeAt(n$1)) {
        n$1++, ignored(), l2 = selectionSet();
      }
      e2.push({
        kind: "Field",
        alias: t2,
        name: r2,
        arguments: a2,
        directives: o2,
        selectionSet: l2
      });
    }
  } while (125 !== i$3.charCodeAt(n$1));
  return n$1++, ignored(), {
    kind: "SelectionSet",
    selections: e2
  };
}
function variableDefinitions() {
  if (ignored(), 40 === i$3.charCodeAt(n$1)) {
    var e2 = [];
    n$1++, ignored();
    do {
      if (36 !== i$3.charCodeAt(n$1++)) {
        throw error("Variable");
      }
      var r2 = nameNode();
      if (58 !== i$3.charCodeAt(n$1++)) {
        throw error("VariableDefinition");
      }
      ignored();
      var t2 = type();
      var a2 = void 0;
      if (61 === i$3.charCodeAt(n$1)) {
        n$1++, ignored(), a2 = value(true);
      }
      ignored(), e2.push({
        kind: "VariableDefinition",
        variable: {
          kind: "Variable",
          name: r2
        },
        type: t2,
        defaultValue: a2,
        directives: directives(true)
      });
    } while (41 !== i$3.charCodeAt(n$1));
    return n$1++, ignored(), e2;
  }
}
function fragmentDefinition() {
  var e2 = nameNode();
  if (111 !== i$3.charCodeAt(n$1++) || 110 !== i$3.charCodeAt(n$1++)) {
    throw error("FragmentDefinition");
  }
  return ignored(), {
    kind: "FragmentDefinition",
    name: e2,
    typeCondition: {
      kind: "NamedType",
      name: nameNode()
    },
    directives: directives(false),
    selectionSet: selectionSetStart()
  };
}
function definitions() {
  var e2 = [];
  do {
    if (123 === i$3.charCodeAt(n$1)) {
      n$1++, ignored(), e2.push({
        kind: "OperationDefinition",
        operation: "query",
        name: void 0,
        variableDefinitions: void 0,
        directives: void 0,
        selectionSet: selectionSet()
      });
    } else {
      var r2 = name();
      switch (r2) {
        case "fragment":
          e2.push(fragmentDefinition());
          break;
        case "query":
        case "mutation":
        case "subscription":
          var t2;
          var a2 = void 0;
          if (40 !== (t2 = i$3.charCodeAt(n$1)) && 64 !== t2 && 123 !== t2) {
            a2 = nameNode();
          }
          e2.push({
            kind: "OperationDefinition",
            operation: r2,
            name: a2,
            variableDefinitions: variableDefinitions(),
            directives: directives(false),
            selectionSet: selectionSetStart()
          });
          break;
        default:
          throw error("Document");
      }
    }
  } while (n$1 < i$3.length);
  return e2;
}
function parse(e2, r2) {
  if (i$3 = e2.body ? e2.body : e2, n$1 = 0, ignored(), r2 && r2.noLocation) {
    return {
      kind: "Document",
      definitions: definitions()
    };
  } else {
    return {
      kind: "Document",
      definitions: definitions(),
      loc: {
        start: 0,
        end: i$3.length,
        startToken: void 0,
        endToken: void 0,
        source: {
          body: i$3,
          name: "graphql.web",
          locationOffset: {
            line: 1,
            column: 1
          }
        }
      }
    };
  }
}
function mapJoin(e2, r2, i2) {
  var n2 = "";
  for (var t2 = 0; t2 < e2.length; t2++) {
    if (t2) {
      n2 += r2;
    }
    n2 += i2(e2[t2]);
  }
  return n2;
}
function printString(e2) {
  return JSON.stringify(e2);
}
function printBlockString(e2) {
  return '"""\n' + e2.replace(/"""/g, '\\"""') + '\n"""';
}
var d$5 = "\n";
var u$3 = {
  OperationDefinition(e2) {
    var r2 = e2.operation;
    if (e2.name) {
      r2 += " " + e2.name.value;
    }
    if (e2.variableDefinitions && e2.variableDefinitions.length) {
      if (!e2.name) {
        r2 += " ";
      }
      r2 += "(" + mapJoin(e2.variableDefinitions, ", ", u$3.VariableDefinition) + ")";
    }
    if (e2.directives && e2.directives.length) {
      r2 += " " + mapJoin(e2.directives, " ", u$3.Directive);
    }
    return "query" !== r2 ? r2 + " " + u$3.SelectionSet(e2.selectionSet) : u$3.SelectionSet(e2.selectionSet);
  },
  VariableDefinition(e2) {
    var r2 = u$3.Variable(e2.variable) + ": " + _print(e2.type);
    if (e2.defaultValue) {
      r2 += " = " + _print(e2.defaultValue);
    }
    if (e2.directives && e2.directives.length) {
      r2 += " " + mapJoin(e2.directives, " ", u$3.Directive);
    }
    return r2;
  },
  Field(e2) {
    var r2 = e2.alias ? e2.alias.value + ": " + e2.name.value : e2.name.value;
    if (e2.arguments && e2.arguments.length) {
      var i2 = mapJoin(e2.arguments, ", ", u$3.Argument);
      if (r2.length + i2.length + 2 > 80) {
        r2 += "(" + (d$5 += "  ") + mapJoin(e2.arguments, d$5, u$3.Argument) + (d$5 = d$5.slice(0, -2)) + ")";
      } else {
        r2 += "(" + i2 + ")";
      }
    }
    if (e2.directives && e2.directives.length) {
      r2 += " " + mapJoin(e2.directives, " ", u$3.Directive);
    }
    if (e2.selectionSet && e2.selectionSet.selections.length) {
      r2 += " " + u$3.SelectionSet(e2.selectionSet);
    }
    return r2;
  },
  StringValue(e2) {
    if (e2.block) {
      return printBlockString(e2.value).replace(/\n/g, d$5);
    } else {
      return printString(e2.value);
    }
  },
  BooleanValue: (e2) => "" + e2.value,
  NullValue: (e2) => "null",
  IntValue: (e2) => e2.value,
  FloatValue: (e2) => e2.value,
  EnumValue: (e2) => e2.value,
  Name: (e2) => e2.value,
  Variable: (e2) => "$" + e2.name.value,
  ListValue: (e2) => "[" + mapJoin(e2.values, ", ", _print) + "]",
  ObjectValue: (e2) => "{" + mapJoin(e2.fields, ", ", u$3.ObjectField) + "}",
  ObjectField: (e2) => e2.name.value + ": " + _print(e2.value),
  Document(e2) {
    if (!e2.definitions || !e2.definitions.length) {
      return "";
    } else {
      return mapJoin(e2.definitions, "\n\n", _print);
    }
  },
  SelectionSet: (e2) => "{" + (d$5 += "  ") + mapJoin(e2.selections, d$5, _print) + (d$5 = d$5.slice(0, -2)) + "}",
  Argument: (e2) => e2.name.value + ": " + _print(e2.value),
  FragmentSpread(e2) {
    var r2 = "..." + e2.name.value;
    if (e2.directives && e2.directives.length) {
      r2 += " " + mapJoin(e2.directives, " ", u$3.Directive);
    }
    return r2;
  },
  InlineFragment(e2) {
    var r2 = "...";
    if (e2.typeCondition) {
      r2 += " on " + e2.typeCondition.name.value;
    }
    if (e2.directives && e2.directives.length) {
      r2 += " " + mapJoin(e2.directives, " ", u$3.Directive);
    }
    return r2 += " " + u$3.SelectionSet(e2.selectionSet);
  },
  FragmentDefinition(e2) {
    var r2 = "fragment " + e2.name.value;
    if (r2 += " on " + e2.typeCondition.name.value, e2.directives && e2.directives.length) {
      r2 += " " + mapJoin(e2.directives, " ", u$3.Directive);
    }
    return r2 + " " + u$3.SelectionSet(e2.selectionSet);
  },
  Directive(e2) {
    var r2 = "@" + e2.name.value;
    if (e2.arguments && e2.arguments.length) {
      r2 += "(" + mapJoin(e2.arguments, ", ", u$3.Argument) + ")";
    }
    return r2;
  },
  NamedType: (e2) => e2.name.value,
  ListType: (e2) => "[" + _print(e2.type) + "]",
  NonNullType: (e2) => _print(e2.type) + "!"
};
var _print = (e2) => u$3[e2.kind](e2);
function print(e2) {
  return d$5 = "\n", u$3[e2.kind] ? u$3[e2.kind](e2) : "";
}
var teardownPlaceholder = () => {
};
var e$2 = teardownPlaceholder;
function start(e2) {
  return {
    tag: 0,
    0: e2
  };
}
function push(e2) {
  return {
    tag: 1,
    0: e2
  };
}
var asyncIteratorSymbol = () => "function" == typeof Symbol && Symbol.asyncIterator || "@@asyncIterator";
var identity = (e2) => e2;
function filter(r2) {
  return (t2) => (i2) => {
    var a2 = e$2;
    t2((e2) => {
      if (0 === e2) {
        i2(0);
      } else if (0 === e2.tag) {
        a2 = e2[0];
        i2(e2);
      } else if (!r2(e2[0])) {
        a2(0);
      } else {
        i2(e2);
      }
    });
  };
}
function map(e2) {
  return (r2) => (t2) => r2((r3) => {
    if (0 === r3 || 0 === r3.tag) {
      t2(r3);
    } else {
      t2(push(e2(r3[0])));
    }
  });
}
function mergeMap(r2) {
  return (t2) => (i2) => {
    var a2 = [];
    var f2 = e$2;
    var n2 = false;
    var s2 = false;
    t2((t3) => {
      if (s2) ;
      else if (0 === t3) {
        s2 = true;
        if (!a2.length) {
          i2(0);
        }
      } else if (0 === t3.tag) {
        f2 = t3[0];
      } else {
        n2 = false;
        !function applyInnerSource(r3) {
          var t4 = e$2;
          r3((e2) => {
            if (0 === e2) {
              if (a2.length) {
                var r4 = a2.indexOf(t4);
                if (r4 > -1) {
                  (a2 = a2.slice()).splice(r4, 1);
                }
                if (!a2.length) {
                  if (s2) {
                    i2(0);
                  } else if (!n2) {
                    n2 = true;
                    f2(0);
                  }
                }
              }
            } else if (0 === e2.tag) {
              a2.push(t4 = e2[0]);
              t4(0);
            } else if (a2.length) {
              i2(e2);
              t4(0);
            }
          });
        }(r2(t3[0]));
        if (!n2) {
          n2 = true;
          f2(0);
        }
      }
    });
    i2(start((e2) => {
      if (1 === e2) {
        if (!s2) {
          s2 = true;
          f2(1);
        }
        for (var r3 = 0, t3 = a2, i3 = a2.length; r3 < i3; r3++) {
          t3[r3](1);
        }
        a2.length = 0;
      } else {
        if (!s2 && !n2) {
          n2 = true;
          f2(0);
        } else {
          n2 = false;
        }
        for (var l2 = 0, u2 = a2, o2 = a2.length; l2 < o2; l2++) {
          u2[l2](0);
        }
      }
    }));
  };
}
function mergeAll(e2) {
  return mergeMap(identity)(e2);
}
function merge$1(e2) {
  return mergeAll(r$2(e2));
}
function onEnd(e2) {
  return (r2) => (t2) => {
    var i2 = false;
    r2((r3) => {
      if (i2) ;
      else if (0 === r3) {
        i2 = true;
        t2(0);
        e2();
      } else if (0 === r3.tag) {
        var a2 = r3[0];
        t2(start((r4) => {
          if (1 === r4) {
            i2 = true;
            a2(1);
            e2();
          } else {
            a2(r4);
          }
        }));
      } else {
        t2(r3);
      }
    });
  };
}
function onPush(e2) {
  return (r2) => (t2) => {
    var i2 = false;
    r2((r3) => {
      if (i2) ;
      else if (0 === r3) {
        i2 = true;
        t2(0);
      } else if (0 === r3.tag) {
        var a2 = r3[0];
        t2(start((e3) => {
          if (1 === e3) {
            i2 = true;
          }
          a2(e3);
        }));
      } else {
        e2(r3[0]);
        t2(r3);
      }
    });
  };
}
function onStart(e2) {
  return (r2) => (t2) => r2((r3) => {
    if (0 === r3) {
      t2(0);
    } else if (0 === r3.tag) {
      t2(r3);
      e2();
    } else {
      t2(r3);
    }
  });
}
function share(r2) {
  var t2 = [];
  var i2 = e$2;
  var a2 = false;
  return (e2) => {
    t2.push(e2);
    if (1 === t2.length) {
      r2((e3) => {
        if (0 === e3) {
          for (var r3 = 0, f2 = t2, n2 = t2.length; r3 < n2; r3++) {
            f2[r3](0);
          }
          t2.length = 0;
        } else if (0 === e3.tag) {
          i2 = e3[0];
        } else {
          a2 = false;
          for (var s2 = 0, l2 = t2, u2 = t2.length; s2 < u2; s2++) {
            l2[s2](e3);
          }
        }
      });
    }
    e2(start((r3) => {
      if (1 === r3) {
        var f2 = t2.indexOf(e2);
        if (f2 > -1) {
          (t2 = t2.slice()).splice(f2, 1);
        }
        if (!t2.length) {
          i2(1);
        }
      } else if (!a2) {
        a2 = true;
        i2(0);
      }
    }));
  };
}
function switchMap(r2) {
  return (t2) => (i2) => {
    var a2 = e$2;
    var f2 = e$2;
    var n2 = false;
    var s2 = false;
    var l2 = false;
    var u2 = false;
    t2((t3) => {
      if (u2) ;
      else if (0 === t3) {
        u2 = true;
        if (!l2) {
          i2(0);
        }
      } else if (0 === t3.tag) {
        a2 = t3[0];
      } else {
        if (l2) {
          f2(1);
          f2 = e$2;
        }
        if (!n2) {
          n2 = true;
          a2(0);
        } else {
          n2 = false;
        }
        !function applyInnerSource(e2) {
          l2 = true;
          e2((e3) => {
            if (!l2) ;
            else if (0 === e3) {
              l2 = false;
              if (u2) {
                i2(0);
              } else if (!n2) {
                n2 = true;
                a2(0);
              }
            } else if (0 === e3.tag) {
              s2 = false;
              (f2 = e3[0])(0);
            } else {
              i2(e3);
              if (!s2) {
                f2(0);
              } else {
                s2 = false;
              }
            }
          });
        }(r2(t3[0]));
      }
    });
    i2(start((e2) => {
      if (1 === e2) {
        if (!u2) {
          u2 = true;
          a2(1);
        }
        if (l2) {
          l2 = false;
          f2(1);
        }
      } else {
        if (!u2 && !n2) {
          n2 = true;
          a2(0);
        }
        if (l2 && !s2) {
          s2 = true;
          f2(0);
        }
      }
    }));
  };
}
function take(r2) {
  return (t2) => (i2) => {
    var a2 = e$2;
    var f2 = false;
    var n2 = 0;
    t2((e2) => {
      if (f2) ;
      else if (0 === e2) {
        f2 = true;
        i2(0);
      } else if (0 === e2.tag) {
        {
          a2 = e2[0];
        }
      } else if (n2++ < r2) {
        i2(e2);
        if (!f2 && n2 >= r2) {
          f2 = true;
          i2(0);
          a2(1);
        }
      } else {
        i2(e2);
      }
    });
    i2(start((e2) => {
      if (1 === e2 && !f2) {
        f2 = true;
        a2(1);
      } else if (0 === e2 && !f2 && n2 < r2) {
        a2(0);
      }
    }));
  };
}
function takeUntil(r2) {
  return (t2) => (i2) => {
    var a2 = e$2;
    var f2 = e$2;
    var n2 = false;
    t2((e2) => {
      if (n2) ;
      else if (0 === e2) {
        n2 = true;
        f2(1);
        i2(0);
      } else if (0 === e2.tag) {
        a2 = e2[0];
        r2((e3) => {
          if (0 === e3) ;
          else if (0 === e3.tag) {
            (f2 = e3[0])(0);
          } else {
            n2 = true;
            f2(1);
            a2(1);
            i2(0);
          }
        });
      } else {
        i2(e2);
      }
    });
    i2(start((e2) => {
      if (1 === e2 && !n2) {
        n2 = true;
        a2(1);
        f2(1);
      } else if (!n2) {
        a2(0);
      }
    }));
  };
}
function takeWhile(r2, t2) {
  return (i2) => (a2) => {
    var f2 = e$2;
    var n2 = false;
    i2((e2) => {
      if (n2) ;
      else if (0 === e2) {
        n2 = true;
        a2(0);
      } else if (0 === e2.tag) {
        f2 = e2[0];
        a2(e2);
      } else if (!r2(e2[0])) {
        n2 = true;
        if (t2) {
          a2(e2);
        }
        a2(0);
        f2(1);
      } else {
        a2(e2);
      }
    });
  };
}
function lazy(e2) {
  return (r2) => e2()(r2);
}
function fromAsyncIterable(e2) {
  return (r2) => {
    var t2 = e2[asyncIteratorSymbol()] && e2[asyncIteratorSymbol()]() || e2;
    var i2 = false;
    var a2 = false;
    var f2 = false;
    var n2;
    r2(start(async (e3) => {
      if (1 === e3) {
        i2 = true;
        if (t2.return) {
          t2.return();
        }
      } else if (a2) {
        f2 = true;
      } else {
        for (f2 = a2 = true; f2 && !i2; ) {
          if ((n2 = await t2.next()).done) {
            i2 = true;
            if (t2.return) {
              await t2.return();
            }
            r2(0);
          } else {
            try {
              f2 = false;
              r2(push(n2.value));
            } catch (e4) {
              if (t2.throw) {
                if (i2 = !!(await t2.throw(e4)).done) {
                  r2(0);
                }
              } else {
                throw e4;
              }
            }
          }
        }
        a2 = false;
      }
    }));
  };
}
function fromIterable(e2) {
  if (e2[Symbol.asyncIterator]) {
    return fromAsyncIterable(e2);
  }
  return (r2) => {
    var t2 = e2[Symbol.iterator]();
    var i2 = false;
    var a2 = false;
    var f2 = false;
    var n2;
    r2(start((e3) => {
      if (1 === e3) {
        i2 = true;
        if (t2.return) {
          t2.return();
        }
      } else if (a2) {
        f2 = true;
      } else {
        for (f2 = a2 = true; f2 && !i2; ) {
          if ((n2 = t2.next()).done) {
            i2 = true;
            if (t2.return) {
              t2.return();
            }
            r2(0);
          } else {
            try {
              f2 = false;
              r2(push(n2.value));
            } catch (e4) {
              if (t2.throw) {
                if (i2 = !!t2.throw(e4).done) {
                  r2(0);
                }
              } else {
                throw e4;
              }
            }
          }
        }
        a2 = false;
      }
    }));
  };
}
var r$2 = fromIterable;
function fromValue(e2) {
  return (r2) => {
    var t2 = false;
    r2(start((i2) => {
      if (1 === i2) {
        t2 = true;
      } else if (!t2) {
        t2 = true;
        r2(push(e2));
        r2(0);
      }
    }));
  };
}
function make(e2) {
  return (r2) => {
    var t2 = false;
    var i2 = e2({
      next(e3) {
        if (!t2) {
          r2(push(e3));
        }
      },
      complete() {
        if (!t2) {
          t2 = true;
          r2(0);
        }
      }
    });
    r2(start((e3) => {
      if (1 === e3 && !t2) {
        t2 = true;
        i2();
      }
    }));
  };
}
function makeSubject() {
  var e2;
  var r2;
  return {
    source: share(make((t2) => {
      e2 = t2.next;
      r2 = t2.complete;
      return teardownPlaceholder;
    })),
    next(r3) {
      if (e2) {
        e2(r3);
      }
    },
    complete() {
      if (r2) {
        r2();
      }
    }
  };
}
function subscribe(r2) {
  return (t2) => {
    var i2 = e$2;
    var a2 = false;
    t2((e2) => {
      if (0 === e2) {
        a2 = true;
      } else if (0 === e2.tag) {
        (i2 = e2[0])(0);
      } else if (!a2) {
        r2(e2[0]);
        i2(0);
      }
    });
    return {
      unsubscribe() {
        if (!a2) {
          a2 = true;
          i2(1);
        }
      }
    };
  };
}
function publish(e2) {
  subscribe((e3) => {
  })(e2);
}
function toPromise(r2) {
  return new Promise((t2) => {
    var i2 = e$2;
    var a2;
    r2((e2) => {
      if (0 === e2) {
        Promise.resolve(a2).then(t2);
      } else if (0 === e2.tag) {
        (i2 = e2[0])(0);
      } else {
        a2 = e2[0];
        i2(0);
      }
    });
  });
}
var rehydrateGraphQlError = (r2) => {
  if (r2 && "string" == typeof r2.message && (r2.extensions || "GraphQLError" === r2.name)) {
    return r2;
  } else if ("object" == typeof r2 && "string" == typeof r2.message) {
    return new GraphQLError(r2.message, r2.nodes, r2.source, r2.positions, r2.path, r2, r2.extensions || {});
  } else {
    return new GraphQLError(r2);
  }
};
class CombinedError extends Error {
  constructor(e2) {
    var r2 = (e2.graphQLErrors || []).map(rehydrateGraphQlError);
    var t2 = ((e3, r3) => {
      var t3 = "";
      if (e3) {
        return `[Network] ${e3.message}`;
      }
      if (r3) {
        for (var a2 = 0, n2 = r3.length; a2 < n2; a2++) {
          if (t3) {
            t3 += "\n";
          }
          t3 += `[GraphQL] ${r3[a2].message}`;
        }
      }
      return t3;
    })(e2.networkError, r2);
    super(t2);
    this.name = "CombinedError";
    this.message = t2;
    this.graphQLErrors = r2;
    this.networkError = e2.networkError;
    this.response = e2.response;
  }
  toString() {
    return this.message;
  }
}
var phash = (e2, r2) => {
  var t2 = 0 | (r2 || 5381);
  for (var a2 = 0, n2 = 0 | e2.length; a2 < n2; a2++) {
    t2 = (t2 << 5) + t2 + e2.charCodeAt(a2);
  }
  return t2;
};
var i$2 = /* @__PURE__ */ new Set();
var f$3 = /* @__PURE__ */ new WeakMap();
var stringify = (e2, r2) => {
  if (null === e2 || i$2.has(e2)) {
    return "null";
  } else if ("object" != typeof e2) {
    return JSON.stringify(e2) || "";
  } else if (e2.toJSON) {
    return stringify(e2.toJSON(), r2);
  } else if (Array.isArray(e2)) {
    var t2 = "[";
    for (var a2 = 0, n2 = e2.length; a2 < n2; a2++) {
      if (t2.length > 1) {
        t2 += ",";
      }
      t2 += stringify(e2[a2], r2) || "null";
    }
    return t2 += "]";
  } else if (!r2 && (d$4 !== NoopConstructor && e2 instanceof d$4 || l$3 !== NoopConstructor && e2 instanceof l$3)) {
    return "null";
  }
  var o2 = Object.keys(e2).sort();
  if (!o2.length && e2.constructor && Object.getPrototypeOf(e2).constructor !== Object.prototype.constructor) {
    var s2 = f$3.get(e2) || Math.random().toString(36).slice(2);
    f$3.set(e2, s2);
    return stringify({
      __key: s2
    }, r2);
  }
  i$2.add(e2);
  var c2 = "{";
  for (var v2 = 0, u2 = o2.length; v2 < u2; v2++) {
    var p2 = stringify(e2[o2[v2]], r2);
    if (p2) {
      if (c2.length > 1) {
        c2 += ",";
      }
      c2 += stringify(o2[v2], r2) + ":" + p2;
    }
  }
  i$2.delete(e2);
  return c2 += "}";
};
var extract = (e2, r2, t2) => {
  if (null == t2 || "object" != typeof t2 || t2.toJSON || i$2.has(t2)) ;
  else if (Array.isArray(t2)) {
    for (var a2 = 0, n2 = t2.length; a2 < n2; a2++) {
      extract(e2, `${r2}.${a2}`, t2[a2]);
    }
  } else if (t2 instanceof d$4 || t2 instanceof l$3) {
    e2.set(r2, t2);
  } else {
    i$2.add(t2);
    for (var o2 in t2) {
      extract(e2, `${r2}.${o2}`, t2[o2]);
    }
  }
};
var stringifyVariables = (e2, r2) => {
  i$2.clear();
  return stringify(e2, r2 || false);
};
class NoopConstructor {
}
var d$4 = "undefined" != typeof File ? File : NoopConstructor;
var l$3 = "undefined" != typeof Blob ? Blob : NoopConstructor;
var c$3 = /("{3}[\s\S]*"{3}|"(?:\\.|[^"])*")/g;
var v$3 = /(?:#[^\n\r]+)?(?:[\r\n]+|$)/g;
var replaceOutsideStrings = (e2, r2) => r2 % 2 == 0 ? e2.replace(v$3, "\n") : e2;
var sanitizeDocument = (e2) => e2.split(c$3).map(replaceOutsideStrings).join("").trim();
var u$2 = /* @__PURE__ */ new Map();
var p$3 = /* @__PURE__ */ new Map();
var stringifyDocument = (e2) => {
  var t2;
  if ("string" == typeof e2) {
    t2 = sanitizeDocument(e2);
  } else if (e2.loc && p$3.get(e2.__key) === e2) {
    t2 = e2.loc.source.body;
  } else {
    t2 = u$2.get(e2) || sanitizeDocument(print(e2));
    u$2.set(e2, t2);
  }
  if ("string" != typeof e2 && !e2.loc) {
    e2.loc = {
      start: 0,
      end: t2.length,
      source: {
        body: t2,
        name: "gql",
        locationOffset: {
          line: 1,
          column: 1
        }
      }
    };
  }
  return t2;
};
var hashDocument = (e2) => {
  var r2;
  if (e2.documentId) {
    r2 = phash(e2.documentId);
  } else {
    r2 = phash(stringifyDocument(e2));
    if (e2.definitions) {
      var t2 = getOperationName(e2);
      if (t2) {
        r2 = phash(`
# ${t2}`, r2);
      }
    }
  }
  return r2;
};
var keyDocument = (e2) => {
  var r2;
  var t2;
  if ("string" == typeof e2) {
    r2 = hashDocument(e2);
    t2 = p$3.get(r2) || parse(e2, {
      noLocation: true
    });
  } else {
    r2 = e2.__key || hashDocument(e2);
    t2 = p$3.get(r2) || e2;
  }
  if (!t2.loc) {
    stringifyDocument(t2);
  }
  t2.__key = r2;
  p$3.set(r2, t2);
  return t2;
};
var createRequest = (e2, r2, t2) => {
  var a2 = r2 || {};
  var n2 = keyDocument(e2);
  var o2 = stringifyVariables(a2, true);
  var s2 = n2.__key;
  if ("{}" !== o2) {
    s2 = phash(o2, s2);
  }
  return {
    key: s2,
    query: n2,
    variables: a2,
    extensions: t2
  };
};
var getOperationName = (e2) => {
  for (var r2 = 0, a2 = e2.definitions.length; r2 < a2; r2++) {
    var n2 = e2.definitions[r2];
    if (n2.kind === e$3.OPERATION_DEFINITION) {
      return n2.name ? n2.name.value : void 0;
    }
  }
};
var makeResult = (e2, r2, t2) => {
  if (!("data" in r2 || "errors" in r2 && Array.isArray(r2.errors))) {
    throw new Error("No Content");
  }
  var a2 = "subscription" === e2.kind;
  return {
    operation: e2,
    data: r2.data,
    error: Array.isArray(r2.errors) ? new CombinedError({
      graphQLErrors: r2.errors,
      response: t2
    }) : void 0,
    extensions: r2.extensions ? {
      ...r2.extensions
    } : void 0,
    hasNext: null == r2.hasNext ? a2 : r2.hasNext,
    stale: false
  };
};
var deepMerge = (e2, r2) => {
  if ("object" == typeof e2 && null != e2) {
    if (Array.isArray(e2)) {
      e2 = [...e2];
      for (var t2 = 0, a2 = r2.length; t2 < a2; t2++) {
        e2[t2] = deepMerge(e2[t2], r2[t2]);
      }
      return e2;
    }
    if (!e2.constructor || e2.constructor === Object) {
      e2 = {
        ...e2
      };
      for (var n2 in r2) {
        e2[n2] = deepMerge(e2[n2], r2[n2]);
      }
      return e2;
    }
  }
  return r2;
};
var mergeResultPatch = (e2, r2, t2, a2) => {
  var n2 = e2.error ? e2.error.graphQLErrors : [];
  var o2 = !!e2.extensions || !!(r2.payload || r2).extensions;
  var s2 = {
    ...e2.extensions,
    ...(r2.payload || r2).extensions
  };
  var i2 = r2.incremental;
  if ("path" in r2) {
    i2 = [r2];
  }
  var f2 = {
    data: e2.data
  };
  if (i2) {
    var _loop = function() {
      var e3 = i2[d2];
      if (Array.isArray(e3.errors)) {
        n2.push(...e3.errors);
      }
      if (e3.extensions) {
        Object.assign(s2, e3.extensions);
        o2 = true;
      }
      var r3 = "data";
      var t3 = f2;
      var l3 = [];
      if (e3.path) {
        l3 = e3.path;
      } else if (a2) {
        var c2 = a2.find((r4) => r4.id === e3.id);
        if (e3.subPath) {
          l3 = [...c2.path, ...e3.subPath];
        } else {
          l3 = c2.path;
        }
      }
      for (var v2 = 0, u2 = l3.length; v2 < u2; r3 = l3[v2++]) {
        t3 = t3[r3] = Array.isArray(t3[r3]) ? [...t3[r3]] : {
          ...t3[r3]
        };
      }
      if (e3.items) {
        var p2 = +r3 >= 0 ? r3 : 0;
        for (var h2 = 0, y2 = e3.items.length; h2 < y2; h2++) {
          t3[p2 + h2] = deepMerge(t3[p2 + h2], e3.items[h2]);
        }
      } else if (void 0 !== e3.data) {
        t3[r3] = deepMerge(t3[r3], e3.data);
      }
    };
    for (var d2 = 0, l2 = i2.length; d2 < l2; d2++) {
      _loop();
    }
  } else {
    f2.data = (r2.payload || r2).data || e2.data;
    n2 = r2.errors || r2.payload && r2.payload.errors || n2;
  }
  return {
    operation: e2.operation,
    data: f2.data,
    error: n2.length ? new CombinedError({
      graphQLErrors: n2,
      response: t2
    }) : void 0,
    extensions: o2 ? s2 : void 0,
    hasNext: null != r2.hasNext ? r2.hasNext : e2.hasNext,
    stale: false
  };
};
var makeErrorResult = (e2, r2, t2) => ({
  operation: e2,
  data: void 0,
  error: new CombinedError({
    networkError: r2,
    response: t2
  }),
  extensions: void 0,
  hasNext: false,
  stale: false
});
function makeFetchBody(e2) {
  var r2 = {
    query: void 0,
    documentId: void 0,
    operationName: getOperationName(e2.query),
    variables: e2.variables || void 0,
    extensions: e2.extensions
  };
  if ("documentId" in e2.query && e2.query.documentId && (!e2.query.definitions || !e2.query.definitions.length)) {
    r2.documentId = e2.query.documentId;
  } else if (!e2.extensions || !e2.extensions.persistedQuery || e2.extensions.persistedQuery.miss) {
    r2.query = stringifyDocument(e2.query);
  }
  return r2;
}
var makeFetchURL = (e2, r2) => {
  var t2 = "query" === e2.kind && e2.context.preferGetMethod;
  if (!t2 || !r2) {
    return e2.context.url;
  }
  var a2 = splitOutSearchParams(e2.context.url);
  for (var n2 in r2) {
    var o2 = r2[n2];
    if (o2) {
      a2[1].set(n2, "object" == typeof o2 ? stringifyVariables(o2) : o2);
    }
  }
  var s2 = a2.join("?");
  if (s2.length > 2047 && "force" !== t2) {
    e2.context.preferGetMethod = false;
    return e2.context.url;
  }
  return s2;
};
var splitOutSearchParams = (e2) => {
  var r2 = e2.indexOf("?");
  return r2 > -1 ? [e2.slice(0, r2), new URLSearchParams(e2.slice(r2 + 1))] : [e2, new URLSearchParams()];
};
var serializeBody = (e2, r2) => {
  if (r2 && !("query" === e2.kind && !!e2.context.preferGetMethod)) {
    var t2 = stringifyVariables(r2);
    var a2 = ((e3) => {
      var r3 = /* @__PURE__ */ new Map();
      if (d$4 !== NoopConstructor || l$3 !== NoopConstructor) {
        i$2.clear();
        extract(r3, "variables", e3);
      }
      return r3;
    })(r2.variables);
    if (a2.size) {
      var n2 = new FormData();
      n2.append("operations", t2);
      n2.append("map", stringifyVariables({
        ...[...a2.keys()].map((e3) => [e3])
      }));
      var o2 = 0;
      for (var s2 of a2.values()) {
        n2.append("" + o2++, s2);
      }
      return n2;
    }
    return t2;
  }
};
var makeFetchOptions = (e2, r2) => {
  var t2 = {
    accept: "subscription" === e2.kind ? "text/event-stream, multipart/mixed" : "application/graphql-response+json, application/graphql+json, application/json, text/event-stream, multipart/mixed"
  };
  var a2 = ("function" == typeof e2.context.fetchOptions ? e2.context.fetchOptions() : e2.context.fetchOptions) || {};
  if (a2.headers) {
    if (((e3) => "has" in e3 && !Object.keys(e3).length)(a2.headers)) {
      a2.headers.forEach((e3, r3) => {
        t2[r3] = e3;
      });
    } else if (Array.isArray(a2.headers)) {
      a2.headers.forEach((e3, r3) => {
        if (Array.isArray(e3)) {
          if (t2[e3[0]]) {
            t2[e3[0]] = `${t2[e3[0]]},${e3[1]}`;
          } else {
            t2[e3[0]] = e3[1];
          }
        } else {
          t2[r3] = e3;
        }
      });
    } else {
      for (var n2 in a2.headers) {
        t2[n2.toLowerCase()] = a2.headers[n2];
      }
    }
  }
  var o2 = serializeBody(e2, r2);
  if ("string" == typeof o2 && !t2["content-type"]) {
    t2["content-type"] = "application/json";
  }
  return {
    ...a2,
    method: o2 ? "POST" : "GET",
    body: o2,
    headers: t2
  };
};
var h$3 = /boundary="?([^=";]+)"?/i;
var y$4 = /data: ?([^\n]+)/;
async function* streamBody(e2) {
  if (e2.body[Symbol.asyncIterator]) {
    for await (var r2 of e2.body) {
      yield r2;
    }
  } else {
    var t2 = e2.body.getReader();
    var a2;
    try {
      while (!(a2 = await t2.read()).done) {
        yield a2.value;
      }
    } finally {
      t2.cancel();
    }
  }
}
async function* streamToBoundedChunks(e2, r2) {
  var t2 = "undefined" != typeof TextDecoder ? new TextDecoder() : null;
  var a2 = "";
  var n2;
  for await (var o2 of e2) {
    a2 += "Buffer" === o2.constructor.name ? o2.toString() : t2.decode(o2, {
      stream: true
    });
    while ((n2 = a2.indexOf(r2)) > -1) {
      yield a2.slice(0, n2);
      a2 = a2.slice(n2 + r2.length);
    }
  }
}
async function* fetchOperation(e2, r2, t2) {
  var a2 = true;
  var n2 = null;
  var o2;
  try {
    yield await Promise.resolve();
    var s2 = (o2 = await (e2.context.fetch || fetch)(r2, t2)).headers.get("Content-Type") || "";
    var i2;
    if (/multipart\/mixed/i.test(s2)) {
      i2 = async function* parseMultipartMixed(e3, r3) {
        var t3 = e3.match(h$3);
        var a3 = "--" + (t3 ? t3[1] : "-");
        var n3 = true;
        var o3;
        for await (var s3 of streamToBoundedChunks(streamBody(r3), "\r\n" + a3)) {
          if (n3) {
            n3 = false;
            var i3 = s3.indexOf(a3);
            if (i3 > -1) {
              s3 = s3.slice(i3 + a3.length);
            } else {
              continue;
            }
          }
          try {
            yield o3 = JSON.parse(s3.slice(s3.indexOf("\r\n\r\n") + 4));
          } catch (e4) {
            if (!o3) {
              throw e4;
            }
          }
          if (o3 && false === o3.hasNext) {
            break;
          }
        }
        if (o3 && false !== o3.hasNext) {
          yield {
            hasNext: false
          };
        }
      }(s2, o2);
    } else if (/text\/event-stream/i.test(s2)) {
      i2 = async function* parseEventStream(e3) {
        var r3;
        for await (var t3 of streamToBoundedChunks(streamBody(e3), "\n\n")) {
          var a3 = t3.match(y$4);
          if (a3) {
            var n3 = a3[1];
            try {
              yield r3 = JSON.parse(n3);
            } catch (e4) {
              if (!r3) {
                throw e4;
              }
            }
            if (r3 && false === r3.hasNext) {
              break;
            }
          }
        }
        if (r3 && false !== r3.hasNext) {
          yield {
            hasNext: false
          };
        }
      }(o2);
    } else if (!/text\//i.test(s2)) {
      i2 = async function* parseJSON(e3) {
        yield JSON.parse(await e3.text());
      }(o2);
    } else {
      i2 = async function* parseMaybeJSON(e3) {
        var r3 = await e3.text();
        try {
          var t3 = JSON.parse(r3);
          if (false) ;
          yield t3;
        } catch (e4) {
          throw new Error(r3);
        }
      }(o2);
    }
    var f2;
    for await (var d2 of i2) {
      if (d2.pending && !n2) {
        f2 = d2.pending;
      } else if (d2.pending) {
        f2 = [...f2, ...d2.pending];
      }
      n2 = n2 ? mergeResultPatch(n2, d2, o2, f2) : makeResult(e2, d2, o2);
      a2 = false;
      yield n2;
      a2 = true;
    }
    if (!n2) {
      yield n2 = makeResult(e2, {}, o2);
    }
  } catch (r3) {
    if (!a2) {
      throw r3;
    }
    yield makeErrorResult(e2, o2 && (o2.status < 200 || o2.status >= 300) && o2.statusText ? new Error(o2.statusText) : r3, o2);
  }
}
function makeFetchSource(e2, r2, t2) {
  var a2;
  if ("undefined" != typeof AbortController) {
    t2.signal = (a2 = new AbortController()).signal;
  }
  return onEnd(() => {
    if (a2) {
      a2.abort();
    }
  })(filter((e3) => !!e3)(fromAsyncIterable(fetchOperation(e2, r2, t2))));
}
var collectTypes = (e2, r2) => {
  if (Array.isArray(e2)) {
    for (var t2 = 0, n2 = e2.length; t2 < n2; t2++) {
      collectTypes(e2[t2], r2);
    }
  } else if ("object" == typeof e2 && null !== e2) {
    for (var a2 in e2) {
      if ("__typename" === a2 && "string" == typeof e2[a2]) {
        r2.add(e2[a2]);
      } else {
        collectTypes(e2[a2], r2);
      }
    }
  }
  return r2;
};
var formatNode = (r2) => {
  if ("definitions" in r2) {
    var t2 = [];
    for (var n2 = 0, a2 = r2.definitions.length; n2 < a2; n2++) {
      var i2 = formatNode(r2.definitions[n2]);
      t2.push(i2);
    }
    return {
      ...r2,
      definitions: t2
    };
  }
  if ("directives" in r2 && r2.directives && r2.directives.length) {
    var o2 = [];
    var s2 = {};
    for (var c2 = 0, u2 = r2.directives.length; c2 < u2; c2++) {
      var p2 = r2.directives[c2];
      var d2 = p2.name.value;
      if ("_" !== d2[0]) {
        o2.push(p2);
      } else {
        d2 = d2.slice(1);
      }
      s2[d2] = p2;
    }
    r2 = {
      ...r2,
      directives: o2,
      _directives: s2
    };
  }
  if ("selectionSet" in r2) {
    var v2 = [];
    var l2 = r2.kind === e$3.OPERATION_DEFINITION;
    if (r2.selectionSet) {
      for (var f2 = 0, h2 = r2.selectionSet.selections.length; f2 < h2; f2++) {
        var k2 = r2.selectionSet.selections[f2];
        l2 = l2 || k2.kind === e$3.FIELD && "__typename" === k2.name.value && !k2.alias;
        var y2 = formatNode(k2);
        v2.push(y2);
      }
      if (!l2) {
        v2.push({
          kind: e$3.FIELD,
          name: {
            kind: e$3.NAME,
            value: "__typename"
          },
          _generated: true
        });
      }
      return {
        ...r2,
        selectionSet: {
          ...r2.selectionSet,
          selections: v2
        }
      };
    }
  }
  return r2;
};
var I$2 = /* @__PURE__ */ new Map();
var formatDocument = (e2) => {
  var t2 = keyDocument(e2);
  var n2 = I$2.get(t2.__key);
  if (!n2) {
    I$2.set(t2.__key, n2 = formatNode(t2));
    Object.defineProperty(n2, "__key", {
      value: t2.__key,
      enumerable: false
    });
  }
  return n2;
};
function withPromise(e2) {
  var source$ = (r2) => e2(r2);
  source$.toPromise = () => toPromise(take(1)(filter((e3) => !e3.stale && !e3.hasNext)(source$)));
  source$.then = (e3, r2) => source$.toPromise().then(e3, r2);
  source$.subscribe = (e3) => subscribe(e3)(source$);
  return source$;
}
function makeOperation(e2, r2, t2) {
  return {
    ...r2,
    kind: e2,
    context: r2.context ? {
      ...r2.context,
      ...t2
    } : t2 || r2.context
  };
}
var addMetadata = (e2, r2) => makeOperation(e2.kind, e2, {
  meta: {
    ...e2.context.meta,
    ...r2
  }
});
var noop$3 = () => {
};
function gql(n2) {
  var a2 = /* @__PURE__ */ new Map();
  var i2 = [];
  var o2 = [];
  var s2 = Array.isArray(n2) ? n2[0] : n2 || "";
  for (var c2 = 1; c2 < arguments.length; c2++) {
    var u2 = arguments[c2];
    if (u2 && u2.definitions) {
      o2.push(u2);
    } else {
      s2 += u2;
    }
    s2 += arguments[0][c2];
  }
  o2.unshift(keyDocument(s2));
  for (var p2 = 0; p2 < o2.length; p2++) {
    for (var d2 = 0; d2 < o2[p2].definitions.length; d2++) {
      var v2 = o2[p2].definitions[d2];
      if (v2.kind === e$3.FRAGMENT_DEFINITION) {
        var l2 = v2.name.value;
        var f2 = stringifyDocument(v2);
        if (!a2.has(l2)) {
          a2.set(l2, f2);
          i2.push(v2);
        }
      } else {
        i2.push(v2);
      }
    }
  }
  return keyDocument({
    kind: e$3.DOCUMENT,
    definitions: i2
  });
}
var shouldSkip = ({ kind: e2 }) => "mutation" !== e2 && "query" !== e2;
var mapTypeNames = (e2) => {
  var r2 = formatDocument(e2.query);
  if (r2 !== e2.query) {
    var t2 = makeOperation(e2.kind, e2);
    t2.query = r2;
    return t2;
  } else {
    return e2;
  }
};
var cacheExchange = ({ forward: e2, client: r2, dispatchDebug: t2 }) => {
  var a2 = /* @__PURE__ */ new Map();
  var i2 = /* @__PURE__ */ new Map();
  var isOperationCached = (e3) => "query" === e3.kind && "network-only" !== e3.context.requestPolicy && ("cache-only" === e3.context.requestPolicy || a2.has(e3.key));
  return (o2) => {
    var s2 = map((e3) => {
      var i3 = a2.get(e3.key);
      var o3 = i3 || makeResult(e3, {
        data: null
      });
      o3 = {
        ...o3,
        operation: addMetadata(e3, {
          cacheOutcome: i3 ? "hit" : "miss"
        })
      };
      if ("cache-and-network" === e3.context.requestPolicy) {
        o3.stale = true;
        reexecuteOperation(r2, e3);
      }
      return o3;
    })(filter((e3) => !shouldSkip(e3) && isOperationCached(e3))(o2));
    var c2 = onPush((e3) => {
      var { operation: n2 } = e3;
      if (!n2) {
        return;
      }
      var o3 = n2.context.additionalTypenames || [];
      if ("subscription" !== e3.operation.kind) {
        o3 = ((e4) => [...collectTypes(e4, /* @__PURE__ */ new Set())])(e3.data).concat(o3);
      }
      if ("mutation" === e3.operation.kind || "subscription" === e3.operation.kind) {
        var s3 = /* @__PURE__ */ new Set();
        for (var c3 = 0; c3 < o3.length; c3++) {
          var u2 = o3[c3];
          var p2 = i2.get(u2);
          if (!p2) {
            i2.set(u2, p2 = /* @__PURE__ */ new Set());
          }
          for (var d2 of p2.values()) {
            s3.add(d2);
          }
          p2.clear();
        }
        for (var v2 of s3.values()) {
          if (a2.has(v2)) {
            n2 = a2.get(v2).operation;
            a2.delete(v2);
            reexecuteOperation(r2, n2);
          }
        }
      } else if ("query" === n2.kind && e3.data) {
        a2.set(n2.key, e3);
        for (var l2 = 0; l2 < o3.length; l2++) {
          var f2 = o3[l2];
          var h2 = i2.get(f2);
          if (!h2) {
            i2.set(f2, h2 = /* @__PURE__ */ new Set());
          }
          h2.add(n2.key);
        }
      }
    })(e2(filter((e3) => "query" !== e3.kind || "cache-only" !== e3.context.requestPolicy)(map((e3) => addMetadata(e3, {
      cacheOutcome: "miss"
    }))(merge$1([map(mapTypeNames)(filter((e3) => !shouldSkip(e3) && !isOperationCached(e3))(o2)), filter((e3) => shouldSkip(e3))(o2)])))));
    return merge$1([s2, c2]);
  };
};
var reexecuteOperation = (e2, r2) => e2.reexecuteOperation(makeOperation(r2.kind, r2, {
  requestPolicy: "network-only"
}));
var fetchExchange = ({ forward: e2, dispatchDebug: r2 }) => (t2) => {
  var n2 = mergeMap((e3) => {
    var n3 = makeFetchBody(e3);
    var a3 = makeFetchURL(e3, n3);
    var i2 = makeFetchOptions(e3, n3);
    var s2 = takeUntil(filter((r3) => "teardown" === r3.kind && r3.key === e3.key)(t2))(makeFetchSource(e3, a3, i2));
    return s2;
  })(filter((e3) => "teardown" !== e3.kind && ("subscription" !== e3.kind || !!e3.context.fetchSubscriptions))(t2));
  var a2 = e2(filter((e3) => "teardown" === e3.kind || "subscription" === e3.kind && !e3.context.fetchSubscriptions)(t2));
  return merge$1([n2, a2]);
};
var composeExchanges = (e2) => ({ client: r2, forward: t2, dispatchDebug: n2 }) => e2.reduceRight((e3, t3) => {
  return t3({
    client: r2,
    forward(r3) {
      return share(e3(share(r3)));
    },
    dispatchDebug(e4) {
    }
  });
}, t2);
var fallbackExchange = ({ dispatchDebug: e2 }) => (r2) => {
  return filter((e3) => false)(r2);
};
var C$2 = function Client(e2) {
  var r2 = 0;
  var t2 = /* @__PURE__ */ new Map();
  var n2 = /* @__PURE__ */ new Map();
  var a2 = /* @__PURE__ */ new Set();
  var i2 = [];
  var o2 = {
    url: e2.url,
    fetchSubscriptions: e2.fetchSubscriptions,
    fetchOptions: e2.fetchOptions,
    fetch: e2.fetch,
    preferGetMethod: e2.preferGetMethod,
    requestPolicy: e2.requestPolicy || "cache-first"
  };
  var s2 = makeSubject();
  function nextOperation(e3) {
    if ("mutation" === e3.kind || "teardown" === e3.kind || !a2.has(e3.key)) {
      if ("teardown" === e3.kind) {
        a2.delete(e3.key);
      } else if ("mutation" !== e3.kind) {
        a2.add(e3.key);
      }
      s2.next(e3);
    }
  }
  var c2 = false;
  function dispatchOperation(e3) {
    if (e3) {
      nextOperation(e3);
    }
    if (!c2) {
      c2 = true;
      while (c2 && (e3 = i2.shift())) {
        nextOperation(e3);
      }
      c2 = false;
    }
  }
  var makeResultSource = (e3) => {
    var r3 = takeUntil(filter((r4) => "teardown" === r4.kind && r4.key === e3.key)(s2.source))(filter((r4) => r4.operation.kind === e3.kind && r4.operation.key === e3.key && (!r4.operation.context._instance || r4.operation.context._instance === e3.context._instance))(E2));
    if ("query" !== e3.kind) {
      r3 = takeWhile((e4) => !!e4.hasNext, true)(r3);
    } else {
      r3 = switchMap((r4) => {
        var t3 = fromValue(r4);
        return r4.stale || r4.hasNext ? t3 : merge$1([t3, map(() => {
          r4.stale = true;
          return r4;
        })(take(1)(filter((r5) => r5.key === e3.key)(s2.source)))]);
      })(r3);
    }
    if ("mutation" !== e3.kind) {
      r3 = onEnd(() => {
        a2.delete(e3.key);
        t2.delete(e3.key);
        n2.delete(e3.key);
        c2 = false;
        for (var r4 = i2.length - 1; r4 >= 0; r4--) {
          if (i2[r4].key === e3.key) {
            i2.splice(r4, 1);
          }
        }
        nextOperation(makeOperation("teardown", e3, e3.context));
      })(onPush((r4) => {
        if (r4.stale) {
          if (!r4.hasNext) {
            a2.delete(e3.key);
          } else {
            for (var n3 = 0; n3 < i2.length; n3++) {
              var o3 = i2[n3];
              if (o3.key === r4.operation.key) {
                a2.delete(o3.key);
                break;
              }
            }
          }
        } else if (!r4.hasNext) {
          a2.delete(e3.key);
        }
        t2.set(e3.key, r4);
      })(r3));
    } else {
      r3 = onStart(() => {
        nextOperation(e3);
      })(r3);
    }
    return share(r3);
  };
  var u2 = this instanceof Client ? this : Object.create(Client.prototype);
  var p2 = Object.assign(u2, {
    suspense: !!e2.suspense,
    operations$: s2.source,
    reexecuteOperation(e3) {
      if ("teardown" === e3.kind) {
        dispatchOperation(e3);
      } else if ("mutation" === e3.kind) {
        i2.push(e3);
        Promise.resolve().then(dispatchOperation);
      } else if (n2.has(e3.key)) {
        var r3 = false;
        for (var t3 = 0; t3 < i2.length; t3++) {
          if (i2[t3].key === e3.key) {
            i2[t3] = e3;
            r3 = true;
          }
        }
        if (!(r3 || a2.has(e3.key) && "network-only" !== e3.context.requestPolicy)) {
          i2.push(e3);
          Promise.resolve().then(dispatchOperation);
        } else {
          a2.delete(e3.key);
          Promise.resolve().then(dispatchOperation);
        }
      }
    },
    createRequestOperation(e3, t3, n3) {
      if (!n3) {
        n3 = {};
      }
      return makeOperation(e3, t3, {
        _instance: "mutation" === e3 ? r2 = r2 + 1 | 0 : void 0,
        ...o2,
        ...n3,
        requestPolicy: n3.requestPolicy || o2.requestPolicy,
        suspense: n3.suspense || false !== n3.suspense && p2.suspense
      });
    },
    executeRequestOperation(e3) {
      if ("mutation" === e3.kind) {
        return withPromise(makeResultSource(e3));
      }
      return withPromise(lazy(() => {
        var r3 = n2.get(e3.key);
        if (!r3) {
          n2.set(e3.key, r3 = makeResultSource(e3));
        }
        r3 = onStart(() => {
          dispatchOperation(e3);
        })(r3);
        var a3 = t2.get(e3.key);
        if ("query" === e3.kind && a3 && (a3.stale || a3.hasNext)) {
          return switchMap(fromValue)(merge$1([r3, filter((r4) => r4 === t2.get(e3.key))(fromValue(a3))]));
        } else {
          return r3;
        }
      }));
    },
    executeQuery(e3, r3) {
      var t3 = p2.createRequestOperation("query", e3, r3);
      return p2.executeRequestOperation(t3);
    },
    executeSubscription(e3, r3) {
      var t3 = p2.createRequestOperation("subscription", e3, r3);
      return p2.executeRequestOperation(t3);
    },
    executeMutation(e3, r3) {
      var t3 = p2.createRequestOperation("mutation", e3, r3);
      return p2.executeRequestOperation(t3);
    },
    readQuery(e3, r3, t3) {
      var n3 = null;
      subscribe((e4) => {
        n3 = e4;
      })(p2.query(e3, r3, t3)).unsubscribe();
      return n3;
    },
    query: (e3, r3, t3) => p2.executeQuery(createRequest(e3, r3), t3),
    subscription: (e3, r3, t3) => p2.executeSubscription(createRequest(e3, r3), t3),
    mutation: (e3, r3, t3) => p2.executeMutation(createRequest(e3, r3), t3)
  });
  var d2 = noop$3;
  var w2 = composeExchanges(e2.exchanges);
  var E2 = share(w2({
    client: p2,
    dispatchDebug: d2,
    forward: fallbackExchange({
      dispatchDebug: d2
    })
  })(s2.source));
  publish(E2);
  return p2;
};
var Q$2 = C$2;
var t$1, r$1, u$1, i$1, o$1 = 0, f$2 = [], c$2 = l$4, e$1 = c$2.__b, a$1 = c$2.__r, v$2 = c$2.diffed, l$2 = c$2.__c, m$1 = c$2.unmount, s$1 = c$2.__;
function p$2(n2, t2) {
  c$2.__h && c$2.__h(r$1, n2, o$1 || t2), o$1 = 0;
  var u2 = r$1.__H || (r$1.__H = { __: [], __h: [] });
  return n2 >= u2.__.length && u2.__.push({}), u2.__[n2];
}
function d$3(n2) {
  return o$1 = 1, h$2(D$1, n2);
}
function h$2(n2, u2, i2) {
  var o2 = p$2(t$1++, 2);
  if (o2.t = n2, !o2.__c && (o2.__ = [i2 ? i2(u2) : D$1(void 0, u2), function(n3) {
    var t2 = o2.__N ? o2.__N[0] : o2.__[0], r2 = o2.t(t2, n3);
    t2 !== r2 && (o2.__N = [r2, o2.__[1]], o2.__c.setState({}));
  }], o2.__c = r$1, !r$1.__f)) {
    var f2 = function(n3, t2, r2) {
      if (!o2.__c.__H) return true;
      var u3 = o2.__c.__H.__.filter(function(n4) {
        return !!n4.__c;
      });
      if (u3.every(function(n4) {
        return !n4.__N;
      })) return !c2 || c2.call(this, n3, t2, r2);
      var i3 = o2.__c.props !== n3;
      return u3.forEach(function(n4) {
        if (n4.__N) {
          var t3 = n4.__[0];
          n4.__ = n4.__N, n4.__N = void 0, t3 !== n4.__[0] && (i3 = true);
        }
      }), c2 && c2.call(this, n3, t2, r2) || i3;
    };
    r$1.__f = true;
    var c2 = r$1.shouldComponentUpdate, e2 = r$1.componentWillUpdate;
    r$1.componentWillUpdate = function(n3, t2, r2) {
      if (this.__e) {
        var u3 = c2;
        c2 = void 0, f2(n3, t2, r2), c2 = u3;
      }
      e2 && e2.call(this, n3, t2, r2);
    }, r$1.shouldComponentUpdate = f2;
  }
  return o2.__N || o2.__;
}
function y$3(n2, u2) {
  var i2 = p$2(t$1++, 3);
  !c$2.__s && C$1(i2.__H, u2) && (i2.__ = n2, i2.u = u2, r$1.__H.__h.push(i2));
}
function _$2(n2, u2) {
  var i2 = p$2(t$1++, 4);
  !c$2.__s && C$1(i2.__H, u2) && (i2.__ = n2, i2.u = u2, r$1.__h.push(i2));
}
function A$2(n2) {
  return o$1 = 5, T$2(function() {
    return { current: n2 };
  }, []);
}
function F$3(n2, t2, r2) {
  o$1 = 6, _$2(function() {
    if ("function" == typeof n2) {
      var r3 = n2(t2());
      return function() {
        n2(null), r3 && "function" == typeof r3 && r3();
      };
    }
    if (n2) return n2.current = t2(), function() {
      return n2.current = null;
    };
  }, null == r2 ? r2 : r2.concat(n2));
}
function T$2(n2, r2) {
  var u2 = p$2(t$1++, 7);
  return C$1(u2.__H, r2) && (u2.__ = n2(), u2.__H = r2, u2.__h = n2), u2.__;
}
function q$2(n2, t2) {
  return o$1 = 8, T$2(function() {
    return n2;
  }, t2);
}
function x$1(n2) {
  var u2 = r$1.context[n2.__c], i2 = p$2(t$1++, 9);
  return i2.c = n2, u2 ? (null == i2.__ && (i2.__ = true, u2.sub(r$1)), u2.props.value) : n2.__;
}
function P$2(n2, t2) {
  c$2.useDebugValue && c$2.useDebugValue(t2 ? t2(n2) : n2);
}
function g$3() {
  var n2 = p$2(t$1++, 11);
  if (!n2.__) {
    for (var u2 = r$1.__v; null !== u2 && !u2.__m && null !== u2.__; ) u2 = u2.__;
    var i2 = u2.__m || (u2.__m = [0, 0]);
    n2.__ = "P" + i2[0] + "-" + i2[1]++;
  }
  return n2.__;
}
function j$1() {
  for (var n2; n2 = f$2.shift(); ) if (n2.__P && n2.__H) try {
    n2.__H.__h.forEach(z$2), n2.__H.__h.forEach(B$1), n2.__H.__h = [];
  } catch (t2) {
    n2.__H.__h = [], c$2.__e(t2, n2.__v);
  }
}
c$2.__b = function(n2) {
  r$1 = null, e$1 && e$1(n2);
}, c$2.__ = function(n2, t2) {
  n2 && t2.__k && t2.__k.__m && (n2.__m = t2.__k.__m), s$1 && s$1(n2, t2);
}, c$2.__r = function(n2) {
  a$1 && a$1(n2), t$1 = 0;
  var i2 = (r$1 = n2.__c).__H;
  i2 && (u$1 === r$1 ? (i2.__h = [], r$1.__h = [], i2.__.forEach(function(n3) {
    n3.__N && (n3.__ = n3.__N), n3.u = n3.__N = void 0;
  })) : (i2.__h.forEach(z$2), i2.__h.forEach(B$1), i2.__h = [], t$1 = 0)), u$1 = r$1;
}, c$2.diffed = function(n2) {
  v$2 && v$2(n2);
  var t2 = n2.__c;
  t2 && t2.__H && (t2.__H.__h.length && (1 !== f$2.push(t2) && i$1 === c$2.requestAnimationFrame || ((i$1 = c$2.requestAnimationFrame) || w$3)(j$1)), t2.__H.__.forEach(function(n3) {
    n3.u && (n3.__H = n3.u), n3.u = void 0;
  })), u$1 = r$1 = null;
}, c$2.__c = function(n2, t2) {
  t2.some(function(n3) {
    try {
      n3.__h.forEach(z$2), n3.__h = n3.__h.filter(function(n4) {
        return !n4.__ || B$1(n4);
      });
    } catch (r2) {
      t2.some(function(n4) {
        n4.__h && (n4.__h = []);
      }), t2 = [], c$2.__e(r2, n3.__v);
    }
  }), l$2 && l$2(n2, t2);
}, c$2.unmount = function(n2) {
  m$1 && m$1(n2);
  var t2, r2 = n2.__c;
  r2 && r2.__H && (r2.__H.__.forEach(function(n3) {
    try {
      z$2(n3);
    } catch (n4) {
      t2 = n4;
    }
  }), r2.__H = void 0, t2 && c$2.__e(t2, r2.__v));
};
var k$2 = "function" == typeof requestAnimationFrame;
function w$3(n2) {
  var t2, r2 = function() {
    clearTimeout(u2), k$2 && cancelAnimationFrame(t2), setTimeout(n2);
  }, u2 = setTimeout(r2, 35);
  k$2 && (t2 = requestAnimationFrame(r2));
}
function z$2(n2) {
  var t2 = r$1, u2 = n2.__c;
  "function" == typeof u2 && (n2.__c = void 0, u2()), r$1 = t2;
}
function B$1(n2) {
  var t2 = r$1;
  n2.__c = n2.__(), r$1 = t2;
}
function C$1(n2, t2) {
  return !n2 || n2.length !== t2.length || t2.some(function(t3, r2) {
    return t3 !== n2[r2];
  });
}
function D$1(n2, t2) {
  return "function" == typeof t2 ? t2(n2) : t2;
}
function g$2(n2, t2) {
  for (var e2 in t2) n2[e2] = t2[e2];
  return n2;
}
function E$1(n2, t2) {
  for (var e2 in n2) if ("__source" !== e2 && !(e2 in t2)) return true;
  for (var r2 in t2) if ("__source" !== r2 && n2[r2] !== t2[r2]) return true;
  return false;
}
function C(n2, t2) {
  var e2 = t2(), r2 = d$3({ t: { __: e2, u: t2 } }), u2 = r2[0].t, o2 = r2[1];
  return _$2(function() {
    u2.__ = e2, u2.u = t2, x(u2) && o2({ t: u2 });
  }, [n2, e2, t2]), y$3(function() {
    return x(u2) && o2({ t: u2 }), n2(function() {
      x(u2) && o2({ t: u2 });
    });
  }, [n2]), e2;
}
function x(n2) {
  var t2, e2, r2 = n2.u, u2 = n2.__;
  try {
    var o2 = r2();
    return !((t2 = u2) === (e2 = o2) && (0 !== t2 || 1 / t2 == 1 / e2) || t2 != t2 && e2 != e2);
  } catch (n3) {
    return true;
  }
}
function R(n2) {
  n2();
}
function w$2(n2) {
  return n2;
}
function k$1() {
  return [false, R];
}
var I$1 = _$2;
function N$1(n2, t2) {
  this.props = n2, this.context = t2;
}
function M(n2, e2) {
  function r2(n3) {
    var t2 = this.props.ref, r3 = t2 == n3.ref;
    return !r3 && t2 && (t2.call ? t2(null) : t2.current = null), e2 ? !e2(this.props, n3) || !r3 : E$1(this.props, n3);
  }
  function u2(e3) {
    return this.shouldComponentUpdate = r2, _$3(n2, e3);
  }
  return u2.displayName = "Memo(" + (n2.displayName || n2.name) + ")", u2.prototype.isReactComponent = true, u2.__f = true, u2;
}
(N$1.prototype = new x$2()).isPureReactComponent = true, N$1.prototype.shouldComponentUpdate = function(n2, t2) {
  return E$1(this.props, n2) || E$1(this.state, t2);
};
var T$1 = l$4.__b;
l$4.__b = function(n2) {
  n2.type && n2.type.__f && n2.ref && (n2.props.ref = n2.ref, n2.ref = null), T$1 && T$1(n2);
};
var A$1 = "undefined" != typeof Symbol && Symbol.for && Symbol.for("react.forward_ref") || 3911;
function D(n2) {
  function t2(t3) {
    var e2 = g$2({}, t3);
    return delete e2.ref, n2(e2, t3.ref || null);
  }
  return t2.$$typeof = A$1, t2.render = t2, t2.prototype.isReactComponent = t2.__f = true, t2.displayName = "ForwardRef(" + (n2.displayName || n2.name) + ")", t2;
}
var L$1 = function(n2, t2) {
  return null == n2 ? null : H$1(H$1(n2).map(t2));
}, O = { map: L$1, forEach: L$1, count: function(n2) {
  return n2 ? H$1(n2).length : 0;
}, only: function(n2) {
  var t2 = H$1(n2);
  if (1 !== t2.length) throw "Children.only";
  return t2[0];
}, toArray: H$1 }, F$2 = l$4.__e;
l$4.__e = function(n2, t2, e2, r2) {
  if (n2.then) {
    for (var u2, o2 = t2; o2 = o2.__; ) if ((u2 = o2.__c) && u2.__c) return null == t2.__e && (t2.__e = e2.__e, t2.__k = e2.__k), u2.__c(n2, t2);
  }
  F$2(n2, t2, e2, r2);
};
var U$1 = l$4.unmount;
function V$1(n2, t2, e2) {
  return n2 && (n2.__c && n2.__c.__H && (n2.__c.__H.__.forEach(function(n3) {
    "function" == typeof n3.__c && n3.__c();
  }), n2.__c.__H = null), null != (n2 = g$2({}, n2)).__c && (n2.__c.__P === e2 && (n2.__c.__P = t2), n2.__c.__e = true, n2.__c = null), n2.__k = n2.__k && n2.__k.map(function(n3) {
    return V$1(n3, t2, e2);
  })), n2;
}
function W(n2, t2, e2) {
  return n2 && e2 && (n2.__v = null, n2.__k = n2.__k && n2.__k.map(function(n3) {
    return W(n3, t2, e2);
  }), n2.__c && n2.__c.__P === t2 && (n2.__e && e2.appendChild(n2.__e), n2.__c.__e = true, n2.__c.__P = e2)), n2;
}
function P$1() {
  this.__u = 0, this.o = null, this.__b = null;
}
function j(n2) {
  var t2 = n2.__.__c;
  return t2 && t2.__a && t2.__a(n2);
}
function z$1(n2) {
  var e2, r2, u2;
  function o2(o3) {
    if (e2 || (e2 = n2()).then(function(n3) {
      r2 = n3.default || n3;
    }, function(n3) {
      u2 = n3;
    }), u2) throw u2;
    if (!r2) throw e2;
    return _$3(r2, o3);
  }
  return o2.displayName = "Lazy", o2.__f = true, o2;
}
function B() {
  this.i = null, this.l = null;
}
l$4.unmount = function(n2) {
  var t2 = n2.__c;
  t2 && t2.__R && t2.__R(), t2 && 32 & n2.__u && (n2.type = null), U$1 && U$1(n2);
}, (P$1.prototype = new x$2()).__c = function(n2, t2) {
  var e2 = t2.__c, r2 = this;
  null == r2.o && (r2.o = []), r2.o.push(e2);
  var u2 = j(r2.__v), o2 = false, i2 = function() {
    o2 || (o2 = true, e2.__R = null, u2 ? u2(l2) : l2());
  };
  e2.__R = i2;
  var l2 = function() {
    if (!--r2.__u) {
      if (r2.state.__a) {
        var n3 = r2.state.__a;
        r2.__v.__k[0] = W(n3, n3.__c.__P, n3.__c.__O);
      }
      var t3;
      for (r2.setState({ __a: r2.__b = null }); t3 = r2.o.pop(); ) t3.forceUpdate();
    }
  };
  r2.__u++ || 32 & t2.__u || r2.setState({ __a: r2.__b = r2.__v.__k[0] }), n2.then(i2, i2);
}, P$1.prototype.componentWillUnmount = function() {
  this.o = [];
}, P$1.prototype.render = function(n2, e2) {
  if (this.__b) {
    if (this.__v.__k) {
      var r2 = document.createElement("div"), o2 = this.__v.__k[0].__c;
      this.__v.__k[0] = V$1(this.__b, r2, o2.__O = o2.__P);
    }
    this.__b = null;
  }
  var i2 = e2.__a && _$3(k$3, null, n2.fallback);
  return i2 && (i2.__u &= -33), [_$3(k$3, null, e2.__a ? null : n2.children), i2];
};
var H = function(n2, t2, e2) {
  if (++e2[1] === e2[0] && n2.l.delete(t2), n2.props.revealOrder && ("t" !== n2.props.revealOrder[0] || !n2.l.size)) for (e2 = n2.i; e2; ) {
    for (; e2.length > 3; ) e2.pop()();
    if (e2[1] < e2[0]) break;
    n2.i = e2 = e2[2];
  }
};
function Z(n2) {
  return this.getChildContext = function() {
    return n2.context;
  }, n2.children;
}
function Y(n2) {
  var e2 = this, r2 = n2.h;
  if (e2.componentWillUnmount = function() {
    E$2(null, e2.v), e2.v = null, e2.h = null;
  }, e2.h && e2.h !== r2 && e2.componentWillUnmount(), !e2.v) {
    for (var u2 = e2.__v; null !== u2 && !u2.__m && null !== u2.__; ) u2 = u2.__;
    e2.h = r2, e2.v = { nodeType: 1, parentNode: r2, childNodes: [], __k: { __m: u2.__m }, contains: function() {
      return true;
    }, insertBefore: function(n3, t2) {
      this.childNodes.push(n3), e2.h.insertBefore(n3, t2);
    }, removeChild: function(n3) {
      this.childNodes.splice(this.childNodes.indexOf(n3) >>> 1, 1), e2.h.removeChild(n3);
    } };
  }
  E$2(_$3(Z, { context: e2.context }, n2.__v), e2.v);
}
function $$1(n2, e2) {
  var r2 = _$3(Y, { __v: n2, h: e2 });
  return r2.containerInfo = e2, r2;
}
(B.prototype = new x$2()).__a = function(n2) {
  var t2 = this, e2 = j(t2.__v), r2 = t2.l.get(n2);
  return r2[0]++, function(u2) {
    var o2 = function() {
      t2.props.revealOrder ? (r2.push(u2), H(t2, n2, r2)) : u2();
    };
    e2 ? e2(o2) : o2();
  };
}, B.prototype.render = function(n2) {
  this.i = null, this.l = /* @__PURE__ */ new Map();
  var t2 = H$1(n2.children);
  n2.revealOrder && "b" === n2.revealOrder[0] && t2.reverse();
  for (var e2 = t2.length; e2--; ) this.l.set(t2[e2], this.i = [1, 0, this.i]);
  return n2.children;
}, B.prototype.componentDidUpdate = B.prototype.componentDidMount = function() {
  var n2 = this;
  this.l.forEach(function(t2, e2) {
    H(n2, e2, t2);
  });
};
var q$1 = "undefined" != typeof Symbol && Symbol.for && Symbol.for("react.element") || 60103, G = /^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/, J = /^on(Ani|Tra|Tou|BeforeInp|Compo)/, K = /[A-Z0-9]/g, Q$1 = "undefined" != typeof document, X$2 = function(n2) {
  return ("undefined" != typeof Symbol && "symbol" == typeof Symbol() ? /fil|che|rad/ : /fil|che|ra/).test(n2);
};
function nn(n2, t2, e2) {
  return null == t2.__k && (t2.textContent = ""), E$2(n2, t2), "function" == typeof e2 && e2(), n2 ? n2.__c : null;
}
function tn(n2, t2, e2) {
  return G$1(n2, t2), "function" == typeof e2 && e2(), n2 ? n2.__c : null;
}
x$2.prototype.isReactComponent = {}, ["componentWillMount", "componentWillReceiveProps", "componentWillUpdate"].forEach(function(t2) {
  Object.defineProperty(x$2.prototype, t2, { configurable: true, get: function() {
    return this["UNSAFE_" + t2];
  }, set: function(n2) {
    Object.defineProperty(this, t2, { configurable: true, writable: true, value: n2 });
  } });
});
var en = l$4.event;
function rn() {
}
function un() {
  return this.cancelBubble;
}
function on() {
  return this.defaultPrevented;
}
l$4.event = function(n2) {
  return en && (n2 = en(n2)), n2.persist = rn, n2.isPropagationStopped = un, n2.isDefaultPrevented = on, n2.nativeEvent = n2;
};
var ln, cn = { enumerable: false, configurable: true, get: function() {
  return this.class;
} }, fn = l$4.vnode;
l$4.vnode = function(n2) {
  "string" == typeof n2.type && function(n3) {
    var t2 = n3.props, e2 = n3.type, u2 = {}, o2 = -1 === e2.indexOf("-");
    for (var i2 in t2) {
      var l2 = t2[i2];
      if (!("value" === i2 && "defaultValue" in t2 && null == l2 || Q$1 && "children" === i2 && "noscript" === e2 || "class" === i2 || "className" === i2)) {
        var c2 = i2.toLowerCase();
        "defaultValue" === i2 && "value" in t2 && null == t2.value ? i2 = "value" : "download" === i2 && true === l2 ? l2 = "" : "translate" === c2 && "no" === l2 ? l2 = false : "o" === c2[0] && "n" === c2[1] ? "ondoubleclick" === c2 ? i2 = "ondblclick" : "onchange" !== c2 || "input" !== e2 && "textarea" !== e2 || X$2(t2.type) ? "onfocus" === c2 ? i2 = "onfocusin" : "onblur" === c2 ? i2 = "onfocusout" : J.test(i2) && (i2 = c2) : c2 = i2 = "oninput" : o2 && G.test(i2) ? i2 = i2.replace(K, "-$&").toLowerCase() : null === l2 && (l2 = void 0), "oninput" === c2 && u2[i2 = c2] && (i2 = "oninputCapture"), u2[i2] = l2;
      }
    }
    "select" == e2 && u2.multiple && Array.isArray(u2.value) && (u2.value = H$1(t2.children).forEach(function(n4) {
      n4.props.selected = -1 != u2.value.indexOf(n4.props.value);
    })), "select" == e2 && null != u2.defaultValue && (u2.value = H$1(t2.children).forEach(function(n4) {
      n4.props.selected = u2.multiple ? -1 != u2.defaultValue.indexOf(n4.props.value) : u2.defaultValue == n4.props.value;
    })), t2.class && !t2.className ? (u2.class = t2.class, Object.defineProperty(u2, "className", cn)) : (t2.className && !t2.class || t2.class && t2.className) && (u2.class = u2.className = t2.className), n3.props = u2;
  }(n2), n2.$$typeof = q$1, fn && fn(n2);
};
var an = l$4.__r;
l$4.__r = function(n2) {
  an && an(n2), ln = n2.__c;
};
var sn = l$4.diffed;
l$4.diffed = function(n2) {
  sn && sn(n2);
  var t2 = n2.props, e2 = n2.__e;
  null != e2 && "textarea" === n2.type && "value" in t2 && t2.value !== e2.value && (e2.value = null == t2.value ? "" : t2.value), ln = null;
};
var hn = { ReactCurrentDispatcher: { current: { readContext: function(n2) {
  return ln.__n[n2.__c].props.value;
}, useCallback: q$2, useContext: x$1, useDebugValue: P$2, useDeferredValue: w$2, useEffect: y$3, useId: g$3, useImperativeHandle: F$3, useInsertionEffect: I$1, useLayoutEffect: _$2, useMemo: T$2, useReducer: h$2, useRef: A$2, useState: d$3, useSyncExternalStore: C, useTransition: k$1 } } }, vn = "18.3.1";
function dn(n2) {
  return _$3.bind(null, n2);
}
function mn(n2) {
  return !!n2 && n2.$$typeof === q$1;
}
function pn(n2) {
  return mn(n2) && n2.type === k$3;
}
function yn(n2) {
  return !!n2 && !!n2.displayName && ("string" == typeof n2.displayName || n2.displayName instanceof String) && n2.displayName.startsWith("Memo(");
}
function _n(n2) {
  return mn(n2) ? J$1.apply(null, arguments) : n2;
}
function bn(n2) {
  return !!n2.__k && (E$2(null, n2), true);
}
function Sn(n2) {
  return n2 && (n2.base || 1 === n2.nodeType && n2) || null;
}
var gn = function(n2, t2) {
  return n2(t2);
}, En = function(n2, t2) {
  return n2(t2);
}, Cn = k$3, xn = mn, Rn = { useState: d$3, useId: g$3, useReducer: h$2, useEffect: y$3, useLayoutEffect: _$2, useInsertionEffect: I$1, useTransition: k$1, useDeferredValue: w$2, useSyncExternalStore: C, startTransition: R, useRef: A$2, useImperativeHandle: F$3, useMemo: T$2, useCallback: q$2, useContext: x$1, useDebugValue: P$2, version: "18.3.1", Children: O, render: nn, hydrate: tn, unmountComponentAtNode: bn, createPortal: $$1, createElement: _$3, createContext: K$1, createFactory: dn, cloneElement: _n, createRef: b$2, Fragment: k$3, isValidElement: mn, isElement: xn, isFragment: pn, isMemo: yn, findDOMNode: Sn, Component: x$2, PureComponent: N$1, memo: M, forwardRef: D, flushSync: En, unstable_batchedUpdates: gn, StrictMode: Cn, Suspense: P$1, SuspenseList: B, lazy: z$1, __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED: hn };
var c$1 = {};
var v$1 = K$1(c$1);
var f$1 = v$1.Provider;
v$1.Consumer;
v$1.displayName = "UrqlContext";
var useClient = () => {
  var e2 = x$1(v$1);
  return e2;
};
var d$2 = {
  fetching: false,
  stale: false,
  hasNext: false,
  error: void 0,
  data: void 0,
  extensions: void 0,
  operation: void 0
};
var areOperationsEqual = (e2, r2) => e2 === r2 || !(!e2 || !r2 || e2.key !== r2.key);
var computeNextState = (e2, r2) => {
  var t2 = {
    ...e2,
    ...r2,
    data: void 0 !== r2.data || r2.error ? r2.data : e2.data,
    fetching: !!r2.fetching,
    stale: !!r2.stale
  };
  return ((e3, r3) => {
    for (var t3 in e3) {
      if (!(t3 in r3)) {
        return true;
      }
    }
    for (var a2 in r3) {
      if ("operation" === a2 ? !areOperationsEqual(e3[a2], r3[a2]) : e3[a2] !== r3[a2]) {
        return true;
      }
    }
    return false;
  })(e2, t2) ? t2 : e2;
};
var hasDepsChanged = (e2, r2) => {
  for (var t2 = 0, a2 = r2.length; t2 < a2; t2++) {
    if (e2[t2] !== r2[t2]) {
      return true;
    }
  }
  return false;
};
function deferDispatch(e2, r2) {
  {
    e2(r2);
  }
}
function useRequest(t2, a2) {
  var n2 = A$2(void 0);
  return T$2(() => {
    var r2 = createRequest(t2, a2);
    if (void 0 !== n2.current && n2.current.key === r2.key) {
      return n2.current;
    } else {
      n2.current = r2;
      return r2;
    }
  }, [t2, a2]);
}
var getCacheForClient = (e2) => {
  if (!e2._react) {
    var r2 = /* @__PURE__ */ new Set();
    var t2 = /* @__PURE__ */ new Map();
    if (e2.operations$) {
      subscribe((e3) => {
        if ("teardown" === e3.kind && r2.has(e3.key)) {
          r2.delete(e3.key);
          t2.delete(e3.key);
        }
      })(e2.operations$);
    }
    e2._react = {
      get: (e3) => t2.get(e3),
      set(e3, a2) {
        r2.delete(e3);
        t2.set(e3, a2);
      },
      dispose(e3) {
        r2.add(e3);
      }
    };
  }
  return e2._react;
};
var isSuspense = (e2, r2) => r2 && void 0 !== r2.suspense ? !!r2.suspense : e2.suspense;
function useQuery(e2) {
  var t2 = useClient();
  var a2 = getCacheForClient(t2);
  var n2 = isSuspense(t2, e2.context);
  var c2 = useRequest(e2.query, e2.variables);
  var v2 = T$2(() => {
    if (e2.pause) {
      return null;
    }
    var r2 = t2.executeQuery(c2, {
      requestPolicy: e2.requestPolicy,
      ...e2.context
    });
    return n2 ? onPush((e3) => {
      a2.set(c2.key, e3);
    })(r2) : r2;
  }, [a2, t2, c2, n2, e2.pause, e2.requestPolicy, e2.context]);
  var f2 = q$2((e3, r2) => {
    if (!e3) {
      return {
        fetching: false
      };
    }
    var t3 = a2.get(c2.key);
    if (!t3) {
      var n3;
      var u2 = subscribe((e4) => {
        t3 = e4;
        if (n3) {
          n3(t3);
        }
      })(takeWhile(() => r2 && !n3 || !t3 || "hasNext" in t3 && t3.hasNext)(e3));
      if (null == t3 && r2) {
        var o2 = new Promise((e4) => {
          n3 = e4;
        });
        a2.set(c2.key, o2);
        throw o2;
      } else {
        u2.unsubscribe();
      }
    } else if (r2 && null != t3 && "then" in t3) {
      throw t3;
    }
    return t3 || {
      fetching: true
    };
  }, [a2, c2]);
  var l2 = [t2, c2, e2.requestPolicy, e2.context, e2.pause];
  var [p2, h2] = d$3(() => [v2, computeNextState(d$2, f2(v2, n2)), l2]);
  var x2 = p2[1];
  if (v2 !== p2[0] && hasDepsChanged(p2[2], l2)) {
    h2([v2, x2 = computeNextState(p2[1], f2(v2, n2)), l2]);
  }
  y$3(() => {
    var e3 = p2[0];
    var r2 = p2[2][1];
    var t3 = false;
    var updateResult = (e4) => {
      t3 = true;
      deferDispatch(h2, (r3) => {
        var t4 = computeNextState(r3[1], e4);
        return r3[1] !== t4 ? [r3[0], t4, r3[2]] : r3;
      });
    };
    if (e3) {
      var n3 = subscribe(updateResult)(onEnd(() => {
        updateResult({
          fetching: false
        });
      })(e3));
      if (!t3) {
        updateResult({
          fetching: true
        });
      }
      return () => {
        a2.dispose(r2.key);
        n3.unsubscribe();
      };
    } else {
      updateResult({
        fetching: false
      });
    }
  }, [a2, p2[0], p2[2][1]]);
  var y2 = q$2((r2) => {
    var i2 = {
      requestPolicy: e2.requestPolicy,
      ...e2.context,
      ...r2
    };
    deferDispatch(h2, (e3) => [n2 ? onPush((e4) => {
      a2.set(c2.key, e4);
    })(t2.executeQuery(c2, i2)) : t2.executeQuery(c2, i2), e3[1], l2]);
  }, [t2, a2, c2, n2, e2.requestPolicy, e2.context, e2.pause]);
  return [x2, y2];
}
const graphqlURL = "https://localhost:5002/api/sdata/graphql";
const customFetch = async (input, init) => {
  return fetch(input, {
    ...init,
    credentials: "include",
    headers: {
      ...init?.headers || {}
    }
  });
};
const client = Q$2({
  url: graphqlURL,
  exchanges: [cacheExchange, fetchExchange],
  fetch: customFetch
});
const styleString = ":root {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n  line-height: 1.5;\r\n  font-weight: 400;\r\n  color-scheme: light;\r\n  color: #131722;\r\n  background-color: #ffffff;\r\n  font-synthesis: none;\r\n  text-rendering: optimizeLegibility;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  --color-border: #e1e3e6;\r\n}\r\n\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\nbody {\r\n  margin: 0;\r\n  min-width: 320px;\r\n  min-height: 100vh;\r\n  background: #f8f9fa;\r\n  color: #131722;\r\n}\r\n\r\nbutton {\r\n  border: 1px solid transparent;\r\n  padding: 0.6em 1.2em;\r\n  font-size: 1em;\r\n  font-weight: 500;\r\n  font-family: inherit;\r\n  background-color: #ffffff;\r\n  color: #131722;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  border: 1px solid --color-border;\r\n}\r\n\r\nbutton:hover {\r\n  background-color: #f8f9fa;\r\n}\r\n\r\nbutton:focus,\r\nbutton:focus-visible {\r\n  outline: 2px solid #2962ff;\r\n  outline-offset: 2px;\r\n}\r\n\r\ninput {\r\n  font-family: inherit;\r\n  font-size: 1em;\r\n}\r\n\r\n\r\n.watchlist-container {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n}\r\n\r\n.add-instrument-container {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n}\r\n\r\n.root-iframe-widget {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n}";
const watchlistStyleString = ".content-area {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .watchlist-container {\r\n    margin: 0;\r\n    border-radius: 0;\r\n  }\r\n}\r\n/* Loading and Error States */\r\n.loading-spinner {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200px;\r\n  color: #6c757d;\r\n  font-size: 1.2rem;\r\n}\r\n\r\n.spinner {\r\n  border: 4px solid #f3f3f3;\r\n  border-top: 4px solid #3498db;\r\n  border-radius: 50%;\r\n  width: 50px;\r\n  height: 50px;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n.error-message {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200px;\r\n  font-size: 1.2rem;\r\n  padding: 1rem;\r\n  text-align: center;\r\n}";
const watchlistTabsStyleString = "/* Tab Bar Styles */\r\n.tab-bar {\r\n  border-bottom: 1px solid #e1e3e6;\r\n  padding: 0;\r\n  overflow-x: auto;\r\n  overflow-y: hidden;\r\n}\r\n\r\n.tabs-container {\r\n  display: flex;\r\n  align-items: flex-end;\r\n  gap: 2px;\r\n  min-width: min-content;\r\n}\r\n\r\n.tab {\r\n  display: flex;\r\n  align-items: center;\r\n  min-width: 120px;\r\n  max-width: 240px;\r\n  height: 30px;\r\n  background: #e9ecef;\r\n  border: 1px solid #d1d4dc;\r\n  border-bottom: none;\r\n  border-radius: 4px 4px 0 0;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  position: relative;\r\n  padding: 0 8px;\r\n  margin-bottom: 1px;\r\n}\r\n\r\n.tab:hover {\r\n  background: #dee2e6;\r\n}\r\n\r\n.tab.active {\r\n  background: #ffffff;\r\n  border-color: #e1e3e6;\r\n  margin-bottom: 0;\r\n  z-index: 1;\r\n}\r\n\r\n.tab-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.tab-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #131722;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  flex: 1;\r\n}\r\n\r\n.tab-star {\r\n  color: #ffc107;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.tab-count {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.tab-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 2px;\r\n  opacity: 0;\r\n  transition: opacity 0.2s;\r\n  margin-left: 16px;\r\n}\r\n\r\n.tab:hover .tab-actions {\r\n  opacity: 1;\r\n}\r\n\r\n.tab.active .tab-actions {\r\n  opacity: 1;\r\n}\r\n\r\n.tab-action-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: none;\r\n  border-radius: 3px;\r\n  background: transparent;\r\n  color: #6c757d;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n}\r\n\r\nbutton.tab-action-btn {\r\n  width: 20px;\r\n  height: 20px;\r\n  padding: 0;\r\n}\r\n\r\n.tab-action-btn:hover {\r\n  background: rgba(0, 0, 0, 0.1);\r\n  color: #131722;\r\n}\r\n\r\n.close-tab-btn:hover {\r\n  background: rgba(244, 67, 54, 0.1);\r\n  color: #f44336;\r\n}\r\n\r\n.tab-action-btn:disabled {\r\n  opacity: 0.3;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.tab-action-btn:disabled:hover {\r\n  background: transparent;\r\n  color: #6c757d;\r\n}\r\n\r\n/* Tab Edit Form */\r\n.tab-edit-form {\r\n  flex: 1;\r\n  padding: 2px;\r\n}\r\n\r\n.tab-edit-input {\r\n  width: 100%;\r\n  padding: 4px 6px;\r\n  border: 1px solid #2962ff;\r\n  border-radius: 3px;\r\n  background: #ffffff;\r\n  color: #131722;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.tab-edit-input:focus {\r\n  outline: none;\r\n  border-color: #2962ff;\r\n}\r\n\r\n/* Delete Confirmation */\r\n.delete-confirm {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 2px;\r\n}\r\n\r\n.confirm-delete-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 3px;\r\n  border:1px solid #f44336;\r\n  color: #f44336;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\nbutton.confirm-delete-btn {\r\n  width: 20px;\r\n  height: 20px;\r\n  padding: 0;\r\n}\r\n\r\n.cancel-delete-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 3px;\r\n  border:1px solid #6c757d;\r\n  color: #6c757d;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\nbutton.cancel-delete-btn {\r\n  width: 20px;\r\n  height: 20px;\r\n  padding: 0;\r\n}\r\n\r\n/* Add Tab */\r\n.add-tab-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #e9ecef;\r\n  border: 1px solid #d1d4dc;\r\n  border-bottom: none;\r\n  border-radius: 4px 4px 0 0;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  color: #6c757d;\r\n  margin-bottom: 1px;\r\n  flex-shrink: 0;\r\n}\r\n\r\nbutton.add-tab-btn {\r\n  width: 30px;\r\n  height: 30px;\r\n  padding: 0;\r\n}\r\n\r\n.add-tab-btn:hover {\r\n  background: #dee2e6;\r\n  color: #131722;\r\n}\r\n\r\n.add-tab-form {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  min-width: 200px;\r\n  height: 30px;\r\n  background: #ffffff;\r\n  border: 1px solid #2962ff;\r\n  border-bottom: none;\r\n  border-radius: 4px 4px 0 0;\r\n  padding: 0 8px;\r\n  margin-bottom: 1px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.add-tab-input {\r\n  flex: 1;\r\n  padding: 4px 6px;\r\n  border: none;\r\n  background: transparent;\r\n  color: #131722;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  min-width: 100px; /* Ensure minimum width */\r\n}\r\n\r\n.add-tab-input:focus {\r\n  outline: none;\r\n}\r\n\r\n.add-tab-input::placeholder {\r\n  color: #6c757d;\r\n}\r\n\r\n.add-tab-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 2px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.confirm-add-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 1px solid #00c853;\r\n  border-radius: 3px;\r\n  color: #00c853;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  flex-shrink: 0;\r\n}\r\n\r\nbutton.confirm-add-btn {\r\n  width: 20px;\r\n  height: 20px;\r\n  padding: 0;\r\n}\r\n\r\n.cancel-add-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 1px solid #6c757d;\r\n  border-radius: 3px;\r\n  color: #6c757d;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  flex-shrink: 0;\r\n}\r\n\r\nbutton.cancel-add-btn {\r\n  width: 20px;\r\n  height: 20px;\r\n  padding: 0;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .tab {\r\n    min-width: 120px;\r\n    max-width: 180px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .tab {\r\n    min-width: 120px;\r\n    max-width: 120px;\r\n    padding: 0 4px;\r\n  }\r\n\r\n  .tab-name {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .tab-count {\r\n    display: none;\r\n  }\r\n}\r\n";
const EmptyWatchlistStateString = "/* Empty Watchlist State */\r\n.empty-watchlist-state {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.empty-watchlist-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  text-align: center;\r\n  max-width: 400px;\r\n  width: 100%;\r\n}\r\n\r\n.empty-watchlist-icon {\r\n  margin-bottom: 12px;\r\n  color: #6c757d;\r\n  opacity: 0.6;\r\n}\r\n\r\n.empty-watchlist-title {\r\n  margin: 0 0 12px 0;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  color: #131722;\r\n}\r\n\r\n.empty-watchlist-description {\r\n  margin: 0 0 24px 0;\r\n  font-size: 16px;\r\n  color: #6c757d;\r\n  line-height: 1.5;\r\n}\r\n\r\n.create-first-watchlist-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px 16px;\r\n  border: 1px solid #6c757d;\r\n  border-radius: 8px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.create-first-watchlist-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(41, 98, 255, 0.3);\r\n}\r\n\r\n.first-watchlist-form {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n  width: 100%;\r\n  max-width: 300px;\r\n  padding: 24px;\r\n  background: white;\r\n  border: 1px solid #e1e3e6;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.first-watchlist-input {\r\n  padding: 8px 12px;\r\n  border: 1px solid #d1d4dc;\r\n  border-radius: 8px;\r\n  background: #ffffff;\r\n  color: #131722;\r\n  font-size: 16px;\r\n  transition: border-color 0.2s;\r\n}\r\n\r\n.first-watchlist-input:focus {\r\n  outline: none;\r\n  border-color: #2962ff;\r\n  box-shadow: 0 0 0 3px rgba(41, 98, 255, 0.1);\r\n}\r\n\r\n.first-watchlist-input::placeholder {\r\n  color: #6c757d;\r\n}\r\n\r\n.first-watchlist-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.confirm-first-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  flex: 1;\r\n  padding: 6px 10px;\r\n  border: 1px solid #00c853;\r\n  color: #00c853;\r\n  border-radius: 8px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  justify-content: center;\r\n}\r\n\r\n.cancel-first-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  flex: 1;\r\n  padding: 6px 10px;\r\n  border: 1px solid #6c757d;\r\n  border-radius: 8px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  justify-content: center;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 480px) {\r\n  /* Empty Watchlist State - Mobile */\r\n  .empty-watchlist-state {\r\n    padding: 20px 16px;\r\n  }\r\n\r\n  .empty-watchlist-title {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .empty-watchlist-description {\r\n    font-size: 14px;\r\n    margin-bottom: 24px;\r\n  }\r\n\r\n  .create-first-watchlist-btn {\r\n    padding: 10px 20px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .first-watchlist-form {\r\n    padding: 20px;\r\n    max-width: 280px;\r\n  }\r\n\r\n  .first-watchlist-actions {\r\n    gap: 8px;\r\n  }\r\n}\r\n";
const InstrumentSearchStyleString = "/* Add Instrument Section */\r\n.add-instrument-section {\r\n  position: relative;\r\n  width: 100%;\r\n  padding: 8px 0;\r\n  border-top: 1px solid #e1e3e6;\r\n}\r\n\r\n.add-section-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #131722;\r\n}\r\n\r\n.search-container {\r\n  position: relative;\r\n}\r\n\r\n.search-icon {\r\n  position: absolute;\r\n  left: 10px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #6c757d;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n  padding: 10px 8px 10px 32px;\r\n  border: 1px solid #d1d4dc;\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  color: #131722;\r\n  font-size: 14px;\r\n}\r\n\r\n.search-input:focus {\r\n  outline: none;\r\n  border-color: #2962ff;\r\n}\r\n\r\n.search-results {\r\n  position: absolute;\r\n  top: 84px;\r\n  left: 0;\r\n  right: 0;\r\n  border: 1px solid #e1e3e6;\r\n  border-radius: 4%;\r\n  background: #ffffff;\r\n  margin-bottom: 16px;\r\n  z-index: 999;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* Empty State */\r\n.search-results-empty {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 24px;\r\n  color: var(--text-secondary);\r\n  text-align: center;\r\n  min-height: 120px;\r\n}\r\n\r\n.search-results-empty-icon {\r\n  font-size: 24px;\r\n  margin-bottom: 8px;\r\n  color: #d1d4dc;\r\n}\r\n\r\n.search-results-empty-text {\r\n  font-size: 14px;\r\n  color: #6c757d;\r\n}\r\n\r\n/* Virtual List Styles */\r\n.search-results .rc-virtual-list {\r\n  border-radius: 6px;\r\n}\r\n\r\n.search-results .rc-virtual-list-holder {\r\n  border-radius: 6px;\r\n}\r\n\r\n.search-results .rc-virtual-list-holder-inner {\r\n  border-radius: 6px;\r\n}\r\n\r\n.search-result-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  cursor: pointer;\r\n  padding: 12px 16px;\r\n  gap: 4px;\r\n  border-bottom: 1px solid #f1f3f4;\r\n  transition: background-color 0.2s;\r\n  height: 60px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.search-result-item:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.search-result-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.instrument-info {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: 16px;\r\n  flex: 1;\r\n}\r\n\r\n.instrument-info .symbol {\r\n  font-weight: 600;\r\n  color: #131722;\r\n  min-width: 60px;\r\n}\r\n\r\n.symbol-info {\r\n  display: flex;\r\n  align-items: left;\r\n  flex-direction: column;\r\n}\r\n\r\n.instrument-info .name {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  flex: 1;\r\n}\r\n\r\n.instrument-info .abbreviation {\r\n  font-weight: 600;\r\n  color: #131722;\r\n  min-width: 80px;\r\n}\r\n\r\n.instrument-info .change {\r\n  font-weight: 500;\r\n  min-width: 100px;\r\n}\r\n\r\n.instrument-info .change.positive {\r\n  color: #00c853;\r\n}\r\n\r\n.instrument-info .change.negative {\r\n  color: #f44336;\r\n}\r\n\r\n.add-instrument-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #a4a6ac;\r\n  border: none;\r\n  border-radius: 4px;\r\n  padding-left: 16px;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\nbutton.add-instrument-btn {\r\n  width: 36px;\r\n  height: 36px;\r\n  padding: 0;\r\n}\r\n\r\nbutton.add-instrument-btn:hover {\r\n  background: #d8dbe2;\r\n  color: white;\r\n}\r\n\r\nbutton.add-instrument-btn:disabled {\r\n  background: #d1d4dc;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .instrument-info {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 4px;\r\n  }\r\n\r\n  .instrument-info .symbol,\r\n  .instrument-info .price,\r\n  .instrument-info .change {\r\n    min-width: auto;\r\n  }\r\n}\r\n";
const InstrumentTableStyleString = "/* Instruments Section */\n.instruments-section {\n  flex: 1;\n  padding: 10px 0 0 0;\n  overflow-y: auto;\n  max-height: calc(100vh - 430px);\n  display: flex;\n  flex-direction: column;\n}\n\n.instruments-table-container {\n  width: 100%;\n  overflow-y: auto;\n  max-height: calc(100vh - 430px);\n  border: 1px solid #e1e3e6;\n  border-radius: 6px;\n}\n\n.instruments-table {\n  width: 100%;\n  border-collapse: collapse;\n  background: #ffffff;\n  table-layout: fixed;\n}\n\n.instruments-table thead {\n  position: sticky;\n  top: 0;\n  z-index: 1;\n}\n\n.table-header {\n  background: #f8f9fa;\n  padding: 8px 12px;\n  font-weight: 600;\n  font-size: 12px;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  color: #6c757d;\n  border-bottom: 1px solid #e1e3e6;\n  text-align: left;\n}\n\n.table-row {\n  display: table-row;\n  position: relative;\n  cursor: pointer;\n  border-bottom: 1px solid #f1f3f4;\n  transition: background-color 0.2s;\n}\n\n.table-row td {\n  display: table-cell;\n  padding: 8px 12px;\n  vertical-align: middle;\n}\n\n.header-cell {\n  display: flex;\n  align-items: center;\n}\n\n.symbol {\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.symbol-text {\n  font-weight: 600;\n}\n\n.market-text {\n  font-size: 11px;\n  color: #6c757d;\n}\n\n.positive {\n  color: #28a745;\n}\n\n.negative {\n  color: #dc3545;\n}\n\n.remove-btn {\n  background: none;\n  border: none;\n  cursor: pointer;\n  color: #dc3545;\n  padding: 0;\n}\n\n.table-row:hover .remove-btn {\n  visibility: visible;\n}\n\n.table-row.selected {\n  background: #e3f2fd;\n}\n\n.table-row:last-child {\n  border-bottom: none;\n}\n\n.cell {\n  display: flex;\n  align-items: center;\n  font-size: 14px;\n  font-weight: 500;\n}\n\n.cell.symbol {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  gap: 2px;\n}\n\n\n.symbol-text {\n  font-weight: 600;\n  color: #131722;\n}\n\n.market-text {\n  font-size: 11px;\n  color: #6c757d;\n  font-weight: 400;\n}\n\n.cell.name {\n  color: #6c757d;\n  font-weight: 400;\n}\n\n.cell.price {\n  color: #131722;\n  font-weight: 600;\n}\n\n.cell.change {\n  font-weight: 500;\n}\n\n.cell.change-percent {\n  text-align: end;\n  font-weight: 500;\n}\n\n.cell.change.positive {\n  color: #26a69a;\n}\n\n.cell.change.negative {\n  color: #f44336;\n}\n\n.cell.change-percent.positive {\n  color: #26a69a;\n}\n\n.cell.change-percent.negative {\n  color: #f44336;\n}\n\n.cell.high {\n  color: #131722;\n  font-weight: 600;\n}\n\n.cell.low {\n  color: #131722;\n  font-weight: 600;\n}\n\n.cell.week-high {\n  color: #131722;\n  font-weight: 600;\n}\n\n.remove-btn {\n  background: none;\n  visibility: hidden;\n  position: absolute;\n  right: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  background: white;\n  border: none;\n  cursor: pointer;\n  padding: 6px;\n  border-radius: 4px;\n  color: #6c757d;\n  transition: all 0.2s;\n}\n\n.remove-btn:hover {\n  background: rgba(243, 147, 140);\n  color: #f44336;\n}\n\n.delete-confirm {\n  display: flex;\n  gap: 4px;\n}\n\n.confirm-delete-btn,\n.cancel-delete-btn {\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 6px;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.2s;\n}\n\n.confirm-delete-btn {\n  color: #00c853;\n}\n\n.confirm-delete-btn:hover {\n  background: rgba(0, 200, 83, 0.1);\n}\n\n.cancel-delete-btn {\n  color: #6c757d;\n}\n\n.cancel-delete-btn:hover {\n  background: rgba(108, 117, 125, 0.1);\n}\n\n/* Skeleton Loading Animations */\n@keyframes shimmer {\n  0% {\n    background-position: -200px 0;\n  }\n  100% {\n    background-position: calc(200px + 100%) 0;\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Skeleton Rows */\n.skeleton-row {\n  border-bottom: 1px solid #f1f3f4;\n  animation: fadeIn 0.3s ease-out;\n}\n\n.skeleton-row:hover {\n  background: transparent;\n}\n\n.skeleton-cell {\n  padding: 12px;\n  vertical-align: middle;\n}\n\n.skeleton-line {\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200px 100%;\n  animation: shimmer 1.5s infinite;\n  border-radius: 4px;\n  height: 16px;\n}\n\n.skeleton-symbol {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n}\n\n.skeleton-symbol-text {\n  width: 80px;\n  height: 18px;\n}\n\n.skeleton-market-text {\n  width: 60px;\n  height: 12px;\n}\n\n.skeleton-price {\n  width: 70px;\n}\n\n.skeleton-change {\n  width: 50px;\n}\n\n.skeleton-change-percent {\n  width: 60px;\n  margin-left: auto;\n}\n\n/* Enhanced Loading and Empty States */\n.loading-state,\n.empty-state,\n.error-state {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  padding: 60px 20px;\n  text-align: center;\n  animation: fadeIn 0.5s ease-out;\n}\n\n.loading-state {\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\n  border-radius: 12px;\n  margin: 20px;\n  border: 1px solid #e9ecef;\n}\n\n.empty-state {\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\n  border-radius: 12px;\n  margin: 20px;\n  border: 1px solid #e9ecef;\n}\n\n.error-state {\n  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);\n  border-radius: 12px;\n  margin: 20px;\n  border: 1px solid #fed7d7;\n}\n\n/* Icons */\n.loading-icon,\n.empty-icon,\n.error-icon {\n  font-size: 48px;\n  margin-bottom: 16px;\n  animation: pulse 2s infinite;\n}\n\n.loading-icon {\n  animation: pulse 1.5s infinite;\n}\n\n.empty-icon {\n  opacity: 0.7;\n}\n\n.error-icon {\n  color: #f44336;\n}\n\n/* Text Styling */\n.loading-title,\n.empty-title,\n.error-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #2d3748;\n  margin: 0 0 8px 0;\n  line-height: 1.4;\n}\n\n.loading-subtitle,\n.empty-subtitle,\n.error-subtitle {\n  font-size: 14px;\n  color: #718096;\n  margin: 0;\n  line-height: 1.5;\n  max-width: 300px;\n}\n\n.error-title {\n  color: #e53e3e;\n}\n\n.error-subtitle {\n  color: #c53030;\n}\n\n/* Legacy support for existing p tags */\n.empty-state p,\n.loading-state p,\n.error-state p {\n  margin: 4px 0;\n  font-size: 14px;\n}\n\n.error-state {\n  color: #f44336;\n}\n\n/* Responsive Design for States */\n@media (max-width: 768px) {\n  .loading-state,\n  .empty-state,\n  .error-state {\n    padding: 40px 16px;\n    margin: 16px;\n  }\n\n  .loading-icon,\n  .empty-icon,\n  .error-icon {\n    font-size: 36px;\n    margin-bottom: 12px;\n  }\n\n  .loading-title,\n  .empty-title,\n  .error-title {\n    font-size: 16px;\n  }\n\n  .loading-subtitle,\n  .empty-subtitle,\n  .error-subtitle {\n    font-size: 13px;\n  }\n}\n\n@media (max-width: 480px) {\n  .skeleton-symbol-text {\n    width: 60px;\n  }\n\n  .skeleton-market-text {\n    width: 45px;\n  }\n\n  .skeleton-price {\n    width: 50px;\n  }\n\n  .skeleton-change {\n    width: 40px;\n  }\n\n  .skeleton-change-percent {\n    width: 45px;\n  }\n\n  .loading-state,\n  .empty-state,\n  .error-state {\n    padding: 30px 12px;\n    margin: 12px;\n  }\n\n  .loading-icon,\n  .empty-icon,\n  .error-icon {\n    font-size: 32px;\n    margin-bottom: 10px;\n  }\n\n  .loading-title,\n  .empty-title,\n  .error-title {\n    font-size: 15px;\n  }\n\n  .loading-subtitle,\n  .empty-subtitle,\n  .error-subtitle {\n    font-size: 12px;\n  }\n}";
const AddInstrumentButtonStyleString = ".add-instrument-container {\r\n  display: flex;\r\n}\r\n\r\n.add-instrument-widget {\r\n  position: relative;\r\n  display: inline-block;\r\n}\r\n\r\n.add-instrument-trigger {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 2px;\r\n  border: 1px solid #e1e3e6;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  font-weight: 500;\r\n  font-size: 14px;\r\n  padding: 6px 10px;\r\n}\r\n\r\n.add-instrument-loading {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 8px;\r\n}\r\n\r\n.add-instrument-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 2px;\r\n  border: 1px solid #e1e3e6;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  font-weight: 500;\r\n  font-size: 14px;\r\n  padding: 6px 10px;\r\n}";
const WatchlistModalStyleString = "\r\n\r\n.watchlist-modal {\r\n  background: white;\r\n  width: 100%;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 1px solid #e1e3e6;\r\n  border-radius: 6px;\r\n  padding: 24px;\r\n  max-height: 100vh;\r\n}\r\n\r\n.modal-header {\r\n  padding: 10px;\r\n  border-bottom: 1px solid #e1e3e6;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.modal-title {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.modal-title h3 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #131722;\r\n}\r\n\r\n.modal-close {\r\n  background: none;\r\n  border: none;\r\n  cursor: pointer;\r\n  padding: 4px;\r\n  color: #6c757d;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.modal-close:hover {\r\n  background: #e9ecef;\r\n  color: #131722;\r\n}\r\n\r\n.instrument-preview {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 12px 16px;\r\n  background: white;\r\n  border: 1px solid #e1e3e6;\r\n}\r\n\r\n.instrument-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2px;\r\n}\r\n\r\n.instrument-symbol {\r\n  font-weight: 600;\r\n  color: #131722;\r\n  font-size: 16px;\r\n}\r\n\r\n.instrument-name {\r\n  color: #6c757d;\r\n  font-size: 14px;\r\n}\r\n\r\n.instrument-price {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n  gap: 2px;\r\n}\r\n\r\n.price {\r\n  font-weight: 600;\r\n  color: #131722;\r\n  font-size: 16px;\r\n}\r\n\r\n.change {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.change.positive {\r\n  color: #00c853;\r\n}\r\n\r\n.change.negative {\r\n  color: #f44336;\r\n}\r\n\r\n.modal-content {\r\n  flex: 1;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n}\r\n\r\n.search-container {\r\n  position: relative;\r\n}\r\n\r\n.search-icon {\r\n  position: absolute;\r\n  left: 12px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #6c757d;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n  padding: 10px 10px 10px 36px;\r\n  border: 1px solid #d1d4dc;\r\n  color: #131722;\r\n  font-size: 14px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.search-input:focus {\r\n  outline: none;\r\n  border-color: #2962ff;\r\n}\r\n\r\n.watchlists-section {\r\n  flex: 1;\r\n  padding-top: 24px;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.section-header h4 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #131722;\r\n}\r\n\r\n.create-new-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #333;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  border: 1px solid #e1e3e6;\r\n  border-radius: 4px;\r\n}\r\n\r\n.create-new-btn:hover:not(:disabled) {\r\n  color: black;\r\n}\r\n\r\n.create-new-btn:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.watchlists-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.create-watchlist-form {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 12px 16px;\r\n  border: 1px solid #e1e3e6;\r\n  margin-bottom: 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.create-input {\r\n  flex: 1;\r\n  padding: 4px 12px;\r\n  border: 1px solid #d1d4dc;\r\n  background: white;\r\n  color: #131722;\r\n  font-size: 14px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.create-input:focus {\r\n  outline: none;\r\n  border-color: #2962ff;\r\n}\r\n\r\n.create-actions {\r\n  display: flex;\r\n  gap: 4px;\r\n}\r\n\r\n.confirm-btn,\r\n.cancel-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: none;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\nbutton.confirm-btn,\r\nbutton.cancel-btn {\r\n  width: 28px;\r\n  height: 28px;\r\n  padding: 0;\r\n}\r\n\r\n.confirm-btn {\r\n  border-radius: 4px;\r\n  background: #00c853;\r\n  color: white;\r\n}\r\n\r\n.confirm-btn:hover:not(:disabled) {\r\n  background: #05f168;\r\n}\r\n\r\n.cancel-btn {\r\n  border-radius: 4px;\r\n  background: #6c757d;\r\n  color: white;\r\n}\r\n\r\n.cancel-btn:hover:not(:disabled) {\r\n  background: #333;\r\n}\r\n\r\n.watchlist-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 10px 12px;\r\n  cursor: pointer;\r\n  background: white;\r\n  border: 1px solid #e1e3e6;\r\n  transition: all 0.2s;\r\n  border-radius: 4px;\r\n}\r\n\r\n.watchlist-item:hover {\r\n  background: #f8f9fa;\r\n  box-shadow: 0 2px 4px rgba(41, 98, 255, 0.1);\r\n}\r\n\r\n.watchlist-info {\r\n  flex: 1;\r\n}\r\n\r\n.watchlist-name {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-weight: 500;\r\n  color: #131722;\r\n  font-size: 16px;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.default-star {\r\n  color: #ffc107;\r\n}\r\n\r\n.watchlist-count {\r\n  color: #6c757d;\r\n  font-size: 14px;\r\n}\r\n\r\n.add-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  cursor: pointer;\r\n  color: #333;\r\n  transition: all 0.2s;\r\n}\r\n\r\nbutton.add-btn {\r\n  width: 36px;\r\n  height: 36px;\r\n  padding: 0;\r\n}\r\n\r\n.add-btn:hover:not(:disabled) {\r\n  background: #e1e3e6;\r\n}\r\n\r\n.add-btn:disabled {\r\n  cursor: not-allowed;\r\n  opacity: 0.6;\r\n}\r\n\r\n.empty-state {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40px 20px;\r\n  text-align: center;\r\n  color: #6c757d;\r\n}\r\n\r\n.empty-state p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n}\r\n\r\n/* Loading Spinner */\r\n.loading-spinner {\r\n  width: 20px;\r\n  height: 20px;\r\n  border: 2px solid #f3f3f3;\r\n  border-top: 2px solid #2962ff;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n.loading-spinner.small {\r\n  width: 16px;\r\n  height: 16px;\r\n}\r\n\r\n.border-radius {\r\n  border-radius: 6px;\r\n}\r\n\r\n@keyframes spin {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* Responsive Design */\r\n\r\n.watchlist-item-skeleton {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 10px 12px;\r\n  height: 56px;\r\n  cursor: pointer;\r\n  background: white;\r\n  border: 1px solid #e1e3e6;\r\n  transition: all 0.2s;\r\n  border-radius: 4px;\r\n}\r\n\r\n.watchlist-info-skeleton {\r\n  flex: 1;\r\n}\r\n\r\n.add-btn-skeleton {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  cursor: pointer;\r\n  color: #333;\r\n  transition: all 0.2s;\r\n}\r\n";
const AddInstrumentModalStyleString = ".root-iframe-widget {\r\n  display: flex;\r\n}\r\n";
class Subscribable {
  constructor() {
    this.listeners = /* @__PURE__ */ new Set();
    this.subscribe = this.subscribe.bind(this);
  }
  subscribe(listener) {
    const identity2 = {
      listener
    };
    this.listeners.add(identity2);
    this.onSubscribe();
    return () => {
      this.listeners.delete(identity2);
      this.onUnsubscribe();
    };
  }
  hasListeners() {
    return this.listeners.size > 0;
  }
  onSubscribe() {
  }
  onUnsubscribe() {
  }
}
const isServer = typeof window === "undefined" || "Deno" in window;
function noop$2() {
  return void 0;
}
function functionalUpdate(updater, input) {
  return typeof updater === "function" ? updater(input) : updater;
}
function isValidTimeout(value2) {
  return typeof value2 === "number" && value2 >= 0 && value2 !== Infinity;
}
function timeUntilStale(updatedAt, staleTime) {
  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);
}
function parseQueryArgs(arg1, arg2, arg3) {
  if (!isQueryKey(arg1)) {
    return arg1;
  }
  if (typeof arg2 === "function") {
    return {
      ...arg3,
      queryKey: arg1,
      queryFn: arg2
    };
  }
  return {
    ...arg2,
    queryKey: arg1
  };
}
function parseFilterArgs(arg1, arg2, arg3) {
  return isQueryKey(arg1) ? [{
    ...arg2,
    queryKey: arg1
  }, arg3] : [arg1 || {}, arg2];
}
function matchQuery(filters, query) {
  const {
    type: type2 = "all",
    exact,
    fetchStatus,
    predicate,
    queryKey,
    stale
  } = filters;
  if (isQueryKey(queryKey)) {
    if (exact) {
      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {
        return false;
      }
    } else if (!partialMatchKey(query.queryKey, queryKey)) {
      return false;
    }
  }
  if (type2 !== "all") {
    const isActive = query.isActive();
    if (type2 === "active" && !isActive) {
      return false;
    }
    if (type2 === "inactive" && isActive) {
      return false;
    }
  }
  if (typeof stale === "boolean" && query.isStale() !== stale) {
    return false;
  }
  if (typeof fetchStatus !== "undefined" && fetchStatus !== query.state.fetchStatus) {
    return false;
  }
  if (predicate && !predicate(query)) {
    return false;
  }
  return true;
}
function matchMutation(filters, mutation) {
  const {
    exact,
    fetching,
    predicate,
    mutationKey
  } = filters;
  if (isQueryKey(mutationKey)) {
    if (!mutation.options.mutationKey) {
      return false;
    }
    if (exact) {
      if (hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)) {
        return false;
      }
    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {
      return false;
    }
  }
  if (typeof fetching === "boolean" && mutation.state.status === "loading" !== fetching) {
    return false;
  }
  if (predicate && !predicate(mutation)) {
    return false;
  }
  return true;
}
function hashQueryKeyByOptions(queryKey, options) {
  const hashFn = (options == null ? void 0 : options.queryKeyHashFn) || hashQueryKey;
  return hashFn(queryKey);
}
function hashQueryKey(queryKey) {
  return JSON.stringify(queryKey, (_2, val) => isPlainObject$1(val) ? Object.keys(val).sort().reduce((result, key) => {
    result[key] = val[key];
    return result;
  }, {}) : val);
}
function partialMatchKey(a2, b2) {
  return partialDeepEqual(a2, b2);
}
function partialDeepEqual(a2, b2) {
  if (a2 === b2) {
    return true;
  }
  if (typeof a2 !== typeof b2) {
    return false;
  }
  if (a2 && b2 && typeof a2 === "object" && typeof b2 === "object") {
    return !Object.keys(b2).some((key) => !partialDeepEqual(a2[key], b2[key]));
  }
  return false;
}
function replaceEqualDeep(a2, b2) {
  if (a2 === b2) {
    return a2;
  }
  const array = isPlainArray(a2) && isPlainArray(b2);
  if (array || isPlainObject$1(a2) && isPlainObject$1(b2)) {
    const aSize = array ? a2.length : Object.keys(a2).length;
    const bItems = array ? b2 : Object.keys(b2);
    const bSize = bItems.length;
    const copy = array ? [] : {};
    let equalItems = 0;
    for (let i2 = 0; i2 < bSize; i2++) {
      const key = array ? i2 : bItems[i2];
      copy[key] = replaceEqualDeep(a2[key], b2[key]);
      if (copy[key] === a2[key]) {
        equalItems++;
      }
    }
    return aSize === bSize && equalItems === aSize ? a2 : copy;
  }
  return b2;
}
function shallowEqualObjects(a2, b2) {
  if (a2 && !b2 || b2 && !a2) {
    return false;
  }
  for (const key in a2) {
    if (a2[key] !== b2[key]) {
      return false;
    }
  }
  return true;
}
function isPlainArray(value2) {
  return Array.isArray(value2) && value2.length === Object.keys(value2).length;
}
function isPlainObject$1(o2) {
  if (!hasObjectPrototype(o2)) {
    return false;
  }
  const ctor = o2.constructor;
  if (typeof ctor === "undefined") {
    return true;
  }
  const prot = ctor.prototype;
  if (!hasObjectPrototype(prot)) {
    return false;
  }
  if (!prot.hasOwnProperty("isPrototypeOf")) {
    return false;
  }
  return true;
}
function hasObjectPrototype(o2) {
  return Object.prototype.toString.call(o2) === "[object Object]";
}
function isQueryKey(value2) {
  return Array.isArray(value2);
}
function sleep(timeout) {
  return new Promise((resolve) => {
    setTimeout(resolve, timeout);
  });
}
function scheduleMicrotask(callback) {
  sleep(0).then(callback);
}
function getAbortController() {
  if (typeof AbortController === "function") {
    return new AbortController();
  }
  return;
}
function replaceData(prevData, data, options) {
  if (options.isDataEqual != null && options.isDataEqual(prevData, data)) {
    return prevData;
  } else if (typeof options.structuralSharing === "function") {
    return options.structuralSharing(prevData, data);
  } else if (options.structuralSharing !== false) {
    return replaceEqualDeep(prevData, data);
  }
  return data;
}
class FocusManager extends Subscribable {
  constructor() {
    super();
    this.setup = (onFocus) => {
      if (!isServer && window.addEventListener) {
        const listener = () => onFocus();
        window.addEventListener("visibilitychange", listener, false);
        window.addEventListener("focus", listener, false);
        return () => {
          window.removeEventListener("visibilitychange", listener);
          window.removeEventListener("focus", listener);
        };
      }
      return;
    };
  }
  onSubscribe() {
    if (!this.cleanup) {
      this.setEventListener(this.setup);
    }
  }
  onUnsubscribe() {
    if (!this.hasListeners()) {
      var _this$cleanup;
      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);
      this.cleanup = void 0;
    }
  }
  setEventListener(setup) {
    var _this$cleanup2;
    this.setup = setup;
    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);
    this.cleanup = setup((focused) => {
      if (typeof focused === "boolean") {
        this.setFocused(focused);
      } else {
        this.onFocus();
      }
    });
  }
  setFocused(focused) {
    const changed = this.focused !== focused;
    if (changed) {
      this.focused = focused;
      this.onFocus();
    }
  }
  onFocus() {
    this.listeners.forEach(({
      listener
    }) => {
      listener();
    });
  }
  isFocused() {
    if (typeof this.focused === "boolean") {
      return this.focused;
    }
    if (typeof document === "undefined") {
      return true;
    }
    return [void 0, "visible", "prerender"].includes(document.visibilityState);
  }
}
const focusManager = new FocusManager();
const onlineEvents = ["online", "offline"];
class OnlineManager extends Subscribable {
  constructor() {
    super();
    this.setup = (onOnline) => {
      if (!isServer && window.addEventListener) {
        const listener = () => onOnline();
        onlineEvents.forEach((event) => {
          window.addEventListener(event, listener, false);
        });
        return () => {
          onlineEvents.forEach((event) => {
            window.removeEventListener(event, listener);
          });
        };
      }
      return;
    };
  }
  onSubscribe() {
    if (!this.cleanup) {
      this.setEventListener(this.setup);
    }
  }
  onUnsubscribe() {
    if (!this.hasListeners()) {
      var _this$cleanup;
      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);
      this.cleanup = void 0;
    }
  }
  setEventListener(setup) {
    var _this$cleanup2;
    this.setup = setup;
    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);
    this.cleanup = setup((online) => {
      if (typeof online === "boolean") {
        this.setOnline(online);
      } else {
        this.onOnline();
      }
    });
  }
  setOnline(online) {
    const changed = this.online !== online;
    if (changed) {
      this.online = online;
      this.onOnline();
    }
  }
  onOnline() {
    this.listeners.forEach(({
      listener
    }) => {
      listener();
    });
  }
  isOnline() {
    if (typeof this.online === "boolean") {
      return this.online;
    }
    if (typeof navigator === "undefined" || typeof navigator.onLine === "undefined") {
      return true;
    }
    return navigator.onLine;
  }
}
const onlineManager = new OnlineManager();
function defaultRetryDelay(failureCount) {
  return Math.min(1e3 * 2 ** failureCount, 3e4);
}
function canFetch(networkMode) {
  return (networkMode != null ? networkMode : "online") === "online" ? onlineManager.isOnline() : true;
}
class CancelledError {
  constructor(options) {
    this.revert = options == null ? void 0 : options.revert;
    this.silent = options == null ? void 0 : options.silent;
  }
}
function isCancelledError(value2) {
  return value2 instanceof CancelledError;
}
function createRetryer(config) {
  let isRetryCancelled = false;
  let failureCount = 0;
  let isResolved = false;
  let continueFn;
  let promiseResolve;
  let promiseReject;
  const promise = new Promise((outerResolve, outerReject) => {
    promiseResolve = outerResolve;
    promiseReject = outerReject;
  });
  const cancel = (cancelOptions) => {
    if (!isResolved) {
      reject(new CancelledError(cancelOptions));
      config.abort == null ? void 0 : config.abort();
    }
  };
  const cancelRetry = () => {
    isRetryCancelled = true;
  };
  const continueRetry = () => {
    isRetryCancelled = false;
  };
  const shouldPause = () => !focusManager.isFocused() || config.networkMode !== "always" && !onlineManager.isOnline();
  const resolve = (value2) => {
    if (!isResolved) {
      isResolved = true;
      config.onSuccess == null ? void 0 : config.onSuccess(value2);
      continueFn == null ? void 0 : continueFn();
      promiseResolve(value2);
    }
  };
  const reject = (value2) => {
    if (!isResolved) {
      isResolved = true;
      config.onError == null ? void 0 : config.onError(value2);
      continueFn == null ? void 0 : continueFn();
      promiseReject(value2);
    }
  };
  const pause = () => {
    return new Promise((continueResolve) => {
      continueFn = (value2) => {
        const canContinue = isResolved || !shouldPause();
        if (canContinue) {
          continueResolve(value2);
        }
        return canContinue;
      };
      config.onPause == null ? void 0 : config.onPause();
    }).then(() => {
      continueFn = void 0;
      if (!isResolved) {
        config.onContinue == null ? void 0 : config.onContinue();
      }
    });
  };
  const run = () => {
    if (isResolved) {
      return;
    }
    let promiseOrValue;
    try {
      promiseOrValue = config.fn();
    } catch (error2) {
      promiseOrValue = Promise.reject(error2);
    }
    Promise.resolve(promiseOrValue).then(resolve).catch((error2) => {
      var _config$retry, _config$retryDelay;
      if (isResolved) {
        return;
      }
      const retry = (_config$retry = config.retry) != null ? _config$retry : 3;
      const retryDelay = (_config$retryDelay = config.retryDelay) != null ? _config$retryDelay : defaultRetryDelay;
      const delay = typeof retryDelay === "function" ? retryDelay(failureCount, error2) : retryDelay;
      const shouldRetry = retry === true || typeof retry === "number" && failureCount < retry || typeof retry === "function" && retry(failureCount, error2);
      if (isRetryCancelled || !shouldRetry) {
        reject(error2);
        return;
      }
      failureCount++;
      config.onFail == null ? void 0 : config.onFail(failureCount, error2);
      sleep(delay).then(() => {
        if (shouldPause()) {
          return pause();
        }
        return;
      }).then(() => {
        if (isRetryCancelled) {
          reject(error2);
        } else {
          run();
        }
      });
    });
  };
  if (canFetch(config.networkMode)) {
    run();
  } else {
    pause().then(run);
  }
  return {
    promise,
    cancel,
    continue: () => {
      const didContinue = continueFn == null ? void 0 : continueFn();
      return didContinue ? promise : Promise.resolve();
    },
    cancelRetry,
    continueRetry
  };
}
const defaultLogger = console;
function createNotifyManager() {
  let queue = [];
  let transactions = 0;
  let notifyFn = (callback) => {
    callback();
  };
  let batchNotifyFn = (callback) => {
    callback();
  };
  const batch2 = (callback) => {
    let result;
    transactions++;
    try {
      result = callback();
    } finally {
      transactions--;
      if (!transactions) {
        flush();
      }
    }
    return result;
  };
  const schedule = (callback) => {
    if (transactions) {
      queue.push(callback);
    } else {
      scheduleMicrotask(() => {
        notifyFn(callback);
      });
    }
  };
  const batchCalls = (callback) => {
    return (...args) => {
      schedule(() => {
        callback(...args);
      });
    };
  };
  const flush = () => {
    const originalQueue = queue;
    queue = [];
    if (originalQueue.length) {
      scheduleMicrotask(() => {
        batchNotifyFn(() => {
          originalQueue.forEach((callback) => {
            notifyFn(callback);
          });
        });
      });
    }
  };
  const setNotifyFunction = (fn2) => {
    notifyFn = fn2;
  };
  const setBatchNotifyFunction = (fn2) => {
    batchNotifyFn = fn2;
  };
  return {
    batch: batch2,
    batchCalls,
    schedule,
    setNotifyFunction,
    setBatchNotifyFunction
  };
}
const notifyManager = createNotifyManager();
class Removable {
  destroy() {
    this.clearGcTimeout();
  }
  scheduleGc() {
    this.clearGcTimeout();
    if (isValidTimeout(this.cacheTime)) {
      this.gcTimeout = setTimeout(() => {
        this.optionalRemove();
      }, this.cacheTime);
    }
  }
  updateCacheTime(newCacheTime) {
    this.cacheTime = Math.max(this.cacheTime || 0, newCacheTime != null ? newCacheTime : isServer ? Infinity : 5 * 60 * 1e3);
  }
  clearGcTimeout() {
    if (this.gcTimeout) {
      clearTimeout(this.gcTimeout);
      this.gcTimeout = void 0;
    }
  }
}
class Query extends Removable {
  constructor(config) {
    super();
    this.abortSignalConsumed = false;
    this.defaultOptions = config.defaultOptions;
    this.setOptions(config.options);
    this.observers = [];
    this.cache = config.cache;
    this.logger = config.logger || defaultLogger;
    this.queryKey = config.queryKey;
    this.queryHash = config.queryHash;
    this.initialState = config.state || getDefaultState$1(this.options);
    this.state = this.initialState;
    this.scheduleGc();
  }
  get meta() {
    return this.options.meta;
  }
  setOptions(options) {
    this.options = {
      ...this.defaultOptions,
      ...options
    };
    this.updateCacheTime(this.options.cacheTime);
  }
  optionalRemove() {
    if (!this.observers.length && this.state.fetchStatus === "idle") {
      this.cache.remove(this);
    }
  }
  setData(newData, options) {
    const data = replaceData(this.state.data, newData, this.options);
    this.dispatch({
      data,
      type: "success",
      dataUpdatedAt: options == null ? void 0 : options.updatedAt,
      manual: options == null ? void 0 : options.manual
    });
    return data;
  }
  setState(state, setStateOptions) {
    this.dispatch({
      type: "setState",
      state,
      setStateOptions
    });
  }
  cancel(options) {
    var _this$retryer;
    const promise = this.promise;
    (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.cancel(options);
    return promise ? promise.then(noop$2).catch(noop$2) : Promise.resolve();
  }
  destroy() {
    super.destroy();
    this.cancel({
      silent: true
    });
  }
  reset() {
    this.destroy();
    this.setState(this.initialState);
  }
  isActive() {
    return this.observers.some((observer) => observer.options.enabled !== false);
  }
  isDisabled() {
    return this.getObserversCount() > 0 && !this.isActive();
  }
  isStale() {
    return this.state.isInvalidated || !this.state.dataUpdatedAt || this.observers.some((observer) => observer.getCurrentResult().isStale);
  }
  isStaleByTime(staleTime = 0) {
    return this.state.isInvalidated || !this.state.dataUpdatedAt || !timeUntilStale(this.state.dataUpdatedAt, staleTime);
  }
  onFocus() {
    var _this$retryer2;
    const observer = this.observers.find((x2) => x2.shouldFetchOnWindowFocus());
    if (observer) {
      observer.refetch({
        cancelRefetch: false
      });
    }
    (_this$retryer2 = this.retryer) == null ? void 0 : _this$retryer2.continue();
  }
  onOnline() {
    var _this$retryer3;
    const observer = this.observers.find((x2) => x2.shouldFetchOnReconnect());
    if (observer) {
      observer.refetch({
        cancelRefetch: false
      });
    }
    (_this$retryer3 = this.retryer) == null ? void 0 : _this$retryer3.continue();
  }
  addObserver(observer) {
    if (!this.observers.includes(observer)) {
      this.observers.push(observer);
      this.clearGcTimeout();
      this.cache.notify({
        type: "observerAdded",
        query: this,
        observer
      });
    }
  }
  removeObserver(observer) {
    if (this.observers.includes(observer)) {
      this.observers = this.observers.filter((x2) => x2 !== observer);
      if (!this.observers.length) {
        if (this.retryer) {
          if (this.abortSignalConsumed) {
            this.retryer.cancel({
              revert: true
            });
          } else {
            this.retryer.cancelRetry();
          }
        }
        this.scheduleGc();
      }
      this.cache.notify({
        type: "observerRemoved",
        query: this,
        observer
      });
    }
  }
  getObserversCount() {
    return this.observers.length;
  }
  invalidate() {
    if (!this.state.isInvalidated) {
      this.dispatch({
        type: "invalidate"
      });
    }
  }
  fetch(options, fetchOptions) {
    var _this$options$behavio, _context$fetchOptions;
    if (this.state.fetchStatus !== "idle") {
      if (this.state.dataUpdatedAt && fetchOptions != null && fetchOptions.cancelRefetch) {
        this.cancel({
          silent: true
        });
      } else if (this.promise) {
        var _this$retryer4;
        (_this$retryer4 = this.retryer) == null ? void 0 : _this$retryer4.continueRetry();
        return this.promise;
      }
    }
    if (options) {
      this.setOptions(options);
    }
    if (!this.options.queryFn) {
      const observer = this.observers.find((x2) => x2.options.queryFn);
      if (observer) {
        this.setOptions(observer.options);
      }
    }
    if (!Array.isArray(this.options.queryKey)) ;
    const abortController = getAbortController();
    const queryFnContext = {
      queryKey: this.queryKey,
      pageParam: void 0,
      meta: this.meta
    };
    const addSignalProperty = (object) => {
      Object.defineProperty(object, "signal", {
        enumerable: true,
        get: () => {
          if (abortController) {
            this.abortSignalConsumed = true;
            return abortController.signal;
          }
          return void 0;
        }
      });
    };
    addSignalProperty(queryFnContext);
    const fetchFn = () => {
      if (!this.options.queryFn) {
        return Promise.reject("Missing queryFn for queryKey '" + this.options.queryHash + "'");
      }
      this.abortSignalConsumed = false;
      return this.options.queryFn(queryFnContext);
    };
    const context = {
      fetchOptions,
      options: this.options,
      queryKey: this.queryKey,
      state: this.state,
      fetchFn
    };
    addSignalProperty(context);
    (_this$options$behavio = this.options.behavior) == null ? void 0 : _this$options$behavio.onFetch(context);
    this.revertState = this.state;
    if (this.state.fetchStatus === "idle" || this.state.fetchMeta !== ((_context$fetchOptions = context.fetchOptions) == null ? void 0 : _context$fetchOptions.meta)) {
      var _context$fetchOptions2;
      this.dispatch({
        type: "fetch",
        meta: (_context$fetchOptions2 = context.fetchOptions) == null ? void 0 : _context$fetchOptions2.meta
      });
    }
    const onError = (error2) => {
      if (!(isCancelledError(error2) && error2.silent)) {
        this.dispatch({
          type: "error",
          error: error2
        });
      }
      if (!isCancelledError(error2)) {
        var _this$cache$config$on, _this$cache$config, _this$cache$config$on2, _this$cache$config2;
        (_this$cache$config$on = (_this$cache$config = this.cache.config).onError) == null ? void 0 : _this$cache$config$on.call(_this$cache$config, error2, this);
        (_this$cache$config$on2 = (_this$cache$config2 = this.cache.config).onSettled) == null ? void 0 : _this$cache$config$on2.call(_this$cache$config2, this.state.data, error2, this);
      }
      if (!this.isFetchingOptimistic) {
        this.scheduleGc();
      }
      this.isFetchingOptimistic = false;
    };
    this.retryer = createRetryer({
      fn: context.fetchFn,
      abort: abortController == null ? void 0 : abortController.abort.bind(abortController),
      onSuccess: (data) => {
        var _this$cache$config$on3, _this$cache$config3, _this$cache$config$on4, _this$cache$config4;
        if (typeof data === "undefined") {
          onError(new Error(this.queryHash + " data is undefined"));
          return;
        }
        this.setData(data);
        (_this$cache$config$on3 = (_this$cache$config3 = this.cache.config).onSuccess) == null ? void 0 : _this$cache$config$on3.call(_this$cache$config3, data, this);
        (_this$cache$config$on4 = (_this$cache$config4 = this.cache.config).onSettled) == null ? void 0 : _this$cache$config$on4.call(_this$cache$config4, data, this.state.error, this);
        if (!this.isFetchingOptimistic) {
          this.scheduleGc();
        }
        this.isFetchingOptimistic = false;
      },
      onError,
      onFail: (failureCount, error2) => {
        this.dispatch({
          type: "failed",
          failureCount,
          error: error2
        });
      },
      onPause: () => {
        this.dispatch({
          type: "pause"
        });
      },
      onContinue: () => {
        this.dispatch({
          type: "continue"
        });
      },
      retry: context.options.retry,
      retryDelay: context.options.retryDelay,
      networkMode: context.options.networkMode
    });
    this.promise = this.retryer.promise;
    return this.promise;
  }
  dispatch(action) {
    const reducer = (state) => {
      var _action$meta, _action$dataUpdatedAt;
      switch (action.type) {
        case "failed":
          return {
            ...state,
            fetchFailureCount: action.failureCount,
            fetchFailureReason: action.error
          };
        case "pause":
          return {
            ...state,
            fetchStatus: "paused"
          };
        case "continue":
          return {
            ...state,
            fetchStatus: "fetching"
          };
        case "fetch":
          return {
            ...state,
            fetchFailureCount: 0,
            fetchFailureReason: null,
            fetchMeta: (_action$meta = action.meta) != null ? _action$meta : null,
            fetchStatus: canFetch(this.options.networkMode) ? "fetching" : "paused",
            ...!state.dataUpdatedAt && {
              error: null,
              status: "loading"
            }
          };
        case "success":
          return {
            ...state,
            data: action.data,
            dataUpdateCount: state.dataUpdateCount + 1,
            dataUpdatedAt: (_action$dataUpdatedAt = action.dataUpdatedAt) != null ? _action$dataUpdatedAt : Date.now(),
            error: null,
            isInvalidated: false,
            status: "success",
            ...!action.manual && {
              fetchStatus: "idle",
              fetchFailureCount: 0,
              fetchFailureReason: null
            }
          };
        case "error":
          const error2 = action.error;
          if (isCancelledError(error2) && error2.revert && this.revertState) {
            return {
              ...this.revertState
            };
          }
          return {
            ...state,
            error: error2,
            errorUpdateCount: state.errorUpdateCount + 1,
            errorUpdatedAt: Date.now(),
            fetchFailureCount: state.fetchFailureCount + 1,
            fetchFailureReason: error2,
            fetchStatus: "idle",
            status: "error"
          };
        case "invalidate":
          return {
            ...state,
            isInvalidated: true
          };
        case "setState":
          return {
            ...state,
            ...action.state
          };
      }
    };
    this.state = reducer(this.state);
    notifyManager.batch(() => {
      this.observers.forEach((observer) => {
        observer.onQueryUpdate(action);
      });
      this.cache.notify({
        query: this,
        type: "updated",
        action
      });
    });
  }
}
function getDefaultState$1(options) {
  const data = typeof options.initialData === "function" ? options.initialData() : options.initialData;
  const hasData = typeof data !== "undefined";
  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === "function" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;
  return {
    data,
    dataUpdateCount: 0,
    dataUpdatedAt: hasData ? initialDataUpdatedAt != null ? initialDataUpdatedAt : Date.now() : 0,
    error: null,
    errorUpdateCount: 0,
    errorUpdatedAt: 0,
    fetchFailureCount: 0,
    fetchFailureReason: null,
    fetchMeta: null,
    isInvalidated: false,
    status: hasData ? "success" : "loading",
    fetchStatus: "idle"
  };
}
class QueryCache extends Subscribable {
  constructor(config) {
    super();
    this.config = config || {};
    this.queries = [];
    this.queriesMap = {};
  }
  build(client2, options, state) {
    var _options$queryHash;
    const queryKey = options.queryKey;
    const queryHash = (_options$queryHash = options.queryHash) != null ? _options$queryHash : hashQueryKeyByOptions(queryKey, options);
    let query = this.get(queryHash);
    if (!query) {
      query = new Query({
        cache: this,
        logger: client2.getLogger(),
        queryKey,
        queryHash,
        options: client2.defaultQueryOptions(options),
        state,
        defaultOptions: client2.getQueryDefaults(queryKey)
      });
      this.add(query);
    }
    return query;
  }
  add(query) {
    if (!this.queriesMap[query.queryHash]) {
      this.queriesMap[query.queryHash] = query;
      this.queries.push(query);
      this.notify({
        type: "added",
        query
      });
    }
  }
  remove(query) {
    const queryInMap = this.queriesMap[query.queryHash];
    if (queryInMap) {
      query.destroy();
      this.queries = this.queries.filter((x2) => x2 !== query);
      if (queryInMap === query) {
        delete this.queriesMap[query.queryHash];
      }
      this.notify({
        type: "removed",
        query
      });
    }
  }
  clear() {
    notifyManager.batch(() => {
      this.queries.forEach((query) => {
        this.remove(query);
      });
    });
  }
  get(queryHash) {
    return this.queriesMap[queryHash];
  }
  getAll() {
    return this.queries;
  }
  find(arg1, arg2) {
    const [filters] = parseFilterArgs(arg1, arg2);
    if (typeof filters.exact === "undefined") {
      filters.exact = true;
    }
    return this.queries.find((query) => matchQuery(filters, query));
  }
  findAll(arg1, arg2) {
    const [filters] = parseFilterArgs(arg1, arg2);
    return Object.keys(filters).length > 0 ? this.queries.filter((query) => matchQuery(filters, query)) : this.queries;
  }
  notify(event) {
    notifyManager.batch(() => {
      this.listeners.forEach(({
        listener
      }) => {
        listener(event);
      });
    });
  }
  onFocus() {
    notifyManager.batch(() => {
      this.queries.forEach((query) => {
        query.onFocus();
      });
    });
  }
  onOnline() {
    notifyManager.batch(() => {
      this.queries.forEach((query) => {
        query.onOnline();
      });
    });
  }
}
class Mutation extends Removable {
  constructor(config) {
    super();
    this.defaultOptions = config.defaultOptions;
    this.mutationId = config.mutationId;
    this.mutationCache = config.mutationCache;
    this.logger = config.logger || defaultLogger;
    this.observers = [];
    this.state = config.state || getDefaultState();
    this.setOptions(config.options);
    this.scheduleGc();
  }
  setOptions(options) {
    this.options = {
      ...this.defaultOptions,
      ...options
    };
    this.updateCacheTime(this.options.cacheTime);
  }
  get meta() {
    return this.options.meta;
  }
  setState(state) {
    this.dispatch({
      type: "setState",
      state
    });
  }
  addObserver(observer) {
    if (!this.observers.includes(observer)) {
      this.observers.push(observer);
      this.clearGcTimeout();
      this.mutationCache.notify({
        type: "observerAdded",
        mutation: this,
        observer
      });
    }
  }
  removeObserver(observer) {
    this.observers = this.observers.filter((x2) => x2 !== observer);
    this.scheduleGc();
    this.mutationCache.notify({
      type: "observerRemoved",
      mutation: this,
      observer
    });
  }
  optionalRemove() {
    if (!this.observers.length) {
      if (this.state.status === "loading") {
        this.scheduleGc();
      } else {
        this.mutationCache.remove(this);
      }
    }
  }
  continue() {
    var _this$retryer$continu, _this$retryer;
    return (_this$retryer$continu = (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.continue()) != null ? _this$retryer$continu : this.execute();
  }
  async execute() {
    const executeMutation = () => {
      var _this$options$retry;
      this.retryer = createRetryer({
        fn: () => {
          if (!this.options.mutationFn) {
            return Promise.reject("No mutationFn found");
          }
          return this.options.mutationFn(this.state.variables);
        },
        onFail: (failureCount, error2) => {
          this.dispatch({
            type: "failed",
            failureCount,
            error: error2
          });
        },
        onPause: () => {
          this.dispatch({
            type: "pause"
          });
        },
        onContinue: () => {
          this.dispatch({
            type: "continue"
          });
        },
        retry: (_this$options$retry = this.options.retry) != null ? _this$options$retry : 0,
        retryDelay: this.options.retryDelay,
        networkMode: this.options.networkMode
      });
      return this.retryer.promise;
    };
    const restored = this.state.status === "loading";
    try {
      var _this$mutationCache$c3, _this$mutationCache$c4, _this$options$onSucce, _this$options2, _this$mutationCache$c5, _this$mutationCache$c6, _this$options$onSettl, _this$options3;
      if (!restored) {
        var _this$mutationCache$c, _this$mutationCache$c2, _this$options$onMutat, _this$options;
        this.dispatch({
          type: "loading",
          variables: this.options.variables
        });
        await ((_this$mutationCache$c = (_this$mutationCache$c2 = this.mutationCache.config).onMutate) == null ? void 0 : _this$mutationCache$c.call(_this$mutationCache$c2, this.state.variables, this));
        const context = await ((_this$options$onMutat = (_this$options = this.options).onMutate) == null ? void 0 : _this$options$onMutat.call(_this$options, this.state.variables));
        if (context !== this.state.context) {
          this.dispatch({
            type: "loading",
            context,
            variables: this.state.variables
          });
        }
      }
      const data = await executeMutation();
      await ((_this$mutationCache$c3 = (_this$mutationCache$c4 = this.mutationCache.config).onSuccess) == null ? void 0 : _this$mutationCache$c3.call(_this$mutationCache$c4, data, this.state.variables, this.state.context, this));
      await ((_this$options$onSucce = (_this$options2 = this.options).onSuccess) == null ? void 0 : _this$options$onSucce.call(_this$options2, data, this.state.variables, this.state.context));
      await ((_this$mutationCache$c5 = (_this$mutationCache$c6 = this.mutationCache.config).onSettled) == null ? void 0 : _this$mutationCache$c5.call(_this$mutationCache$c6, data, null, this.state.variables, this.state.context, this));
      await ((_this$options$onSettl = (_this$options3 = this.options).onSettled) == null ? void 0 : _this$options$onSettl.call(_this$options3, data, null, this.state.variables, this.state.context));
      this.dispatch({
        type: "success",
        data
      });
      return data;
    } catch (error2) {
      try {
        var _this$mutationCache$c7, _this$mutationCache$c8, _this$options$onError, _this$options4, _this$mutationCache$c9, _this$mutationCache$c10, _this$options$onSettl2, _this$options5;
        await ((_this$mutationCache$c7 = (_this$mutationCache$c8 = this.mutationCache.config).onError) == null ? void 0 : _this$mutationCache$c7.call(_this$mutationCache$c8, error2, this.state.variables, this.state.context, this));
        if (false) ;
        await ((_this$options$onError = (_this$options4 = this.options).onError) == null ? void 0 : _this$options$onError.call(_this$options4, error2, this.state.variables, this.state.context));
        await ((_this$mutationCache$c9 = (_this$mutationCache$c10 = this.mutationCache.config).onSettled) == null ? void 0 : _this$mutationCache$c9.call(_this$mutationCache$c10, void 0, error2, this.state.variables, this.state.context, this));
        await ((_this$options$onSettl2 = (_this$options5 = this.options).onSettled) == null ? void 0 : _this$options$onSettl2.call(_this$options5, void 0, error2, this.state.variables, this.state.context));
        throw error2;
      } finally {
        this.dispatch({
          type: "error",
          error: error2
        });
      }
    }
  }
  dispatch(action) {
    const reducer = (state) => {
      switch (action.type) {
        case "failed":
          return {
            ...state,
            failureCount: action.failureCount,
            failureReason: action.error
          };
        case "pause":
          return {
            ...state,
            isPaused: true
          };
        case "continue":
          return {
            ...state,
            isPaused: false
          };
        case "loading":
          return {
            ...state,
            context: action.context,
            data: void 0,
            failureCount: 0,
            failureReason: null,
            error: null,
            isPaused: !canFetch(this.options.networkMode),
            status: "loading",
            variables: action.variables
          };
        case "success":
          return {
            ...state,
            data: action.data,
            failureCount: 0,
            failureReason: null,
            error: null,
            status: "success",
            isPaused: false
          };
        case "error":
          return {
            ...state,
            data: void 0,
            error: action.error,
            failureCount: state.failureCount + 1,
            failureReason: action.error,
            isPaused: false,
            status: "error"
          };
        case "setState":
          return {
            ...state,
            ...action.state
          };
      }
    };
    this.state = reducer(this.state);
    notifyManager.batch(() => {
      this.observers.forEach((observer) => {
        observer.onMutationUpdate(action);
      });
      this.mutationCache.notify({
        mutation: this,
        type: "updated",
        action
      });
    });
  }
}
function getDefaultState() {
  return {
    context: void 0,
    data: void 0,
    error: null,
    failureCount: 0,
    failureReason: null,
    isPaused: false,
    status: "idle",
    variables: void 0
  };
}
class MutationCache extends Subscribable {
  constructor(config) {
    super();
    this.config = config || {};
    this.mutations = [];
    this.mutationId = 0;
  }
  build(client2, options, state) {
    const mutation = new Mutation({
      mutationCache: this,
      logger: client2.getLogger(),
      mutationId: ++this.mutationId,
      options: client2.defaultMutationOptions(options),
      state,
      defaultOptions: options.mutationKey ? client2.getMutationDefaults(options.mutationKey) : void 0
    });
    this.add(mutation);
    return mutation;
  }
  add(mutation) {
    this.mutations.push(mutation);
    this.notify({
      type: "added",
      mutation
    });
  }
  remove(mutation) {
    this.mutations = this.mutations.filter((x2) => x2 !== mutation);
    this.notify({
      type: "removed",
      mutation
    });
  }
  clear() {
    notifyManager.batch(() => {
      this.mutations.forEach((mutation) => {
        this.remove(mutation);
      });
    });
  }
  getAll() {
    return this.mutations;
  }
  find(filters) {
    if (typeof filters.exact === "undefined") {
      filters.exact = true;
    }
    return this.mutations.find((mutation) => matchMutation(filters, mutation));
  }
  findAll(filters) {
    return this.mutations.filter((mutation) => matchMutation(filters, mutation));
  }
  notify(event) {
    notifyManager.batch(() => {
      this.listeners.forEach(({
        listener
      }) => {
        listener(event);
      });
    });
  }
  resumePausedMutations() {
    var _this$resuming;
    this.resuming = ((_this$resuming = this.resuming) != null ? _this$resuming : Promise.resolve()).then(() => {
      const pausedMutations = this.mutations.filter((x2) => x2.state.isPaused);
      return notifyManager.batch(() => pausedMutations.reduce((promise, mutation) => promise.then(() => mutation.continue().catch(noop$2)), Promise.resolve()));
    }).then(() => {
      this.resuming = void 0;
    });
    return this.resuming;
  }
}
function infiniteQueryBehavior() {
  return {
    onFetch: (context) => {
      context.fetchFn = () => {
        var _context$fetchOptions, _context$fetchOptions2, _context$fetchOptions3, _context$fetchOptions4, _context$state$data, _context$state$data2;
        const refetchPage = (_context$fetchOptions = context.fetchOptions) == null ? void 0 : (_context$fetchOptions2 = _context$fetchOptions.meta) == null ? void 0 : _context$fetchOptions2.refetchPage;
        const fetchMore = (_context$fetchOptions3 = context.fetchOptions) == null ? void 0 : (_context$fetchOptions4 = _context$fetchOptions3.meta) == null ? void 0 : _context$fetchOptions4.fetchMore;
        const pageParam = fetchMore == null ? void 0 : fetchMore.pageParam;
        const isFetchingNextPage = (fetchMore == null ? void 0 : fetchMore.direction) === "forward";
        const isFetchingPreviousPage = (fetchMore == null ? void 0 : fetchMore.direction) === "backward";
        const oldPages = ((_context$state$data = context.state.data) == null ? void 0 : _context$state$data.pages) || [];
        const oldPageParams = ((_context$state$data2 = context.state.data) == null ? void 0 : _context$state$data2.pageParams) || [];
        let newPageParams = oldPageParams;
        let cancelled = false;
        const addSignalProperty = (object) => {
          Object.defineProperty(object, "signal", {
            enumerable: true,
            get: () => {
              var _context$signal;
              if ((_context$signal = context.signal) != null && _context$signal.aborted) {
                cancelled = true;
              } else {
                var _context$signal2;
                (_context$signal2 = context.signal) == null ? void 0 : _context$signal2.addEventListener("abort", () => {
                  cancelled = true;
                });
              }
              return context.signal;
            }
          });
        };
        const queryFn = context.options.queryFn || (() => Promise.reject("Missing queryFn for queryKey '" + context.options.queryHash + "'"));
        const buildNewPages = (pages, param, page, previous) => {
          newPageParams = previous ? [param, ...newPageParams] : [...newPageParams, param];
          return previous ? [page, ...pages] : [...pages, page];
        };
        const fetchPage = (pages, manual, param, previous) => {
          if (cancelled) {
            return Promise.reject("Cancelled");
          }
          if (typeof param === "undefined" && !manual && pages.length) {
            return Promise.resolve(pages);
          }
          const queryFnContext = {
            queryKey: context.queryKey,
            pageParam: param,
            meta: context.options.meta
          };
          addSignalProperty(queryFnContext);
          const queryFnResult = queryFn(queryFnContext);
          const promise2 = Promise.resolve(queryFnResult).then((page) => buildNewPages(pages, param, page, previous));
          return promise2;
        };
        let promise;
        if (!oldPages.length) {
          promise = fetchPage([]);
        } else if (isFetchingNextPage) {
          const manual = typeof pageParam !== "undefined";
          const param = manual ? pageParam : getNextPageParam(context.options, oldPages);
          promise = fetchPage(oldPages, manual, param);
        } else if (isFetchingPreviousPage) {
          const manual = typeof pageParam !== "undefined";
          const param = manual ? pageParam : getPreviousPageParam(context.options, oldPages);
          promise = fetchPage(oldPages, manual, param, true);
        } else {
          newPageParams = [];
          const manual = typeof context.options.getNextPageParam === "undefined";
          const shouldFetchFirstPage = refetchPage && oldPages[0] ? refetchPage(oldPages[0], 0, oldPages) : true;
          promise = shouldFetchFirstPage ? fetchPage([], manual, oldPageParams[0]) : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0]));
          for (let i2 = 1; i2 < oldPages.length; i2++) {
            promise = promise.then((pages) => {
              const shouldFetchNextPage = refetchPage && oldPages[i2] ? refetchPage(oldPages[i2], i2, oldPages) : true;
              if (shouldFetchNextPage) {
                const param = manual ? oldPageParams[i2] : getNextPageParam(context.options, pages);
                return fetchPage(pages, manual, param);
              }
              return Promise.resolve(buildNewPages(pages, oldPageParams[i2], oldPages[i2]));
            });
          }
        }
        const finalPromise = promise.then((pages) => ({
          pages,
          pageParams: newPageParams
        }));
        return finalPromise;
      };
    }
  };
}
function getNextPageParam(options, pages) {
  return options.getNextPageParam == null ? void 0 : options.getNextPageParam(pages[pages.length - 1], pages);
}
function getPreviousPageParam(options, pages) {
  return options.getPreviousPageParam == null ? void 0 : options.getPreviousPageParam(pages[0], pages);
}
class QueryClient {
  constructor(config = {}) {
    this.queryCache = config.queryCache || new QueryCache();
    this.mutationCache = config.mutationCache || new MutationCache();
    this.logger = config.logger || defaultLogger;
    this.defaultOptions = config.defaultOptions || {};
    this.queryDefaults = [];
    this.mutationDefaults = [];
    this.mountCount = 0;
  }
  mount() {
    this.mountCount++;
    if (this.mountCount !== 1) return;
    this.unsubscribeFocus = focusManager.subscribe(() => {
      if (focusManager.isFocused()) {
        this.resumePausedMutations();
        this.queryCache.onFocus();
      }
    });
    this.unsubscribeOnline = onlineManager.subscribe(() => {
      if (onlineManager.isOnline()) {
        this.resumePausedMutations();
        this.queryCache.onOnline();
      }
    });
  }
  unmount() {
    var _this$unsubscribeFocu, _this$unsubscribeOnli;
    this.mountCount--;
    if (this.mountCount !== 0) return;
    (_this$unsubscribeFocu = this.unsubscribeFocus) == null ? void 0 : _this$unsubscribeFocu.call(this);
    this.unsubscribeFocus = void 0;
    (_this$unsubscribeOnli = this.unsubscribeOnline) == null ? void 0 : _this$unsubscribeOnli.call(this);
    this.unsubscribeOnline = void 0;
  }
  isFetching(arg1, arg2) {
    const [filters] = parseFilterArgs(arg1, arg2);
    filters.fetchStatus = "fetching";
    return this.queryCache.findAll(filters).length;
  }
  isMutating(filters) {
    return this.mutationCache.findAll({
      ...filters,
      fetching: true
    }).length;
  }
  getQueryData(queryKey, filters) {
    var _this$queryCache$find;
    return (_this$queryCache$find = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find.state.data;
  }
  ensureQueryData(arg1, arg2, arg3) {
    const parsedOptions = parseQueryArgs(arg1, arg2, arg3);
    const cachedData = this.getQueryData(parsedOptions.queryKey);
    return cachedData ? Promise.resolve(cachedData) : this.fetchQuery(parsedOptions);
  }
  getQueriesData(queryKeyOrFilters) {
    return this.getQueryCache().findAll(queryKeyOrFilters).map(({
      queryKey,
      state
    }) => {
      const data = state.data;
      return [queryKey, data];
    });
  }
  setQueryData(queryKey, updater, options) {
    const query = this.queryCache.find(queryKey);
    const prevData = query == null ? void 0 : query.state.data;
    const data = functionalUpdate(updater, prevData);
    if (typeof data === "undefined") {
      return void 0;
    }
    const parsedOptions = parseQueryArgs(queryKey);
    const defaultedOptions = this.defaultQueryOptions(parsedOptions);
    return this.queryCache.build(this, defaultedOptions).setData(data, {
      ...options,
      manual: true
    });
  }
  setQueriesData(queryKeyOrFilters, updater, options) {
    return notifyManager.batch(() => this.getQueryCache().findAll(queryKeyOrFilters).map(({
      queryKey
    }) => [queryKey, this.setQueryData(queryKey, updater, options)]));
  }
  getQueryState(queryKey, filters) {
    var _this$queryCache$find2;
    return (_this$queryCache$find2 = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find2.state;
  }
  removeQueries(arg1, arg2) {
    const [filters] = parseFilterArgs(arg1, arg2);
    const queryCache = this.queryCache;
    notifyManager.batch(() => {
      queryCache.findAll(filters).forEach((query) => {
        queryCache.remove(query);
      });
    });
  }
  resetQueries(arg1, arg2, arg3) {
    const [filters, options] = parseFilterArgs(arg1, arg2, arg3);
    const queryCache = this.queryCache;
    const refetchFilters = {
      type: "active",
      ...filters
    };
    return notifyManager.batch(() => {
      queryCache.findAll(filters).forEach((query) => {
        query.reset();
      });
      return this.refetchQueries(refetchFilters, options);
    });
  }
  cancelQueries(arg1, arg2, arg3) {
    const [filters, cancelOptions = {}] = parseFilterArgs(arg1, arg2, arg3);
    if (typeof cancelOptions.revert === "undefined") {
      cancelOptions.revert = true;
    }
    const promises = notifyManager.batch(() => this.queryCache.findAll(filters).map((query) => query.cancel(cancelOptions)));
    return Promise.all(promises).then(noop$2).catch(noop$2);
  }
  invalidateQueries(arg1, arg2, arg3) {
    const [filters, options] = parseFilterArgs(arg1, arg2, arg3);
    return notifyManager.batch(() => {
      var _ref, _filters$refetchType;
      this.queryCache.findAll(filters).forEach((query) => {
        query.invalidate();
      });
      if (filters.refetchType === "none") {
        return Promise.resolve();
      }
      const refetchFilters = {
        ...filters,
        type: (_ref = (_filters$refetchType = filters.refetchType) != null ? _filters$refetchType : filters.type) != null ? _ref : "active"
      };
      return this.refetchQueries(refetchFilters, options);
    });
  }
  refetchQueries(arg1, arg2, arg3) {
    const [filters, options] = parseFilterArgs(arg1, arg2, arg3);
    const promises = notifyManager.batch(() => this.queryCache.findAll(filters).filter((query) => !query.isDisabled()).map((query) => {
      var _options$cancelRefetc;
      return query.fetch(void 0, {
        ...options,
        cancelRefetch: (_options$cancelRefetc = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc : true,
        meta: {
          refetchPage: filters.refetchPage
        }
      });
    }));
    let promise = Promise.all(promises).then(noop$2);
    if (!(options != null && options.throwOnError)) {
      promise = promise.catch(noop$2);
    }
    return promise;
  }
  fetchQuery(arg1, arg2, arg3) {
    const parsedOptions = parseQueryArgs(arg1, arg2, arg3);
    const defaultedOptions = this.defaultQueryOptions(parsedOptions);
    if (typeof defaultedOptions.retry === "undefined") {
      defaultedOptions.retry = false;
    }
    const query = this.queryCache.build(this, defaultedOptions);
    return query.isStaleByTime(defaultedOptions.staleTime) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);
  }
  prefetchQuery(arg1, arg2, arg3) {
    return this.fetchQuery(arg1, arg2, arg3).then(noop$2).catch(noop$2);
  }
  fetchInfiniteQuery(arg1, arg2, arg3) {
    const parsedOptions = parseQueryArgs(arg1, arg2, arg3);
    parsedOptions.behavior = infiniteQueryBehavior();
    return this.fetchQuery(parsedOptions);
  }
  prefetchInfiniteQuery(arg1, arg2, arg3) {
    return this.fetchInfiniteQuery(arg1, arg2, arg3).then(noop$2).catch(noop$2);
  }
  resumePausedMutations() {
    return this.mutationCache.resumePausedMutations();
  }
  getQueryCache() {
    return this.queryCache;
  }
  getMutationCache() {
    return this.mutationCache;
  }
  getLogger() {
    return this.logger;
  }
  getDefaultOptions() {
    return this.defaultOptions;
  }
  setDefaultOptions(options) {
    this.defaultOptions = options;
  }
  setQueryDefaults(queryKey, options) {
    const result = this.queryDefaults.find((x2) => hashQueryKey(queryKey) === hashQueryKey(x2.queryKey));
    if (result) {
      result.defaultOptions = options;
    } else {
      this.queryDefaults.push({
        queryKey,
        defaultOptions: options
      });
    }
  }
  getQueryDefaults(queryKey) {
    if (!queryKey) {
      return void 0;
    }
    const firstMatchingDefaults = this.queryDefaults.find((x2) => partialMatchKey(queryKey, x2.queryKey));
    return firstMatchingDefaults == null ? void 0 : firstMatchingDefaults.defaultOptions;
  }
  setMutationDefaults(mutationKey, options) {
    const result = this.mutationDefaults.find((x2) => hashQueryKey(mutationKey) === hashQueryKey(x2.mutationKey));
    if (result) {
      result.defaultOptions = options;
    } else {
      this.mutationDefaults.push({
        mutationKey,
        defaultOptions: options
      });
    }
  }
  getMutationDefaults(mutationKey) {
    if (!mutationKey) {
      return void 0;
    }
    const firstMatchingDefaults = this.mutationDefaults.find((x2) => partialMatchKey(mutationKey, x2.mutationKey));
    return firstMatchingDefaults == null ? void 0 : firstMatchingDefaults.defaultOptions;
  }
  defaultQueryOptions(options) {
    if (options != null && options._defaulted) {
      return options;
    }
    const defaultedOptions = {
      ...this.defaultOptions.queries,
      ...this.getQueryDefaults(options == null ? void 0 : options.queryKey),
      ...options,
      _defaulted: true
    };
    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {
      defaultedOptions.queryHash = hashQueryKeyByOptions(defaultedOptions.queryKey, defaultedOptions);
    }
    if (typeof defaultedOptions.refetchOnReconnect === "undefined") {
      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== "always";
    }
    if (typeof defaultedOptions.useErrorBoundary === "undefined") {
      defaultedOptions.useErrorBoundary = !!defaultedOptions.suspense;
    }
    return defaultedOptions;
  }
  defaultMutationOptions(options) {
    if (options != null && options._defaulted) {
      return options;
    }
    return {
      ...this.defaultOptions.mutations,
      ...this.getMutationDefaults(options == null ? void 0 : options.mutationKey),
      ...options,
      _defaulted: true
    };
  }
  clear() {
    this.queryCache.clear();
    this.mutationCache.clear();
  }
}
class QueryObserver extends Subscribable {
  constructor(client2, options) {
    super();
    this.client = client2;
    this.options = options;
    this.trackedProps = /* @__PURE__ */ new Set();
    this.selectError = null;
    this.bindMethods();
    this.setOptions(options);
  }
  bindMethods() {
    this.remove = this.remove.bind(this);
    this.refetch = this.refetch.bind(this);
  }
  onSubscribe() {
    if (this.listeners.size === 1) {
      this.currentQuery.addObserver(this);
      if (shouldFetchOnMount(this.currentQuery, this.options)) {
        this.executeFetch();
      }
      this.updateTimers();
    }
  }
  onUnsubscribe() {
    if (!this.hasListeners()) {
      this.destroy();
    }
  }
  shouldFetchOnReconnect() {
    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnReconnect);
  }
  shouldFetchOnWindowFocus() {
    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnWindowFocus);
  }
  destroy() {
    this.listeners = /* @__PURE__ */ new Set();
    this.clearStaleTimeout();
    this.clearRefetchInterval();
    this.currentQuery.removeObserver(this);
  }
  setOptions(options, notifyOptions) {
    const prevOptions = this.options;
    const prevQuery = this.currentQuery;
    this.options = this.client.defaultQueryOptions(options);
    if (!shallowEqualObjects(prevOptions, this.options)) {
      this.client.getQueryCache().notify({
        type: "observerOptionsUpdated",
        query: this.currentQuery,
        observer: this
      });
    }
    if (typeof this.options.enabled !== "undefined" && typeof this.options.enabled !== "boolean") {
      throw new Error("Expected enabled to be a boolean");
    }
    if (!this.options.queryKey) {
      this.options.queryKey = prevOptions.queryKey;
    }
    this.updateQuery();
    const mounted = this.hasListeners();
    if (mounted && shouldFetchOptionally(this.currentQuery, prevQuery, this.options, prevOptions)) {
      this.executeFetch();
    }
    this.updateResult(notifyOptions);
    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || this.options.staleTime !== prevOptions.staleTime)) {
      this.updateStaleTimeout();
    }
    const nextRefetchInterval = this.computeRefetchInterval();
    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || nextRefetchInterval !== this.currentRefetchInterval)) {
      this.updateRefetchInterval(nextRefetchInterval);
    }
  }
  getOptimisticResult(options) {
    const query = this.client.getQueryCache().build(this.client, options);
    const result = this.createResult(query, options);
    if (shouldAssignObserverCurrentProperties(this, result, options)) {
      this.currentResult = result;
      this.currentResultOptions = this.options;
      this.currentResultState = this.currentQuery.state;
    }
    return result;
  }
  getCurrentResult() {
    return this.currentResult;
  }
  trackResult(result) {
    const trackedResult = {};
    Object.keys(result).forEach((key) => {
      Object.defineProperty(trackedResult, key, {
        configurable: false,
        enumerable: true,
        get: () => {
          this.trackedProps.add(key);
          return result[key];
        }
      });
    });
    return trackedResult;
  }
  getCurrentQuery() {
    return this.currentQuery;
  }
  remove() {
    this.client.getQueryCache().remove(this.currentQuery);
  }
  refetch({
    refetchPage,
    ...options
  } = {}) {
    return this.fetch({
      ...options,
      meta: {
        refetchPage
      }
    });
  }
  fetchOptimistic(options) {
    const defaultedOptions = this.client.defaultQueryOptions(options);
    const query = this.client.getQueryCache().build(this.client, defaultedOptions);
    query.isFetchingOptimistic = true;
    return query.fetch().then(() => this.createResult(query, defaultedOptions));
  }
  fetch(fetchOptions) {
    var _fetchOptions$cancelR;
    return this.executeFetch({
      ...fetchOptions,
      cancelRefetch: (_fetchOptions$cancelR = fetchOptions.cancelRefetch) != null ? _fetchOptions$cancelR : true
    }).then(() => {
      this.updateResult();
      return this.currentResult;
    });
  }
  executeFetch(fetchOptions) {
    this.updateQuery();
    let promise = this.currentQuery.fetch(this.options, fetchOptions);
    if (!(fetchOptions != null && fetchOptions.throwOnError)) {
      promise = promise.catch(noop$2);
    }
    return promise;
  }
  updateStaleTimeout() {
    this.clearStaleTimeout();
    if (isServer || this.currentResult.isStale || !isValidTimeout(this.options.staleTime)) {
      return;
    }
    const time = timeUntilStale(this.currentResult.dataUpdatedAt, this.options.staleTime);
    const timeout = time + 1;
    this.staleTimeoutId = setTimeout(() => {
      if (!this.currentResult.isStale) {
        this.updateResult();
      }
    }, timeout);
  }
  computeRefetchInterval() {
    var _this$options$refetch;
    return typeof this.options.refetchInterval === "function" ? this.options.refetchInterval(this.currentResult.data, this.currentQuery) : (_this$options$refetch = this.options.refetchInterval) != null ? _this$options$refetch : false;
  }
  updateRefetchInterval(nextInterval) {
    this.clearRefetchInterval();
    this.currentRefetchInterval = nextInterval;
    if (isServer || this.options.enabled === false || !isValidTimeout(this.currentRefetchInterval) || this.currentRefetchInterval === 0) {
      return;
    }
    this.refetchIntervalId = setInterval(() => {
      if (this.options.refetchIntervalInBackground || focusManager.isFocused()) {
        this.executeFetch();
      }
    }, this.currentRefetchInterval);
  }
  updateTimers() {
    this.updateStaleTimeout();
    this.updateRefetchInterval(this.computeRefetchInterval());
  }
  clearStaleTimeout() {
    if (this.staleTimeoutId) {
      clearTimeout(this.staleTimeoutId);
      this.staleTimeoutId = void 0;
    }
  }
  clearRefetchInterval() {
    if (this.refetchIntervalId) {
      clearInterval(this.refetchIntervalId);
      this.refetchIntervalId = void 0;
    }
  }
  createResult(query, options) {
    const prevQuery = this.currentQuery;
    const prevOptions = this.options;
    const prevResult = this.currentResult;
    const prevResultState = this.currentResultState;
    const prevResultOptions = this.currentResultOptions;
    const queryChange = query !== prevQuery;
    const queryInitialState = queryChange ? query.state : this.currentQueryInitialState;
    const prevQueryResult = queryChange ? this.currentResult : this.previousQueryResult;
    const {
      state
    } = query;
    let {
      dataUpdatedAt,
      error: error2,
      errorUpdatedAt,
      fetchStatus,
      status
    } = state;
    let isPreviousData = false;
    let isPlaceholderData = false;
    let data;
    if (options._optimisticResults) {
      const mounted = this.hasListeners();
      const fetchOnMount = !mounted && shouldFetchOnMount(query, options);
      const fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);
      if (fetchOnMount || fetchOptionally) {
        fetchStatus = canFetch(query.options.networkMode) ? "fetching" : "paused";
        if (!dataUpdatedAt) {
          status = "loading";
        }
      }
      if (options._optimisticResults === "isRestoring") {
        fetchStatus = "idle";
      }
    }
    if (options.keepPreviousData && !state.dataUpdatedAt && prevQueryResult != null && prevQueryResult.isSuccess && status !== "error") {
      data = prevQueryResult.data;
      dataUpdatedAt = prevQueryResult.dataUpdatedAt;
      status = prevQueryResult.status;
      isPreviousData = true;
    } else if (options.select && typeof state.data !== "undefined") {
      if (prevResult && state.data === (prevResultState == null ? void 0 : prevResultState.data) && options.select === this.selectFn) {
        data = this.selectResult;
      } else {
        try {
          this.selectFn = options.select;
          data = options.select(state.data);
          data = replaceData(prevResult == null ? void 0 : prevResult.data, data, options);
          this.selectResult = data;
          this.selectError = null;
        } catch (selectError) {
          this.selectError = selectError;
        }
      }
    } else {
      data = state.data;
    }
    if (typeof options.placeholderData !== "undefined" && typeof data === "undefined" && status === "loading") {
      let placeholderData;
      if (prevResult != null && prevResult.isPlaceholderData && options.placeholderData === (prevResultOptions == null ? void 0 : prevResultOptions.placeholderData)) {
        placeholderData = prevResult.data;
      } else {
        placeholderData = typeof options.placeholderData === "function" ? options.placeholderData() : options.placeholderData;
        if (options.select && typeof placeholderData !== "undefined") {
          try {
            placeholderData = options.select(placeholderData);
            this.selectError = null;
          } catch (selectError) {
            this.selectError = selectError;
          }
        }
      }
      if (typeof placeholderData !== "undefined") {
        status = "success";
        data = replaceData(prevResult == null ? void 0 : prevResult.data, placeholderData, options);
        isPlaceholderData = true;
      }
    }
    if (this.selectError) {
      error2 = this.selectError;
      data = this.selectResult;
      errorUpdatedAt = Date.now();
      status = "error";
    }
    const isFetching = fetchStatus === "fetching";
    const isLoading = status === "loading";
    const isError = status === "error";
    const result = {
      status,
      fetchStatus,
      isLoading,
      isSuccess: status === "success",
      isError,
      isInitialLoading: isLoading && isFetching,
      data,
      dataUpdatedAt,
      error: error2,
      errorUpdatedAt,
      failureCount: state.fetchFailureCount,
      failureReason: state.fetchFailureReason,
      errorUpdateCount: state.errorUpdateCount,
      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,
      isFetchedAfterMount: state.dataUpdateCount > queryInitialState.dataUpdateCount || state.errorUpdateCount > queryInitialState.errorUpdateCount,
      isFetching,
      isRefetching: isFetching && !isLoading,
      isLoadingError: isError && state.dataUpdatedAt === 0,
      isPaused: fetchStatus === "paused",
      isPlaceholderData,
      isPreviousData,
      isRefetchError: isError && state.dataUpdatedAt !== 0,
      isStale: isStale(query, options),
      refetch: this.refetch,
      remove: this.remove
    };
    return result;
  }
  updateResult(notifyOptions) {
    const prevResult = this.currentResult;
    const nextResult = this.createResult(this.currentQuery, this.options);
    this.currentResultState = this.currentQuery.state;
    this.currentResultOptions = this.options;
    if (shallowEqualObjects(nextResult, prevResult)) {
      return;
    }
    this.currentResult = nextResult;
    const defaultNotifyOptions = {
      cache: true
    };
    const shouldNotifyListeners = () => {
      if (!prevResult) {
        return true;
      }
      const {
        notifyOnChangeProps
      } = this.options;
      if (notifyOnChangeProps === "all" || !notifyOnChangeProps && !this.trackedProps.size) {
        return true;
      }
      const includedProps = new Set(notifyOnChangeProps != null ? notifyOnChangeProps : this.trackedProps);
      if (this.options.useErrorBoundary) {
        includedProps.add("error");
      }
      return Object.keys(this.currentResult).some((key) => {
        const typedKey = key;
        const changed = this.currentResult[typedKey] !== prevResult[typedKey];
        return changed && includedProps.has(typedKey);
      });
    };
    if ((notifyOptions == null ? void 0 : notifyOptions.listeners) !== false && shouldNotifyListeners()) {
      defaultNotifyOptions.listeners = true;
    }
    this.notify({
      ...defaultNotifyOptions,
      ...notifyOptions
    });
  }
  updateQuery() {
    const query = this.client.getQueryCache().build(this.client, this.options);
    if (query === this.currentQuery) {
      return;
    }
    const prevQuery = this.currentQuery;
    this.currentQuery = query;
    this.currentQueryInitialState = query.state;
    this.previousQueryResult = this.currentResult;
    if (this.hasListeners()) {
      prevQuery == null ? void 0 : prevQuery.removeObserver(this);
      query.addObserver(this);
    }
  }
  onQueryUpdate(action) {
    const notifyOptions = {};
    if (action.type === "success") {
      notifyOptions.onSuccess = !action.manual;
    } else if (action.type === "error" && !isCancelledError(action.error)) {
      notifyOptions.onError = true;
    }
    this.updateResult(notifyOptions);
    if (this.hasListeners()) {
      this.updateTimers();
    }
  }
  notify(notifyOptions) {
    notifyManager.batch(() => {
      if (notifyOptions.onSuccess) {
        var _this$options$onSucce, _this$options, _this$options$onSettl, _this$options2;
        (_this$options$onSucce = (_this$options = this.options).onSuccess) == null ? void 0 : _this$options$onSucce.call(_this$options, this.currentResult.data);
        (_this$options$onSettl = (_this$options2 = this.options).onSettled) == null ? void 0 : _this$options$onSettl.call(_this$options2, this.currentResult.data, null);
      } else if (notifyOptions.onError) {
        var _this$options$onError, _this$options3, _this$options$onSettl2, _this$options4;
        (_this$options$onError = (_this$options3 = this.options).onError) == null ? void 0 : _this$options$onError.call(_this$options3, this.currentResult.error);
        (_this$options$onSettl2 = (_this$options4 = this.options).onSettled) == null ? void 0 : _this$options$onSettl2.call(_this$options4, void 0, this.currentResult.error);
      }
      if (notifyOptions.listeners) {
        this.listeners.forEach(({
          listener
        }) => {
          listener(this.currentResult);
        });
      }
      if (notifyOptions.cache) {
        this.client.getQueryCache().notify({
          query: this.currentQuery,
          type: "observerResultsUpdated"
        });
      }
    });
  }
}
function shouldLoadOnMount(query, options) {
  return options.enabled !== false && !query.state.dataUpdatedAt && !(query.state.status === "error" && options.retryOnMount === false);
}
function shouldFetchOnMount(query, options) {
  return shouldLoadOnMount(query, options) || query.state.dataUpdatedAt > 0 && shouldFetchOn(query, options, options.refetchOnMount);
}
function shouldFetchOn(query, options, field) {
  if (options.enabled !== false) {
    const value2 = typeof field === "function" ? field(query) : field;
    return value2 === "always" || value2 !== false && isStale(query, options);
  }
  return false;
}
function shouldFetchOptionally(query, prevQuery, options, prevOptions) {
  return options.enabled !== false && (query !== prevQuery || prevOptions.enabled === false) && (!options.suspense || query.state.status !== "error") && isStale(query, options);
}
function isStale(query, options) {
  return query.isStaleByTime(options.staleTime);
}
function shouldAssignObserverCurrentProperties(observer, optimisticResult, options) {
  if (options.keepPreviousData) {
    return false;
  }
  if (options.placeholderData !== void 0) {
    return optimisticResult.isPlaceholderData;
  }
  if (observer.getCurrentResult() !== optimisticResult) {
    return true;
  }
  return false;
}
let MutationObserver$1 = class MutationObserver2 extends Subscribable {
  constructor(client2, options) {
    super();
    this.client = client2;
    this.setOptions(options);
    this.bindMethods();
    this.updateResult();
  }
  bindMethods() {
    this.mutate = this.mutate.bind(this);
    this.reset = this.reset.bind(this);
  }
  setOptions(options) {
    var _this$currentMutation;
    const prevOptions = this.options;
    this.options = this.client.defaultMutationOptions(options);
    if (!shallowEqualObjects(prevOptions, this.options)) {
      this.client.getMutationCache().notify({
        type: "observerOptionsUpdated",
        mutation: this.currentMutation,
        observer: this
      });
    }
    (_this$currentMutation = this.currentMutation) == null ? void 0 : _this$currentMutation.setOptions(this.options);
  }
  onUnsubscribe() {
    if (!this.hasListeners()) {
      var _this$currentMutation2;
      (_this$currentMutation2 = this.currentMutation) == null ? void 0 : _this$currentMutation2.removeObserver(this);
    }
  }
  onMutationUpdate(action) {
    this.updateResult();
    const notifyOptions = {
      listeners: true
    };
    if (action.type === "success") {
      notifyOptions.onSuccess = true;
    } else if (action.type === "error") {
      notifyOptions.onError = true;
    }
    this.notify(notifyOptions);
  }
  getCurrentResult() {
    return this.currentResult;
  }
  reset() {
    this.currentMutation = void 0;
    this.updateResult();
    this.notify({
      listeners: true
    });
  }
  mutate(variables, options) {
    this.mutateOptions = options;
    if (this.currentMutation) {
      this.currentMutation.removeObserver(this);
    }
    this.currentMutation = this.client.getMutationCache().build(this.client, {
      ...this.options,
      variables: typeof variables !== "undefined" ? variables : this.options.variables
    });
    this.currentMutation.addObserver(this);
    return this.currentMutation.execute();
  }
  updateResult() {
    const state = this.currentMutation ? this.currentMutation.state : getDefaultState();
    const result = {
      ...state,
      isLoading: state.status === "loading",
      isSuccess: state.status === "success",
      isError: state.status === "error",
      isIdle: state.status === "idle",
      mutate: this.mutate,
      reset: this.reset
    };
    this.currentResult = result;
  }
  notify(options) {
    notifyManager.batch(() => {
      if (this.mutateOptions && this.hasListeners()) {
        if (options.onSuccess) {
          var _this$mutateOptions$o, _this$mutateOptions, _this$mutateOptions$o2, _this$mutateOptions2;
          (_this$mutateOptions$o = (_this$mutateOptions = this.mutateOptions).onSuccess) == null ? void 0 : _this$mutateOptions$o.call(_this$mutateOptions, this.currentResult.data, this.currentResult.variables, this.currentResult.context);
          (_this$mutateOptions$o2 = (_this$mutateOptions2 = this.mutateOptions).onSettled) == null ? void 0 : _this$mutateOptions$o2.call(_this$mutateOptions2, this.currentResult.data, null, this.currentResult.variables, this.currentResult.context);
        } else if (options.onError) {
          var _this$mutateOptions$o3, _this$mutateOptions3, _this$mutateOptions$o4, _this$mutateOptions4;
          (_this$mutateOptions$o3 = (_this$mutateOptions3 = this.mutateOptions).onError) == null ? void 0 : _this$mutateOptions$o3.call(_this$mutateOptions3, this.currentResult.error, this.currentResult.variables, this.currentResult.context);
          (_this$mutateOptions$o4 = (_this$mutateOptions4 = this.mutateOptions).onSettled) == null ? void 0 : _this$mutateOptions$o4.call(_this$mutateOptions4, void 0, this.currentResult.error, this.currentResult.variables, this.currentResult.context);
        }
      }
      if (options.listeners) {
        this.listeners.forEach(({
          listener
        }) => {
          listener(this.currentResult);
        });
      }
    });
  }
};
function getDefaultExportFromCjs(x2) {
  return x2 && x2.__esModule && Object.prototype.hasOwnProperty.call(x2, "default") ? x2["default"] : x2;
}
var i = Symbol.for("preact-signals");
function t() {
  if (!(s > 1)) {
    var i2, t2 = false;
    while (void 0 !== h$1) {
      var r2 = h$1;
      h$1 = void 0;
      f++;
      while (void 0 !== r2) {
        var o2 = r2.o;
        r2.o = void 0;
        r2.f &= -3;
        if (!(8 & r2.f) && c(r2)) try {
          r2.c();
        } catch (r3) {
          if (!t2) {
            i2 = r3;
            t2 = true;
          }
        }
        r2 = o2;
      }
    }
    f = 0;
    s--;
    if (t2) throw i2;
  } else s--;
}
function r(i2) {
  if (s > 0) return i2();
  s++;
  try {
    return i2();
  } finally {
    t();
  }
}
var o = void 0;
function n(i2) {
  var t2 = o;
  o = void 0;
  try {
    return i2();
  } finally {
    o = t2;
  }
}
var h$1 = void 0, s = 0, f = 0, v = 0;
function e(i2) {
  if (void 0 !== o) {
    var t2 = i2.n;
    if (void 0 === t2 || t2.t !== o) {
      t2 = { i: 0, S: i2, p: o.s, n: void 0, t: o, e: void 0, x: void 0, r: t2 };
      if (void 0 !== o.s) o.s.n = t2;
      o.s = t2;
      i2.n = t2;
      if (32 & o.f) i2.S(t2);
      return t2;
    } else if (-1 === t2.i) {
      t2.i = 0;
      if (void 0 !== t2.n) {
        t2.n.p = t2.p;
        if (void 0 !== t2.p) t2.p.n = t2.n;
        t2.p = o.s;
        t2.n = void 0;
        o.s.n = t2;
        o.s = t2;
      }
      return t2;
    }
  }
}
function u(i2, t2) {
  this.v = i2;
  this.i = 0;
  this.n = void 0;
  this.t = void 0;
  this.W = null == t2 ? void 0 : t2.watched;
  this.Z = null == t2 ? void 0 : t2.unwatched;
}
u.prototype.brand = i;
u.prototype.h = function() {
  return true;
};
u.prototype.S = function(i2) {
  var t2 = this, r2 = this.t;
  if (r2 !== i2 && void 0 === i2.e) {
    i2.x = r2;
    this.t = i2;
    if (void 0 !== r2) r2.e = i2;
    else n(function() {
      var i3;
      null == (i3 = t2.W) || i3.call(t2);
    });
  }
};
u.prototype.U = function(i2) {
  var t2 = this;
  if (void 0 !== this.t) {
    var r2 = i2.e, o2 = i2.x;
    if (void 0 !== r2) {
      r2.x = o2;
      i2.e = void 0;
    }
    if (void 0 !== o2) {
      o2.e = r2;
      i2.x = void 0;
    }
    if (i2 === this.t) {
      this.t = o2;
      if (void 0 === o2) n(function() {
        var i3;
        null == (i3 = t2.Z) || i3.call(t2);
      });
    }
  }
};
u.prototype.subscribe = function(i2) {
  var t2 = this;
  return E(function() {
    var r2 = t2.value, n2 = o;
    o = void 0;
    try {
      i2(r2);
    } finally {
      o = n2;
    }
  });
};
u.prototype.valueOf = function() {
  return this.value;
};
u.prototype.toString = function() {
  return this.value + "";
};
u.prototype.toJSON = function() {
  return this.value;
};
u.prototype.peek = function() {
  var i2 = o;
  o = void 0;
  try {
    return this.value;
  } finally {
    o = i2;
  }
};
Object.defineProperty(u.prototype, "value", { get: function() {
  var i2 = e(this);
  if (void 0 !== i2) i2.i = this.i;
  return this.v;
}, set: function(i2) {
  if (i2 !== this.v) {
    if (f > 100) throw new Error("Cycle detected");
    this.v = i2;
    this.i++;
    v++;
    s++;
    try {
      for (var r2 = this.t; void 0 !== r2; r2 = r2.x) r2.t.N();
    } finally {
      t();
    }
  }
} });
function d$1(i2, t2) {
  return new u(i2, t2);
}
function c(i2) {
  for (var t2 = i2.s; void 0 !== t2; t2 = t2.n) if (t2.S.i !== t2.i || !t2.S.h() || t2.S.i !== t2.i) return true;
  return false;
}
function a(i2) {
  for (var t2 = i2.s; void 0 !== t2; t2 = t2.n) {
    var r2 = t2.S.n;
    if (void 0 !== r2) t2.r = r2;
    t2.S.n = t2;
    t2.i = -1;
    if (void 0 === t2.n) {
      i2.s = t2;
      break;
    }
  }
}
function l$1(i2) {
  var t2 = i2.s, r2 = void 0;
  while (void 0 !== t2) {
    var o2 = t2.p;
    if (-1 === t2.i) {
      t2.S.U(t2);
      if (void 0 !== o2) o2.n = t2.n;
      if (void 0 !== t2.n) t2.n.p = o2;
    } else r2 = t2;
    t2.S.n = t2.r;
    if (void 0 !== t2.r) t2.r = void 0;
    t2 = o2;
  }
  i2.s = r2;
}
function y$2(i2, t2) {
  u.call(this, void 0);
  this.x = i2;
  this.s = void 0;
  this.g = v - 1;
  this.f = 4;
  this.W = null == t2 ? void 0 : t2.watched;
  this.Z = null == t2 ? void 0 : t2.unwatched;
}
y$2.prototype = new u();
y$2.prototype.h = function() {
  this.f &= -3;
  if (1 & this.f) return false;
  if (32 == (36 & this.f)) return true;
  this.f &= -5;
  if (this.g === v) return true;
  this.g = v;
  this.f |= 1;
  if (this.i > 0 && !c(this)) {
    this.f &= -2;
    return true;
  }
  var i2 = o;
  try {
    a(this);
    o = this;
    var t2 = this.x();
    if (16 & this.f || this.v !== t2 || 0 === this.i) {
      this.v = t2;
      this.f &= -17;
      this.i++;
    }
  } catch (i3) {
    this.v = i3;
    this.f |= 16;
    this.i++;
  }
  o = i2;
  l$1(this);
  this.f &= -2;
  return true;
};
y$2.prototype.S = function(i2) {
  if (void 0 === this.t) {
    this.f |= 36;
    for (var t2 = this.s; void 0 !== t2; t2 = t2.n) t2.S.S(t2);
  }
  u.prototype.S.call(this, i2);
};
y$2.prototype.U = function(i2) {
  if (void 0 !== this.t) {
    u.prototype.U.call(this, i2);
    if (void 0 === this.t) {
      this.f &= -33;
      for (var t2 = this.s; void 0 !== t2; t2 = t2.n) t2.S.U(t2);
    }
  }
};
y$2.prototype.N = function() {
  if (!(2 & this.f)) {
    this.f |= 6;
    for (var i2 = this.t; void 0 !== i2; i2 = i2.x) i2.t.N();
  }
};
Object.defineProperty(y$2.prototype, "value", { get: function() {
  if (1 & this.f) throw new Error("Cycle detected");
  var i2 = e(this);
  this.h();
  if (void 0 !== i2) i2.i = this.i;
  if (16 & this.f) throw this.v;
  return this.v;
} });
function w$1(i2, t2) {
  return new y$2(i2, t2);
}
function _$1(i2) {
  var r2 = i2.u;
  i2.u = void 0;
  if ("function" == typeof r2) {
    s++;
    var n2 = o;
    o = void 0;
    try {
      r2();
    } catch (t2) {
      i2.f &= -2;
      i2.f |= 8;
      b$1(i2);
      throw t2;
    } finally {
      o = n2;
      t();
    }
  }
}
function b$1(i2) {
  for (var t2 = i2.s; void 0 !== t2; t2 = t2.n) t2.S.U(t2);
  i2.x = void 0;
  i2.s = void 0;
  _$1(i2);
}
function g$1(i2) {
  if (o !== this) throw new Error("Out-of-order effect");
  l$1(this);
  o = i2;
  this.f &= -2;
  if (8 & this.f) b$1(this);
  t();
}
function p$1(i2) {
  this.x = i2;
  this.u = void 0;
  this.s = void 0;
  this.o = void 0;
  this.f = 32;
}
p$1.prototype.c = function() {
  var i2 = this.S();
  try {
    if (8 & this.f) return;
    if (void 0 === this.x) return;
    var t2 = this.x();
    if ("function" == typeof t2) this.u = t2;
  } finally {
    i2();
  }
};
p$1.prototype.S = function() {
  if (1 & this.f) throw new Error("Cycle detected");
  this.f |= 1;
  this.f &= -9;
  _$1(this);
  a(this);
  s++;
  var i2 = o;
  o = this;
  return g$1.bind(this, i2);
};
p$1.prototype.N = function() {
  if (!(2 & this.f)) {
    this.f |= 2;
    this.o = h$1;
    h$1 = this;
  }
};
p$1.prototype.d = function() {
  this.f |= 8;
  if (!(1 & this.f)) b$1(this);
};
p$1.prototype.dispose = function() {
  this.d();
};
function E(i2) {
  var t2 = new p$1(i2);
  try {
    t2.c();
  } catch (i3) {
    t2.d();
    throw i3;
  }
  var r2 = t2.d.bind(t2);
  r2[Symbol.dispose] = r2;
  return r2;
}
var h, l, d, p = [], m = [];
E(function() {
  h = this.N;
})();
function _(i2, r2) {
  l$4[i2] = r2.bind(null, l$4[i2] || function() {
  });
}
function g(i2) {
  if (d) d();
  d = i2 && i2.S();
}
function b(i2) {
  var n2 = this, t2 = i2.data, o2 = useSignal$1(t2);
  o2.value = t2;
  var e2 = T$2(function() {
    var i3 = n2, t3 = n2.__v;
    while (t3 = t3.__) if (t3.__c) {
      t3.__c.__$f |= 4;
      break;
    }
    var f2 = w$1(function() {
      var i4 = o2.value.value;
      return 0 === i4 ? 0 : true === i4 ? "" : i4 || "";
    }), e3 = w$1(function() {
      return !Array.isArray(f2.value) && !t$3(f2.value);
    }), a3 = E(function() {
      this.N = T;
      if (e3.value) {
        var n3 = f2.value;
        if (i3.__v && i3.__v.__e && 3 === i3.__v.__e.nodeType) i3.__v.__e.data = n3;
      }
    }), v3 = n2.__$u.d;
    n2.__$u.d = function() {
      a3();
      v3.call(this);
    };
    return [e3, f2];
  }, []), a2 = e2[0], v2 = e2[1];
  return a2.value ? v2.peek() : v2.value;
}
b.displayName = "_st";
Object.defineProperties(u.prototype, { constructor: { configurable: true, value: void 0 }, type: { configurable: true, value: b }, props: { configurable: true, get: function() {
  return { data: this };
} }, __b: { configurable: true, value: 1 } });
_("__b", function(i2, n2) {
  if ("string" == typeof n2.type) {
    var r2, t2 = n2.props;
    for (var f2 in t2) if ("children" !== f2) {
      var o2 = t2[f2];
      if (o2 instanceof u) {
        if (!r2) n2.__np = r2 = {};
        r2[f2] = o2;
        t2[f2] = o2.peek();
      }
    }
  }
  i2(n2);
});
_("__r", function(i2, n2) {
  if (n2.type !== k$3) {
    g();
    var r2, f2 = n2.__c;
    if (f2) {
      f2.__$f &= -2;
      if (void 0 === (r2 = f2.__$u)) f2.__$u = r2 = function(i3) {
        var n3;
        E(function() {
          n3 = this;
        });
        n3.c = function() {
          f2.__$f |= 1;
          f2.setState({});
        };
        return n3;
      }();
    }
    l = f2;
    g(r2);
  }
  i2(n2);
});
_("__e", function(i2, n2, r2, t2) {
  g();
  l = void 0;
  i2(n2, r2, t2);
});
_("diffed", function(i2, n2) {
  g();
  l = void 0;
  var r2;
  if ("string" == typeof n2.type && (r2 = n2.__e)) {
    var t2 = n2.__np, f2 = n2.props;
    if (t2) {
      var o2 = r2.U;
      if (o2) for (var e2 in o2) {
        var u2 = o2[e2];
        if (void 0 !== u2 && !(e2 in t2)) {
          u2.d();
          o2[e2] = void 0;
        }
      }
      else {
        o2 = {};
        r2.U = o2;
      }
      for (var a2 in t2) {
        var c2 = o2[a2], v2 = t2[a2];
        if (void 0 === c2) {
          c2 = y$1(r2, a2, v2, f2);
          o2[a2] = c2;
        } else c2.o(v2, f2);
      }
    }
  }
  i2(n2);
});
function y$1(i2, n2, r2, t2) {
  var f2 = n2 in i2 && void 0 === i2.ownerSVGElement, o2 = d$1(r2);
  return { o: function(i3, n3) {
    o2.value = i3;
    t2 = n3;
  }, d: E(function() {
    this.N = T;
    var r3 = o2.value.value;
    if (t2[n2] !== r3) {
      t2[n2] = r3;
      if (f2) i2[n2] = r3;
      else if (r3) i2.setAttribute(n2, r3);
      else i2.removeAttribute(n2);
    }
  }) };
}
_("unmount", function(i2, n2) {
  if ("string" == typeof n2.type) {
    var r2 = n2.__e;
    if (r2) {
      var t2 = r2.U;
      if (t2) {
        r2.U = void 0;
        for (var f2 in t2) {
          var o2 = t2[f2];
          if (o2) o2.d();
        }
      }
    }
  } else {
    var e2 = n2.__c;
    if (e2) {
      var u2 = e2.__$u;
      if (u2) {
        e2.__$u = void 0;
        u2.d();
      }
    }
  }
  i2(n2);
});
_("__h", function(i2, n2, r2, t2) {
  if (t2 < 3 || 9 === t2) n2.__$f |= 2;
  i2(n2, r2, t2);
});
x$2.prototype.shouldComponentUpdate = function(i2, n2) {
  var r2 = this.__$u, t2 = r2 && void 0 !== r2.s;
  for (var f2 in n2) return true;
  if (this.__f || "boolean" == typeof this.u && true === this.u) {
    var o2 = 2 & this.__$f;
    if (!(t2 || o2 || 4 & this.__$f)) return true;
    if (1 & this.__$f) return true;
  } else {
    if (!(t2 || 4 & this.__$f)) return true;
    if (3 & this.__$f) return true;
  }
  for (var e2 in i2) if ("__source" !== e2 && i2[e2] !== this.props[e2]) return true;
  for (var u2 in this.props) if (!(u2 in i2)) return true;
  return false;
};
function useSignal$1(i2, n2) {
  return T$2(function() {
    return d$1(i2, n2);
  }, []);
}
function useComputed$1(i2, n2) {
  var r2 = A$2(i2);
  r2.current = i2;
  l.__$f |= 4;
  return T$2(function() {
    return w$1(function() {
      return r2.current();
    }, n2);
  }, []);
}
var k = "undefined" == typeof requestAnimationFrame ? setTimeout : function(i2) {
  var n2 = function() {
    clearTimeout(r2);
    cancelAnimationFrame(t2);
    i2();
  }, r2 = setTimeout(n2, 35), t2 = requestAnimationFrame(n2);
}, q = function(i2) {
  queueMicrotask(function() {
    queueMicrotask(i2);
  });
};
function A() {
  r(function() {
    var i2;
    while (i2 = p.shift()) h.call(i2);
  });
}
function w() {
  if (1 === p.push(this)) (l$4.requestAnimationFrame || k)(A);
}
function F$1() {
  r(function() {
    var i2;
    while (i2 = m.shift()) h.call(i2);
  });
}
function T() {
  if (1 === m.push(this)) (l$4.requestAnimationFrame || q)(F$1);
}
function useSignalEffect$1(i2) {
  var n2 = A$2(i2);
  n2.current = i2;
  y$3(function() {
    return E(function() {
      this.N = w;
      return n2.current();
    });
  }, []);
}
const signals = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  Signal: u,
  batch: r,
  computed: w$1,
  effect: E,
  signal: d$1,
  untracked: n,
  useComputed: useComputed$1,
  useSignal: useSignal$1,
  useSignalEffect: useSignalEffect$1
}, Symbol.toStringTag, { value: "Module" }));
var _a, _b, _c;
const throwNotSupportedByPreactSignalsRuntime = () => {
  throw new Error("preact signals runtime not implemented hooks");
};
const { Signal, batch, computed, effect, signal, untracked } = signals;
const useComputed = (_a = signals == null ? void 0 : useComputed$1) != null ? _a : throwNotSupportedByPreactSignalsRuntime;
const useSignal = (_b = signals == null ? void 0 : useSignal$1) != null ? _b : throwNotSupportedByPreactSignalsRuntime;
const useSignalEffect = (_c = signals == null ? void 0 : useSignalEffect$1) != null ? _c : throwNotSupportedByPreactSignalsRuntime;
const setterOfFlatStore = (store) => (newValue) => {
  batch(() => {
    for (const key in newValue) {
      store[key] = newValue[key];
    }
  });
};
const __storeState = Symbol("store-state");
const handler = {
  get(target, key, self2) {
    if (key === __storeState) {
      return target[key];
    }
    const storageState = target[__storeState];
    if (key in storageState) {
      return storageState[key]?.value;
    }
    const prop = Object.getOwnPropertyDescriptor(target, key);
    if (prop?.get) {
      storageState[key] = computed(prop.get?.bind(self2));
      delete target[key];
      return storageState[key]?.value;
    }
    if (typeof target[key] === "function") {
      return target[key];
    }
    storageState[key] = signal(target[key]);
    delete target[key];
    return storageState[key].value;
  },
  set(target, key, value2) {
    if (typeof target[key] === "function") {
      target[key] = value2;
      return true;
    }
    const storageState = target[__storeState];
    if (key in target) {
      delete target[key];
    }
    if (!storageState[key]) {
      storageState[key] = signal(value2);
      return true;
    }
    storageState[key].value = value2;
    return true;
  },
  deleteProperty(target, key) {
    const storage = key in target ? target : key in target[__storeState] ? target[__storeState] : null;
    if (!storage) {
      return false;
    }
    delete storage[key];
    return true;
  },
  has(target, key) {
    return key in target || key in target[__storeState];
  },
  ownKeys(target) {
    return [...Object.keys(target), ...Object.keys(target[__storeState])];
  },
  getOwnPropertyDescriptor() {
    return {
      enumerable: true,
      configurable: true
    };
  }
};
const flatStore = (initialState) => {
  if (initialState[__storeState]) {
    return initialState;
  }
  initialState[__storeState] = {};
  return new Proxy(initialState, handler);
};
const createFlatStore = (initialState) => {
  const store = flatStore(initialState);
  return [store, setterOfFlatStore(store)];
};
var UncachedField;
(function(UncachedField2) {
  UncachedField2["Accessor"] = "_a";
  UncachedField2["Setter"] = "_s";
})(UncachedField || (UncachedField = {}));
function ReactiveRef(accessor) {
  this[UncachedField.Accessor] = accessor;
}
function WritableReactiveRef(get, set) {
  this[UncachedField.Accessor] = get;
  this[UncachedField.Setter] = set;
}
ReactiveRef.prototype = Object.create(Signal.prototype);
WritableReactiveRef.prototype = Object.create(ReactiveRef.prototype);
Object.defineProperties(ReactiveRef.prototype, {
  value: {
    get() {
      return this[UncachedField.Accessor]();
    },
    set() {
      throw new Error("Uncached value is readonly");
    }
  },
  peek: {
    value() {
      return untracked(() => this[UncachedField.Accessor]());
    }
  },
  valueOf: {
    value() {
      return this[UncachedField.Accessor]();
    }
  },
  toString: {
    value() {
      return String(this[UncachedField.Accessor]());
    }
  }
});
Object.defineProperty(WritableReactiveRef.prototype, "value", {
  get() {
    return this[UncachedField.Accessor]();
  },
  set(value2) {
    this[UncachedField.Setter](value2);
  }
});
const $ = (accessor) => new ReactiveRef(accessor);
const unwrapReactive = (signalOrAccessor) => typeof signalOrAccessor === "function" ? signalOrAccessor() : signalOrAccessor.value;
const EMPTY_ARRAY$1 = [];
const useSignalOfReactive = (reactive) => useComputed(() => (
  /** __PURE__ */
  unwrapReactive(reactive)
));
const useSignalOfState = (state) => {
  const s2 = useSignal(state);
  if (s2.peek() !== state) {
    s2.value = state;
  }
  return s2;
};
const useSignalContext = (context) => useSignalOfState(x$1(context));
const useSignalEffectOnce = (_effect) => {
  y$3(() => effect(_effect), EMPTY_ARRAY$1);
};
const useFlatStore = (storeCreator) => {
  const storeRef = A$2();
  if (!storeRef.current) {
    storeRef.current = createFlatStore(untracked(storeCreator));
  }
  return storeRef.current;
};
const useComputedOnce = (compute) => {
  const c2 = A$2(null);
  if (c2.current === null) {
    c2.current = computed(compute);
  }
  return c2.current;
};
const defaultContext = K$1(void 0);
const QueryClientSharingContext = K$1(false);
function getQueryClientContext(context, contextSharing) {
  if (context) {
    return context;
  }
  if (contextSharing && typeof window !== "undefined") {
    if (!window.ReactQueryClientContext) {
      window.ReactQueryClientContext = defaultContext;
    }
    return window.ReactQueryClientContext;
  }
  return defaultContext;
}
const useQueryClient = ({ context } = {}) => {
  const queryClient2 = x$1(getQueryClientContext(context, x$1(QueryClientSharingContext)));
  if (!queryClient2) {
    throw new Error("No QueryClient set, use QueryClientProvider to set one");
  }
  return queryClient2;
};
const useQueryClient$ = (options) => useSignalOfState(useQueryClient(options));
const QueryClientProvider = ({ client: client2, children, context, contextSharing = false }) => {
  y$3(() => {
    client2.mount();
    return () => {
      client2.unmount();
    };
  }, [client2]);
  const Context = getQueryClientContext(context, contextSharing);
  return _$3(
    QueryClientSharingContext.Provider,
    { value: !context && contextSharing },
    _$3(Context.Provider, { value: client2 }, children)
  );
};
const IsRestoringContext = K$1(false);
const useIsRestoring$ = () => useSignalContext(IsRestoringContext);
IsRestoringContext.Provider;
function createValue() {
  let isReset = false;
  return {
    clearReset: () => {
      isReset = false;
    },
    reset: () => {
      isReset = true;
    },
    isReset: () => {
      return isReset;
    }
  };
}
const QueryErrorResetBoundaryContext = K$1(createValue());
const useQueryErrorResetBoundary$ = () => useSignalContext(QueryErrorResetBoundaryContext);
function shouldThrowError(_useErrorBoundary, params) {
  if (typeof _useErrorBoundary === "function") {
    return _useErrorBoundary(...params);
  }
  return !!_useErrorBoundary;
}
const ensurePreventErrorBoundaryRetry = (options, errorResetBoundary) => {
  if (options.suspense || options.useErrorBoundary) {
    if (!errorResetBoundary.isReset()) {
      options.retryOnMount = false;
    }
  }
};
const useClearResetErrorBoundary$ = (errorResetBoundary) => {
  const $boundary = useSignalOfReactive(errorResetBoundary);
  useSignalEffectOnce(() => {
    $boundary.value.clearReset();
  });
};
const getHasError = ({ result, errorResetBoundary, useErrorBoundary, query }) => {
  return result.isError && !errorResetBoundary.isReset() && !result.isFetching && shouldThrowError(useErrorBoundary, [result.error, query]);
};
const ensureStaleTime = (defaultedOptions) => {
  if (defaultedOptions.suspense) {
    if (typeof defaultedOptions.staleTime !== "number") {
      defaultedOptions.staleTime = 1e3;
    }
  }
};
const willFetch = (result, isRestoring) => result.isLoading && result.isFetching && !isRestoring;
const shouldSuspend = (defaultedOptions, result, isRestoring) => defaultedOptions?.suspense && willFetch(result, isRestoring);
const useObserverStore = (createObserverStore) => {
  const observer = useSignalOfReactive(createObserverStore);
  const [store, setStore] = useFlatStore(() => observer.value.getCurrent());
  useSignalEffectOnce(() => {
    setStore(observer.value.getCurrent());
  });
  useSignalEffectOnce(() => observer.value.subscribe(setStore));
  return store;
};
const EMPTY_ARRAY = [];
const wrapHandler = {
  apply(target, thisArg, args) {
    return untracked(() => target.apply(thisArg, args));
  }
};
const wrapWithUntracked = (
  /** __PURE__ */
  (fn2) => new Proxy(fn2, wrapHandler)
);
const wrapFunctionsInUntracked = (
  /** __PURE__ */
  (obj) => {
    for (const key in obj) {
      if (typeof obj[key] === "function") {
        obj[key] = wrapWithUntracked(obj[key]);
      }
    }
    return obj;
  }
);
const defaultExecuteOptions = true;
const useRefBasedOptions = (options) => {
  const optionsRef = A$2(options);
  optionsRef.current = options;
  const unstableCallbackButReferencesLatest = q$2(() => optionsRef.current(), [options]);
  const needToChangeOptionOnNextRerender = A$2(true);
  const sig = useSignal(unstableCallbackButReferencesLatest);
  if (needToChangeOptionOnNextRerender.current) {
    sig.value = unstableCallbackButReferencesLatest;
  }
  const computed2 = useComputed(() => sig.value());
  useSignalEffect(() => {
    needToChangeOptionOnNextRerender.current = computed2.value.executeOptionsOnReferenceChange ?? defaultExecuteOptions;
  });
  return computed2;
};
var ReturnStatus;
(function(ReturnStatus2) {
  ReturnStatus2[ReturnStatus2["Error"] = 0] = "Error";
  ReturnStatus2[ReturnStatus2["Success"] = 1] = "Success";
  ReturnStatus2[ReturnStatus2["Suspense"] = 2] = "Suspense";
})(ReturnStatus || (ReturnStatus = {}));
const createBaseQuery = (Observer) => (options) => {
  const $options = useRefBasedOptions(options);
  const $queryClient = useQueryClient$({
    context: useComputedOnce(() => $options.value.context).value
  });
  const $isRestoring = useIsRestoring$();
  const $errorBoundary = useQueryErrorResetBoundary$();
  const $suspenseBehavior = $(() => $options.value.suspenseBehavior ?? "load-on-access");
  const $defaultedOptions = useComputedOnce(() => {
    const defaulted = wrapFunctionsInUntracked($queryClient.value.defaultQueryOptions($options.value));
    defaulted._optimisticResults = $isRestoring.value ? "isRestoring" : "optimistic";
    ensureStaleTime(defaulted);
    ensurePreventErrorBoundaryRetry(defaulted, $errorBoundary.value);
    return defaulted;
  });
  const $observer = useComputedOnce(() => new Observer($queryClient.value, $defaultedOptions.peek()));
  useSignalEffectOnce(() => {
    $observer.value.setOptions($defaultedOptions.value);
  });
  const state = useObserverStore(() => ({
    getCurrent: () => $observer.value.getOptimisticResult($defaultedOptions.value),
    subscribe: (emit) => $observer.value.subscribe((newValue) => {
      emit(newValue);
    })
  }));
  useClearResetErrorBoundary$($errorBoundary);
  const $shouldSuspend = $(() => shouldSuspend($defaultedOptions.value, state, $isRestoring.value));
  const getData = () => {
    if (getHasError({
      result: state,
      errorResetBoundary: $errorBoundary.value,
      query: $observer.value.getCurrentQuery(),
      useErrorBoundary: $defaultedOptions.value.useErrorBoundary
    })) {
      return {
        type: ReturnStatus.Error,
        data: state.error
      };
    }
    if ($shouldSuspend.value) {
      return {
        type: ReturnStatus.Suspense,
        data: $observer.value.fetchOptimistic($defaultedOptions.value)
      };
    }
    return {
      type: ReturnStatus.Success,
      data: state.data
    };
  };
  const dataComputed = useComputedOnce(() => {
    const res = getData();
    if (res.type === ReturnStatus.Success) {
      return res.data;
    }
    throw res.data;
  });
  untracked(() => {
    if ($shouldSuspend.value && $suspenseBehavior.value !== "load-on-access") {
      const data = getData();
      if (data.type === ReturnStatus.Suspense && $suspenseBehavior.value === "suspend-eagerly") {
        throw data.data;
      }
    }
  });
  const willSuspendOrThrow = useComputedOnce(() => {
    if (!$shouldSuspend.value || $suspenseBehavior.value !== "suspend-eagerly") {
      return false;
    }
    return getData().type !== ReturnStatus.Success;
  });
  willSuspendOrThrow.value;
  state.dataSafe = void 0;
  return T$2(() => new Proxy(state, {
    get(target, prop) {
      if (prop === "data") {
        return dataComputed.value;
      }
      if (prop === "dataSafe") {
        return target.data;
      }
      return Reflect.get(...arguments);
    }
  }), []);
};
const useQuery$ = createBaseQuery(QueryObserver);
function noop$1() {
}
const useMutation$ = (options) => {
  const $options = useRefBasedOptions(options);
  const $client = useQueryClient$({
    context: useComputedOnce(() => $options.value.context).value
  });
  const observer = useComputedOnce(
    // we will update current mutation observer with new options, so using `peek`
    () => new MutationObserver$1($client.value, wrapFunctionsInUntracked($options.peek()))
  );
  useSignalEffect(() => {
    observer.value.setOptions(wrapFunctionsInUntracked($options.value));
  });
  const mutate = T$2(() => (variables, mutateOptions) => void observer.peek().mutate(variables, mutateOptions).catch(noop$1), EMPTY_ARRAY);
  const observerResultToStore = (result) => ({
    ...result,
    mutate,
    mutateAsync: result.mutate
  });
  const store = useObserverStore(() => ({
    getCurrent: () => observerResultToStore(observer.value.getCurrentResult()),
    subscribe: (emit) => observer.value.subscribe((newValue) => {
      emit(observerResultToStore(newValue));
    })
  }));
  const shouldThrow = useComputedOnce(() => store.error && shouldThrowError(observer.value.options.useErrorBoundary, [store.error]));
  if (shouldThrow.value) {
    untracked(() => {
      throw store.error;
    });
  }
  return store;
};
const useAuth = () => {
  const [auth, setAuth] = d$3(window.EurolandAppContext?.command("authState") || {});
  y$3(() => {
    const handleAuthChange = () => {
      setAuth(window.EurolandAppContext?.command("authState"));
    };
    window.EurolandAppContext?.on("authChanged", handleAuthChange);
    return () => {
      window.EurolandAppContext?.off("authChanged", handleAuthChange);
    };
  }, []);
  return auth;
};
const isIFrame = window.self !== window.top;
const AddInstrumentWidget = ({ instrumentId }) => {
  const auth = useAuth();
  if (!auth.isAuthenticated) {
    return null;
  }
  return /* @__PURE__ */ u$4(
    "button",
    {
      className: "add-instrument-button",
      onClick: () => {
        const component = window.euroland?.components.WatchlistAddInstrument({
          instrumentId
        });
        if (!component) {
          console.error("euroland component not available");
          return;
        }
        const integrationLayoutPosition = window.xprops?.layout?.middle || "#middleLayout";
        if (isIFrame) {
          component.renderTo(window.parent, integrationLayoutPosition);
        } else {
          let middle = document.getElementById("middleLayout");
          if (!middle) {
            middle = document.createElement("div");
            middle.id = "middleLayout";
            document.body.appendChild(middle);
          }
          component.renderTo(window.parent, integrationLayoutPosition);
        }
      },
      children: "Add to watchlist"
    }
  );
};
function Mt(t2) {
  if (typeof document == "undefined") return;
  let o2 = document.head || document.getElementsByTagName("head")[0], e2 = document.createElement("style");
  e2.type = "text/css", o2.firstChild ? o2.insertBefore(e2, o2.firstChild) : o2.appendChild(e2), e2.styleSheet ? e2.styleSheet.cssText = t2 : e2.appendChild(document.createTextNode(t2));
}
Mt(`:root{--toastify-color-light: #fff;--toastify-color-dark: #121212;--toastify-color-info: #3498db;--toastify-color-success: #07bc0c;--toastify-color-warning: #f1c40f;--toastify-color-error: hsl(6, 78%, 57%);--toastify-color-transparent: rgba(255, 255, 255, .7);--toastify-icon-color-info: var(--toastify-color-info);--toastify-icon-color-success: var(--toastify-color-success);--toastify-icon-color-warning: var(--toastify-color-warning);--toastify-icon-color-error: var(--toastify-color-error);--toastify-container-width: fit-content;--toastify-toast-width: 320px;--toastify-toast-offset: 16px;--toastify-toast-top: max(var(--toastify-toast-offset), env(safe-area-inset-top));--toastify-toast-right: max(var(--toastify-toast-offset), env(safe-area-inset-right));--toastify-toast-left: max(var(--toastify-toast-offset), env(safe-area-inset-left));--toastify-toast-bottom: max(var(--toastify-toast-offset), env(safe-area-inset-bottom));--toastify-toast-background: #fff;--toastify-toast-padding: 14px;--toastify-toast-min-height: 64px;--toastify-toast-max-height: 800px;--toastify-toast-bd-radius: 6px;--toastify-toast-shadow: 0px 4px 12px rgba(0, 0, 0, .1);--toastify-font-family: sans-serif;--toastify-z-index: 9999;--toastify-text-color-light: #757575;--toastify-text-color-dark: #fff;--toastify-text-color-info: #fff;--toastify-text-color-success: #fff;--toastify-text-color-warning: #fff;--toastify-text-color-error: #fff;--toastify-spinner-color: #616161;--toastify-spinner-color-empty-area: #e0e0e0;--toastify-color-progress-light: linear-gradient(to right, #4cd964, #5ac8fa, #007aff, #34aadc, #5856d6, #ff2d55);--toastify-color-progress-dark: #bb86fc;--toastify-color-progress-info: var(--toastify-color-info);--toastify-color-progress-success: var(--toastify-color-success);--toastify-color-progress-warning: var(--toastify-color-warning);--toastify-color-progress-error: var(--toastify-color-error);--toastify-color-progress-bgo: .2}.Toastify__toast-container{z-index:var(--toastify-z-index);-webkit-transform:translate3d(0,0,var(--toastify-z-index));position:fixed;width:var(--toastify-container-width);box-sizing:border-box;color:#fff;display:flex;flex-direction:column}.Toastify__toast-container--top-left{top:var(--toastify-toast-top);left:var(--toastify-toast-left)}.Toastify__toast-container--top-center{top:var(--toastify-toast-top);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--top-right{top:var(--toastify-toast-top);right:var(--toastify-toast-right);align-items:end}.Toastify__toast-container--bottom-left{bottom:var(--toastify-toast-bottom);left:var(--toastify-toast-left)}.Toastify__toast-container--bottom-center{bottom:var(--toastify-toast-bottom);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--bottom-right{bottom:var(--toastify-toast-bottom);right:var(--toastify-toast-right);align-items:end}.Toastify__toast{--y: 0;position:relative;touch-action:none;width:var(--toastify-toast-width);min-height:var(--toastify-toast-min-height);box-sizing:border-box;margin-bottom:1rem;padding:var(--toastify-toast-padding);border-radius:var(--toastify-toast-bd-radius);box-shadow:var(--toastify-toast-shadow);max-height:var(--toastify-toast-max-height);font-family:var(--toastify-font-family);z-index:0;display:flex;flex:1 auto;align-items:center;word-break:break-word}@media only screen and (max-width: 480px){.Toastify__toast-container{width:100vw;left:env(safe-area-inset-left);margin:0}.Toastify__toast-container--top-left,.Toastify__toast-container--top-center,.Toastify__toast-container--top-right{top:env(safe-area-inset-top);transform:translate(0)}.Toastify__toast-container--bottom-left,.Toastify__toast-container--bottom-center,.Toastify__toast-container--bottom-right{bottom:env(safe-area-inset-bottom);transform:translate(0)}.Toastify__toast-container--rtl{right:env(safe-area-inset-right);left:initial}.Toastify__toast{--toastify-toast-width: 100%;margin-bottom:0;border-radius:0}}.Toastify__toast-container[data-stacked=true]{width:var(--toastify-toast-width)}.Toastify__toast--stacked{position:absolute;width:100%;transform:translate3d(0,var(--y),0) scale(var(--s));transition:transform .3s}.Toastify__toast--stacked[data-collapsed] .Toastify__toast-body,.Toastify__toast--stacked[data-collapsed] .Toastify__close-button{transition:opacity .1s}.Toastify__toast--stacked[data-collapsed=false]{overflow:visible}.Toastify__toast--stacked[data-collapsed=true]:not(:last-child)>*{opacity:0}.Toastify__toast--stacked:after{content:"";position:absolute;left:0;right:0;height:calc(var(--g) * 1px);bottom:100%}.Toastify__toast--stacked[data-pos=top]{top:0}.Toastify__toast--stacked[data-pos=bot]{bottom:0}.Toastify__toast--stacked[data-pos=bot].Toastify__toast--stacked:before{transform-origin:top}.Toastify__toast--stacked[data-pos=top].Toastify__toast--stacked:before{transform-origin:bottom}.Toastify__toast--stacked:before{content:"";position:absolute;left:0;right:0;bottom:0;height:100%;transform:scaleY(3);z-index:-1}.Toastify__toast--rtl{direction:rtl}.Toastify__toast--close-on-click{cursor:pointer}.Toastify__toast-icon{margin-inline-end:10px;width:22px;flex-shrink:0;display:flex}.Toastify--animate{animation-fill-mode:both;animation-duration:.5s}.Toastify--animate-icon{animation-fill-mode:both;animation-duration:.3s}.Toastify__toast-theme--dark{background:var(--toastify-color-dark);color:var(--toastify-text-color-dark)}.Toastify__toast-theme--light,.Toastify__toast-theme--colored.Toastify__toast--default{background:var(--toastify-color-light);color:var(--toastify-text-color-light)}.Toastify__toast-theme--colored.Toastify__toast--info{color:var(--toastify-text-color-info);background:var(--toastify-color-info)}.Toastify__toast-theme--colored.Toastify__toast--success{color:var(--toastify-text-color-success);background:var(--toastify-color-success)}.Toastify__toast-theme--colored.Toastify__toast--warning{color:var(--toastify-text-color-warning);background:var(--toastify-color-warning)}.Toastify__toast-theme--colored.Toastify__toast--error{color:var(--toastify-text-color-error);background:var(--toastify-color-error)}.Toastify__progress-bar-theme--light{background:var(--toastify-color-progress-light)}.Toastify__progress-bar-theme--dark{background:var(--toastify-color-progress-dark)}.Toastify__progress-bar--info{background:var(--toastify-color-progress-info)}.Toastify__progress-bar--success{background:var(--toastify-color-progress-success)}.Toastify__progress-bar--warning{background:var(--toastify-color-progress-warning)}.Toastify__progress-bar--error{background:var(--toastify-color-progress-error)}.Toastify__progress-bar-theme--colored.Toastify__progress-bar--info,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--success,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--warning,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--error{background:var(--toastify-color-transparent)}.Toastify__close-button{color:#fff;position:absolute;top:6px;right:6px;background:transparent;outline:none;border:none;padding:0;cursor:pointer;opacity:.7;transition:.3s ease;z-index:1}.Toastify__toast--rtl .Toastify__close-button{left:6px;right:unset}.Toastify__close-button--light{color:#000;opacity:.3}.Toastify__close-button>svg{fill:currentColor;height:16px;width:14px}.Toastify__close-button:hover,.Toastify__close-button:focus{opacity:1}@keyframes Toastify__trackProgress{0%{transform:scaleX(1)}to{transform:scaleX(0)}}.Toastify__progress-bar{position:absolute;bottom:0;left:0;width:100%;height:100%;z-index:1;opacity:.7;transform-origin:left}.Toastify__progress-bar--animated{animation:Toastify__trackProgress linear 1 forwards}.Toastify__progress-bar--controlled{transition:transform .2s}.Toastify__progress-bar--rtl{right:0;left:initial;transform-origin:right;border-bottom-left-radius:initial}.Toastify__progress-bar--wrp{position:absolute;overflow:hidden;bottom:0;left:0;width:100%;height:5px;border-bottom-left-radius:var(--toastify-toast-bd-radius);border-bottom-right-radius:var(--toastify-toast-bd-radius)}.Toastify__progress-bar--wrp[data-hidden=true]{opacity:0}.Toastify__progress-bar--bg{opacity:var(--toastify-color-progress-bgo);width:100%;height:100%}.Toastify__spinner{width:20px;height:20px;box-sizing:border-box;border:2px solid;border-radius:100%;border-color:var(--toastify-spinner-color-empty-area);border-right-color:var(--toastify-spinner-color);animation:Toastify__spin .65s linear infinite}@keyframes Toastify__bounceInRight{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(3000px,0,0)}60%{opacity:1;transform:translate3d(-25px,0,0)}75%{transform:translate3d(10px,0,0)}90%{transform:translate3d(-5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutRight{20%{opacity:1;transform:translate3d(-20px,var(--y),0)}to{opacity:0;transform:translate3d(2000px,var(--y),0)}}@keyframes Toastify__bounceInLeft{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(-3000px,0,0)}60%{opacity:1;transform:translate3d(25px,0,0)}75%{transform:translate3d(-10px,0,0)}90%{transform:translate3d(5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutLeft{20%{opacity:1;transform:translate3d(20px,var(--y),0)}to{opacity:0;transform:translate3d(-2000px,var(--y),0)}}@keyframes Toastify__bounceInUp{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,3000px,0)}60%{opacity:1;transform:translate3d(0,-20px,0)}75%{transform:translate3d(0,10px,0)}90%{transform:translate3d(0,-5px,0)}to{transform:translateZ(0)}}@keyframes Toastify__bounceOutUp{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,-2000px,0)}}@keyframes Toastify__bounceInDown{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,-3000px,0)}60%{opacity:1;transform:translate3d(0,25px,0)}75%{transform:translate3d(0,-10px,0)}90%{transform:translate3d(0,5px,0)}to{transform:none}}@keyframes Toastify__bounceOutDown{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,2000px,0)}}.Toastify__bounce-enter--top-left,.Toastify__bounce-enter--bottom-left{animation-name:Toastify__bounceInLeft}.Toastify__bounce-enter--top-right,.Toastify__bounce-enter--bottom-right{animation-name:Toastify__bounceInRight}.Toastify__bounce-enter--top-center{animation-name:Toastify__bounceInDown}.Toastify__bounce-enter--bottom-center{animation-name:Toastify__bounceInUp}.Toastify__bounce-exit--top-left,.Toastify__bounce-exit--bottom-left{animation-name:Toastify__bounceOutLeft}.Toastify__bounce-exit--top-right,.Toastify__bounce-exit--bottom-right{animation-name:Toastify__bounceOutRight}.Toastify__bounce-exit--top-center{animation-name:Toastify__bounceOutUp}.Toastify__bounce-exit--bottom-center{animation-name:Toastify__bounceOutDown}@keyframes Toastify__zoomIn{0%{opacity:0;transform:scale3d(.3,.3,.3)}50%{opacity:1}}@keyframes Toastify__zoomOut{0%{opacity:1}50%{opacity:0;transform:translate3d(0,var(--y),0) scale3d(.3,.3,.3)}to{opacity:0}}.Toastify__zoom-enter{animation-name:Toastify__zoomIn}.Toastify__zoom-exit{animation-name:Toastify__zoomOut}@keyframes Toastify__flipIn{0%{transform:perspective(400px) rotateX(90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotateX(-20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotateX(10deg);opacity:1}80%{transform:perspective(400px) rotateX(-5deg)}to{transform:perspective(400px)}}@keyframes Toastify__flipOut{0%{transform:translate3d(0,var(--y),0) perspective(400px)}30%{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(-20deg);opacity:1}to{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(90deg);opacity:0}}.Toastify__flip-enter{animation-name:Toastify__flipIn}.Toastify__flip-exit{animation-name:Toastify__flipOut}@keyframes Toastify__slideInRight{0%{transform:translate3d(110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInLeft{0%{transform:translate3d(-110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInUp{0%{transform:translate3d(0,110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInDown{0%{transform:translate3d(0,-110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideOutRight{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(110%,var(--y),0)}}@keyframes Toastify__slideOutLeft{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(-110%,var(--y),0)}}@keyframes Toastify__slideOutDown{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,500px,0)}}@keyframes Toastify__slideOutUp{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,-500px,0)}}.Toastify__slide-enter--top-left,.Toastify__slide-enter--bottom-left{animation-name:Toastify__slideInLeft}.Toastify__slide-enter--top-right,.Toastify__slide-enter--bottom-right{animation-name:Toastify__slideInRight}.Toastify__slide-enter--top-center{animation-name:Toastify__slideInDown}.Toastify__slide-enter--bottom-center{animation-name:Toastify__slideInUp}.Toastify__slide-exit--top-left,.Toastify__slide-exit--bottom-left{animation-name:Toastify__slideOutLeft;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-right,.Toastify__slide-exit--bottom-right{animation-name:Toastify__slideOutRight;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-center{animation-name:Toastify__slideOutUp;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--bottom-center{animation-name:Toastify__slideOutDown;animation-timing-function:ease-in;animation-duration:.3s}@keyframes Toastify__spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}
`);
var L = (t2) => typeof t2 == "number" && !isNaN(t2), N = (t2) => typeof t2 == "string", P = (t2) => typeof t2 == "function", mt = (t2) => N(t2) || L(t2), z = (t2) => mn(t2) || N(t2) || P(t2) || L(t2);
var Xt = 1, at = () => `${Xt++}`;
var I = /* @__PURE__ */ new Map(), F = [], st = /* @__PURE__ */ new Set(), bt = () => I.size > 0;
var vt = (t2, { containerId: o2 }) => {
  var e2;
  return (e2 = I.get(o2 || 1)) == null ? void 0 : e2.toasts.get(t2);
};
function X$1(t2, o2) {
  var r2;
  if (o2) return !!((r2 = I.get(o2)) != null && r2.isToastActive(t2));
  let e2 = false;
  return I.forEach((s2) => {
    s2.isToastActive(t2) && (e2 = true);
  }), e2;
}
function ht(t2) {
  if (!bt()) {
    F = F.filter((o2) => t2 != null && o2.options.toastId !== t2);
    return;
  }
  if (t2 == null || mt(t2)) I.forEach((o2) => {
    o2.removeToast(t2);
  });
  else if (t2 && ("containerId" in t2 || "id" in t2)) {
    let o2 = I.get(t2.containerId);
    o2 ? o2.removeToast(t2.id) : I.forEach((e2) => {
      e2.removeToast(t2.id);
    });
  }
}
var Ct = (t2 = {}) => {
  I.forEach((o2) => {
    o2.props.limit && (!t2.containerId || o2.id === t2.containerId) && o2.clearQueue();
  });
};
function nt(t2, o2) {
  z(t2) && (bt() || F.push({ content: t2, options: o2 }), I.forEach((e2) => {
    e2.buildToast(t2, o2);
  }));
}
function rt(t2, o2) {
  I.forEach((e2) => {
    (o2 == null || !(o2 != null && o2.containerId) || (o2 == null ? void 0 : o2.containerId) === e2.id) && e2.toggle(t2, o2 == null ? void 0 : o2.id);
  });
}
function Pt(t2) {
  return st.add(t2), () => {
    st.delete(t2);
  };
}
function Wt(t2) {
  return t2 && (N(t2.toastId) || L(t2.toastId)) ? t2.toastId : at();
}
function U(t2, o2) {
  return nt(t2, o2), o2.toastId;
}
function V(t2, o2) {
  return { ...o2, type: o2 && o2.type || t2, toastId: Wt(o2) };
}
function Q(t2) {
  return (o2, e2) => U(o2, V(t2, e2));
}
function y(t2, o2) {
  return U(t2, V("default", o2));
}
y.loading = (t2, o2) => U(t2, V("default", { isLoading: true, autoClose: false, closeOnClick: false, closeButton: false, draggable: false, ...o2 }));
function Gt(t2, { pending: o2, error: e2, success: r2 }, s2) {
  let l2;
  o2 && (l2 = N(o2) ? y.loading(o2, s2) : y.loading(o2.render, { ...s2, ...o2 }));
  let a2 = { isLoading: null, autoClose: null, closeOnClick: null, closeButton: null, draggable: null }, d2 = (T2, g2, v2) => {
    if (g2 == null) {
      y.dismiss(l2);
      return;
    }
    let x2 = { type: T2, ...a2, ...s2, data: v2 }, C2 = N(g2) ? { render: g2 } : g2;
    return l2 ? y.update(l2, { ...x2, ...C2 }) : y(C2.render, { ...x2, ...C2 }), v2;
  }, c2 = P(t2) ? t2() : t2;
  return c2.then((T2) => d2("success", r2, T2)).catch((T2) => d2("error", e2, T2)), c2;
}
y.promise = Gt;
y.success = Q("success");
y.info = Q("info");
y.error = Q("error");
y.warning = Q("warning");
y.warn = y.warning;
y.dark = (t2, o2) => U(t2, V("default", { theme: "dark", ...o2 }));
function qt(t2) {
  ht(t2);
}
y.dismiss = qt;
y.clearWaitingQueue = Ct;
y.isActive = X$1;
y.update = (t2, o2 = {}) => {
  let e2 = vt(t2, o2);
  if (e2) {
    let { props: r2, content: s2 } = e2, l2 = { delay: 100, ...r2, ...o2, toastId: o2.toastId || t2, updateId: at() };
    l2.toastId !== t2 && (l2.staleId = t2);
    let a2 = l2.render || s2;
    delete l2.render, U(a2, l2);
  }
};
y.done = (t2) => {
  y.update(t2, { progress: 1 });
};
y.onChange = Pt;
y.play = (t2) => rt(true, t2);
y.pause = (t2) => rt(false, t2);
function bind(fn2, thisArg) {
  return function wrap() {
    return fn2.apply(thisArg, arguments);
  };
}
const { toString } = Object.prototype;
const { getPrototypeOf } = Object;
const { iterator, toStringTag } = Symbol;
const kindOf = /* @__PURE__ */ ((cache) => (thing) => {
  const str = toString.call(thing);
  return cache[str] || (cache[str] = str.slice(8, -1).toLowerCase());
})(/* @__PURE__ */ Object.create(null));
const kindOfTest = (type2) => {
  type2 = type2.toLowerCase();
  return (thing) => kindOf(thing) === type2;
};
const typeOfTest = (type2) => (thing) => typeof thing === type2;
const { isArray } = Array;
const isUndefined = typeOfTest("undefined");
function isBuffer(val) {
  return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor) && isFunction(val.constructor.isBuffer) && val.constructor.isBuffer(val);
}
const isArrayBuffer = kindOfTest("ArrayBuffer");
function isArrayBufferView(val) {
  let result;
  if (typeof ArrayBuffer !== "undefined" && ArrayBuffer.isView) {
    result = ArrayBuffer.isView(val);
  } else {
    result = val && val.buffer && isArrayBuffer(val.buffer);
  }
  return result;
}
const isString = typeOfTest("string");
const isFunction = typeOfTest("function");
const isNumber = typeOfTest("number");
const isObject = (thing) => thing !== null && typeof thing === "object";
const isBoolean = (thing) => thing === true || thing === false;
const isPlainObject = (val) => {
  if (kindOf(val) !== "object") {
    return false;
  }
  const prototype2 = getPrototypeOf(val);
  return (prototype2 === null || prototype2 === Object.prototype || Object.getPrototypeOf(prototype2) === null) && !(toStringTag in val) && !(iterator in val);
};
const isDate = kindOfTest("Date");
const isFile = kindOfTest("File");
const isBlob = kindOfTest("Blob");
const isFileList = kindOfTest("FileList");
const isStream = (val) => isObject(val) && isFunction(val.pipe);
const isFormData = (thing) => {
  let kind;
  return thing && (typeof FormData === "function" && thing instanceof FormData || isFunction(thing.append) && ((kind = kindOf(thing)) === "formdata" || // detect form-data instance
  kind === "object" && isFunction(thing.toString) && thing.toString() === "[object FormData]"));
};
const isURLSearchParams = kindOfTest("URLSearchParams");
const [isReadableStream, isRequest, isResponse, isHeaders] = ["ReadableStream", "Request", "Response", "Headers"].map(kindOfTest);
const trim = (str) => str.trim ? str.trim() : str.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, "");
function forEach(obj, fn2, { allOwnKeys = false } = {}) {
  if (obj === null || typeof obj === "undefined") {
    return;
  }
  let i2;
  let l2;
  if (typeof obj !== "object") {
    obj = [obj];
  }
  if (isArray(obj)) {
    for (i2 = 0, l2 = obj.length; i2 < l2; i2++) {
      fn2.call(null, obj[i2], i2, obj);
    }
  } else {
    const keys = allOwnKeys ? Object.getOwnPropertyNames(obj) : Object.keys(obj);
    const len = keys.length;
    let key;
    for (i2 = 0; i2 < len; i2++) {
      key = keys[i2];
      fn2.call(null, obj[key], key, obj);
    }
  }
}
function findKey(obj, key) {
  key = key.toLowerCase();
  const keys = Object.keys(obj);
  let i2 = keys.length;
  let _key;
  while (i2-- > 0) {
    _key = keys[i2];
    if (key === _key.toLowerCase()) {
      return _key;
    }
  }
  return null;
}
const _global = (() => {
  if (typeof globalThis !== "undefined") return globalThis;
  return typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : global;
})();
const isContextDefined = (context) => !isUndefined(context) && context !== _global;
function merge() {
  const { caseless } = isContextDefined(this) && this || {};
  const result = {};
  const assignValue = (val, key) => {
    const targetKey = caseless && findKey(result, key) || key;
    if (isPlainObject(result[targetKey]) && isPlainObject(val)) {
      result[targetKey] = merge(result[targetKey], val);
    } else if (isPlainObject(val)) {
      result[targetKey] = merge({}, val);
    } else if (isArray(val)) {
      result[targetKey] = val.slice();
    } else {
      result[targetKey] = val;
    }
  };
  for (let i2 = 0, l2 = arguments.length; i2 < l2; i2++) {
    arguments[i2] && forEach(arguments[i2], assignValue);
  }
  return result;
}
const extend = (a2, b2, thisArg, { allOwnKeys } = {}) => {
  forEach(b2, (val, key) => {
    if (thisArg && isFunction(val)) {
      a2[key] = bind(val, thisArg);
    } else {
      a2[key] = val;
    }
  }, { allOwnKeys });
  return a2;
};
const stripBOM = (content) => {
  if (content.charCodeAt(0) === 65279) {
    content = content.slice(1);
  }
  return content;
};
const inherits = (constructor, superConstructor, props, descriptors2) => {
  constructor.prototype = Object.create(superConstructor.prototype, descriptors2);
  constructor.prototype.constructor = constructor;
  Object.defineProperty(constructor, "super", {
    value: superConstructor.prototype
  });
  props && Object.assign(constructor.prototype, props);
};
const toFlatObject = (sourceObj, destObj, filter3, propFilter) => {
  let props;
  let i2;
  let prop;
  const merged = {};
  destObj = destObj || {};
  if (sourceObj == null) return destObj;
  do {
    props = Object.getOwnPropertyNames(sourceObj);
    i2 = props.length;
    while (i2-- > 0) {
      prop = props[i2];
      if ((!propFilter || propFilter(prop, sourceObj, destObj)) && !merged[prop]) {
        destObj[prop] = sourceObj[prop];
        merged[prop] = true;
      }
    }
    sourceObj = filter3 !== false && getPrototypeOf(sourceObj);
  } while (sourceObj && (!filter3 || filter3(sourceObj, destObj)) && sourceObj !== Object.prototype);
  return destObj;
};
const endsWith = (str, searchString2, position) => {
  str = String(str);
  if (position === void 0 || position > str.length) {
    position = str.length;
  }
  position -= searchString2.length;
  const lastIndex = str.indexOf(searchString2, position);
  return lastIndex !== -1 && lastIndex === position;
};
const toArray$1 = (thing) => {
  if (!thing) return null;
  if (isArray(thing)) return thing;
  let i2 = thing.length;
  if (!isNumber(i2)) return null;
  const arr = new Array(i2);
  while (i2-- > 0) {
    arr[i2] = thing[i2];
  }
  return arr;
};
const isTypedArray = /* @__PURE__ */ ((TypedArray) => {
  return (thing) => {
    return TypedArray && thing instanceof TypedArray;
  };
})(typeof Uint8Array !== "undefined" && getPrototypeOf(Uint8Array));
const forEachEntry = (obj, fn2) => {
  const generator = obj && obj[iterator];
  const _iterator = generator.call(obj);
  let result;
  while ((result = _iterator.next()) && !result.done) {
    const pair = result.value;
    fn2.call(obj, pair[0], pair[1]);
  }
};
const matchAll = (regExp, str) => {
  let matches;
  const arr = [];
  while ((matches = regExp.exec(str)) !== null) {
    arr.push(matches);
  }
  return arr;
};
const isHTMLForm = kindOfTest("HTMLFormElement");
const toCamelCase$1 = (str) => {
  return str.toLowerCase().replace(
    /[-_\s]([a-z\d])(\w*)/g,
    function replacer(m2, p1, p2) {
      return p1.toUpperCase() + p2;
    }
  );
};
const hasOwnProperty = (({ hasOwnProperty: hasOwnProperty2 }) => (obj, prop) => hasOwnProperty2.call(obj, prop))(Object.prototype);
const isRegExp = kindOfTest("RegExp");
const reduceDescriptors = (obj, reducer) => {
  const descriptors2 = Object.getOwnPropertyDescriptors(obj);
  const reducedDescriptors = {};
  forEach(descriptors2, (descriptor, name2) => {
    let ret;
    if ((ret = reducer(descriptor, name2, obj)) !== false) {
      reducedDescriptors[name2] = ret || descriptor;
    }
  });
  Object.defineProperties(obj, reducedDescriptors);
};
const freezeMethods = (obj) => {
  reduceDescriptors(obj, (descriptor, name2) => {
    if (isFunction(obj) && ["arguments", "caller", "callee"].indexOf(name2) !== -1) {
      return false;
    }
    const value2 = obj[name2];
    if (!isFunction(value2)) return;
    descriptor.enumerable = false;
    if ("writable" in descriptor) {
      descriptor.writable = false;
      return;
    }
    if (!descriptor.set) {
      descriptor.set = () => {
        throw Error("Can not rewrite read-only method '" + name2 + "'");
      };
    }
  });
};
const toObjectSet = (arrayOrString, delimiter) => {
  const obj = {};
  const define = (arr) => {
    arr.forEach((value2) => {
      obj[value2] = true;
    });
  };
  isArray(arrayOrString) ? define(arrayOrString) : define(String(arrayOrString).split(delimiter));
  return obj;
};
const noop = () => {
};
const toFiniteNumber = (value2, defaultValue) => {
  return value2 != null && Number.isFinite(value2 = +value2) ? value2 : defaultValue;
};
function isSpecCompliantForm(thing) {
  return !!(thing && isFunction(thing.append) && thing[toStringTag] === "FormData" && thing[iterator]);
}
const toJSONObject = (obj) => {
  const stack = new Array(10);
  const visit = (source, i2) => {
    if (isObject(source)) {
      if (stack.indexOf(source) >= 0) {
        return;
      }
      if (!("toJSON" in source)) {
        stack[i2] = source;
        const target = isArray(source) ? [] : {};
        forEach(source, (value2, key) => {
          const reducedValue = visit(value2, i2 + 1);
          !isUndefined(reducedValue) && (target[key] = reducedValue);
        });
        stack[i2] = void 0;
        return target;
      }
    }
    return source;
  };
  return visit(obj, 0);
};
const isAsyncFn = kindOfTest("AsyncFunction");
const isThenable = (thing) => thing && (isObject(thing) || isFunction(thing)) && isFunction(thing.then) && isFunction(thing.catch);
const _setImmediate = ((setImmediateSupported, postMessageSupported) => {
  if (setImmediateSupported) {
    return setImmediate;
  }
  return postMessageSupported ? ((token, callbacks) => {
    _global.addEventListener("message", ({ source, data }) => {
      if (source === _global && data === token) {
        callbacks.length && callbacks.shift()();
      }
    }, false);
    return (cb) => {
      callbacks.push(cb);
      _global.postMessage(token, "*");
    };
  })(`axios@${Math.random()}`, []) : (cb) => setTimeout(cb);
})(
  typeof setImmediate === "function",
  isFunction(_global.postMessage)
);
const asap = typeof queueMicrotask !== "undefined" ? queueMicrotask.bind(_global) : typeof process !== "undefined" && process.nextTick || _setImmediate;
const isIterable = (thing) => thing != null && isFunction(thing[iterator]);
const utils$1 = {
  isArray,
  isArrayBuffer,
  isBuffer,
  isFormData,
  isArrayBufferView,
  isString,
  isNumber,
  isBoolean,
  isObject,
  isPlainObject,
  isReadableStream,
  isRequest,
  isResponse,
  isHeaders,
  isUndefined,
  isDate,
  isFile,
  isBlob,
  isRegExp,
  isFunction,
  isStream,
  isURLSearchParams,
  isTypedArray,
  isFileList,
  forEach,
  merge,
  extend,
  trim,
  stripBOM,
  inherits,
  toFlatObject,
  kindOf,
  kindOfTest,
  endsWith,
  toArray: toArray$1,
  forEachEntry,
  matchAll,
  isHTMLForm,
  hasOwnProperty,
  hasOwnProp: hasOwnProperty,
  // an alias to avoid ESLint no-prototype-builtins detection
  reduceDescriptors,
  freezeMethods,
  toObjectSet,
  toCamelCase: toCamelCase$1,
  noop,
  toFiniteNumber,
  findKey,
  global: _global,
  isContextDefined,
  isSpecCompliantForm,
  toJSONObject,
  isAsyncFn,
  isThenable,
  setImmediate: _setImmediate,
  asap,
  isIterable
};
function AxiosError$1(message, code, config, request, response) {
  Error.call(this);
  if (Error.captureStackTrace) {
    Error.captureStackTrace(this, this.constructor);
  } else {
    this.stack = new Error().stack;
  }
  this.message = message;
  this.name = "AxiosError";
  code && (this.code = code);
  config && (this.config = config);
  request && (this.request = request);
  if (response) {
    this.response = response;
    this.status = response.status ? response.status : null;
  }
}
utils$1.inherits(AxiosError$1, Error, {
  toJSON: function toJSON() {
    return {
      // Standard
      message: this.message,
      name: this.name,
      // Microsoft
      description: this.description,
      number: this.number,
      // Mozilla
      fileName: this.fileName,
      lineNumber: this.lineNumber,
      columnNumber: this.columnNumber,
      stack: this.stack,
      // Axios
      config: utils$1.toJSONObject(this.config),
      code: this.code,
      status: this.status
    };
  }
});
const prototype$1 = AxiosError$1.prototype;
const descriptors = {};
[
  "ERR_BAD_OPTION_VALUE",
  "ERR_BAD_OPTION",
  "ECONNABORTED",
  "ETIMEDOUT",
  "ERR_NETWORK",
  "ERR_FR_TOO_MANY_REDIRECTS",
  "ERR_DEPRECATED",
  "ERR_BAD_RESPONSE",
  "ERR_BAD_REQUEST",
  "ERR_CANCELED",
  "ERR_NOT_SUPPORT",
  "ERR_INVALID_URL"
  // eslint-disable-next-line func-names
].forEach((code) => {
  descriptors[code] = { value: code };
});
Object.defineProperties(AxiosError$1, descriptors);
Object.defineProperty(prototype$1, "isAxiosError", { value: true });
AxiosError$1.from = (error2, code, config, request, response, customProps) => {
  const axiosError = Object.create(prototype$1);
  utils$1.toFlatObject(error2, axiosError, function filter3(obj) {
    return obj !== Error.prototype;
  }, (prop) => {
    return prop !== "isAxiosError";
  });
  AxiosError$1.call(axiosError, error2.message, code, config, request, response);
  axiosError.cause = error2;
  axiosError.name = error2.name;
  customProps && Object.assign(axiosError, customProps);
  return axiosError;
};
const httpAdapter = null;
function isVisitable(thing) {
  return utils$1.isPlainObject(thing) || utils$1.isArray(thing);
}
function removeBrackets(key) {
  return utils$1.endsWith(key, "[]") ? key.slice(0, -2) : key;
}
function renderKey(path, key, dots) {
  if (!path) return key;
  return path.concat(key).map(function each(token, i2) {
    token = removeBrackets(token);
    return !dots && i2 ? "[" + token + "]" : token;
  }).join(dots ? "." : "");
}
function isFlatArray(arr) {
  return utils$1.isArray(arr) && !arr.some(isVisitable);
}
const predicates = utils$1.toFlatObject(utils$1, {}, null, function filter2(prop) {
  return /^is[A-Z]/.test(prop);
});
function toFormData$1(obj, formData, options) {
  if (!utils$1.isObject(obj)) {
    throw new TypeError("target must be an object");
  }
  formData = formData || new FormData();
  options = utils$1.toFlatObject(options, {
    metaTokens: true,
    dots: false,
    indexes: false
  }, false, function defined(option, source) {
    return !utils$1.isUndefined(source[option]);
  });
  const metaTokens = options.metaTokens;
  const visitor = options.visitor || defaultVisitor;
  const dots = options.dots;
  const indexes = options.indexes;
  const _Blob = options.Blob || typeof Blob !== "undefined" && Blob;
  const useBlob = _Blob && utils$1.isSpecCompliantForm(formData);
  if (!utils$1.isFunction(visitor)) {
    throw new TypeError("visitor must be a function");
  }
  function convertValue(value2) {
    if (value2 === null) return "";
    if (utils$1.isDate(value2)) {
      return value2.toISOString();
    }
    if (utils$1.isBoolean(value2)) {
      return value2.toString();
    }
    if (!useBlob && utils$1.isBlob(value2)) {
      throw new AxiosError$1("Blob is not supported. Use a Buffer instead.");
    }
    if (utils$1.isArrayBuffer(value2) || utils$1.isTypedArray(value2)) {
      return useBlob && typeof Blob === "function" ? new Blob([value2]) : Buffer.from(value2);
    }
    return value2;
  }
  function defaultVisitor(value2, key, path) {
    let arr = value2;
    if (value2 && !path && typeof value2 === "object") {
      if (utils$1.endsWith(key, "{}")) {
        key = metaTokens ? key : key.slice(0, -2);
        value2 = JSON.stringify(value2);
      } else if (utils$1.isArray(value2) && isFlatArray(value2) || (utils$1.isFileList(value2) || utils$1.endsWith(key, "[]")) && (arr = utils$1.toArray(value2))) {
        key = removeBrackets(key);
        arr.forEach(function each(el, index2) {
          !(utils$1.isUndefined(el) || el === null) && formData.append(
            // eslint-disable-next-line no-nested-ternary
            indexes === true ? renderKey([key], index2, dots) : indexes === null ? key : key + "[]",
            convertValue(el)
          );
        });
        return false;
      }
    }
    if (isVisitable(value2)) {
      return true;
    }
    formData.append(renderKey(path, key, dots), convertValue(value2));
    return false;
  }
  const stack = [];
  const exposedHelpers = Object.assign(predicates, {
    defaultVisitor,
    convertValue,
    isVisitable
  });
  function build(value2, path) {
    if (utils$1.isUndefined(value2)) return;
    if (stack.indexOf(value2) !== -1) {
      throw Error("Circular reference detected in " + path.join("."));
    }
    stack.push(value2);
    utils$1.forEach(value2, function each(el, key) {
      const result = !(utils$1.isUndefined(el) || el === null) && visitor.call(
        formData,
        el,
        utils$1.isString(key) ? key.trim() : key,
        path,
        exposedHelpers
      );
      if (result === true) {
        build(el, path ? path.concat(key) : [key]);
      }
    });
    stack.pop();
  }
  if (!utils$1.isObject(obj)) {
    throw new TypeError("data must be an object");
  }
  build(obj);
  return formData;
}
function encode$1(str) {
  const charMap = {
    "!": "%21",
    "'": "%27",
    "(": "%28",
    ")": "%29",
    "~": "%7E",
    "%20": "+",
    "%00": "\0"
  };
  return encodeURIComponent(str).replace(/[!'()~]|%20|%00/g, function replacer(match) {
    return charMap[match];
  });
}
function AxiosURLSearchParams(params, options) {
  this._pairs = [];
  params && toFormData$1(params, this, options);
}
const prototype = AxiosURLSearchParams.prototype;
prototype.append = function append(name2, value2) {
  this._pairs.push([name2, value2]);
};
prototype.toString = function toString2(encoder) {
  const _encode = encoder ? function(value2) {
    return encoder.call(this, value2, encode$1);
  } : encode$1;
  return this._pairs.map(function each(pair) {
    return _encode(pair[0]) + "=" + _encode(pair[1]);
  }, "").join("&");
};
function encode(val) {
  return encodeURIComponent(val).replace(/%3A/gi, ":").replace(/%24/g, "$").replace(/%2C/gi, ",").replace(/%20/g, "+").replace(/%5B/gi, "[").replace(/%5D/gi, "]");
}
function buildURL(url, params, options) {
  if (!params) {
    return url;
  }
  const _encode = options && options.encode || encode;
  if (utils$1.isFunction(options)) {
    options = {
      serialize: options
    };
  }
  const serializeFn = options && options.serialize;
  let serializedParams;
  if (serializeFn) {
    serializedParams = serializeFn(params, options);
  } else {
    serializedParams = utils$1.isURLSearchParams(params) ? params.toString() : new AxiosURLSearchParams(params, options).toString(_encode);
  }
  if (serializedParams) {
    const hashmarkIndex = url.indexOf("#");
    if (hashmarkIndex !== -1) {
      url = url.slice(0, hashmarkIndex);
    }
    url += (url.indexOf("?") === -1 ? "?" : "&") + serializedParams;
  }
  return url;
}
class InterceptorManager {
  constructor() {
    this.handlers = [];
  }
  /**
   * Add a new interceptor to the stack
   *
   * @param {Function} fulfilled The function to handle `then` for a `Promise`
   * @param {Function} rejected The function to handle `reject` for a `Promise`
   *
   * @return {Number} An ID used to remove interceptor later
   */
  use(fulfilled, rejected, options) {
    this.handlers.push({
      fulfilled,
      rejected,
      synchronous: options ? options.synchronous : false,
      runWhen: options ? options.runWhen : null
    });
    return this.handlers.length - 1;
  }
  /**
   * Remove an interceptor from the stack
   *
   * @param {Number} id The ID that was returned by `use`
   *
   * @returns {Boolean} `true` if the interceptor was removed, `false` otherwise
   */
  eject(id) {
    if (this.handlers[id]) {
      this.handlers[id] = null;
    }
  }
  /**
   * Clear all interceptors from the stack
   *
   * @returns {void}
   */
  clear() {
    if (this.handlers) {
      this.handlers = [];
    }
  }
  /**
   * Iterate over all the registered interceptors
   *
   * This method is particularly useful for skipping over any
   * interceptors that may have become `null` calling `eject`.
   *
   * @param {Function} fn The function to call for each interceptor
   *
   * @returns {void}
   */
  forEach(fn2) {
    utils$1.forEach(this.handlers, function forEachHandler(h2) {
      if (h2 !== null) {
        fn2(h2);
      }
    });
  }
}
const transitionalDefaults = {
  silentJSONParsing: true,
  forcedJSONParsing: true,
  clarifyTimeoutError: false
};
const URLSearchParams$1 = typeof URLSearchParams !== "undefined" ? URLSearchParams : AxiosURLSearchParams;
const FormData$1 = typeof FormData !== "undefined" ? FormData : null;
const Blob$1 = typeof Blob !== "undefined" ? Blob : null;
const platform$1 = {
  isBrowser: true,
  classes: {
    URLSearchParams: URLSearchParams$1,
    FormData: FormData$1,
    Blob: Blob$1
  },
  protocols: ["http", "https", "file", "blob", "url", "data"]
};
const hasBrowserEnv = typeof window !== "undefined" && typeof document !== "undefined";
const _navigator = typeof navigator === "object" && navigator || void 0;
const hasStandardBrowserEnv = hasBrowserEnv && (!_navigator || ["ReactNative", "NativeScript", "NS"].indexOf(_navigator.product) < 0);
const hasStandardBrowserWebWorkerEnv = (() => {
  return typeof WorkerGlobalScope !== "undefined" && // eslint-disable-next-line no-undef
  self instanceof WorkerGlobalScope && typeof self.importScripts === "function";
})();
const origin = hasBrowserEnv && window.location.href || "http://localhost";
const utils = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  hasBrowserEnv,
  hasStandardBrowserEnv,
  hasStandardBrowserWebWorkerEnv,
  navigator: _navigator,
  origin
}, Symbol.toStringTag, { value: "Module" }));
const platform = {
  ...utils,
  ...platform$1
};
function toURLEncodedForm(data, options) {
  return toFormData$1(data, new platform.classes.URLSearchParams(), Object.assign({
    visitor: function(value2, key, path, helpers) {
      if (platform.isNode && utils$1.isBuffer(value2)) {
        this.append(key, value2.toString("base64"));
        return false;
      }
      return helpers.defaultVisitor.apply(this, arguments);
    }
  }, options));
}
function parsePropPath(name2) {
  return utils$1.matchAll(/\w+|\[(\w*)]/g, name2).map((match) => {
    return match[0] === "[]" ? "" : match[1] || match[0];
  });
}
function arrayToObject(arr) {
  const obj = {};
  const keys = Object.keys(arr);
  let i2;
  const len = keys.length;
  let key;
  for (i2 = 0; i2 < len; i2++) {
    key = keys[i2];
    obj[key] = arr[key];
  }
  return obj;
}
function formDataToJSON(formData) {
  function buildPath(path, value2, target, index2) {
    let name2 = path[index2++];
    if (name2 === "__proto__") return true;
    const isNumericKey = Number.isFinite(+name2);
    const isLast = index2 >= path.length;
    name2 = !name2 && utils$1.isArray(target) ? target.length : name2;
    if (isLast) {
      if (utils$1.hasOwnProp(target, name2)) {
        target[name2] = [target[name2], value2];
      } else {
        target[name2] = value2;
      }
      return !isNumericKey;
    }
    if (!target[name2] || !utils$1.isObject(target[name2])) {
      target[name2] = [];
    }
    const result = buildPath(path, value2, target[name2], index2);
    if (result && utils$1.isArray(target[name2])) {
      target[name2] = arrayToObject(target[name2]);
    }
    return !isNumericKey;
  }
  if (utils$1.isFormData(formData) && utils$1.isFunction(formData.entries)) {
    const obj = {};
    utils$1.forEachEntry(formData, (name2, value2) => {
      buildPath(parsePropPath(name2), value2, obj, 0);
    });
    return obj;
  }
  return null;
}
function stringifySafely(rawValue, parser, encoder) {
  if (utils$1.isString(rawValue)) {
    try {
      (parser || JSON.parse)(rawValue);
      return utils$1.trim(rawValue);
    } catch (e2) {
      if (e2.name !== "SyntaxError") {
        throw e2;
      }
    }
  }
  return (encoder || JSON.stringify)(rawValue);
}
const defaults = {
  transitional: transitionalDefaults,
  adapter: ["xhr", "http", "fetch"],
  transformRequest: [function transformRequest(data, headers) {
    const contentType = headers.getContentType() || "";
    const hasJSONContentType = contentType.indexOf("application/json") > -1;
    const isObjectPayload = utils$1.isObject(data);
    if (isObjectPayload && utils$1.isHTMLForm(data)) {
      data = new FormData(data);
    }
    const isFormData2 = utils$1.isFormData(data);
    if (isFormData2) {
      return hasJSONContentType ? JSON.stringify(formDataToJSON(data)) : data;
    }
    if (utils$1.isArrayBuffer(data) || utils$1.isBuffer(data) || utils$1.isStream(data) || utils$1.isFile(data) || utils$1.isBlob(data) || utils$1.isReadableStream(data)) {
      return data;
    }
    if (utils$1.isArrayBufferView(data)) {
      return data.buffer;
    }
    if (utils$1.isURLSearchParams(data)) {
      headers.setContentType("application/x-www-form-urlencoded;charset=utf-8", false);
      return data.toString();
    }
    let isFileList2;
    if (isObjectPayload) {
      if (contentType.indexOf("application/x-www-form-urlencoded") > -1) {
        return toURLEncodedForm(data, this.formSerializer).toString();
      }
      if ((isFileList2 = utils$1.isFileList(data)) || contentType.indexOf("multipart/form-data") > -1) {
        const _FormData = this.env && this.env.FormData;
        return toFormData$1(
          isFileList2 ? { "files[]": data } : data,
          _FormData && new _FormData(),
          this.formSerializer
        );
      }
    }
    if (isObjectPayload || hasJSONContentType) {
      headers.setContentType("application/json", false);
      return stringifySafely(data);
    }
    return data;
  }],
  transformResponse: [function transformResponse(data) {
    const transitional2 = this.transitional || defaults.transitional;
    const forcedJSONParsing = transitional2 && transitional2.forcedJSONParsing;
    const JSONRequested = this.responseType === "json";
    if (utils$1.isResponse(data) || utils$1.isReadableStream(data)) {
      return data;
    }
    if (data && utils$1.isString(data) && (forcedJSONParsing && !this.responseType || JSONRequested)) {
      const silentJSONParsing = transitional2 && transitional2.silentJSONParsing;
      const strictJSONParsing = !silentJSONParsing && JSONRequested;
      try {
        return JSON.parse(data);
      } catch (e2) {
        if (strictJSONParsing) {
          if (e2.name === "SyntaxError") {
            throw AxiosError$1.from(e2, AxiosError$1.ERR_BAD_RESPONSE, this, null, this.response);
          }
          throw e2;
        }
      }
    }
    return data;
  }],
  /**
   * A timeout in milliseconds to abort a request. If set to 0 (default) a
   * timeout is not created.
   */
  timeout: 0,
  xsrfCookieName: "XSRF-TOKEN",
  xsrfHeaderName: "X-XSRF-TOKEN",
  maxContentLength: -1,
  maxBodyLength: -1,
  env: {
    FormData: platform.classes.FormData,
    Blob: platform.classes.Blob
  },
  validateStatus: function validateStatus(status) {
    return status >= 200 && status < 300;
  },
  headers: {
    common: {
      "Accept": "application/json, text/plain, */*",
      "Content-Type": void 0
    }
  }
};
utils$1.forEach(["delete", "get", "head", "post", "put", "patch"], (method) => {
  defaults.headers[method] = {};
});
const ignoreDuplicateOf = utils$1.toObjectSet([
  "age",
  "authorization",
  "content-length",
  "content-type",
  "etag",
  "expires",
  "from",
  "host",
  "if-modified-since",
  "if-unmodified-since",
  "last-modified",
  "location",
  "max-forwards",
  "proxy-authorization",
  "referer",
  "retry-after",
  "user-agent"
]);
const parseHeaders = (rawHeaders) => {
  const parsed = {};
  let key;
  let val;
  let i2;
  rawHeaders && rawHeaders.split("\n").forEach(function parser(line) {
    i2 = line.indexOf(":");
    key = line.substring(0, i2).trim().toLowerCase();
    val = line.substring(i2 + 1).trim();
    if (!key || parsed[key] && ignoreDuplicateOf[key]) {
      return;
    }
    if (key === "set-cookie") {
      if (parsed[key]) {
        parsed[key].push(val);
      } else {
        parsed[key] = [val];
      }
    } else {
      parsed[key] = parsed[key] ? parsed[key] + ", " + val : val;
    }
  });
  return parsed;
};
const $internals = Symbol("internals");
function normalizeHeader(header) {
  return header && String(header).trim().toLowerCase();
}
function normalizeValue(value2) {
  if (value2 === false || value2 == null) {
    return value2;
  }
  return utils$1.isArray(value2) ? value2.map(normalizeValue) : String(value2);
}
function parseTokens(str) {
  const tokens = /* @__PURE__ */ Object.create(null);
  const tokensRE = /([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;
  let match;
  while (match = tokensRE.exec(str)) {
    tokens[match[1]] = match[2];
  }
  return tokens;
}
const isValidHeaderName = (str) => /^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(str.trim());
function matchHeaderValue(context, value2, header, filter3, isHeaderNameFilter) {
  if (utils$1.isFunction(filter3)) {
    return filter3.call(this, value2, header);
  }
  if (isHeaderNameFilter) {
    value2 = header;
  }
  if (!utils$1.isString(value2)) return;
  if (utils$1.isString(filter3)) {
    return value2.indexOf(filter3) !== -1;
  }
  if (utils$1.isRegExp(filter3)) {
    return filter3.test(value2);
  }
}
function formatHeader(header) {
  return header.trim().toLowerCase().replace(/([a-z\d])(\w*)/g, (w2, char, str) => {
    return char.toUpperCase() + str;
  });
}
function buildAccessors(obj, header) {
  const accessorName = utils$1.toCamelCase(" " + header);
  ["get", "set", "has"].forEach((methodName) => {
    Object.defineProperty(obj, methodName + accessorName, {
      value: function(arg1, arg2, arg3) {
        return this[methodName].call(this, header, arg1, arg2, arg3);
      },
      configurable: true
    });
  });
}
let AxiosHeaders$1 = class AxiosHeaders {
  constructor(headers) {
    headers && this.set(headers);
  }
  set(header, valueOrRewrite, rewrite) {
    const self2 = this;
    function setHeader(_value, _header, _rewrite) {
      const lHeader = normalizeHeader(_header);
      if (!lHeader) {
        throw new Error("header name must be a non-empty string");
      }
      const key = utils$1.findKey(self2, lHeader);
      if (!key || self2[key] === void 0 || _rewrite === true || _rewrite === void 0 && self2[key] !== false) {
        self2[key || _header] = normalizeValue(_value);
      }
    }
    const setHeaders = (headers, _rewrite) => utils$1.forEach(headers, (_value, _header) => setHeader(_value, _header, _rewrite));
    if (utils$1.isPlainObject(header) || header instanceof this.constructor) {
      setHeaders(header, valueOrRewrite);
    } else if (utils$1.isString(header) && (header = header.trim()) && !isValidHeaderName(header)) {
      setHeaders(parseHeaders(header), valueOrRewrite);
    } else if (utils$1.isObject(header) && utils$1.isIterable(header)) {
      let obj = {}, dest, key;
      for (const entry of header) {
        if (!utils$1.isArray(entry)) {
          throw TypeError("Object iterator must return a key-value pair");
        }
        obj[key = entry[0]] = (dest = obj[key]) ? utils$1.isArray(dest) ? [...dest, entry[1]] : [dest, entry[1]] : entry[1];
      }
      setHeaders(obj, valueOrRewrite);
    } else {
      header != null && setHeader(valueOrRewrite, header, rewrite);
    }
    return this;
  }
  get(header, parser) {
    header = normalizeHeader(header);
    if (header) {
      const key = utils$1.findKey(this, header);
      if (key) {
        const value2 = this[key];
        if (!parser) {
          return value2;
        }
        if (parser === true) {
          return parseTokens(value2);
        }
        if (utils$1.isFunction(parser)) {
          return parser.call(this, value2, key);
        }
        if (utils$1.isRegExp(parser)) {
          return parser.exec(value2);
        }
        throw new TypeError("parser must be boolean|regexp|function");
      }
    }
  }
  has(header, matcher) {
    header = normalizeHeader(header);
    if (header) {
      const key = utils$1.findKey(this, header);
      return !!(key && this[key] !== void 0 && (!matcher || matchHeaderValue(this, this[key], key, matcher)));
    }
    return false;
  }
  delete(header, matcher) {
    const self2 = this;
    let deleted = false;
    function deleteHeader(_header) {
      _header = normalizeHeader(_header);
      if (_header) {
        const key = utils$1.findKey(self2, _header);
        if (key && (!matcher || matchHeaderValue(self2, self2[key], key, matcher))) {
          delete self2[key];
          deleted = true;
        }
      }
    }
    if (utils$1.isArray(header)) {
      header.forEach(deleteHeader);
    } else {
      deleteHeader(header);
    }
    return deleted;
  }
  clear(matcher) {
    const keys = Object.keys(this);
    let i2 = keys.length;
    let deleted = false;
    while (i2--) {
      const key = keys[i2];
      if (!matcher || matchHeaderValue(this, this[key], key, matcher, true)) {
        delete this[key];
        deleted = true;
      }
    }
    return deleted;
  }
  normalize(format) {
    const self2 = this;
    const headers = {};
    utils$1.forEach(this, (value2, header) => {
      const key = utils$1.findKey(headers, header);
      if (key) {
        self2[key] = normalizeValue(value2);
        delete self2[header];
        return;
      }
      const normalized = format ? formatHeader(header) : String(header).trim();
      if (normalized !== header) {
        delete self2[header];
      }
      self2[normalized] = normalizeValue(value2);
      headers[normalized] = true;
    });
    return this;
  }
  concat(...targets) {
    return this.constructor.concat(this, ...targets);
  }
  toJSON(asStrings) {
    const obj = /* @__PURE__ */ Object.create(null);
    utils$1.forEach(this, (value2, header) => {
      value2 != null && value2 !== false && (obj[header] = asStrings && utils$1.isArray(value2) ? value2.join(", ") : value2);
    });
    return obj;
  }
  [Symbol.iterator]() {
    return Object.entries(this.toJSON())[Symbol.iterator]();
  }
  toString() {
    return Object.entries(this.toJSON()).map(([header, value2]) => header + ": " + value2).join("\n");
  }
  getSetCookie() {
    return this.get("set-cookie") || [];
  }
  get [Symbol.toStringTag]() {
    return "AxiosHeaders";
  }
  static from(thing) {
    return thing instanceof this ? thing : new this(thing);
  }
  static concat(first, ...targets) {
    const computed2 = new this(first);
    targets.forEach((target) => computed2.set(target));
    return computed2;
  }
  static accessor(header) {
    const internals = this[$internals] = this[$internals] = {
      accessors: {}
    };
    const accessors = internals.accessors;
    const prototype2 = this.prototype;
    function defineAccessor(_header) {
      const lHeader = normalizeHeader(_header);
      if (!accessors[lHeader]) {
        buildAccessors(prototype2, _header);
        accessors[lHeader] = true;
      }
    }
    utils$1.isArray(header) ? header.forEach(defineAccessor) : defineAccessor(header);
    return this;
  }
};
AxiosHeaders$1.accessor(["Content-Type", "Content-Length", "Accept", "Accept-Encoding", "User-Agent", "Authorization"]);
utils$1.reduceDescriptors(AxiosHeaders$1.prototype, ({ value: value2 }, key) => {
  let mapped = key[0].toUpperCase() + key.slice(1);
  return {
    get: () => value2,
    set(headerValue) {
      this[mapped] = headerValue;
    }
  };
});
utils$1.freezeMethods(AxiosHeaders$1);
function transformData(fns, response) {
  const config = this || defaults;
  const context = response || config;
  const headers = AxiosHeaders$1.from(context.headers);
  let data = context.data;
  utils$1.forEach(fns, function transform(fn2) {
    data = fn2.call(config, data, headers.normalize(), response ? response.status : void 0);
  });
  headers.normalize();
  return data;
}
function isCancel$1(value2) {
  return !!(value2 && value2.__CANCEL__);
}
function CanceledError$1(message, config, request) {
  AxiosError$1.call(this, message == null ? "canceled" : message, AxiosError$1.ERR_CANCELED, config, request);
  this.name = "CanceledError";
}
utils$1.inherits(CanceledError$1, AxiosError$1, {
  __CANCEL__: true
});
function settle(resolve, reject, response) {
  const validateStatus2 = response.config.validateStatus;
  if (!response.status || !validateStatus2 || validateStatus2(response.status)) {
    resolve(response);
  } else {
    reject(new AxiosError$1(
      "Request failed with status code " + response.status,
      [AxiosError$1.ERR_BAD_REQUEST, AxiosError$1.ERR_BAD_RESPONSE][Math.floor(response.status / 100) - 4],
      response.config,
      response.request,
      response
    ));
  }
}
function parseProtocol(url) {
  const match = /^([-+\w]{1,25})(:?\/\/|:)/.exec(url);
  return match && match[1] || "";
}
function speedometer(samplesCount, min) {
  samplesCount = samplesCount || 10;
  const bytes = new Array(samplesCount);
  const timestamps = new Array(samplesCount);
  let head = 0;
  let tail = 0;
  let firstSampleTS;
  min = min !== void 0 ? min : 1e3;
  return function push2(chunkLength) {
    const now = Date.now();
    const startedAt = timestamps[tail];
    if (!firstSampleTS) {
      firstSampleTS = now;
    }
    bytes[head] = chunkLength;
    timestamps[head] = now;
    let i2 = tail;
    let bytesCount = 0;
    while (i2 !== head) {
      bytesCount += bytes[i2++];
      i2 = i2 % samplesCount;
    }
    head = (head + 1) % samplesCount;
    if (head === tail) {
      tail = (tail + 1) % samplesCount;
    }
    if (now - firstSampleTS < min) {
      return;
    }
    const passed = startedAt && now - startedAt;
    return passed ? Math.round(bytesCount * 1e3 / passed) : void 0;
  };
}
function throttle$1(fn2, freq) {
  let timestamp = 0;
  let threshold = 1e3 / freq;
  let lastArgs;
  let timer;
  const invoke = (args, now = Date.now()) => {
    timestamp = now;
    lastArgs = null;
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
    fn2.apply(null, args);
  };
  const throttled = (...args) => {
    const now = Date.now();
    const passed = now - timestamp;
    if (passed >= threshold) {
      invoke(args, now);
    } else {
      lastArgs = args;
      if (!timer) {
        timer = setTimeout(() => {
          timer = null;
          invoke(lastArgs);
        }, threshold - passed);
      }
    }
  };
  const flush = () => lastArgs && invoke(lastArgs);
  return [throttled, flush];
}
const progressEventReducer = (listener, isDownloadStream, freq = 3) => {
  let bytesNotified = 0;
  const _speedometer = speedometer(50, 250);
  return throttle$1((e2) => {
    const loaded = e2.loaded;
    const total = e2.lengthComputable ? e2.total : void 0;
    const progressBytes = loaded - bytesNotified;
    const rate = _speedometer(progressBytes);
    const inRange = loaded <= total;
    bytesNotified = loaded;
    const data = {
      loaded,
      total,
      progress: total ? loaded / total : void 0,
      bytes: progressBytes,
      rate: rate ? rate : void 0,
      estimated: rate && total && inRange ? (total - loaded) / rate : void 0,
      event: e2,
      lengthComputable: total != null,
      [isDownloadStream ? "download" : "upload"]: true
    };
    listener(data);
  }, freq);
};
const progressEventDecorator = (total, throttled) => {
  const lengthComputable = total != null;
  return [(loaded) => throttled[0]({
    lengthComputable,
    total,
    loaded
  }), throttled[1]];
};
const asyncDecorator = (fn2) => (...args) => utils$1.asap(() => fn2(...args));
const isURLSameOrigin = platform.hasStandardBrowserEnv ? /* @__PURE__ */ ((origin2, isMSIE) => (url) => {
  url = new URL(url, platform.origin);
  return origin2.protocol === url.protocol && origin2.host === url.host && (isMSIE || origin2.port === url.port);
})(
  new URL(platform.origin),
  platform.navigator && /(msie|trident)/i.test(platform.navigator.userAgent)
) : () => true;
const cookies = platform.hasStandardBrowserEnv ? (
  // Standard browser envs support document.cookie
  {
    write(name2, value2, expires, path, domain, secure) {
      const cookie = [name2 + "=" + encodeURIComponent(value2)];
      utils$1.isNumber(expires) && cookie.push("expires=" + new Date(expires).toGMTString());
      utils$1.isString(path) && cookie.push("path=" + path);
      utils$1.isString(domain) && cookie.push("domain=" + domain);
      secure === true && cookie.push("secure");
      document.cookie = cookie.join("; ");
    },
    read(name2) {
      const match = document.cookie.match(new RegExp("(^|;\\s*)(" + name2 + ")=([^;]*)"));
      return match ? decodeURIComponent(match[3]) : null;
    },
    remove(name2) {
      this.write(name2, "", Date.now() - 864e5);
    }
  }
) : (
  // Non-standard browser env (web workers, react-native) lack needed support.
  {
    write() {
    },
    read() {
      return null;
    },
    remove() {
    }
  }
);
function isAbsoluteURL(url) {
  return /^([a-z][a-z\d+\-.]*:)?\/\//i.test(url);
}
function combineURLs(baseURL, relativeURL) {
  return relativeURL ? baseURL.replace(/\/?\/$/, "") + "/" + relativeURL.replace(/^\/+/, "") : baseURL;
}
function buildFullPath(baseURL, requestedURL, allowAbsoluteUrls) {
  let isRelativeUrl = !isAbsoluteURL(requestedURL);
  if (baseURL && (isRelativeUrl || allowAbsoluteUrls == false)) {
    return combineURLs(baseURL, requestedURL);
  }
  return requestedURL;
}
const headersToObject = (thing) => thing instanceof AxiosHeaders$1 ? { ...thing } : thing;
function mergeConfig$1(config1, config2) {
  config2 = config2 || {};
  const config = {};
  function getMergedValue(target, source, prop, caseless) {
    if (utils$1.isPlainObject(target) && utils$1.isPlainObject(source)) {
      return utils$1.merge.call({ caseless }, target, source);
    } else if (utils$1.isPlainObject(source)) {
      return utils$1.merge({}, source);
    } else if (utils$1.isArray(source)) {
      return source.slice();
    }
    return source;
  }
  function mergeDeepProperties(a2, b2, prop, caseless) {
    if (!utils$1.isUndefined(b2)) {
      return getMergedValue(a2, b2, prop, caseless);
    } else if (!utils$1.isUndefined(a2)) {
      return getMergedValue(void 0, a2, prop, caseless);
    }
  }
  function valueFromConfig2(a2, b2) {
    if (!utils$1.isUndefined(b2)) {
      return getMergedValue(void 0, b2);
    }
  }
  function defaultToConfig2(a2, b2) {
    if (!utils$1.isUndefined(b2)) {
      return getMergedValue(void 0, b2);
    } else if (!utils$1.isUndefined(a2)) {
      return getMergedValue(void 0, a2);
    }
  }
  function mergeDirectKeys(a2, b2, prop) {
    if (prop in config2) {
      return getMergedValue(a2, b2);
    } else if (prop in config1) {
      return getMergedValue(void 0, a2);
    }
  }
  const mergeMap2 = {
    url: valueFromConfig2,
    method: valueFromConfig2,
    data: valueFromConfig2,
    baseURL: defaultToConfig2,
    transformRequest: defaultToConfig2,
    transformResponse: defaultToConfig2,
    paramsSerializer: defaultToConfig2,
    timeout: defaultToConfig2,
    timeoutMessage: defaultToConfig2,
    withCredentials: defaultToConfig2,
    withXSRFToken: defaultToConfig2,
    adapter: defaultToConfig2,
    responseType: defaultToConfig2,
    xsrfCookieName: defaultToConfig2,
    xsrfHeaderName: defaultToConfig2,
    onUploadProgress: defaultToConfig2,
    onDownloadProgress: defaultToConfig2,
    decompress: defaultToConfig2,
    maxContentLength: defaultToConfig2,
    maxBodyLength: defaultToConfig2,
    beforeRedirect: defaultToConfig2,
    transport: defaultToConfig2,
    httpAgent: defaultToConfig2,
    httpsAgent: defaultToConfig2,
    cancelToken: defaultToConfig2,
    socketPath: defaultToConfig2,
    responseEncoding: defaultToConfig2,
    validateStatus: mergeDirectKeys,
    headers: (a2, b2, prop) => mergeDeepProperties(headersToObject(a2), headersToObject(b2), prop, true)
  };
  utils$1.forEach(Object.keys(Object.assign({}, config1, config2)), function computeConfigValue(prop) {
    const merge2 = mergeMap2[prop] || mergeDeepProperties;
    const configValue = merge2(config1[prop], config2[prop], prop);
    utils$1.isUndefined(configValue) && merge2 !== mergeDirectKeys || (config[prop] = configValue);
  });
  return config;
}
const resolveConfig = (config) => {
  const newConfig = mergeConfig$1({}, config);
  let { data, withXSRFToken, xsrfHeaderName, xsrfCookieName, headers, auth } = newConfig;
  newConfig.headers = headers = AxiosHeaders$1.from(headers);
  newConfig.url = buildURL(buildFullPath(newConfig.baseURL, newConfig.url, newConfig.allowAbsoluteUrls), config.params, config.paramsSerializer);
  if (auth) {
    headers.set(
      "Authorization",
      "Basic " + btoa((auth.username || "") + ":" + (auth.password ? unescape(encodeURIComponent(auth.password)) : ""))
    );
  }
  let contentType;
  if (utils$1.isFormData(data)) {
    if (platform.hasStandardBrowserEnv || platform.hasStandardBrowserWebWorkerEnv) {
      headers.setContentType(void 0);
    } else if ((contentType = headers.getContentType()) !== false) {
      const [type2, ...tokens] = contentType ? contentType.split(";").map((token) => token.trim()).filter(Boolean) : [];
      headers.setContentType([type2 || "multipart/form-data", ...tokens].join("; "));
    }
  }
  if (platform.hasStandardBrowserEnv) {
    withXSRFToken && utils$1.isFunction(withXSRFToken) && (withXSRFToken = withXSRFToken(newConfig));
    if (withXSRFToken || withXSRFToken !== false && isURLSameOrigin(newConfig.url)) {
      const xsrfValue = xsrfHeaderName && xsrfCookieName && cookies.read(xsrfCookieName);
      if (xsrfValue) {
        headers.set(xsrfHeaderName, xsrfValue);
      }
    }
  }
  return newConfig;
};
const isXHRAdapterSupported = typeof XMLHttpRequest !== "undefined";
const xhrAdapter = isXHRAdapterSupported && function(config) {
  return new Promise(function dispatchXhrRequest(resolve, reject) {
    const _config = resolveConfig(config);
    let requestData = _config.data;
    const requestHeaders = AxiosHeaders$1.from(_config.headers).normalize();
    let { responseType, onUploadProgress, onDownloadProgress } = _config;
    let onCanceled;
    let uploadThrottled, downloadThrottled;
    let flushUpload, flushDownload;
    function done() {
      flushUpload && flushUpload();
      flushDownload && flushDownload();
      _config.cancelToken && _config.cancelToken.unsubscribe(onCanceled);
      _config.signal && _config.signal.removeEventListener("abort", onCanceled);
    }
    let request = new XMLHttpRequest();
    request.open(_config.method.toUpperCase(), _config.url, true);
    request.timeout = _config.timeout;
    function onloadend() {
      if (!request) {
        return;
      }
      const responseHeaders = AxiosHeaders$1.from(
        "getAllResponseHeaders" in request && request.getAllResponseHeaders()
      );
      const responseData = !responseType || responseType === "text" || responseType === "json" ? request.responseText : request.response;
      const response = {
        data: responseData,
        status: request.status,
        statusText: request.statusText,
        headers: responseHeaders,
        config,
        request
      };
      settle(function _resolve(value2) {
        resolve(value2);
        done();
      }, function _reject(err) {
        reject(err);
        done();
      }, response);
      request = null;
    }
    if ("onloadend" in request) {
      request.onloadend = onloadend;
    } else {
      request.onreadystatechange = function handleLoad() {
        if (!request || request.readyState !== 4) {
          return;
        }
        if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf("file:") === 0)) {
          return;
        }
        setTimeout(onloadend);
      };
    }
    request.onabort = function handleAbort() {
      if (!request) {
        return;
      }
      reject(new AxiosError$1("Request aborted", AxiosError$1.ECONNABORTED, config, request));
      request = null;
    };
    request.onerror = function handleError() {
      reject(new AxiosError$1("Network Error", AxiosError$1.ERR_NETWORK, config, request));
      request = null;
    };
    request.ontimeout = function handleTimeout() {
      let timeoutErrorMessage = _config.timeout ? "timeout of " + _config.timeout + "ms exceeded" : "timeout exceeded";
      const transitional2 = _config.transitional || transitionalDefaults;
      if (_config.timeoutErrorMessage) {
        timeoutErrorMessage = _config.timeoutErrorMessage;
      }
      reject(new AxiosError$1(
        timeoutErrorMessage,
        transitional2.clarifyTimeoutError ? AxiosError$1.ETIMEDOUT : AxiosError$1.ECONNABORTED,
        config,
        request
      ));
      request = null;
    };
    requestData === void 0 && requestHeaders.setContentType(null);
    if ("setRequestHeader" in request) {
      utils$1.forEach(requestHeaders.toJSON(), function setRequestHeader(val, key) {
        request.setRequestHeader(key, val);
      });
    }
    if (!utils$1.isUndefined(_config.withCredentials)) {
      request.withCredentials = !!_config.withCredentials;
    }
    if (responseType && responseType !== "json") {
      request.responseType = _config.responseType;
    }
    if (onDownloadProgress) {
      [downloadThrottled, flushDownload] = progressEventReducer(onDownloadProgress, true);
      request.addEventListener("progress", downloadThrottled);
    }
    if (onUploadProgress && request.upload) {
      [uploadThrottled, flushUpload] = progressEventReducer(onUploadProgress);
      request.upload.addEventListener("progress", uploadThrottled);
      request.upload.addEventListener("loadend", flushUpload);
    }
    if (_config.cancelToken || _config.signal) {
      onCanceled = (cancel) => {
        if (!request) {
          return;
        }
        reject(!cancel || cancel.type ? new CanceledError$1(null, config, request) : cancel);
        request.abort();
        request = null;
      };
      _config.cancelToken && _config.cancelToken.subscribe(onCanceled);
      if (_config.signal) {
        _config.signal.aborted ? onCanceled() : _config.signal.addEventListener("abort", onCanceled);
      }
    }
    const protocol = parseProtocol(_config.url);
    if (protocol && platform.protocols.indexOf(protocol) === -1) {
      reject(new AxiosError$1("Unsupported protocol " + protocol + ":", AxiosError$1.ERR_BAD_REQUEST, config));
      return;
    }
    request.send(requestData || null);
  });
};
const composeSignals = (signals2, timeout) => {
  const { length } = signals2 = signals2 ? signals2.filter(Boolean) : [];
  if (timeout || length) {
    let controller = new AbortController();
    let aborted;
    const onabort = function(reason) {
      if (!aborted) {
        aborted = true;
        unsubscribe();
        const err = reason instanceof Error ? reason : this.reason;
        controller.abort(err instanceof AxiosError$1 ? err : new CanceledError$1(err instanceof Error ? err.message : err));
      }
    };
    let timer = timeout && setTimeout(() => {
      timer = null;
      onabort(new AxiosError$1(`timeout ${timeout} of ms exceeded`, AxiosError$1.ETIMEDOUT));
    }, timeout);
    const unsubscribe = () => {
      if (signals2) {
        timer && clearTimeout(timer);
        timer = null;
        signals2.forEach((signal3) => {
          signal3.unsubscribe ? signal3.unsubscribe(onabort) : signal3.removeEventListener("abort", onabort);
        });
        signals2 = null;
      }
    };
    signals2.forEach((signal3) => signal3.addEventListener("abort", onabort));
    const { signal: signal2 } = controller;
    signal2.unsubscribe = () => utils$1.asap(unsubscribe);
    return signal2;
  }
};
const streamChunk = function* (chunk, chunkSize) {
  let len = chunk.byteLength;
  if (len < chunkSize) {
    yield chunk;
    return;
  }
  let pos = 0;
  let end;
  while (pos < len) {
    end = pos + chunkSize;
    yield chunk.slice(pos, end);
    pos = end;
  }
};
const readBytes = async function* (iterable, chunkSize) {
  for await (const chunk of readStream(iterable)) {
    yield* streamChunk(chunk, chunkSize);
  }
};
const readStream = async function* (stream) {
  if (stream[Symbol.asyncIterator]) {
    yield* stream;
    return;
  }
  const reader = stream.getReader();
  try {
    for (; ; ) {
      const { done, value: value2 } = await reader.read();
      if (done) {
        break;
      }
      yield value2;
    }
  } finally {
    await reader.cancel();
  }
};
const trackStream = (stream, chunkSize, onProgress, onFinish) => {
  const iterator2 = readBytes(stream, chunkSize);
  let bytes = 0;
  let done;
  let _onFinish = (e2) => {
    if (!done) {
      done = true;
      onFinish && onFinish(e2);
    }
  };
  return new ReadableStream({
    async pull(controller) {
      try {
        const { done: done2, value: value2 } = await iterator2.next();
        if (done2) {
          _onFinish();
          controller.close();
          return;
        }
        let len = value2.byteLength;
        if (onProgress) {
          let loadedBytes = bytes += len;
          onProgress(loadedBytes);
        }
        controller.enqueue(new Uint8Array(value2));
      } catch (err) {
        _onFinish(err);
        throw err;
      }
    },
    cancel(reason) {
      _onFinish(reason);
      return iterator2.return();
    }
  }, {
    highWaterMark: 2
  });
};
const isFetchSupported = typeof fetch === "function" && typeof Request === "function" && typeof Response === "function";
const isReadableStreamSupported = isFetchSupported && typeof ReadableStream === "function";
const encodeText = isFetchSupported && (typeof TextEncoder === "function" ? /* @__PURE__ */ ((encoder) => (str) => encoder.encode(str))(new TextEncoder()) : async (str) => new Uint8Array(await new Response(str).arrayBuffer()));
const test = (fn2, ...args) => {
  try {
    return !!fn2(...args);
  } catch (e2) {
    return false;
  }
};
const supportsRequestStream = isReadableStreamSupported && test(() => {
  let duplexAccessed = false;
  const hasContentType = new Request(platform.origin, {
    body: new ReadableStream(),
    method: "POST",
    get duplex() {
      duplexAccessed = true;
      return "half";
    }
  }).headers.has("Content-Type");
  return duplexAccessed && !hasContentType;
});
const DEFAULT_CHUNK_SIZE = 64 * 1024;
const supportsResponseStream = isReadableStreamSupported && test(() => utils$1.isReadableStream(new Response("").body));
const resolvers = {
  stream: supportsResponseStream && ((res) => res.body)
};
isFetchSupported && ((res) => {
  ["text", "arrayBuffer", "blob", "formData", "stream"].forEach((type2) => {
    !resolvers[type2] && (resolvers[type2] = utils$1.isFunction(res[type2]) ? (res2) => res2[type2]() : (_2, config) => {
      throw new AxiosError$1(`Response type '${type2}' is not supported`, AxiosError$1.ERR_NOT_SUPPORT, config);
    });
  });
})(new Response());
const getBodyLength = async (body) => {
  if (body == null) {
    return 0;
  }
  if (utils$1.isBlob(body)) {
    return body.size;
  }
  if (utils$1.isSpecCompliantForm(body)) {
    const _request = new Request(platform.origin, {
      method: "POST",
      body
    });
    return (await _request.arrayBuffer()).byteLength;
  }
  if (utils$1.isArrayBufferView(body) || utils$1.isArrayBuffer(body)) {
    return body.byteLength;
  }
  if (utils$1.isURLSearchParams(body)) {
    body = body + "";
  }
  if (utils$1.isString(body)) {
    return (await encodeText(body)).byteLength;
  }
};
const resolveBodyLength = async (headers, body) => {
  const length = utils$1.toFiniteNumber(headers.getContentLength());
  return length == null ? getBodyLength(body) : length;
};
const fetchAdapter = isFetchSupported && (async (config) => {
  let {
    url,
    method,
    data,
    signal: signal2,
    cancelToken,
    timeout,
    onDownloadProgress,
    onUploadProgress,
    responseType,
    headers,
    withCredentials = "same-origin",
    fetchOptions
  } = resolveConfig(config);
  responseType = responseType ? (responseType + "").toLowerCase() : "text";
  let composedSignal = composeSignals([signal2, cancelToken && cancelToken.toAbortSignal()], timeout);
  let request;
  const unsubscribe = composedSignal && composedSignal.unsubscribe && (() => {
    composedSignal.unsubscribe();
  });
  let requestContentLength;
  try {
    if (onUploadProgress && supportsRequestStream && method !== "get" && method !== "head" && (requestContentLength = await resolveBodyLength(headers, data)) !== 0) {
      let _request = new Request(url, {
        method: "POST",
        body: data,
        duplex: "half"
      });
      let contentTypeHeader;
      if (utils$1.isFormData(data) && (contentTypeHeader = _request.headers.get("content-type"))) {
        headers.setContentType(contentTypeHeader);
      }
      if (_request.body) {
        const [onProgress, flush] = progressEventDecorator(
          requestContentLength,
          progressEventReducer(asyncDecorator(onUploadProgress))
        );
        data = trackStream(_request.body, DEFAULT_CHUNK_SIZE, onProgress, flush);
      }
    }
    if (!utils$1.isString(withCredentials)) {
      withCredentials = withCredentials ? "include" : "omit";
    }
    const isCredentialsSupported = "credentials" in Request.prototype;
    request = new Request(url, {
      ...fetchOptions,
      signal: composedSignal,
      method: method.toUpperCase(),
      headers: headers.normalize().toJSON(),
      body: data,
      duplex: "half",
      credentials: isCredentialsSupported ? withCredentials : void 0
    });
    let response = await fetch(request, fetchOptions);
    const isStreamResponse = supportsResponseStream && (responseType === "stream" || responseType === "response");
    if (supportsResponseStream && (onDownloadProgress || isStreamResponse && unsubscribe)) {
      const options = {};
      ["status", "statusText", "headers"].forEach((prop) => {
        options[prop] = response[prop];
      });
      const responseContentLength = utils$1.toFiniteNumber(response.headers.get("content-length"));
      const [onProgress, flush] = onDownloadProgress && progressEventDecorator(
        responseContentLength,
        progressEventReducer(asyncDecorator(onDownloadProgress), true)
      ) || [];
      response = new Response(
        trackStream(response.body, DEFAULT_CHUNK_SIZE, onProgress, () => {
          flush && flush();
          unsubscribe && unsubscribe();
        }),
        options
      );
    }
    responseType = responseType || "text";
    let responseData = await resolvers[utils$1.findKey(resolvers, responseType) || "text"](response, config);
    !isStreamResponse && unsubscribe && unsubscribe();
    return await new Promise((resolve, reject) => {
      settle(resolve, reject, {
        data: responseData,
        headers: AxiosHeaders$1.from(response.headers),
        status: response.status,
        statusText: response.statusText,
        config,
        request
      });
    });
  } catch (err) {
    unsubscribe && unsubscribe();
    if (err && err.name === "TypeError" && /Load failed|fetch/i.test(err.message)) {
      throw Object.assign(
        new AxiosError$1("Network Error", AxiosError$1.ERR_NETWORK, config, request),
        {
          cause: err.cause || err
        }
      );
    }
    throw AxiosError$1.from(err, err && err.code, config, request);
  }
});
const knownAdapters = {
  http: httpAdapter,
  xhr: xhrAdapter,
  fetch: fetchAdapter
};
utils$1.forEach(knownAdapters, (fn2, value2) => {
  if (fn2) {
    try {
      Object.defineProperty(fn2, "name", { value: value2 });
    } catch (e2) {
    }
    Object.defineProperty(fn2, "adapterName", { value: value2 });
  }
});
const renderReason = (reason) => `- ${reason}`;
const isResolvedHandle = (adapter) => utils$1.isFunction(adapter) || adapter === null || adapter === false;
const adapters = {
  getAdapter: (adapters2) => {
    adapters2 = utils$1.isArray(adapters2) ? adapters2 : [adapters2];
    const { length } = adapters2;
    let nameOrAdapter;
    let adapter;
    const rejectedReasons = {};
    for (let i2 = 0; i2 < length; i2++) {
      nameOrAdapter = adapters2[i2];
      let id;
      adapter = nameOrAdapter;
      if (!isResolvedHandle(nameOrAdapter)) {
        adapter = knownAdapters[(id = String(nameOrAdapter)).toLowerCase()];
        if (adapter === void 0) {
          throw new AxiosError$1(`Unknown adapter '${id}'`);
        }
      }
      if (adapter) {
        break;
      }
      rejectedReasons[id || "#" + i2] = adapter;
    }
    if (!adapter) {
      const reasons = Object.entries(rejectedReasons).map(
        ([id, state]) => `adapter ${id} ` + (state === false ? "is not supported by the environment" : "is not available in the build")
      );
      let s2 = length ? reasons.length > 1 ? "since :\n" + reasons.map(renderReason).join("\n") : " " + renderReason(reasons[0]) : "as no adapter specified";
      throw new AxiosError$1(
        `There is no suitable adapter to dispatch the request ` + s2,
        "ERR_NOT_SUPPORT"
      );
    }
    return adapter;
  },
  adapters: knownAdapters
};
function throwIfCancellationRequested(config) {
  if (config.cancelToken) {
    config.cancelToken.throwIfRequested();
  }
  if (config.signal && config.signal.aborted) {
    throw new CanceledError$1(null, config);
  }
}
function dispatchRequest(config) {
  throwIfCancellationRequested(config);
  config.headers = AxiosHeaders$1.from(config.headers);
  config.data = transformData.call(
    config,
    config.transformRequest
  );
  if (["post", "put", "patch"].indexOf(config.method) !== -1) {
    config.headers.setContentType("application/x-www-form-urlencoded", false);
  }
  const adapter = adapters.getAdapter(config.adapter || defaults.adapter);
  return adapter(config).then(function onAdapterResolution(response) {
    throwIfCancellationRequested(config);
    response.data = transformData.call(
      config,
      config.transformResponse,
      response
    );
    response.headers = AxiosHeaders$1.from(response.headers);
    return response;
  }, function onAdapterRejection(reason) {
    if (!isCancel$1(reason)) {
      throwIfCancellationRequested(config);
      if (reason && reason.response) {
        reason.response.data = transformData.call(
          config,
          config.transformResponse,
          reason.response
        );
        reason.response.headers = AxiosHeaders$1.from(reason.response.headers);
      }
    }
    return Promise.reject(reason);
  });
}
const VERSION$1 = "1.10.0";
const validators$1 = {};
["object", "boolean", "number", "function", "string", "symbol"].forEach((type2, i2) => {
  validators$1[type2] = function validator2(thing) {
    return typeof thing === type2 || "a" + (i2 < 1 ? "n " : " ") + type2;
  };
});
const deprecatedWarnings = {};
validators$1.transitional = function transitional(validator2, version, message) {
  function formatMessage(opt, desc) {
    return "[Axios v" + VERSION$1 + "] Transitional option '" + opt + "'" + desc + (message ? ". " + message : "");
  }
  return (value2, opt, opts) => {
    if (validator2 === false) {
      throw new AxiosError$1(
        formatMessage(opt, " has been removed" + (version ? " in " + version : "")),
        AxiosError$1.ERR_DEPRECATED
      );
    }
    if (version && !deprecatedWarnings[opt]) {
      deprecatedWarnings[opt] = true;
      console.warn(
        formatMessage(
          opt,
          " has been deprecated since v" + version + " and will be removed in the near future"
        )
      );
    }
    return validator2 ? validator2(value2, opt, opts) : true;
  };
};
validators$1.spelling = function spelling(correctSpelling) {
  return (value2, opt) => {
    console.warn(`${opt} is likely a misspelling of ${correctSpelling}`);
    return true;
  };
};
function assertOptions(options, schema, allowUnknown) {
  if (typeof options !== "object") {
    throw new AxiosError$1("options must be an object", AxiosError$1.ERR_BAD_OPTION_VALUE);
  }
  const keys = Object.keys(options);
  let i2 = keys.length;
  while (i2-- > 0) {
    const opt = keys[i2];
    const validator2 = schema[opt];
    if (validator2) {
      const value2 = options[opt];
      const result = value2 === void 0 || validator2(value2, opt, options);
      if (result !== true) {
        throw new AxiosError$1("option " + opt + " must be " + result, AxiosError$1.ERR_BAD_OPTION_VALUE);
      }
      continue;
    }
    if (allowUnknown !== true) {
      throw new AxiosError$1("Unknown option " + opt, AxiosError$1.ERR_BAD_OPTION);
    }
  }
}
const validator = {
  assertOptions,
  validators: validators$1
};
const validators = validator.validators;
let Axios$1 = class Axios {
  constructor(instanceConfig) {
    this.defaults = instanceConfig || {};
    this.interceptors = {
      request: new InterceptorManager(),
      response: new InterceptorManager()
    };
  }
  /**
   * Dispatch a request
   *
   * @param {String|Object} configOrUrl The config specific for this request (merged with this.defaults)
   * @param {?Object} config
   *
   * @returns {Promise} The Promise to be fulfilled
   */
  async request(configOrUrl, config) {
    try {
      return await this._request(configOrUrl, config);
    } catch (err) {
      if (err instanceof Error) {
        let dummy = {};
        Error.captureStackTrace ? Error.captureStackTrace(dummy) : dummy = new Error();
        const stack = dummy.stack ? dummy.stack.replace(/^.+\n/, "") : "";
        try {
          if (!err.stack) {
            err.stack = stack;
          } else if (stack && !String(err.stack).endsWith(stack.replace(/^.+\n.+\n/, ""))) {
            err.stack += "\n" + stack;
          }
        } catch (e2) {
        }
      }
      throw err;
    }
  }
  _request(configOrUrl, config) {
    if (typeof configOrUrl === "string") {
      config = config || {};
      config.url = configOrUrl;
    } else {
      config = configOrUrl || {};
    }
    config = mergeConfig$1(this.defaults, config);
    const { transitional: transitional2, paramsSerializer, headers } = config;
    if (transitional2 !== void 0) {
      validator.assertOptions(transitional2, {
        silentJSONParsing: validators.transitional(validators.boolean),
        forcedJSONParsing: validators.transitional(validators.boolean),
        clarifyTimeoutError: validators.transitional(validators.boolean)
      }, false);
    }
    if (paramsSerializer != null) {
      if (utils$1.isFunction(paramsSerializer)) {
        config.paramsSerializer = {
          serialize: paramsSerializer
        };
      } else {
        validator.assertOptions(paramsSerializer, {
          encode: validators.function,
          serialize: validators.function
        }, true);
      }
    }
    if (config.allowAbsoluteUrls !== void 0) ;
    else if (this.defaults.allowAbsoluteUrls !== void 0) {
      config.allowAbsoluteUrls = this.defaults.allowAbsoluteUrls;
    } else {
      config.allowAbsoluteUrls = true;
    }
    validator.assertOptions(config, {
      baseUrl: validators.spelling("baseURL"),
      withXsrfToken: validators.spelling("withXSRFToken")
    }, true);
    config.method = (config.method || this.defaults.method || "get").toLowerCase();
    let contextHeaders = headers && utils$1.merge(
      headers.common,
      headers[config.method]
    );
    headers && utils$1.forEach(
      ["delete", "get", "head", "post", "put", "patch", "common"],
      (method) => {
        delete headers[method];
      }
    );
    config.headers = AxiosHeaders$1.concat(contextHeaders, headers);
    const requestInterceptorChain = [];
    let synchronousRequestInterceptors = true;
    this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {
      if (typeof interceptor.runWhen === "function" && interceptor.runWhen(config) === false) {
        return;
      }
      synchronousRequestInterceptors = synchronousRequestInterceptors && interceptor.synchronous;
      requestInterceptorChain.unshift(interceptor.fulfilled, interceptor.rejected);
    });
    const responseInterceptorChain = [];
    this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {
      responseInterceptorChain.push(interceptor.fulfilled, interceptor.rejected);
    });
    let promise;
    let i2 = 0;
    let len;
    if (!synchronousRequestInterceptors) {
      const chain = [dispatchRequest.bind(this), void 0];
      chain.unshift.apply(chain, requestInterceptorChain);
      chain.push.apply(chain, responseInterceptorChain);
      len = chain.length;
      promise = Promise.resolve(config);
      while (i2 < len) {
        promise = promise.then(chain[i2++], chain[i2++]);
      }
      return promise;
    }
    len = requestInterceptorChain.length;
    let newConfig = config;
    i2 = 0;
    while (i2 < len) {
      const onFulfilled = requestInterceptorChain[i2++];
      const onRejected = requestInterceptorChain[i2++];
      try {
        newConfig = onFulfilled(newConfig);
      } catch (error2) {
        onRejected.call(this, error2);
        break;
      }
    }
    try {
      promise = dispatchRequest.call(this, newConfig);
    } catch (error2) {
      return Promise.reject(error2);
    }
    i2 = 0;
    len = responseInterceptorChain.length;
    while (i2 < len) {
      promise = promise.then(responseInterceptorChain[i2++], responseInterceptorChain[i2++]);
    }
    return promise;
  }
  getUri(config) {
    config = mergeConfig$1(this.defaults, config);
    const fullPath = buildFullPath(config.baseURL, config.url, config.allowAbsoluteUrls);
    return buildURL(fullPath, config.params, config.paramsSerializer);
  }
};
utils$1.forEach(["delete", "get", "head", "options"], function forEachMethodNoData(method) {
  Axios$1.prototype[method] = function(url, config) {
    return this.request(mergeConfig$1(config || {}, {
      method,
      url,
      data: (config || {}).data
    }));
  };
});
utils$1.forEach(["post", "put", "patch"], function forEachMethodWithData(method) {
  function generateHTTPMethod(isForm) {
    return function httpMethod(url, data, config) {
      return this.request(mergeConfig$1(config || {}, {
        method,
        headers: isForm ? {
          "Content-Type": "multipart/form-data"
        } : {},
        url,
        data
      }));
    };
  }
  Axios$1.prototype[method] = generateHTTPMethod();
  Axios$1.prototype[method + "Form"] = generateHTTPMethod(true);
});
let CancelToken$1 = class CancelToken {
  constructor(executor) {
    if (typeof executor !== "function") {
      throw new TypeError("executor must be a function.");
    }
    let resolvePromise;
    this.promise = new Promise(function promiseExecutor(resolve) {
      resolvePromise = resolve;
    });
    const token = this;
    this.promise.then((cancel) => {
      if (!token._listeners) return;
      let i2 = token._listeners.length;
      while (i2-- > 0) {
        token._listeners[i2](cancel);
      }
      token._listeners = null;
    });
    this.promise.then = (onfulfilled) => {
      let _resolve;
      const promise = new Promise((resolve) => {
        token.subscribe(resolve);
        _resolve = resolve;
      }).then(onfulfilled);
      promise.cancel = function reject() {
        token.unsubscribe(_resolve);
      };
      return promise;
    };
    executor(function cancel(message, config, request) {
      if (token.reason) {
        return;
      }
      token.reason = new CanceledError$1(message, config, request);
      resolvePromise(token.reason);
    });
  }
  /**
   * Throws a `CanceledError` if cancellation has been requested.
   */
  throwIfRequested() {
    if (this.reason) {
      throw this.reason;
    }
  }
  /**
   * Subscribe to the cancel signal
   */
  subscribe(listener) {
    if (this.reason) {
      listener(this.reason);
      return;
    }
    if (this._listeners) {
      this._listeners.push(listener);
    } else {
      this._listeners = [listener];
    }
  }
  /**
   * Unsubscribe from the cancel signal
   */
  unsubscribe(listener) {
    if (!this._listeners) {
      return;
    }
    const index2 = this._listeners.indexOf(listener);
    if (index2 !== -1) {
      this._listeners.splice(index2, 1);
    }
  }
  toAbortSignal() {
    const controller = new AbortController();
    const abort = (err) => {
      controller.abort(err);
    };
    this.subscribe(abort);
    controller.signal.unsubscribe = () => this.unsubscribe(abort);
    return controller.signal;
  }
  /**
   * Returns an object that contains a new `CancelToken` and a function that, when called,
   * cancels the `CancelToken`.
   */
  static source() {
    let cancel;
    const token = new CancelToken(function executor(c2) {
      cancel = c2;
    });
    return {
      token,
      cancel
    };
  }
};
function spread$1(callback) {
  return function wrap(arr) {
    return callback.apply(null, arr);
  };
}
function isAxiosError$1(payload) {
  return utils$1.isObject(payload) && payload.isAxiosError === true;
}
const HttpStatusCode$1 = {
  Continue: 100,
  SwitchingProtocols: 101,
  Processing: 102,
  EarlyHints: 103,
  Ok: 200,
  Created: 201,
  Accepted: 202,
  NonAuthoritativeInformation: 203,
  NoContent: 204,
  ResetContent: 205,
  PartialContent: 206,
  MultiStatus: 207,
  AlreadyReported: 208,
  ImUsed: 226,
  MultipleChoices: 300,
  MovedPermanently: 301,
  Found: 302,
  SeeOther: 303,
  NotModified: 304,
  UseProxy: 305,
  Unused: 306,
  TemporaryRedirect: 307,
  PermanentRedirect: 308,
  BadRequest: 400,
  Unauthorized: 401,
  PaymentRequired: 402,
  Forbidden: 403,
  NotFound: 404,
  MethodNotAllowed: 405,
  NotAcceptable: 406,
  ProxyAuthenticationRequired: 407,
  RequestTimeout: 408,
  Conflict: 409,
  Gone: 410,
  LengthRequired: 411,
  PreconditionFailed: 412,
  PayloadTooLarge: 413,
  UriTooLong: 414,
  UnsupportedMediaType: 415,
  RangeNotSatisfiable: 416,
  ExpectationFailed: 417,
  ImATeapot: 418,
  MisdirectedRequest: 421,
  UnprocessableEntity: 422,
  Locked: 423,
  FailedDependency: 424,
  TooEarly: 425,
  UpgradeRequired: 426,
  PreconditionRequired: 428,
  TooManyRequests: 429,
  RequestHeaderFieldsTooLarge: 431,
  UnavailableForLegalReasons: 451,
  InternalServerError: 500,
  NotImplemented: 501,
  BadGateway: 502,
  ServiceUnavailable: 503,
  GatewayTimeout: 504,
  HttpVersionNotSupported: 505,
  VariantAlsoNegotiates: 506,
  InsufficientStorage: 507,
  LoopDetected: 508,
  NotExtended: 510,
  NetworkAuthenticationRequired: 511
};
Object.entries(HttpStatusCode$1).forEach(([key, value2]) => {
  HttpStatusCode$1[value2] = key;
});
function createInstance(defaultConfig) {
  const context = new Axios$1(defaultConfig);
  const instance = bind(Axios$1.prototype.request, context);
  utils$1.extend(instance, Axios$1.prototype, context, { allOwnKeys: true });
  utils$1.extend(instance, context, null, { allOwnKeys: true });
  instance.create = function create(instanceConfig) {
    return createInstance(mergeConfig$1(defaultConfig, instanceConfig));
  };
  return instance;
}
const axios = createInstance(defaults);
axios.Axios = Axios$1;
axios.CanceledError = CanceledError$1;
axios.CancelToken = CancelToken$1;
axios.isCancel = isCancel$1;
axios.VERSION = VERSION$1;
axios.toFormData = toFormData$1;
axios.AxiosError = AxiosError$1;
axios.Cancel = axios.CanceledError;
axios.all = function all(promises) {
  return Promise.all(promises);
};
axios.spread = spread$1;
axios.isAxiosError = isAxiosError$1;
axios.mergeConfig = mergeConfig$1;
axios.AxiosHeaders = AxiosHeaders$1;
axios.formToJSON = (thing) => formDataToJSON(utils$1.isHTMLForm(thing) ? new FormData(thing) : thing);
axios.getAdapter = adapters.getAdapter;
axios.HttpStatusCode = HttpStatusCode$1;
axios.default = axios;
const {
  Axios: Axios2,
  AxiosError,
  CanceledError,
  isCancel,
  CancelToken: CancelToken2,
  VERSION,
  all: all2,
  Cancel,
  isAxiosError,
  spread,
  toFormData,
  AxiosHeaders: AxiosHeaders2,
  HttpStatusCode,
  formToJSON,
  getAdapter,
  mergeConfig
} = axios;
function getBearerToken() {
  return window.EurolandAppContext.command("authState").accessToken;
}
class WatchlistService {
  baseUrl = "http://localhost:5111";
  getHeaders() {
    return {
      "Authorization": `Bearer ${getBearerToken()}`,
      "Content-Type": "application/json"
    };
  }
  handleError(error2, defaultMessage) {
    if (axios.isAxiosError(error2)) {
      const errorMessage = error2.response?.data?.message || `${defaultMessage}: ${error2.message}`;
      throw new Error(errorMessage);
    }
    throw error2;
  }
  async fetchWatchlists() {
    try {
      const response = await axios.get(`${this.baseUrl}/api/watchlist/find-all`, {
        headers: this.getHeaders()
      });
      return response.data;
    } catch (error2) {
      this.handleError(error2, "Failed to fetch watchlists");
    }
  }
  async addInstrument(instrumentId, watchlistId) {
    try {
      const response = await axios.post(
        `${this.baseUrl}/api/WatchList/add-detail`,
        { instrumentIds: [instrumentId], watchlistId },
        { headers: this.getHeaders() }
      );
      return response.data;
    } catch (error2) {
      this.handleError(error2, "Failed to add instrument");
    }
  }
  async removeInstrument(watchlistId, instrumentId) {
    try {
      await axios.delete(`${this.baseUrl}/api/Watchlist/remove-detail`, {
        params: {
          WatchListId: watchlistId,
          Id: instrumentId.toString()
        },
        headers: {
          "Authorization": `Bearer ${getBearerToken()}`
        }
      });
    } catch (error2) {
      this.handleError(error2, "Failed to remove instrument");
    }
  }
  async createWatchlist(name2) {
    try {
      const response = await axios.post(
        `${this.baseUrl}/api/WatchList/add`,
        { name: name2 },
        { headers: this.getHeaders() }
      );
      return response.data;
    } catch (error2) {
      this.handleError(error2, "Failed to create watchlist");
    }
  }
  async updateWatchlist(id, name2) {
    try {
      const response = await axios.put(
        `${this.baseUrl}/api/WatchList/update`,
        { name: name2, id },
        { headers: this.getHeaders() }
      );
      return response.data;
    } catch (error2) {
      this.handleError(error2, "Failed to update watchlist");
    }
  }
  async deleteWatchlist(id) {
    try {
      await axios.delete(`${this.baseUrl}/api/WatchList/delete/${id}`, {
        headers: {
          "Authorization": `Bearer ${getBearerToken()}`
        }
      });
    } catch (error2) {
      this.handleError(error2, "Failed to delete watchlist");
    }
  }
  async getWatchlistById(id) {
    try {
      const response = await axios.get(`${this.baseUrl}/api/WatchList/find/${id}`, {
        headers: this.getHeaders()
      });
      return response.data;
    } catch (error2) {
      this.handleError(error2, "Failed to get watchlist by id");
    }
  }
}
const watchlistService = new WatchlistService();
const useWatchlistQueries = () => {
  const queryClient2 = useQueryClient$();
  const watchlistsQuery = useQuery$(() => ({
    queryKey: ["watchlists"],
    queryFn: () => watchlistService.fetchWatchlists(),
    staleTime: 1e3 * 60 * 5
  }));
  const createWatchlistMutation = useMutation$(() => ({
    mutationFn: (name2) => watchlistService.createWatchlist(name2),
    onSuccess: () => {
      queryClient2.value.invalidateQueries({ queryKey: ["watchlists"] });
    },
    onLoading: () => {
      queryClient2.value.invalidateQueries({ queryKey: ["watchlists"] });
    }
  }));
  const updateWatchlistMutation = useMutation$(() => ({
    mutationFn: ({ id, name: name2 }) => watchlistService.updateWatchlist(id, name2),
    onSuccess: () => {
      queryClient2.value.invalidateQueries({ queryKey: ["watchlists"] });
    }
  }));
  const deleteWatchlistMutation = useMutation$(() => ({
    mutationFn: (id) => watchlistService.deleteWatchlist(id),
    onSuccess: () => {
      queryClient2.value.invalidateQueries({ queryKey: ["watchlists"] });
      queryClient2.value.invalidateQueries({ queryKey: ["instruments"] });
    }
  }));
  const addInstrumentMutation = useMutation$(() => ({
    mutationFn: ({
      instrumentId,
      watchlistId
    }) => watchlistService.addInstrument(instrumentId, watchlistId),
    onSuccess: () => {
      queryClient2.value.invalidateQueries({ queryKey: ["instruments"] });
      queryClient2.value.invalidateQueries({ queryKey: ["watchlists"] });
    }
  }));
  const removeInstrumentMutation = useMutation$(() => ({
    mutationFn: ({
      watchlistId,
      instrumentId
    }) => watchlistService.removeInstrument(watchlistId, instrumentId),
    onSuccess: () => {
      queryClient2.value.invalidateQueries({ queryKey: ["instruments"] });
      queryClient2.value.invalidateQueries({ queryKey: ["watchlists"] });
    }
  }));
  return {
    loadingFetchWatchlist: watchlistsQuery.isLoading,
    watchlistsQuery,
    createWatchlistMutation,
    updateWatchlistMutation,
    deleteWatchlistMutation,
    addInstrumentMutation,
    removeInstrumentMutation
  };
};
/**
 * @license lucide-preact v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const toKebabCase = (string) => string.replace(/([a-z0-9])([A-Z])/g, "$1-$2").toLowerCase();
const toCamelCase = (string) => string.replace(
  /^([A-Z])|[\s-_]+(\w)/g,
  (match, p1, p2) => p2 ? p2.toUpperCase() : p1.toLowerCase()
);
const toPascalCase = (string) => {
  const camelCase = toCamelCase(string);
  return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);
};
const mergeClasses = (...classes) => classes.filter((className, index2, array) => {
  return Boolean(className) && className.trim() !== "" && array.indexOf(className) === index2;
}).join(" ").trim();
/**
 * @license lucide-preact v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
var defaultAttributes = {
  xmlns: "http://www.w3.org/2000/svg",
  width: 24,
  height: 24,
  viewBox: "0 0 24 24",
  fill: "none",
  stroke: "currentColor",
  "stroke-width": "2",
  "stroke-linecap": "round",
  "stroke-linejoin": "round"
};
/**
 * @license lucide-preact v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Icon = ({
  color = "currentColor",
  size = 24,
  strokeWidth = 2,
  absoluteStrokeWidth,
  children,
  iconNode,
  class: classes = "",
  ...rest
}) => _$3(
  "svg",
  {
    ...defaultAttributes,
    width: String(size),
    height: size,
    stroke: color,
    ["stroke-width"]: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,
    class: ["lucide", classes].join(" "),
    ...rest
  },
  [...iconNode.map(([tag, attrs]) => _$3(tag, attrs)), ...H$1(children)]
);
/**
 * @license lucide-preact v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const createLucideIcon = (iconName, iconNode) => {
  const Component = ({ class: classes = "", children, ...props }) => _$3(
    Icon,
    {
      ...props,
      iconNode,
      class: mergeClasses(
        `lucide-${toKebabCase(toPascalCase(iconName))}`,
        `lucide-${toKebabCase(iconName)}`,
        classes
      )
    },
    children
  );
  Component.displayName = toPascalCase(iconName);
  return Component;
};
/**
 * @license lucide-preact v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Check = createLucideIcon("check", [["path", { d: "M20 6 9 17l-5-5", key: "1gmf2c" }]]);
/**
 * @license lucide-preact v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Pen = createLucideIcon("pen", [
  [
    "path",
    {
      d: "M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",
      key: "1a8usu"
    }
  ]
]);
/**
 * @license lucide-preact v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Plus = createLucideIcon("plus", [
  ["path", { d: "M5 12h14", key: "1ays0h" }],
  ["path", { d: "M12 5v14", key: "s699le" }]
]);
/**
 * @license lucide-preact v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Search = createLucideIcon("search", [
  ["path", { d: "m21 21-4.34-4.34", key: "14j7rj" }],
  ["circle", { cx: "11", cy: "11", r: "8", key: "4ej97u" }]
]);
/**
 * @license lucide-preact v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Star = createLucideIcon("star", [
  [
    "path",
    {
      d: "M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",
      key: "r04s7s"
    }
  ]
]);
/**
 * @license lucide-preact v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Trash2 = createLucideIcon("trash-2", [
  ["path", { d: "M3 6h18", key: "d0wm0j" }],
  ["path", { d: "M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6", key: "4alrt4" }],
  ["path", { d: "M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2", key: "v07s0e" }],
  ["line", { x1: "10", x2: "10", y1: "11", y2: "17", key: "1uufr5" }],
  ["line", { x1: "14", x2: "14", y1: "11", y2: "17", key: "xtxkd" }]
]);
/**
 * @license lucide-preact v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const TrendingUp = createLucideIcon("trending-up", [
  ["path", { d: "M16 7h6v6", key: "box55l" }],
  ["path", { d: "m22 7-8.5 8.5-5-5L2 17", key: "1t1m79" }]
]);
/**
 * @license lucide-preact v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const X = createLucideIcon("x", [
  ["path", { d: "M18 6 6 18", key: "1bl5f8" }],
  ["path", { d: "m6 6 12 12", key: "d8bk6v" }]
]);
const EmptyWatchlistState = ({ onCreateWatchlist }) => {
  const [showCreateForm, setShowCreateForm] = d$3(false);
  const [newWatchlistName, setNewWatchlistName] = d$3("");
  const handleCreateWatchlist = async () => {
    if (!newWatchlistName.trim()) {
      return;
    }
    try {
      await onCreateWatchlist(newWatchlistName);
      setNewWatchlistName("");
      setShowCreateForm(false);
    } catch (error2) {
      console.error("Error creating watchlist:", error2);
      y.error("Failed to create watchlist");
    }
  };
  const handleCancel = () => {
    setShowCreateForm(false);
    setNewWatchlistName("");
  };
  const handleKeyDown = (e2) => {
    if (e2.key === "Enter") {
      handleCreateWatchlist();
    }
    if (e2.key === "Escape") {
      handleCancel();
    }
  };
  return /* @__PURE__ */ u$4("div", { className: "watchlist-container", children: /* @__PURE__ */ u$4("div", { className: "empty-watchlist-state", children: /* @__PURE__ */ u$4("div", { className: "empty-watchlist-content", children: [
    /* @__PURE__ */ u$4("div", { className: "empty-watchlist-icon", children: /* @__PURE__ */ u$4(TrendingUp, { size: 64 }) }),
    /* @__PURE__ */ u$4("h2", { className: "empty-watchlist-title", children: "No Watchlists Yet" }),
    /* @__PURE__ */ u$4("p", { className: "empty-watchlist-description", children: "Create your first watchlist to start tracking your favorite instruments" }),
    /* @__PURE__ */ u$4(
      "button",
      {
        className: "create-first-watchlist-btn",
        onClick: () => setShowCreateForm(true),
        children: [
          /* @__PURE__ */ u$4(Plus, { size: 16 }),
          "Create Your First Watchlist"
        ]
      }
    ),
    showCreateForm && /* @__PURE__ */ u$4("div", { className: "first-watchlist-form", children: [
      /* @__PURE__ */ u$4(
        "input",
        {
          type: "text",
          value: newWatchlistName,
          onChange: (e2) => setNewWatchlistName(e2.currentTarget.value),
          placeholder: "Enter watchlist name",
          className: "first-watchlist-input",
          autoFocus: true,
          onKeyDown: handleKeyDown
        }
      ),
      /* @__PURE__ */ u$4("div", { className: "first-watchlist-actions", children: [
        /* @__PURE__ */ u$4(
          "button",
          {
            onClick: handleCreateWatchlist,
            className: "confirm-first-btn",
            children: [
              /* @__PURE__ */ u$4(Check, { size: 16 }),
              "Create"
            ]
          }
        ),
        /* @__PURE__ */ u$4(
          "button",
          {
            onClick: handleCancel,
            className: "cancel-first-btn",
            children: [
              /* @__PURE__ */ u$4(X, { size: 16 }),
              "Cancel"
            ]
          }
        )
      ] })
    ] })
  ] }) }) });
};
const WatchlistTabs = ({
  watchlists,
  activeWatchlistId,
  onWatchlistSelect,
  onCreateWatchlist
}) => {
  const [showCreateForm, setShowCreateForm] = d$3(false);
  const [newWatchlistName, setNewWatchlistName] = d$3("");
  const [editingWatchlistId, setEditingWatchlistId] = d$3("");
  const [editedName, setEditedName] = d$3("");
  const [showDeleteConfirm, setShowDeleteConfirm] = d$3("");
  const {
    updateWatchlistMutation,
    deleteWatchlistMutation
  } = useWatchlistQueries();
  const tabBarRef = A$2(null);
  const addFormRef = A$2(null);
  y$3(() => {
    if (showCreateForm && addFormRef.current && tabBarRef.current) {
      setTimeout(() => {
        addFormRef.current?.scrollIntoView({
          behavior: "smooth",
          block: "nearest",
          inline: "end"
        });
      }, 50);
    }
  }, [showCreateForm]);
  const handleAddStart = () => {
    setShowCreateForm(true);
  };
  const handleStartEdit = (watchlistId, currentName) => {
    setEditingWatchlistId(watchlistId);
    setEditedName(currentName);
  };
  const handleCancelEdit = () => {
    setEditingWatchlistId("");
    setEditedName("");
  };
  const handleCreateWatchlist = async () => {
    if (!newWatchlistName.trim()) {
      return;
    }
    try {
      setShowCreateForm(false);
      await onCreateWatchlist(newWatchlistName);
      setNewWatchlistName("");
    } catch (error2) {
      console.error("Error creating watchlist:", error2);
      setShowCreateForm(true);
    }
  };
  const handleUpdateWatchlistName = async (watchlistId) => {
    if (!editedName.trim()) {
      y.error("Watchlist name cannot be empty");
      return;
    }
    try {
      updateWatchlistMutation.mutate({ id: watchlistId, name: editedName });
      y.success("Watchlist updated successfully");
      setEditingWatchlistId("");
    } catch (error2) {
      console.error("Error updating watchlist name:", error2);
      y.error("Failed to update watchlist");
    }
  };
  const handleDeleteWatchlist = async (id) => {
    try {
      deleteWatchlistMutation.mutate(id);
      y.success("Watchlist deleted successfully");
      setShowDeleteConfirm("");
    } catch (error2) {
      console.error("Error deleting watchlist:", error2);
      y.error("Failed to delete watchlist");
    }
  };
  const handleCreateKeyDown = (e2) => {
    if (e2.key === "Enter") handleCreateWatchlist();
    if (e2.key === "Escape") {
      setShowCreateForm(false);
      setNewWatchlistName("");
    }
  };
  const handleEditKeyDown = (e2, watchlistId) => {
    if (e2.key === "Enter") handleUpdateWatchlistName(watchlistId);
    if (e2.key === "Escape") handleCancelEdit();
  };
  return /* @__PURE__ */ u$4("div", { className: "tab-bar", ref: tabBarRef, children: /* @__PURE__ */ u$4("div", { className: "tabs-container", children: [
    watchlists.map((watchlist) => /* @__PURE__ */ u$4(
      "div",
      {
        className: `tab ${activeWatchlistId === watchlist.id ? "active" : ""}`,
        onClick: () => onWatchlistSelect(watchlist.id),
        children: editingWatchlistId === watchlist.id ? /* @__PURE__ */ u$4(
          "div",
          {
            className: "tab-edit-form",
            onClick: (e2) => e2.stopPropagation(),
            children: /* @__PURE__ */ u$4(
              "input",
              {
                type: "text",
                value: editedName,
                onChange: (e2) => setEditedName(e2.currentTarget.value),
                className: "tab-edit-input",
                autoFocus: true,
                onKeyDown: (e2) => handleEditKeyDown(e2, watchlist.id),
                onBlur: () => handleUpdateWatchlistName(watchlist.id)
              }
            )
          }
        ) : /* @__PURE__ */ u$4(k$3, { children: [
          /* @__PURE__ */ u$4("div", { className: "tab-content", children: /* @__PURE__ */ u$4("span", { className: "tab-name", children: watchlist.name }) }),
          /* @__PURE__ */ u$4("div", { className: "tab-actions", children: [
            /* @__PURE__ */ u$4(
              "button",
              {
                className: "tab-action-btn edit-tab-btn",
                onClick: (e2) => {
                  e2.stopPropagation();
                  handleStartEdit(watchlist.id, watchlist.name);
                },
                children: /* @__PURE__ */ u$4(Pen, { size: 14 })
              }
            ),
            showDeleteConfirm === watchlist.id ? /* @__PURE__ */ u$4(
              "div",
              {
                className: "delete-confirm",
                onClick: (e2) => e2.stopPropagation(),
                children: [
                  /* @__PURE__ */ u$4(
                    "button",
                    {
                      className: "confirm-delete-btn",
                      onClick: () => handleDeleteWatchlist(watchlist.id),
                      children: /* @__PURE__ */ u$4(Check, { size: 16 })
                    }
                  ),
                  /* @__PURE__ */ u$4(
                    "button",
                    {
                      className: "cancel-delete-btn",
                      onClick: () => setShowDeleteConfirm(""),
                      children: /* @__PURE__ */ u$4(X, { size: 16 })
                    }
                  )
                ]
              }
            ) : /* @__PURE__ */ u$4(
              "button",
              {
                className: "tab-action-btn close-tab-btn",
                onClick: (e2) => {
                  e2.stopPropagation();
                  if (watchlists.length <= 1) return;
                  setShowDeleteConfirm(watchlist.id);
                },
                disabled: watchlists.length <= 1,
                children: /* @__PURE__ */ u$4(X, { size: 16 })
              }
            )
          ] })
        ] })
      },
      watchlist.id
    )),
    showCreateForm ? /* @__PURE__ */ u$4("div", { className: "add-tab-form", ref: addFormRef, children: [
      /* @__PURE__ */ u$4(
        "input",
        {
          type: "text",
          value: newWatchlistName,
          onChange: (e2) => setNewWatchlistName(e2.currentTarget.value),
          placeholder: "Watchlist name",
          className: "add-tab-input",
          autoFocus: true,
          onKeyDown: handleCreateKeyDown
        }
      ),
      /* @__PURE__ */ u$4("div", { className: "add-tab-actions", children: [
        /* @__PURE__ */ u$4(
          "button",
          {
            onClick: handleCreateWatchlist,
            className: "confirm-add-btn",
            children: /* @__PURE__ */ u$4(Check, { size: 16 })
          }
        ),
        /* @__PURE__ */ u$4(
          "button",
          {
            onClick: () => {
              setShowCreateForm(false);
              setNewWatchlistName("");
            },
            className: "cancel-add-btn",
            children: /* @__PURE__ */ u$4(X, { size: 16 })
          }
        )
      ] })
    ] }) : /* @__PURE__ */ u$4("button", { className: "add-tab-btn", onClick: handleAddStart, title: "Add new tab", children: /* @__PURE__ */ u$4(Plus, { size: 16 }) })
  ] }) });
};
function _extends() {
  return _extends = Object.assign ? Object.assign.bind() : function(n2) {
    for (var e2 = 1; e2 < arguments.length; e2++) {
      var t2 = arguments[e2];
      for (var r2 in t2) ({}).hasOwnProperty.call(t2, r2) && (n2[r2] = t2[r2]);
    }
    return n2;
  }, _extends.apply(null, arguments);
}
function _typeof(o2) {
  "@babel/helpers - typeof";
  return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o3) {
    return typeof o3;
  } : function(o3) {
    return o3 && "function" == typeof Symbol && o3.constructor === Symbol && o3 !== Symbol.prototype ? "symbol" : typeof o3;
  }, _typeof(o2);
}
function toPrimitive(t2, r2) {
  if ("object" != _typeof(t2) || !t2) return t2;
  var e2 = t2[Symbol.toPrimitive];
  if (void 0 !== e2) {
    var i2 = e2.call(t2, r2);
    if ("object" != _typeof(i2)) return i2;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r2 ? String : Number)(t2);
}
function toPropertyKey(t2) {
  var i2 = toPrimitive(t2, "string");
  return "symbol" == _typeof(i2) ? i2 : i2 + "";
}
function _defineProperty(e2, r2, t2) {
  return (r2 = toPropertyKey(r2)) in e2 ? Object.defineProperty(e2, r2, {
    value: t2,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e2[r2] = t2, e2;
}
function ownKeys(e2, r2) {
  var t2 = Object.keys(e2);
  if (Object.getOwnPropertySymbols) {
    var o2 = Object.getOwnPropertySymbols(e2);
    r2 && (o2 = o2.filter(function(r3) {
      return Object.getOwnPropertyDescriptor(e2, r3).enumerable;
    })), t2.push.apply(t2, o2);
  }
  return t2;
}
function _objectSpread2(e2) {
  for (var r2 = 1; r2 < arguments.length; r2++) {
    var t2 = null != arguments[r2] ? arguments[r2] : {};
    r2 % 2 ? ownKeys(Object(t2), true).forEach(function(r3) {
      _defineProperty(e2, r3, t2[r3]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e2, Object.getOwnPropertyDescriptors(t2)) : ownKeys(Object(t2)).forEach(function(r3) {
      Object.defineProperty(e2, r3, Object.getOwnPropertyDescriptor(t2, r3));
    });
  }
  return e2;
}
function _arrayWithHoles(r2) {
  if (Array.isArray(r2)) return r2;
}
function _iterableToArrayLimit(r2, l2) {
  var t2 = null == r2 ? null : "undefined" != typeof Symbol && r2[Symbol.iterator] || r2["@@iterator"];
  if (null != t2) {
    var e2, n2, i2, u2, a2 = [], f2 = true, o2 = false;
    try {
      if (i2 = (t2 = t2.call(r2)).next, 0 === l2) {
        if (Object(t2) !== t2) return;
        f2 = false;
      } else for (; !(f2 = (e2 = i2.call(t2)).done) && (a2.push(e2.value), a2.length !== l2); f2 = true) ;
    } catch (r3) {
      o2 = true, n2 = r3;
    } finally {
      try {
        if (!f2 && null != t2["return"] && (u2 = t2["return"](), Object(u2) !== u2)) return;
      } finally {
        if (o2) throw n2;
      }
    }
    return a2;
  }
}
function _arrayLikeToArray(r2, a2) {
  (null == a2 || a2 > r2.length) && (a2 = r2.length);
  for (var e2 = 0, n2 = Array(a2); e2 < a2; e2++) n2[e2] = r2[e2];
  return n2;
}
function _unsupportedIterableToArray(r2, a2) {
  if (r2) {
    if ("string" == typeof r2) return _arrayLikeToArray(r2, a2);
    var t2 = {}.toString.call(r2).slice(8, -1);
    return "Object" === t2 && r2.constructor && (t2 = r2.constructor.name), "Map" === t2 || "Set" === t2 ? Array.from(r2) : "Arguments" === t2 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t2) ? _arrayLikeToArray(r2, a2) : void 0;
  }
}
function _nonIterableRest() {
  throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _slicedToArray(r2, e2) {
  return _arrayWithHoles(r2) || _iterableToArrayLimit(r2, e2) || _unsupportedIterableToArray(r2, e2) || _nonIterableRest();
}
function _objectWithoutPropertiesLoose(r2, e2) {
  if (null == r2) return {};
  var t2 = {};
  for (var n2 in r2) if ({}.hasOwnProperty.call(r2, n2)) {
    if (-1 !== e2.indexOf(n2)) continue;
    t2[n2] = r2[n2];
  }
  return t2;
}
function _objectWithoutProperties(e2, t2) {
  if (null == e2) return {};
  var o2, r2, i2 = _objectWithoutPropertiesLoose(e2, t2);
  if (Object.getOwnPropertySymbols) {
    var n2 = Object.getOwnPropertySymbols(e2);
    for (r2 = 0; r2 < n2.length; r2++) o2 = n2[r2], -1 === t2.indexOf(o2) && {}.propertyIsEnumerable.call(e2, o2) && (i2[o2] = e2[o2]);
  }
  return i2;
}
var classnames = { exports: {} };
/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/
var hasRequiredClassnames;
function requireClassnames() {
  if (hasRequiredClassnames) return classnames.exports;
  hasRequiredClassnames = 1;
  (function(module) {
    (function() {
      var hasOwn = {}.hasOwnProperty;
      function classNames2() {
        var classes = "";
        for (var i2 = 0; i2 < arguments.length; i2++) {
          var arg = arguments[i2];
          if (arg) {
            classes = appendClass(classes, parseValue(arg));
          }
        }
        return classes;
      }
      function parseValue(arg) {
        if (typeof arg === "string" || typeof arg === "number") {
          return arg;
        }
        if (typeof arg !== "object") {
          return "";
        }
        if (Array.isArray(arg)) {
          return classNames2.apply(null, arg);
        }
        if (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes("[native code]")) {
          return arg.toString();
        }
        var classes = "";
        for (var key in arg) {
          if (hasOwn.call(arg, key) && arg[key]) {
            classes = appendClass(classes, key);
          }
        }
        return classes;
      }
      function appendClass(value2, newClass) {
        if (!newClass) {
          return value2;
        }
        if (value2) {
          return value2 + " " + newClass;
        }
        return value2 + newClass;
      }
      if (module.exports) {
        classNames2.default = classNames2;
        module.exports = classNames2;
      } else {
        window.classNames = classNames2;
      }
    })();
  })(classnames);
  return classnames.exports;
}
var classnamesExports = requireClassnames();
const classNames = /* @__PURE__ */ getDefaultExportFromCjs(classnamesExports);
var REACT_ELEMENT_TYPE_18 = Symbol.for("react.element");
var REACT_ELEMENT_TYPE_19 = Symbol.for("react.transitional.element");
var REACT_FRAGMENT_TYPE = Symbol.for("react.fragment");
function isFragment(object) {
  return (
    // Base object type
    object && _typeof(object) === "object" && // React Element type
    (object.$$typeof === REACT_ELEMENT_TYPE_18 || object.$$typeof === REACT_ELEMENT_TYPE_19) && // React Fragment type
    object.type === REACT_FRAGMENT_TYPE
  );
}
function toArray(children) {
  var option = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
  var ret = [];
  Rn.Children.forEach(children, function(child) {
    if ((child === void 0 || child === null) && !option.keepEmpty) {
      return;
    }
    if (Array.isArray(child)) {
      ret = ret.concat(toArray(child));
    } else if (isFragment(child) && child.props) {
      ret = ret.concat(toArray(child.props.children, option));
    } else {
      ret.push(child);
    }
  });
  return ret;
}
function isDOM(node) {
  return node instanceof HTMLElement || node instanceof SVGElement;
}
function getDOM(node) {
  if (node && _typeof(node) === "object" && isDOM(node.nativeElement)) {
    return node.nativeElement;
  }
  if (isDOM(node)) {
    return node;
  }
  return null;
}
function findDOMNode(node) {
  var domNode = getDOM(node);
  if (domNode) {
    return domNode;
  }
  if (node instanceof Rn.Component) {
    var _ReactDOM$findDOMNode;
    return (_ReactDOM$findDOMNode = Rn.findDOMNode) === null || _ReactDOM$findDOMNode === void 0 ? void 0 : _ReactDOM$findDOMNode.call(Rn, node);
  }
  return null;
}
var reactIs = { exports: {} };
var reactIs_production_min = {};
/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var hasRequiredReactIs_production_min;
function requireReactIs_production_min() {
  if (hasRequiredReactIs_production_min) return reactIs_production_min;
  hasRequiredReactIs_production_min = 1;
  var b2 = Symbol.for("react.element"), c2 = Symbol.for("react.portal"), d2 = Symbol.for("react.fragment"), e2 = Symbol.for("react.strict_mode"), f2 = Symbol.for("react.profiler"), g2 = Symbol.for("react.provider"), h2 = Symbol.for("react.context"), k2 = Symbol.for("react.server_context"), l2 = Symbol.for("react.forward_ref"), m2 = Symbol.for("react.suspense"), n2 = Symbol.for("react.suspense_list"), p2 = Symbol.for("react.memo"), q2 = Symbol.for("react.lazy"), t2 = Symbol.for("react.offscreen"), u2;
  u2 = Symbol.for("react.module.reference");
  function v2(a2) {
    if ("object" === typeof a2 && null !== a2) {
      var r2 = a2.$$typeof;
      switch (r2) {
        case b2:
          switch (a2 = a2.type, a2) {
            case d2:
            case f2:
            case e2:
            case m2:
            case n2:
              return a2;
            default:
              switch (a2 = a2 && a2.$$typeof, a2) {
                case k2:
                case h2:
                case l2:
                case q2:
                case p2:
                case g2:
                  return a2;
                default:
                  return r2;
              }
          }
        case c2:
          return r2;
      }
    }
  }
  reactIs_production_min.ContextConsumer = h2;
  reactIs_production_min.ContextProvider = g2;
  reactIs_production_min.Element = b2;
  reactIs_production_min.ForwardRef = l2;
  reactIs_production_min.Fragment = d2;
  reactIs_production_min.Lazy = q2;
  reactIs_production_min.Memo = p2;
  reactIs_production_min.Portal = c2;
  reactIs_production_min.Profiler = f2;
  reactIs_production_min.StrictMode = e2;
  reactIs_production_min.Suspense = m2;
  reactIs_production_min.SuspenseList = n2;
  reactIs_production_min.isAsyncMode = function() {
    return false;
  };
  reactIs_production_min.isConcurrentMode = function() {
    return false;
  };
  reactIs_production_min.isContextConsumer = function(a2) {
    return v2(a2) === h2;
  };
  reactIs_production_min.isContextProvider = function(a2) {
    return v2(a2) === g2;
  };
  reactIs_production_min.isElement = function(a2) {
    return "object" === typeof a2 && null !== a2 && a2.$$typeof === b2;
  };
  reactIs_production_min.isForwardRef = function(a2) {
    return v2(a2) === l2;
  };
  reactIs_production_min.isFragment = function(a2) {
    return v2(a2) === d2;
  };
  reactIs_production_min.isLazy = function(a2) {
    return v2(a2) === q2;
  };
  reactIs_production_min.isMemo = function(a2) {
    return v2(a2) === p2;
  };
  reactIs_production_min.isPortal = function(a2) {
    return v2(a2) === c2;
  };
  reactIs_production_min.isProfiler = function(a2) {
    return v2(a2) === f2;
  };
  reactIs_production_min.isStrictMode = function(a2) {
    return v2(a2) === e2;
  };
  reactIs_production_min.isSuspense = function(a2) {
    return v2(a2) === m2;
  };
  reactIs_production_min.isSuspenseList = function(a2) {
    return v2(a2) === n2;
  };
  reactIs_production_min.isValidElementType = function(a2) {
    return "string" === typeof a2 || "function" === typeof a2 || a2 === d2 || a2 === f2 || a2 === e2 || a2 === m2 || a2 === n2 || a2 === t2 || "object" === typeof a2 && null !== a2 && (a2.$$typeof === q2 || a2.$$typeof === p2 || a2.$$typeof === g2 || a2.$$typeof === h2 || a2.$$typeof === l2 || a2.$$typeof === u2 || void 0 !== a2.getModuleId) ? true : false;
  };
  reactIs_production_min.typeOf = v2;
  return reactIs_production_min;
}
var hasRequiredReactIs;
function requireReactIs() {
  if (hasRequiredReactIs) return reactIs.exports;
  hasRequiredReactIs = 1;
  {
    reactIs.exports = requireReactIs_production_min();
  }
  return reactIs.exports;
}
var reactIsExports = requireReactIs();
function useMemo(getValue, condition, shouldUpdate) {
  var cacheRef = A$2({});
  if (!("value" in cacheRef.current) || shouldUpdate(cacheRef.current.condition, condition)) {
    cacheRef.current.value = getValue();
    cacheRef.current.condition = condition;
  }
  return cacheRef.current.value;
}
var ReactMajorVersion = Number(vn.split(".")[0]);
var fillRef = function fillRef2(ref, node) {
  if (typeof ref === "function") {
    ref(node);
  } else if (_typeof(ref) === "object" && ref && "current" in ref) {
    ref.current = node;
  }
};
var composeRef = function composeRef2() {
  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {
    refs[_key] = arguments[_key];
  }
  var refList = refs.filter(Boolean);
  if (refList.length <= 1) {
    return refList[0];
  }
  return function(node) {
    refs.forEach(function(ref) {
      fillRef(ref, node);
    });
  };
};
var useComposeRef = function useComposeRef2() {
  for (var _len2 = arguments.length, refs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
    refs[_key2] = arguments[_key2];
  }
  return useMemo(function() {
    return composeRef.apply(void 0, refs);
  }, refs, function(prev, next) {
    return prev.length !== next.length || prev.every(function(ref, i2) {
      return ref !== next[i2];
    });
  });
};
var supportRef = function supportRef2(nodeOrComponent) {
  var _type$prototype, _nodeOrComponent$prot;
  if (!nodeOrComponent) {
    return false;
  }
  if (isReactElement(nodeOrComponent) && ReactMajorVersion >= 19) {
    return true;
  }
  var type2 = reactIsExports.isMemo(nodeOrComponent) ? nodeOrComponent.type.type : nodeOrComponent.type;
  if (typeof type2 === "function" && !((_type$prototype = type2.prototype) !== null && _type$prototype !== void 0 && _type$prototype.render) && type2.$$typeof !== reactIsExports.ForwardRef) {
    return false;
  }
  if (typeof nodeOrComponent === "function" && !((_nodeOrComponent$prot = nodeOrComponent.prototype) !== null && _nodeOrComponent$prot !== void 0 && _nodeOrComponent$prot.render) && nodeOrComponent.$$typeof !== reactIsExports.ForwardRef) {
    return false;
  }
  return true;
};
function isReactElement(node) {
  return /* @__PURE__ */ mn(node) && !isFragment(node);
}
var getNodeRef = function getNodeRef2(node) {
  if (node && isReactElement(node)) {
    var ele = node;
    return ele.props.propertyIsEnumerable("ref") ? ele.props.ref : ele.ref;
  }
  return null;
};
var CollectionContext = /* @__PURE__ */ K$1(null);
function Collection(_ref) {
  var children = _ref.children, onBatchResize = _ref.onBatchResize;
  var resizeIdRef = A$2(0);
  var resizeInfosRef = A$2([]);
  var onCollectionResize = x$1(CollectionContext);
  var onResize2 = q$2(function(size, element, data) {
    resizeIdRef.current += 1;
    var currentId = resizeIdRef.current;
    resizeInfosRef.current.push({
      size,
      element,
      data
    });
    Promise.resolve().then(function() {
      if (currentId === resizeIdRef.current) {
        onBatchResize === null || onBatchResize === void 0 || onBatchResize(resizeInfosRef.current);
        resizeInfosRef.current = [];
      }
    });
    onCollectionResize === null || onCollectionResize === void 0 || onCollectionResize(size, element, data);
  }, [onBatchResize, onCollectionResize]);
  return /* @__PURE__ */ _$3(CollectionContext.Provider, {
    value: onResize2
  }, children);
}
var MapShim = function() {
  if (typeof Map !== "undefined") {
    return Map;
  }
  function getIndex(arr, key) {
    var result = -1;
    arr.some(function(entry, index2) {
      if (entry[0] === key) {
        result = index2;
        return true;
      }
      return false;
    });
    return result;
  }
  return (
    /** @class */
    function() {
      function class_1() {
        this.__entries__ = [];
      }
      Object.defineProperty(class_1.prototype, "size", {
        /**
         * @returns {boolean}
         */
        get: function() {
          return this.__entries__.length;
        },
        enumerable: true,
        configurable: true
      });
      class_1.prototype.get = function(key) {
        var index2 = getIndex(this.__entries__, key);
        var entry = this.__entries__[index2];
        return entry && entry[1];
      };
      class_1.prototype.set = function(key, value2) {
        var index2 = getIndex(this.__entries__, key);
        if (~index2) {
          this.__entries__[index2][1] = value2;
        } else {
          this.__entries__.push([key, value2]);
        }
      };
      class_1.prototype.delete = function(key) {
        var entries = this.__entries__;
        var index2 = getIndex(entries, key);
        if (~index2) {
          entries.splice(index2, 1);
        }
      };
      class_1.prototype.has = function(key) {
        return !!~getIndex(this.__entries__, key);
      };
      class_1.prototype.clear = function() {
        this.__entries__.splice(0);
      };
      class_1.prototype.forEach = function(callback, ctx) {
        if (ctx === void 0) {
          ctx = null;
        }
        for (var _i = 0, _a2 = this.__entries__; _i < _a2.length; _i++) {
          var entry = _a2[_i];
          callback.call(ctx, entry[1], entry[0]);
        }
      };
      return class_1;
    }()
  );
}();
var isBrowser = typeof window !== "undefined" && typeof document !== "undefined" && window.document === document;
var global$1 = function() {
  if (typeof global !== "undefined" && global.Math === Math) {
    return global;
  }
  if (typeof self !== "undefined" && self.Math === Math) {
    return self;
  }
  if (typeof window !== "undefined" && window.Math === Math) {
    return window;
  }
  return Function("return this")();
}();
var requestAnimationFrame$1 = function() {
  if (typeof requestAnimationFrame === "function") {
    return requestAnimationFrame.bind(global$1);
  }
  return function(callback) {
    return setTimeout(function() {
      return callback(Date.now());
    }, 1e3 / 60);
  };
}();
var trailingTimeout = 2;
function throttle(callback, delay) {
  var leadingCall = false, trailingCall = false, lastCallTime = 0;
  function resolvePending() {
    if (leadingCall) {
      leadingCall = false;
      callback();
    }
    if (trailingCall) {
      proxy();
    }
  }
  function timeoutCallback() {
    requestAnimationFrame$1(resolvePending);
  }
  function proxy() {
    var timeStamp = Date.now();
    if (leadingCall) {
      if (timeStamp - lastCallTime < trailingTimeout) {
        return;
      }
      trailingCall = true;
    } else {
      leadingCall = true;
      trailingCall = false;
      setTimeout(timeoutCallback, delay);
    }
    lastCallTime = timeStamp;
  }
  return proxy;
}
var REFRESH_DELAY = 20;
var transitionKeys = ["top", "right", "bottom", "left", "width", "height", "size", "weight"];
var mutationObserverSupported = typeof MutationObserver !== "undefined";
var ResizeObserverController = (
  /** @class */
  function() {
    function ResizeObserverController2() {
      this.connected_ = false;
      this.mutationEventsAdded_ = false;
      this.mutationsObserver_ = null;
      this.observers_ = [];
      this.onTransitionEnd_ = this.onTransitionEnd_.bind(this);
      this.refresh = throttle(this.refresh.bind(this), REFRESH_DELAY);
    }
    ResizeObserverController2.prototype.addObserver = function(observer) {
      if (!~this.observers_.indexOf(observer)) {
        this.observers_.push(observer);
      }
      if (!this.connected_) {
        this.connect_();
      }
    };
    ResizeObserverController2.prototype.removeObserver = function(observer) {
      var observers2 = this.observers_;
      var index2 = observers2.indexOf(observer);
      if (~index2) {
        observers2.splice(index2, 1);
      }
      if (!observers2.length && this.connected_) {
        this.disconnect_();
      }
    };
    ResizeObserverController2.prototype.refresh = function() {
      var changesDetected = this.updateObservers_();
      if (changesDetected) {
        this.refresh();
      }
    };
    ResizeObserverController2.prototype.updateObservers_ = function() {
      var activeObservers = this.observers_.filter(function(observer) {
        return observer.gatherActive(), observer.hasActive();
      });
      activeObservers.forEach(function(observer) {
        return observer.broadcastActive();
      });
      return activeObservers.length > 0;
    };
    ResizeObserverController2.prototype.connect_ = function() {
      if (!isBrowser || this.connected_) {
        return;
      }
      document.addEventListener("transitionend", this.onTransitionEnd_);
      window.addEventListener("resize", this.refresh);
      if (mutationObserverSupported) {
        this.mutationsObserver_ = new MutationObserver(this.refresh);
        this.mutationsObserver_.observe(document, {
          attributes: true,
          childList: true,
          characterData: true,
          subtree: true
        });
      } else {
        document.addEventListener("DOMSubtreeModified", this.refresh);
        this.mutationEventsAdded_ = true;
      }
      this.connected_ = true;
    };
    ResizeObserverController2.prototype.disconnect_ = function() {
      if (!isBrowser || !this.connected_) {
        return;
      }
      document.removeEventListener("transitionend", this.onTransitionEnd_);
      window.removeEventListener("resize", this.refresh);
      if (this.mutationsObserver_) {
        this.mutationsObserver_.disconnect();
      }
      if (this.mutationEventsAdded_) {
        document.removeEventListener("DOMSubtreeModified", this.refresh);
      }
      this.mutationsObserver_ = null;
      this.mutationEventsAdded_ = false;
      this.connected_ = false;
    };
    ResizeObserverController2.prototype.onTransitionEnd_ = function(_a2) {
      var _b2 = _a2.propertyName, propertyName = _b2 === void 0 ? "" : _b2;
      var isReflowProperty = transitionKeys.some(function(key) {
        return !!~propertyName.indexOf(key);
      });
      if (isReflowProperty) {
        this.refresh();
      }
    };
    ResizeObserverController2.getInstance = function() {
      if (!this.instance_) {
        this.instance_ = new ResizeObserverController2();
      }
      return this.instance_;
    };
    ResizeObserverController2.instance_ = null;
    return ResizeObserverController2;
  }()
);
var defineConfigurable = function(target, props) {
  for (var _i = 0, _a2 = Object.keys(props); _i < _a2.length; _i++) {
    var key = _a2[_i];
    Object.defineProperty(target, key, {
      value: props[key],
      enumerable: false,
      writable: false,
      configurable: true
    });
  }
  return target;
};
var getWindowOf = function(target) {
  var ownerGlobal = target && target.ownerDocument && target.ownerDocument.defaultView;
  return ownerGlobal || global$1;
};
var emptyRect = createRectInit(0, 0, 0, 0);
function toFloat(value2) {
  return parseFloat(value2) || 0;
}
function getBordersSize(styles) {
  var positions = [];
  for (var _i = 1; _i < arguments.length; _i++) {
    positions[_i - 1] = arguments[_i];
  }
  return positions.reduce(function(size, position) {
    var value2 = styles["border-" + position + "-width"];
    return size + toFloat(value2);
  }, 0);
}
function getPaddings(styles) {
  var positions = ["top", "right", "bottom", "left"];
  var paddings = {};
  for (var _i = 0, positions_1 = positions; _i < positions_1.length; _i++) {
    var position = positions_1[_i];
    var value2 = styles["padding-" + position];
    paddings[position] = toFloat(value2);
  }
  return paddings;
}
function getSVGContentRect(target) {
  var bbox = target.getBBox();
  return createRectInit(0, 0, bbox.width, bbox.height);
}
function getHTMLElementContentRect(target) {
  var clientWidth = target.clientWidth, clientHeight = target.clientHeight;
  if (!clientWidth && !clientHeight) {
    return emptyRect;
  }
  var styles = getWindowOf(target).getComputedStyle(target);
  var paddings = getPaddings(styles);
  var horizPad = paddings.left + paddings.right;
  var vertPad = paddings.top + paddings.bottom;
  var width = toFloat(styles.width), height = toFloat(styles.height);
  if (styles.boxSizing === "border-box") {
    if (Math.round(width + horizPad) !== clientWidth) {
      width -= getBordersSize(styles, "left", "right") + horizPad;
    }
    if (Math.round(height + vertPad) !== clientHeight) {
      height -= getBordersSize(styles, "top", "bottom") + vertPad;
    }
  }
  if (!isDocumentElement(target)) {
    var vertScrollbar = Math.round(width + horizPad) - clientWidth;
    var horizScrollbar = Math.round(height + vertPad) - clientHeight;
    if (Math.abs(vertScrollbar) !== 1) {
      width -= vertScrollbar;
    }
    if (Math.abs(horizScrollbar) !== 1) {
      height -= horizScrollbar;
    }
  }
  return createRectInit(paddings.left, paddings.top, width, height);
}
var isSVGGraphicsElement = function() {
  if (typeof SVGGraphicsElement !== "undefined") {
    return function(target) {
      return target instanceof getWindowOf(target).SVGGraphicsElement;
    };
  }
  return function(target) {
    return target instanceof getWindowOf(target).SVGElement && typeof target.getBBox === "function";
  };
}();
function isDocumentElement(target) {
  return target === getWindowOf(target).document.documentElement;
}
function getContentRect(target) {
  if (!isBrowser) {
    return emptyRect;
  }
  if (isSVGGraphicsElement(target)) {
    return getSVGContentRect(target);
  }
  return getHTMLElementContentRect(target);
}
function createReadOnlyRect(_a2) {
  var x2 = _a2.x, y2 = _a2.y, width = _a2.width, height = _a2.height;
  var Constr = typeof DOMRectReadOnly !== "undefined" ? DOMRectReadOnly : Object;
  var rect = Object.create(Constr.prototype);
  defineConfigurable(rect, {
    x: x2,
    y: y2,
    width,
    height,
    top: y2,
    right: x2 + width,
    bottom: height + y2,
    left: x2
  });
  return rect;
}
function createRectInit(x2, y2, width, height) {
  return { x: x2, y: y2, width, height };
}
var ResizeObservation = (
  /** @class */
  function() {
    function ResizeObservation2(target) {
      this.broadcastWidth = 0;
      this.broadcastHeight = 0;
      this.contentRect_ = createRectInit(0, 0, 0, 0);
      this.target = target;
    }
    ResizeObservation2.prototype.isActive = function() {
      var rect = getContentRect(this.target);
      this.contentRect_ = rect;
      return rect.width !== this.broadcastWidth || rect.height !== this.broadcastHeight;
    };
    ResizeObservation2.prototype.broadcastRect = function() {
      var rect = this.contentRect_;
      this.broadcastWidth = rect.width;
      this.broadcastHeight = rect.height;
      return rect;
    };
    return ResizeObservation2;
  }()
);
var ResizeObserverEntry = (
  /** @class */
  /* @__PURE__ */ function() {
    function ResizeObserverEntry2(target, rectInit) {
      var contentRect = createReadOnlyRect(rectInit);
      defineConfigurable(this, { target, contentRect });
    }
    return ResizeObserverEntry2;
  }()
);
var ResizeObserverSPI = (
  /** @class */
  function() {
    function ResizeObserverSPI2(callback, controller, callbackCtx) {
      this.activeObservations_ = [];
      this.observations_ = new MapShim();
      if (typeof callback !== "function") {
        throw new TypeError("The callback provided as parameter 1 is not a function.");
      }
      this.callback_ = callback;
      this.controller_ = controller;
      this.callbackCtx_ = callbackCtx;
    }
    ResizeObserverSPI2.prototype.observe = function(target) {
      if (!arguments.length) {
        throw new TypeError("1 argument required, but only 0 present.");
      }
      if (typeof Element === "undefined" || !(Element instanceof Object)) {
        return;
      }
      if (!(target instanceof getWindowOf(target).Element)) {
        throw new TypeError('parameter 1 is not of type "Element".');
      }
      var observations = this.observations_;
      if (observations.has(target)) {
        return;
      }
      observations.set(target, new ResizeObservation(target));
      this.controller_.addObserver(this);
      this.controller_.refresh();
    };
    ResizeObserverSPI2.prototype.unobserve = function(target) {
      if (!arguments.length) {
        throw new TypeError("1 argument required, but only 0 present.");
      }
      if (typeof Element === "undefined" || !(Element instanceof Object)) {
        return;
      }
      if (!(target instanceof getWindowOf(target).Element)) {
        throw new TypeError('parameter 1 is not of type "Element".');
      }
      var observations = this.observations_;
      if (!observations.has(target)) {
        return;
      }
      observations.delete(target);
      if (!observations.size) {
        this.controller_.removeObserver(this);
      }
    };
    ResizeObserverSPI2.prototype.disconnect = function() {
      this.clearActive();
      this.observations_.clear();
      this.controller_.removeObserver(this);
    };
    ResizeObserverSPI2.prototype.gatherActive = function() {
      var _this = this;
      this.clearActive();
      this.observations_.forEach(function(observation) {
        if (observation.isActive()) {
          _this.activeObservations_.push(observation);
        }
      });
    };
    ResizeObserverSPI2.prototype.broadcastActive = function() {
      if (!this.hasActive()) {
        return;
      }
      var ctx = this.callbackCtx_;
      var entries = this.activeObservations_.map(function(observation) {
        return new ResizeObserverEntry(observation.target, observation.broadcastRect());
      });
      this.callback_.call(ctx, entries, ctx);
      this.clearActive();
    };
    ResizeObserverSPI2.prototype.clearActive = function() {
      this.activeObservations_.splice(0);
    };
    ResizeObserverSPI2.prototype.hasActive = function() {
      return this.activeObservations_.length > 0;
    };
    return ResizeObserverSPI2;
  }()
);
var observers = typeof WeakMap !== "undefined" ? /* @__PURE__ */ new WeakMap() : new MapShim();
var ResizeObserver$1 = (
  /** @class */
  /* @__PURE__ */ function() {
    function ResizeObserver2(callback) {
      if (!(this instanceof ResizeObserver2)) {
        throw new TypeError("Cannot call a class as a function.");
      }
      if (!arguments.length) {
        throw new TypeError("1 argument required, but only 0 present.");
      }
      var controller = ResizeObserverController.getInstance();
      var observer = new ResizeObserverSPI(callback, controller, this);
      observers.set(this, observer);
    }
    return ResizeObserver2;
  }()
);
[
  "observe",
  "unobserve",
  "disconnect"
].forEach(function(method) {
  ResizeObserver$1.prototype[method] = function() {
    var _a2;
    return (_a2 = observers.get(this))[method].apply(_a2, arguments);
  };
});
var index = function() {
  if (typeof global$1.ResizeObserver !== "undefined") {
    return global$1.ResizeObserver;
  }
  return ResizeObserver$1;
}();
var elementListeners = /* @__PURE__ */ new Map();
function onResize(entities) {
  entities.forEach(function(entity) {
    var _elementListeners$get;
    var target = entity.target;
    (_elementListeners$get = elementListeners.get(target)) === null || _elementListeners$get === void 0 || _elementListeners$get.forEach(function(listener) {
      return listener(target);
    });
  });
}
var resizeObserver = new index(onResize);
function observe(element, callback) {
  if (!elementListeners.has(element)) {
    elementListeners.set(element, /* @__PURE__ */ new Set());
    resizeObserver.observe(element);
  }
  elementListeners.get(element).add(callback);
}
function unobserve(element, callback) {
  if (elementListeners.has(element)) {
    elementListeners.get(element).delete(callback);
    if (!elementListeners.get(element).size) {
      resizeObserver.unobserve(element);
      elementListeners.delete(element);
    }
  }
}
function _classCallCheck(a2, n2) {
  if (!(a2 instanceof n2)) throw new TypeError("Cannot call a class as a function");
}
function _defineProperties(e2, r2) {
  for (var t2 = 0; t2 < r2.length; t2++) {
    var o2 = r2[t2];
    o2.enumerable = o2.enumerable || false, o2.configurable = true, "value" in o2 && (o2.writable = true), Object.defineProperty(e2, toPropertyKey(o2.key), o2);
  }
}
function _createClass(e2, r2, t2) {
  return r2 && _defineProperties(e2.prototype, r2), Object.defineProperty(e2, "prototype", {
    writable: false
  }), e2;
}
function _setPrototypeOf(t2, e2) {
  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(t3, e3) {
    return t3.__proto__ = e3, t3;
  }, _setPrototypeOf(t2, e2);
}
function _inherits(t2, e2) {
  if ("function" != typeof e2 && null !== e2) throw new TypeError("Super expression must either be null or a function");
  t2.prototype = Object.create(e2 && e2.prototype, {
    constructor: {
      value: t2,
      writable: true,
      configurable: true
    }
  }), Object.defineProperty(t2, "prototype", {
    writable: false
  }), e2 && _setPrototypeOf(t2, e2);
}
function _getPrototypeOf(t2) {
  return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(t3) {
    return t3.__proto__ || Object.getPrototypeOf(t3);
  }, _getPrototypeOf(t2);
}
function _isNativeReflectConstruct() {
  try {
    var t2 = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
  } catch (t3) {
  }
  return (_isNativeReflectConstruct = function _isNativeReflectConstruct2() {
    return !!t2;
  })();
}
function _assertThisInitialized(e2) {
  if (void 0 === e2) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
  return e2;
}
function _possibleConstructorReturn(t2, e2) {
  if (e2 && ("object" == _typeof(e2) || "function" == typeof e2)) return e2;
  if (void 0 !== e2) throw new TypeError("Derived constructors may only return object or undefined");
  return _assertThisInitialized(t2);
}
function _createSuper(t2) {
  var r2 = _isNativeReflectConstruct();
  return function() {
    var e2, o2 = _getPrototypeOf(t2);
    if (r2) {
      var s2 = _getPrototypeOf(this).constructor;
      e2 = Reflect.construct(o2, arguments, s2);
    } else e2 = o2.apply(this, arguments);
    return _possibleConstructorReturn(this, e2);
  };
}
var DomWrapper = /* @__PURE__ */ function(_React$Component) {
  _inherits(DomWrapper2, _React$Component);
  var _super = _createSuper(DomWrapper2);
  function DomWrapper2() {
    _classCallCheck(this, DomWrapper2);
    return _super.apply(this, arguments);
  }
  _createClass(DomWrapper2, [{
    key: "render",
    value: function render() {
      return this.props.children;
    }
  }]);
  return DomWrapper2;
}(x$2);
function SingleObserver(props, ref) {
  var children = props.children, disabled = props.disabled;
  var elementRef = A$2(null);
  var wrapperRef = A$2(null);
  var onCollectionResize = x$1(CollectionContext);
  var isRenderProps = typeof children === "function";
  var mergedChildren = isRenderProps ? children(elementRef) : children;
  var sizeRef = A$2({
    width: -1,
    height: -1,
    offsetWidth: -1,
    offsetHeight: -1
  });
  var canRef = !isRenderProps && /* @__PURE__ */ mn(mergedChildren) && supportRef(mergedChildren);
  var originRef = canRef ? getNodeRef(mergedChildren) : null;
  var mergedRef = useComposeRef(originRef, elementRef);
  var getDom = function getDom2() {
    var _elementRef$current;
    return findDOMNode(elementRef.current) || // Support `nativeElement` format
    (elementRef.current && _typeof(elementRef.current) === "object" ? findDOMNode((_elementRef$current = elementRef.current) === null || _elementRef$current === void 0 ? void 0 : _elementRef$current.nativeElement) : null) || findDOMNode(wrapperRef.current);
  };
  F$3(ref, function() {
    return getDom();
  });
  var propsRef = A$2(props);
  propsRef.current = props;
  var onInternalResize = q$2(function(target) {
    var _propsRef$current = propsRef.current, onResize2 = _propsRef$current.onResize, data = _propsRef$current.data;
    var _target$getBoundingCl = target.getBoundingClientRect(), width = _target$getBoundingCl.width, height = _target$getBoundingCl.height;
    var offsetWidth = target.offsetWidth, offsetHeight = target.offsetHeight;
    var fixedWidth = Math.floor(width);
    var fixedHeight = Math.floor(height);
    if (sizeRef.current.width !== fixedWidth || sizeRef.current.height !== fixedHeight || sizeRef.current.offsetWidth !== offsetWidth || sizeRef.current.offsetHeight !== offsetHeight) {
      var size = {
        width: fixedWidth,
        height: fixedHeight,
        offsetWidth,
        offsetHeight
      };
      sizeRef.current = size;
      var mergedOffsetWidth = offsetWidth === Math.round(width) ? width : offsetWidth;
      var mergedOffsetHeight = offsetHeight === Math.round(height) ? height : offsetHeight;
      var sizeInfo = _objectSpread2(_objectSpread2({}, size), {}, {
        offsetWidth: mergedOffsetWidth,
        offsetHeight: mergedOffsetHeight
      });
      onCollectionResize === null || onCollectionResize === void 0 || onCollectionResize(sizeInfo, target, data);
      if (onResize2) {
        Promise.resolve().then(function() {
          onResize2(sizeInfo, target);
        });
      }
    }
  }, []);
  y$3(function() {
    var currentElement = getDom();
    if (currentElement && !disabled) {
      observe(currentElement, onInternalResize);
    }
    return function() {
      return unobserve(currentElement, onInternalResize);
    };
  }, [elementRef.current, disabled]);
  return /* @__PURE__ */ _$3(DomWrapper, {
    ref: wrapperRef
  }, canRef ? /* @__PURE__ */ _n(mergedChildren, {
    ref: mergedRef
  }) : mergedChildren);
}
var RefSingleObserver = /* @__PURE__ */ D(SingleObserver);
var INTERNAL_PREFIX_KEY = "rc-observer-key";
function ResizeObserver(props, ref) {
  var children = props.children;
  var childNodes = typeof children === "function" ? [children] : toArray(children);
  return childNodes.map(function(child, index2) {
    var key = (child === null || child === void 0 ? void 0 : child.key) || "".concat(INTERNAL_PREFIX_KEY, "-").concat(index2);
    return /* @__PURE__ */ _$3(RefSingleObserver, _extends({}, props, {
      key,
      ref: index2 === 0 ? ref : void 0
    }), child);
  });
}
var RefResizeObserver = /* @__PURE__ */ D(ResizeObserver);
RefResizeObserver.Collection = Collection;
function useEvent(callback) {
  var fnRef = A$2();
  fnRef.current = callback;
  var memoFn = q$2(function() {
    var _fnRef$current;
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    return (_fnRef$current = fnRef.current) === null || _fnRef$current === void 0 ? void 0 : _fnRef$current.call.apply(_fnRef$current, [fnRef].concat(args));
  }, []);
  return memoFn;
}
function canUseDom() {
  return !!(typeof window !== "undefined" && window.document && window.document.createElement);
}
var useInternalLayoutEffect = canUseDom() ? _$2 : y$3;
var useLayoutEffect = function useLayoutEffect2(callback, deps) {
  var firstMountRef = A$2(true);
  useInternalLayoutEffect(function() {
    return callback(firstMountRef.current);
  }, deps);
  useInternalLayoutEffect(function() {
    firstMountRef.current = false;
    return function() {
      firstMountRef.current = true;
    };
  }, []);
};
var Filler = /* @__PURE__ */ D(function(_ref, ref) {
  var height = _ref.height, offsetY = _ref.offsetY, offsetX = _ref.offsetX, children = _ref.children, prefixCls = _ref.prefixCls, onInnerResize = _ref.onInnerResize, innerProps = _ref.innerProps, rtl = _ref.rtl, extra = _ref.extra;
  var outerStyle = {};
  var innerStyle = {
    display: "flex",
    flexDirection: "column"
  };
  if (offsetY !== void 0) {
    outerStyle = {
      height,
      position: "relative",
      overflow: "hidden"
    };
    innerStyle = _objectSpread2(_objectSpread2({}, innerStyle), {}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({
      transform: "translateY(".concat(offsetY, "px)")
    }, rtl ? "marginRight" : "marginLeft", -offsetX), "position", "absolute"), "left", 0), "right", 0), "top", 0));
  }
  return /* @__PURE__ */ _$3("div", {
    style: outerStyle
  }, /* @__PURE__ */ _$3(RefResizeObserver, {
    onResize: function onResize2(_ref2) {
      var offsetHeight = _ref2.offsetHeight;
      if (offsetHeight && onInnerResize) {
        onInnerResize();
      }
    }
  }, /* @__PURE__ */ _$3("div", _extends({
    style: innerStyle,
    className: classNames(_defineProperty({}, "".concat(prefixCls, "-holder-inner"), prefixCls)),
    ref
  }, innerProps), children, extra)));
});
Filler.displayName = "Filler";
function Item(_ref) {
  var children = _ref.children, setRef = _ref.setRef;
  var refFunc = q$2(function(node) {
    setRef(node);
  }, []);
  return /* @__PURE__ */ _n(children, {
    ref: refFunc
  });
}
function useChildren(list, startIndex, endIndex, scrollWidth, offsetX, setNodeRef, renderFunc, _ref) {
  var getKey = _ref.getKey;
  return list.slice(startIndex, endIndex + 1).map(function(item, index2) {
    var eleIndex = startIndex + index2;
    var node = renderFunc(item, eleIndex, {
      style: {
        width: scrollWidth
      },
      offsetX
    });
    var key = getKey(item);
    return /* @__PURE__ */ _$3(Item, {
      key,
      setRef: function setRef(ele) {
        return setNodeRef(item, ele);
      }
    }, node);
  });
}
function findListDiffIndex(originList, targetList, getKey) {
  var originLen = originList.length;
  var targetLen = targetList.length;
  var shortList;
  var longList;
  if (originLen === 0 && targetLen === 0) {
    return null;
  }
  if (originLen < targetLen) {
    shortList = originList;
    longList = targetList;
  } else {
    shortList = targetList;
    longList = originList;
  }
  var notExistKey = {
    __EMPTY_ITEM__: true
  };
  function getItemKey(item) {
    if (item !== void 0) {
      return getKey(item);
    }
    return notExistKey;
  }
  var diffIndex = null;
  var multiple = Math.abs(originLen - targetLen) !== 1;
  for (var i2 = 0; i2 < longList.length; i2 += 1) {
    var shortKey = getItemKey(shortList[i2]);
    var longKey = getItemKey(longList[i2]);
    if (shortKey !== longKey) {
      diffIndex = i2;
      multiple = multiple || shortKey !== getItemKey(longList[i2 + 1]);
      break;
    }
  }
  return diffIndex === null ? null : {
    index: diffIndex,
    multiple
  };
}
function useDiffItem(data, getKey, onDiff) {
  var _React$useState = d$3(data), _React$useState2 = _slicedToArray(_React$useState, 2), prevData = _React$useState2[0], setPrevData = _React$useState2[1];
  var _React$useState3 = d$3(null), _React$useState4 = _slicedToArray(_React$useState3, 2), diffItem = _React$useState4[0], setDiffItem = _React$useState4[1];
  y$3(function() {
    var diff = findListDiffIndex(prevData || [], data || [], getKey);
    if ((diff === null || diff === void 0 ? void 0 : diff.index) !== void 0) {
      setDiffItem(data[diff.index]);
    }
    setPrevData(data);
  }, [data]);
  return [diffItem];
}
var raf = function raf2(callback) {
  return +setTimeout(callback, 16);
};
var caf = function caf2(num) {
  return clearTimeout(num);
};
if (typeof window !== "undefined" && "requestAnimationFrame" in window) {
  raf = function raf3(callback) {
    return window.requestAnimationFrame(callback);
  };
  caf = function caf3(handle) {
    return window.cancelAnimationFrame(handle);
  };
}
var rafUUID = 0;
var rafIds = /* @__PURE__ */ new Map();
function cleanup(id) {
  rafIds.delete(id);
}
var wrapperRaf = function wrapperRaf2(callback) {
  var times = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;
  rafUUID += 1;
  var id = rafUUID;
  function callRef(leftTimes) {
    if (leftTimes === 0) {
      cleanup(id);
      callback();
    } else {
      var realId = raf(function() {
        callRef(leftTimes - 1);
      });
      rafIds.set(id, realId);
    }
  }
  callRef(times);
  return id;
};
wrapperRaf.cancel = function(id) {
  var realId = rafIds.get(id);
  cleanup(id);
  return caf(realId);
};
var isFF = (typeof navigator === "undefined" ? "undefined" : _typeof(navigator)) === "object" && /Firefox/i.test(navigator.userAgent);
const useOriginScroll = function(isScrollAtTop, isScrollAtBottom, isScrollAtLeft, isScrollAtRight) {
  var lockRef = A$2(false);
  var lockTimeoutRef = A$2(null);
  function lockScroll() {
    clearTimeout(lockTimeoutRef.current);
    lockRef.current = true;
    lockTimeoutRef.current = setTimeout(function() {
      lockRef.current = false;
    }, 50);
  }
  var scrollPingRef = A$2({
    top: isScrollAtTop,
    bottom: isScrollAtBottom,
    left: isScrollAtLeft,
    right: isScrollAtRight
  });
  scrollPingRef.current.top = isScrollAtTop;
  scrollPingRef.current.bottom = isScrollAtBottom;
  scrollPingRef.current.left = isScrollAtLeft;
  scrollPingRef.current.right = isScrollAtRight;
  return function(isHorizontal, delta) {
    var smoothOffset = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
    var originScroll = isHorizontal ? (
      // Pass origin wheel when on the left
      delta < 0 && scrollPingRef.current.left || // Pass origin wheel when on the right
      delta > 0 && scrollPingRef.current.right
    ) : delta < 0 && scrollPingRef.current.top || // Pass origin wheel when on the bottom
    delta > 0 && scrollPingRef.current.bottom;
    if (smoothOffset && originScroll) {
      clearTimeout(lockTimeoutRef.current);
      lockRef.current = false;
    } else if (!originScroll || lockRef.current) {
      lockScroll();
    }
    return !lockRef.current && originScroll;
  };
};
function useFrameWheel(inVirtual, isScrollAtTop, isScrollAtBottom, isScrollAtLeft, isScrollAtRight, horizontalScroll, onWheelDelta) {
  var offsetRef = A$2(0);
  var nextFrameRef = A$2(null);
  var wheelValueRef = A$2(null);
  var isMouseScrollRef = A$2(false);
  var originScroll = useOriginScroll(isScrollAtTop, isScrollAtBottom, isScrollAtLeft, isScrollAtRight);
  function onWheelY(e2, deltaY) {
    wrapperRaf.cancel(nextFrameRef.current);
    if (originScroll(false, deltaY)) return;
    var event = e2;
    if (!event._virtualHandled) {
      event._virtualHandled = true;
    } else {
      return;
    }
    offsetRef.current += deltaY;
    wheelValueRef.current = deltaY;
    if (!isFF) {
      event.preventDefault();
    }
    nextFrameRef.current = wrapperRaf(function() {
      var patchMultiple = isMouseScrollRef.current ? 10 : 1;
      onWheelDelta(offsetRef.current * patchMultiple, false);
      offsetRef.current = 0;
    });
  }
  function onWheelX(event, deltaX) {
    onWheelDelta(deltaX, true);
    if (!isFF) {
      event.preventDefault();
    }
  }
  var wheelDirectionRef = A$2(null);
  var wheelDirectionCleanRef = A$2(null);
  function onWheel(event) {
    if (!inVirtual) return;
    wrapperRaf.cancel(wheelDirectionCleanRef.current);
    wheelDirectionCleanRef.current = wrapperRaf(function() {
      wheelDirectionRef.current = null;
    }, 2);
    var deltaX = event.deltaX, deltaY = event.deltaY, shiftKey = event.shiftKey;
    var mergedDeltaX = deltaX;
    var mergedDeltaY = deltaY;
    if (wheelDirectionRef.current === "sx" || !wheelDirectionRef.current && (shiftKey || false) && deltaY && !deltaX) {
      mergedDeltaX = deltaY;
      mergedDeltaY = 0;
      wheelDirectionRef.current = "sx";
    }
    var absX = Math.abs(mergedDeltaX);
    var absY = Math.abs(mergedDeltaY);
    if (wheelDirectionRef.current === null) {
      wheelDirectionRef.current = horizontalScroll && absX > absY ? "x" : "y";
    }
    if (wheelDirectionRef.current === "y") {
      onWheelY(event, mergedDeltaY);
    } else {
      onWheelX(event, mergedDeltaX);
    }
  }
  function onFireFoxScroll(event) {
    if (!inVirtual) return;
    isMouseScrollRef.current = event.detail === wheelValueRef.current;
  }
  return [onWheel, onFireFoxScroll];
}
function useGetSize(mergedData, getKey, heights, itemHeight) {
  var _React$useMemo = T$2(function() {
    return [/* @__PURE__ */ new Map(), []];
  }, [mergedData, heights.id, itemHeight]), _React$useMemo2 = _slicedToArray(_React$useMemo, 2), key2Index = _React$useMemo2[0], bottomList = _React$useMemo2[1];
  var getSize = function getSize2(startKey) {
    var endKey = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : startKey;
    var startIndex = key2Index.get(startKey);
    var endIndex = key2Index.get(endKey);
    if (startIndex === void 0 || endIndex === void 0) {
      var dataLen = mergedData.length;
      for (var i2 = bottomList.length; i2 < dataLen; i2 += 1) {
        var _heights$get;
        var item = mergedData[i2];
        var key = getKey(item);
        key2Index.set(key, i2);
        var cacheHeight = (_heights$get = heights.get(key)) !== null && _heights$get !== void 0 ? _heights$get : itemHeight;
        bottomList[i2] = (bottomList[i2 - 1] || 0) + cacheHeight;
        if (key === startKey) {
          startIndex = i2;
        }
        if (key === endKey) {
          endIndex = i2;
        }
        if (startIndex !== void 0 && endIndex !== void 0) {
          break;
        }
      }
    }
    return {
      top: bottomList[startIndex - 1] || 0,
      bottom: bottomList[endIndex]
    };
  };
  return getSize;
}
var CacheMap = /* @__PURE__ */ function() {
  function CacheMap2() {
    _classCallCheck(this, CacheMap2);
    _defineProperty(this, "maps", void 0);
    _defineProperty(this, "id", 0);
    _defineProperty(this, "diffRecords", /* @__PURE__ */ new Map());
    this.maps = /* @__PURE__ */ Object.create(null);
  }
  _createClass(CacheMap2, [{
    key: "set",
    value: function set(key, value2) {
      this.diffRecords.set(key, this.maps[key]);
      this.maps[key] = value2;
      this.id += 1;
    }
  }, {
    key: "get",
    value: function get(key) {
      return this.maps[key];
    }
    /**
     * CacheMap will record the key changed.
     * To help to know what's update in the next render.
     */
  }, {
    key: "resetRecord",
    value: function resetRecord() {
      this.diffRecords.clear();
    }
  }, {
    key: "getRecord",
    value: function getRecord() {
      return this.diffRecords;
    }
  }]);
  return CacheMap2;
}();
function parseNumber(value2) {
  var num = parseFloat(value2);
  return isNaN(num) ? 0 : num;
}
function useHeights(getKey, onItemAdd, onItemRemove) {
  var _React$useState = d$3(0), _React$useState2 = _slicedToArray(_React$useState, 2), updatedMark = _React$useState2[0], setUpdatedMark = _React$useState2[1];
  var instanceRef = A$2(/* @__PURE__ */ new Map());
  var heightsRef = A$2(new CacheMap());
  var promiseIdRef = A$2(0);
  function cancelRaf() {
    promiseIdRef.current += 1;
  }
  function collectHeight() {
    var sync = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;
    cancelRaf();
    var doCollect = function doCollect2() {
      var changed = false;
      instanceRef.current.forEach(function(element, key) {
        if (element && element.offsetParent) {
          var offsetHeight = element.offsetHeight;
          var _getComputedStyle = getComputedStyle(element), marginTop = _getComputedStyle.marginTop, marginBottom = _getComputedStyle.marginBottom;
          var marginTopNum = parseNumber(marginTop);
          var marginBottomNum = parseNumber(marginBottom);
          var totalHeight = offsetHeight + marginTopNum + marginBottomNum;
          if (heightsRef.current.get(key) !== totalHeight) {
            heightsRef.current.set(key, totalHeight);
            changed = true;
          }
        }
      });
      if (changed) {
        setUpdatedMark(function(c2) {
          return c2 + 1;
        });
      }
    };
    if (sync) {
      doCollect();
    } else {
      promiseIdRef.current += 1;
      var id = promiseIdRef.current;
      Promise.resolve().then(function() {
        if (id === promiseIdRef.current) {
          doCollect();
        }
      });
    }
  }
  function setInstanceRef(item, instance) {
    var key = getKey(item);
    instanceRef.current.get(key);
    if (instance) {
      instanceRef.current.set(key, instance);
      collectHeight();
    } else {
      instanceRef.current.delete(key);
    }
  }
  y$3(function() {
    return cancelRaf;
  }, []);
  return [setInstanceRef, collectHeight, heightsRef.current, updatedMark];
}
var SMOOTH_PTG = 14 / 15;
function useMobileTouchMove(inVirtual, listRef, callback) {
  var touchedRef = A$2(false);
  var touchXRef = A$2(0);
  var touchYRef = A$2(0);
  var elementRef = A$2(null);
  var intervalRef = A$2(null);
  var cleanUpEvents;
  var onTouchMove = function onTouchMove2(e2) {
    if (touchedRef.current) {
      var currentX = Math.ceil(e2.touches[0].pageX);
      var currentY = Math.ceil(e2.touches[0].pageY);
      var offsetX = touchXRef.current - currentX;
      var offsetY = touchYRef.current - currentY;
      var _isHorizontal = Math.abs(offsetX) > Math.abs(offsetY);
      if (_isHorizontal) {
        touchXRef.current = currentX;
      } else {
        touchYRef.current = currentY;
      }
      var scrollHandled = callback(_isHorizontal, _isHorizontal ? offsetX : offsetY, false, e2);
      if (scrollHandled) {
        e2.preventDefault();
      }
      clearInterval(intervalRef.current);
      if (scrollHandled) {
        intervalRef.current = setInterval(function() {
          if (_isHorizontal) {
            offsetX *= SMOOTH_PTG;
          } else {
            offsetY *= SMOOTH_PTG;
          }
          var offset = Math.floor(_isHorizontal ? offsetX : offsetY);
          if (!callback(_isHorizontal, offset, true) || Math.abs(offset) <= 0.1) {
            clearInterval(intervalRef.current);
          }
        }, 16);
      }
    }
  };
  var onTouchEnd = function onTouchEnd2() {
    touchedRef.current = false;
    cleanUpEvents();
  };
  var onTouchStart = function onTouchStart2(e2) {
    cleanUpEvents();
    if (e2.touches.length === 1 && !touchedRef.current) {
      touchedRef.current = true;
      touchXRef.current = Math.ceil(e2.touches[0].pageX);
      touchYRef.current = Math.ceil(e2.touches[0].pageY);
      elementRef.current = e2.target;
      elementRef.current.addEventListener("touchmove", onTouchMove, {
        passive: false
      });
      elementRef.current.addEventListener("touchend", onTouchEnd, {
        passive: true
      });
    }
  };
  cleanUpEvents = function cleanUpEvents2() {
    if (elementRef.current) {
      elementRef.current.removeEventListener("touchmove", onTouchMove);
      elementRef.current.removeEventListener("touchend", onTouchEnd);
    }
  };
  useLayoutEffect(function() {
    if (inVirtual) {
      listRef.current.addEventListener("touchstart", onTouchStart, {
        passive: true
      });
    }
    return function() {
      var _listRef$current;
      (_listRef$current = listRef.current) === null || _listRef$current === void 0 || _listRef$current.removeEventListener("touchstart", onTouchStart);
      cleanUpEvents();
      clearInterval(intervalRef.current);
    };
  }, [inVirtual]);
}
function smoothScrollOffset(offset) {
  return Math.floor(Math.pow(offset, 0.5));
}
function getPageXY(e2, horizontal) {
  var obj = "touches" in e2 ? e2.touches[0] : e2;
  return obj[horizontal ? "pageX" : "pageY"] - window[horizontal ? "scrollX" : "scrollY"];
}
function useScrollDrag(inVirtual, componentRef, onScrollOffset) {
  y$3(function() {
    var ele = componentRef.current;
    if (inVirtual && ele) {
      var mouseDownLock = false;
      var rafId;
      var _offset;
      var stopScroll = function stopScroll2() {
        wrapperRaf.cancel(rafId);
      };
      var continueScroll = function continueScroll2() {
        stopScroll();
        rafId = wrapperRaf(function() {
          onScrollOffset(_offset);
          continueScroll2();
        });
      };
      var onMouseDown = function onMouseDown2(e2) {
        if (e2.target.draggable || e2.button !== 0) {
          return;
        }
        var event = e2;
        if (!event._virtualHandled) {
          event._virtualHandled = true;
          mouseDownLock = true;
        }
      };
      var onMouseUp = function onMouseUp2() {
        mouseDownLock = false;
        stopScroll();
      };
      var onMouseMove = function onMouseMove2(e2) {
        if (mouseDownLock) {
          var mouseY = getPageXY(e2, false);
          var _ele$getBoundingClien = ele.getBoundingClientRect(), top = _ele$getBoundingClien.top, bottom = _ele$getBoundingClien.bottom;
          if (mouseY <= top) {
            var diff = top - mouseY;
            _offset = -smoothScrollOffset(diff);
            continueScroll();
          } else if (mouseY >= bottom) {
            var _diff = mouseY - bottom;
            _offset = smoothScrollOffset(_diff);
            continueScroll();
          } else {
            stopScroll();
          }
        }
      };
      ele.addEventListener("mousedown", onMouseDown);
      ele.ownerDocument.addEventListener("mouseup", onMouseUp);
      ele.ownerDocument.addEventListener("mousemove", onMouseMove);
      return function() {
        ele.removeEventListener("mousedown", onMouseDown);
        ele.ownerDocument.removeEventListener("mouseup", onMouseUp);
        ele.ownerDocument.removeEventListener("mousemove", onMouseMove);
        stopScroll();
      };
    }
  }, [inVirtual]);
}
var MAX_TIMES = 10;
function useScrollTo(containerRef, data, heights, itemHeight, getKey, collectHeight, syncScrollTop, triggerFlash) {
  var scrollRef = A$2();
  var _React$useState = d$3(null), _React$useState2 = _slicedToArray(_React$useState, 2), syncState = _React$useState2[0], setSyncState = _React$useState2[1];
  useLayoutEffect(function() {
    if (syncState && syncState.times < MAX_TIMES) {
      if (!containerRef.current) {
        setSyncState(function(ori) {
          return _objectSpread2({}, ori);
        });
        return;
      }
      collectHeight();
      var targetAlign = syncState.targetAlign, originAlign = syncState.originAlign, index2 = syncState.index, offset = syncState.offset;
      var height = containerRef.current.clientHeight;
      var needCollectHeight = false;
      var newTargetAlign = targetAlign;
      var targetTop = null;
      if (height) {
        var mergedAlign = targetAlign || originAlign;
        var stackTop = 0;
        var itemTop = 0;
        var itemBottom = 0;
        var maxLen = Math.min(data.length - 1, index2);
        for (var i2 = 0; i2 <= maxLen; i2 += 1) {
          var key = getKey(data[i2]);
          itemTop = stackTop;
          var cacheHeight = heights.get(key);
          itemBottom = itemTop + (cacheHeight === void 0 ? itemHeight : cacheHeight);
          stackTop = itemBottom;
        }
        var leftHeight = mergedAlign === "top" ? offset : height - offset;
        for (var _i = maxLen; _i >= 0; _i -= 1) {
          var _key = getKey(data[_i]);
          var _cacheHeight = heights.get(_key);
          if (_cacheHeight === void 0) {
            needCollectHeight = true;
            break;
          }
          leftHeight -= _cacheHeight;
          if (leftHeight <= 0) {
            break;
          }
        }
        switch (mergedAlign) {
          case "top":
            targetTop = itemTop - offset;
            break;
          case "bottom":
            targetTop = itemBottom - height + offset;
            break;
          default: {
            var scrollTop = containerRef.current.scrollTop;
            var scrollBottom = scrollTop + height;
            if (itemTop < scrollTop) {
              newTargetAlign = "top";
            } else if (itemBottom > scrollBottom) {
              newTargetAlign = "bottom";
            }
          }
        }
        if (targetTop !== null) {
          syncScrollTop(targetTop);
        }
        if (targetTop !== syncState.lastTop) {
          needCollectHeight = true;
        }
      }
      if (needCollectHeight) {
        setSyncState(_objectSpread2(_objectSpread2({}, syncState), {}, {
          times: syncState.times + 1,
          targetAlign: newTargetAlign,
          lastTop: targetTop
        }));
      }
    }
  }, [syncState, containerRef.current]);
  return function(arg) {
    if (arg === null || arg === void 0) {
      triggerFlash();
      return;
    }
    wrapperRaf.cancel(scrollRef.current);
    if (typeof arg === "number") {
      syncScrollTop(arg);
    } else if (arg && _typeof(arg) === "object") {
      var index2;
      var align = arg.align;
      if ("index" in arg) {
        index2 = arg.index;
      } else {
        index2 = data.findIndex(function(item) {
          return getKey(item) === arg.key;
        });
      }
      var _arg$offset = arg.offset, offset = _arg$offset === void 0 ? 0 : _arg$offset;
      setSyncState({
        times: 0,
        index: index2,
        offset,
        originAlign: align
      });
    }
  };
}
var ScrollBar = /* @__PURE__ */ D(function(props, ref) {
  var prefixCls = props.prefixCls, rtl = props.rtl, scrollOffset = props.scrollOffset, scrollRange = props.scrollRange, onStartMove = props.onStartMove, onStopMove = props.onStopMove, onScroll = props.onScroll, horizontal = props.horizontal, spinSize = props.spinSize, containerSize = props.containerSize, style = props.style, propsThumbStyle = props.thumbStyle, showScrollBar = props.showScrollBar;
  var _React$useState = d$3(false), _React$useState2 = _slicedToArray(_React$useState, 2), dragging = _React$useState2[0], setDragging = _React$useState2[1];
  var _React$useState3 = d$3(null), _React$useState4 = _slicedToArray(_React$useState3, 2), pageXY = _React$useState4[0], setPageXY = _React$useState4[1];
  var _React$useState5 = d$3(null), _React$useState6 = _slicedToArray(_React$useState5, 2), startTop = _React$useState6[0], setStartTop = _React$useState6[1];
  var isLTR = !rtl;
  var scrollbarRef = A$2();
  var thumbRef = A$2();
  var _React$useState7 = d$3(showScrollBar), _React$useState8 = _slicedToArray(_React$useState7, 2), visible = _React$useState8[0], setVisible = _React$useState8[1];
  var visibleTimeoutRef = A$2();
  var delayHidden = function delayHidden2() {
    if (showScrollBar === true || showScrollBar === false) return;
    clearTimeout(visibleTimeoutRef.current);
    setVisible(true);
    visibleTimeoutRef.current = setTimeout(function() {
      setVisible(false);
    }, 3e3);
  };
  var enableScrollRange = scrollRange - containerSize || 0;
  var enableOffsetRange = containerSize - spinSize || 0;
  var top = T$2(function() {
    if (scrollOffset === 0 || enableScrollRange === 0) {
      return 0;
    }
    var ptg = scrollOffset / enableScrollRange;
    return ptg * enableOffsetRange;
  }, [scrollOffset, enableScrollRange, enableOffsetRange]);
  var onContainerMouseDown = function onContainerMouseDown2(e2) {
    e2.stopPropagation();
    e2.preventDefault();
  };
  var stateRef = A$2({
    top,
    dragging,
    pageY: pageXY,
    startTop
  });
  stateRef.current = {
    top,
    dragging,
    pageY: pageXY,
    startTop
  };
  var onThumbMouseDown = function onThumbMouseDown2(e2) {
    setDragging(true);
    setPageXY(getPageXY(e2, horizontal));
    setStartTop(stateRef.current.top);
    onStartMove();
    e2.stopPropagation();
    e2.preventDefault();
  };
  y$3(function() {
    var onScrollbarTouchStart = function onScrollbarTouchStart2(e2) {
      e2.preventDefault();
    };
    var scrollbarEle = scrollbarRef.current;
    var thumbEle = thumbRef.current;
    scrollbarEle.addEventListener("touchstart", onScrollbarTouchStart, {
      passive: false
    });
    thumbEle.addEventListener("touchstart", onThumbMouseDown, {
      passive: false
    });
    return function() {
      scrollbarEle.removeEventListener("touchstart", onScrollbarTouchStart);
      thumbEle.removeEventListener("touchstart", onThumbMouseDown);
    };
  }, []);
  var enableScrollRangeRef = A$2();
  enableScrollRangeRef.current = enableScrollRange;
  var enableOffsetRangeRef = A$2();
  enableOffsetRangeRef.current = enableOffsetRange;
  y$3(function() {
    if (dragging) {
      var moveRafId;
      var onMouseMove = function onMouseMove2(e2) {
        var _stateRef$current = stateRef.current, stateDragging = _stateRef$current.dragging, statePageY = _stateRef$current.pageY, stateStartTop = _stateRef$current.startTop;
        wrapperRaf.cancel(moveRafId);
        var rect = scrollbarRef.current.getBoundingClientRect();
        var scale = containerSize / (horizontal ? rect.width : rect.height);
        if (stateDragging) {
          var offset = (getPageXY(e2, horizontal) - statePageY) * scale;
          var newTop = stateStartTop;
          if (!isLTR && horizontal) {
            newTop -= offset;
          } else {
            newTop += offset;
          }
          var tmpEnableScrollRange = enableScrollRangeRef.current;
          var tmpEnableOffsetRange = enableOffsetRangeRef.current;
          var ptg = tmpEnableOffsetRange ? newTop / tmpEnableOffsetRange : 0;
          var newScrollTop = Math.ceil(ptg * tmpEnableScrollRange);
          newScrollTop = Math.max(newScrollTop, 0);
          newScrollTop = Math.min(newScrollTop, tmpEnableScrollRange);
          moveRafId = wrapperRaf(function() {
            onScroll(newScrollTop, horizontal);
          });
        }
      };
      var onMouseUp = function onMouseUp2() {
        setDragging(false);
        onStopMove();
      };
      window.addEventListener("mousemove", onMouseMove, {
        passive: true
      });
      window.addEventListener("touchmove", onMouseMove, {
        passive: true
      });
      window.addEventListener("mouseup", onMouseUp, {
        passive: true
      });
      window.addEventListener("touchend", onMouseUp, {
        passive: true
      });
      return function() {
        window.removeEventListener("mousemove", onMouseMove);
        window.removeEventListener("touchmove", onMouseMove);
        window.removeEventListener("mouseup", onMouseUp);
        window.removeEventListener("touchend", onMouseUp);
        wrapperRaf.cancel(moveRafId);
      };
    }
  }, [dragging]);
  y$3(function() {
    delayHidden();
    return function() {
      clearTimeout(visibleTimeoutRef.current);
    };
  }, [scrollOffset]);
  F$3(ref, function() {
    return {
      delayHidden
    };
  });
  var scrollbarPrefixCls = "".concat(prefixCls, "-scrollbar");
  var containerStyle = {
    position: "absolute",
    visibility: visible ? null : "hidden"
  };
  var thumbStyle = {
    position: "absolute",
    borderRadius: 99,
    background: "var(--rc-virtual-list-scrollbar-bg, rgba(0, 0, 0, 0.5))",
    cursor: "pointer",
    userSelect: "none"
  };
  if (horizontal) {
    Object.assign(containerStyle, {
      height: 8,
      left: 0,
      right: 0,
      bottom: 0
    });
    Object.assign(thumbStyle, _defineProperty({
      height: "100%",
      width: spinSize
    }, isLTR ? "left" : "right", top));
  } else {
    Object.assign(containerStyle, _defineProperty({
      width: 8,
      top: 0,
      bottom: 0
    }, isLTR ? "right" : "left", 0));
    Object.assign(thumbStyle, {
      width: "100%",
      height: spinSize,
      top
    });
  }
  return /* @__PURE__ */ _$3("div", {
    ref: scrollbarRef,
    className: classNames(scrollbarPrefixCls, _defineProperty(_defineProperty(_defineProperty({}, "".concat(scrollbarPrefixCls, "-horizontal"), horizontal), "".concat(scrollbarPrefixCls, "-vertical"), !horizontal), "".concat(scrollbarPrefixCls, "-visible"), visible)),
    style: _objectSpread2(_objectSpread2({}, containerStyle), style),
    onMouseDown: onContainerMouseDown,
    onMouseMove: delayHidden
  }, /* @__PURE__ */ _$3("div", {
    ref: thumbRef,
    className: classNames("".concat(scrollbarPrefixCls, "-thumb"), _defineProperty({}, "".concat(scrollbarPrefixCls, "-thumb-moving"), dragging)),
    style: _objectSpread2(_objectSpread2({}, thumbStyle), propsThumbStyle),
    onMouseDown: onThumbMouseDown
  }));
});
var MIN_SIZE = 20;
function getSpinSize() {
  var containerSize = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;
  var scrollRange = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;
  var baseSize = containerSize / scrollRange * containerSize;
  if (isNaN(baseSize)) {
    baseSize = 0;
  }
  baseSize = Math.max(baseSize, MIN_SIZE);
  return Math.floor(baseSize);
}
var _excluded = ["prefixCls", "className", "height", "itemHeight", "fullHeight", "style", "data", "children", "itemKey", "virtual", "direction", "scrollWidth", "component", "onScroll", "onVirtualScroll", "onVisibleChange", "innerProps", "extraRender", "styles", "showScrollBar"];
var EMPTY_DATA = [];
var ScrollStyle = {
  overflowY: "auto",
  overflowAnchor: "none"
};
function RawList(props, ref) {
  var _props$prefixCls = props.prefixCls, prefixCls = _props$prefixCls === void 0 ? "rc-virtual-list" : _props$prefixCls, className = props.className, height = props.height, itemHeight = props.itemHeight, _props$fullHeight = props.fullHeight, fullHeight = _props$fullHeight === void 0 ? true : _props$fullHeight, style = props.style, data = props.data, children = props.children, itemKey = props.itemKey, virtual = props.virtual, direction = props.direction, scrollWidth = props.scrollWidth, _props$component = props.component, Component = _props$component === void 0 ? "div" : _props$component, onScroll = props.onScroll, onVirtualScroll = props.onVirtualScroll, onVisibleChange = props.onVisibleChange, innerProps = props.innerProps, extraRender = props.extraRender, styles = props.styles, _props$showScrollBar = props.showScrollBar, showScrollBar = _props$showScrollBar === void 0 ? "optional" : _props$showScrollBar, restProps = _objectWithoutProperties(props, _excluded);
  var getKey = q$2(function(item) {
    if (typeof itemKey === "function") {
      return itemKey(item);
    }
    return item === null || item === void 0 ? void 0 : item[itemKey];
  }, [itemKey]);
  var _useHeights = useHeights(getKey), _useHeights2 = _slicedToArray(_useHeights, 4), setInstanceRef = _useHeights2[0], collectHeight = _useHeights2[1], heights = _useHeights2[2], heightUpdatedMark = _useHeights2[3];
  var useVirtual = !!(virtual !== false && height && itemHeight);
  var containerHeight = T$2(function() {
    return Object.values(heights.maps).reduce(function(total, curr) {
      return total + curr;
    }, 0);
  }, [heights.id, heights.maps]);
  var inVirtual = useVirtual && data && (Math.max(itemHeight * data.length, containerHeight) > height || !!scrollWidth);
  var isRTL = direction === "rtl";
  var mergedClassName = classNames(prefixCls, _defineProperty({}, "".concat(prefixCls, "-rtl"), isRTL), className);
  var mergedData = data || EMPTY_DATA;
  var componentRef = A$2();
  var fillerInnerRef = A$2();
  var containerRef = A$2();
  var _useState = d$3(0), _useState2 = _slicedToArray(_useState, 2), offsetTop = _useState2[0], setOffsetTop = _useState2[1];
  var _useState3 = d$3(0), _useState4 = _slicedToArray(_useState3, 2), offsetLeft = _useState4[0], setOffsetLeft = _useState4[1];
  var _useState5 = d$3(false), _useState6 = _slicedToArray(_useState5, 2), scrollMoving = _useState6[0], setScrollMoving = _useState6[1];
  var onScrollbarStartMove = function onScrollbarStartMove2() {
    setScrollMoving(true);
  };
  var onScrollbarStopMove = function onScrollbarStopMove2() {
    setScrollMoving(false);
  };
  var sharedConfig = {
    getKey
  };
  function syncScrollTop(newTop) {
    setOffsetTop(function(origin2) {
      var value2;
      if (typeof newTop === "function") {
        value2 = newTop(origin2);
      } else {
        value2 = newTop;
      }
      var alignedTop = keepInRange(value2);
      componentRef.current.scrollTop = alignedTop;
      return alignedTop;
    });
  }
  var rangeRef = A$2({
    start: 0,
    end: mergedData.length
  });
  var diffItemRef = A$2();
  var _useDiffItem = useDiffItem(mergedData, getKey), _useDiffItem2 = _slicedToArray(_useDiffItem, 1), diffItem = _useDiffItem2[0];
  diffItemRef.current = diffItem;
  var _React$useMemo = T$2(function() {
    if (!useVirtual) {
      return {
        scrollHeight: void 0,
        start: 0,
        end: mergedData.length - 1,
        offset: void 0
      };
    }
    if (!inVirtual) {
      var _fillerInnerRef$curre;
      return {
        scrollHeight: ((_fillerInnerRef$curre = fillerInnerRef.current) === null || _fillerInnerRef$curre === void 0 ? void 0 : _fillerInnerRef$curre.offsetHeight) || 0,
        start: 0,
        end: mergedData.length - 1,
        offset: void 0
      };
    }
    var itemTop = 0;
    var startIndex;
    var startOffset;
    var endIndex;
    var dataLen = mergedData.length;
    for (var i2 = 0; i2 < dataLen; i2 += 1) {
      var _item = mergedData[i2];
      var key = getKey(_item);
      var cacheHeight = heights.get(key);
      var currentItemBottom = itemTop + (cacheHeight === void 0 ? itemHeight : cacheHeight);
      if (currentItemBottom >= offsetTop && startIndex === void 0) {
        startIndex = i2;
        startOffset = itemTop;
      }
      if (currentItemBottom > offsetTop + height && endIndex === void 0) {
        endIndex = i2;
      }
      itemTop = currentItemBottom;
    }
    if (startIndex === void 0) {
      startIndex = 0;
      startOffset = 0;
      endIndex = Math.ceil(height / itemHeight);
    }
    if (endIndex === void 0) {
      endIndex = mergedData.length - 1;
    }
    endIndex = Math.min(endIndex + 1, mergedData.length - 1);
    return {
      scrollHeight: itemTop,
      start: startIndex,
      end: endIndex,
      offset: startOffset
    };
  }, [inVirtual, useVirtual, offsetTop, mergedData, heightUpdatedMark, height]), scrollHeight = _React$useMemo.scrollHeight, start2 = _React$useMemo.start, end = _React$useMemo.end, fillerOffset = _React$useMemo.offset;
  rangeRef.current.start = start2;
  rangeRef.current.end = end;
  _$2(function() {
    var changedRecord = heights.getRecord();
    if (changedRecord.size === 1) {
      var recordKey = Array.from(changedRecord.keys())[0];
      var prevCacheHeight = changedRecord.get(recordKey);
      var startItem = mergedData[start2];
      if (startItem && prevCacheHeight === void 0) {
        var startIndexKey = getKey(startItem);
        if (startIndexKey === recordKey) {
          var realStartHeight = heights.get(recordKey);
          var diffHeight = realStartHeight - itemHeight;
          syncScrollTop(function(ori) {
            return ori + diffHeight;
          });
        }
      }
    }
    heights.resetRecord();
  }, [scrollHeight]);
  var _React$useState = d$3({
    width: 0,
    height
  }), _React$useState2 = _slicedToArray(_React$useState, 2), size = _React$useState2[0], setSize = _React$useState2[1];
  var onHolderResize = function onHolderResize2(sizeInfo) {
    setSize({
      width: sizeInfo.offsetWidth,
      height: sizeInfo.offsetHeight
    });
  };
  var verticalScrollBarRef = A$2();
  var horizontalScrollBarRef = A$2();
  var horizontalScrollBarSpinSize = T$2(function() {
    return getSpinSize(size.width, scrollWidth);
  }, [size.width, scrollWidth]);
  var verticalScrollBarSpinSize = T$2(function() {
    return getSpinSize(size.height, scrollHeight);
  }, [size.height, scrollHeight]);
  var maxScrollHeight = scrollHeight - height;
  var maxScrollHeightRef = A$2(maxScrollHeight);
  maxScrollHeightRef.current = maxScrollHeight;
  function keepInRange(newScrollTop) {
    var newTop = newScrollTop;
    if (!Number.isNaN(maxScrollHeightRef.current)) {
      newTop = Math.min(newTop, maxScrollHeightRef.current);
    }
    newTop = Math.max(newTop, 0);
    return newTop;
  }
  var isScrollAtTop = offsetTop <= 0;
  var isScrollAtBottom = offsetTop >= maxScrollHeight;
  var isScrollAtLeft = offsetLeft <= 0;
  var isScrollAtRight = offsetLeft >= scrollWidth;
  var originScroll = useOriginScroll(isScrollAtTop, isScrollAtBottom, isScrollAtLeft, isScrollAtRight);
  var getVirtualScrollInfo = function getVirtualScrollInfo2() {
    return {
      x: isRTL ? -offsetLeft : offsetLeft,
      y: offsetTop
    };
  };
  var lastVirtualScrollInfoRef = A$2(getVirtualScrollInfo());
  var triggerScroll = useEvent(function(params) {
    if (onVirtualScroll) {
      var nextInfo = _objectSpread2(_objectSpread2({}, getVirtualScrollInfo()), params);
      if (lastVirtualScrollInfoRef.current.x !== nextInfo.x || lastVirtualScrollInfoRef.current.y !== nextInfo.y) {
        onVirtualScroll(nextInfo);
        lastVirtualScrollInfoRef.current = nextInfo;
      }
    }
  });
  function onScrollBar(newScrollOffset, horizontal) {
    var newOffset = newScrollOffset;
    if (horizontal) {
      En(function() {
        setOffsetLeft(newOffset);
      });
      triggerScroll();
    } else {
      syncScrollTop(newOffset);
    }
  }
  function onFallbackScroll(e2) {
    var newScrollTop = e2.currentTarget.scrollTop;
    if (newScrollTop !== offsetTop) {
      syncScrollTop(newScrollTop);
    }
    onScroll === null || onScroll === void 0 || onScroll(e2);
    triggerScroll();
  }
  var keepInHorizontalRange = function keepInHorizontalRange2(nextOffsetLeft) {
    var tmpOffsetLeft = nextOffsetLeft;
    var max = !!scrollWidth ? scrollWidth - size.width : 0;
    tmpOffsetLeft = Math.max(tmpOffsetLeft, 0);
    tmpOffsetLeft = Math.min(tmpOffsetLeft, max);
    return tmpOffsetLeft;
  };
  var onWheelDelta = useEvent(function(offsetXY, fromHorizontal) {
    if (fromHorizontal) {
      En(function() {
        setOffsetLeft(function(left) {
          var nextOffsetLeft = left + (isRTL ? -offsetXY : offsetXY);
          return keepInHorizontalRange(nextOffsetLeft);
        });
      });
      triggerScroll();
    } else {
      syncScrollTop(function(top) {
        var newTop = top + offsetXY;
        return newTop;
      });
    }
  });
  var _useFrameWheel = useFrameWheel(useVirtual, isScrollAtTop, isScrollAtBottom, isScrollAtLeft, isScrollAtRight, !!scrollWidth, onWheelDelta), _useFrameWheel2 = _slicedToArray(_useFrameWheel, 2), onRawWheel = _useFrameWheel2[0], onFireFoxScroll = _useFrameWheel2[1];
  useMobileTouchMove(useVirtual, componentRef, function(isHorizontal, delta, smoothOffset, e2) {
    var event = e2;
    if (originScroll(isHorizontal, delta, smoothOffset)) {
      return false;
    }
    if (!event || !event._virtualHandled) {
      if (event) {
        event._virtualHandled = true;
      }
      onRawWheel({
        preventDefault: function preventDefault() {
        },
        deltaX: isHorizontal ? delta : 0,
        deltaY: isHorizontal ? 0 : delta
      });
      return true;
    }
    return false;
  });
  useScrollDrag(inVirtual, componentRef, function(offset) {
    syncScrollTop(function(top) {
      return top + offset;
    });
  });
  useLayoutEffect(function() {
    function onMozMousePixelScroll(e2) {
      var scrollingUpAtTop = isScrollAtTop && e2.detail < 0;
      var scrollingDownAtBottom = isScrollAtBottom && e2.detail > 0;
      if (useVirtual && !scrollingUpAtTop && !scrollingDownAtBottom) {
        e2.preventDefault();
      }
    }
    var componentEle = componentRef.current;
    componentEle.addEventListener("wheel", onRawWheel, {
      passive: false
    });
    componentEle.addEventListener("DOMMouseScroll", onFireFoxScroll, {
      passive: true
    });
    componentEle.addEventListener("MozMousePixelScroll", onMozMousePixelScroll, {
      passive: false
    });
    return function() {
      componentEle.removeEventListener("wheel", onRawWheel);
      componentEle.removeEventListener("DOMMouseScroll", onFireFoxScroll);
      componentEle.removeEventListener("MozMousePixelScroll", onMozMousePixelScroll);
    };
  }, [useVirtual, isScrollAtTop, isScrollAtBottom]);
  useLayoutEffect(function() {
    if (scrollWidth) {
      var newOffsetLeft = keepInHorizontalRange(offsetLeft);
      setOffsetLeft(newOffsetLeft);
      triggerScroll({
        x: newOffsetLeft
      });
    }
  }, [size.width, scrollWidth]);
  var delayHideScrollBar = function delayHideScrollBar2() {
    var _verticalScrollBarRef, _horizontalScrollBarR;
    (_verticalScrollBarRef = verticalScrollBarRef.current) === null || _verticalScrollBarRef === void 0 || _verticalScrollBarRef.delayHidden();
    (_horizontalScrollBarR = horizontalScrollBarRef.current) === null || _horizontalScrollBarR === void 0 || _horizontalScrollBarR.delayHidden();
  };
  var _scrollTo = useScrollTo(componentRef, mergedData, heights, itemHeight, getKey, function() {
    return collectHeight(true);
  }, syncScrollTop, delayHideScrollBar);
  F$3(ref, function() {
    return {
      nativeElement: containerRef.current,
      getScrollInfo: getVirtualScrollInfo,
      scrollTo: function scrollTo(config) {
        function isPosScroll(arg) {
          return arg && _typeof(arg) === "object" && ("left" in arg || "top" in arg);
        }
        if (isPosScroll(config)) {
          if (config.left !== void 0) {
            setOffsetLeft(keepInHorizontalRange(config.left));
          }
          _scrollTo(config.top);
        } else {
          _scrollTo(config);
        }
      }
    };
  });
  useLayoutEffect(function() {
    if (onVisibleChange) {
      var renderList = mergedData.slice(start2, end + 1);
      onVisibleChange(renderList, mergedData);
    }
  }, [start2, end, mergedData]);
  var getSize = useGetSize(mergedData, getKey, heights, itemHeight);
  var extraContent = extraRender === null || extraRender === void 0 ? void 0 : extraRender({
    start: start2,
    end,
    virtual: inVirtual,
    offsetX: offsetLeft,
    offsetY: fillerOffset,
    rtl: isRTL,
    getSize
  });
  var listChildren = useChildren(mergedData, start2, end, scrollWidth, offsetLeft, setInstanceRef, children, sharedConfig);
  var componentStyle = null;
  if (height) {
    componentStyle = _objectSpread2(_defineProperty({}, fullHeight ? "height" : "maxHeight", height), ScrollStyle);
    if (useVirtual) {
      componentStyle.overflowY = "hidden";
      if (scrollWidth) {
        componentStyle.overflowX = "hidden";
      }
      if (scrollMoving) {
        componentStyle.pointerEvents = "none";
      }
    }
  }
  var containerProps = {};
  if (isRTL) {
    containerProps.dir = "rtl";
  }
  return /* @__PURE__ */ _$3("div", _extends({
    ref: containerRef,
    style: _objectSpread2(_objectSpread2({}, style), {}, {
      position: "relative"
    }),
    className: mergedClassName
  }, containerProps, restProps), /* @__PURE__ */ _$3(RefResizeObserver, {
    onResize: onHolderResize
  }, /* @__PURE__ */ _$3(Component, {
    className: "".concat(prefixCls, "-holder"),
    style: componentStyle,
    ref: componentRef,
    onScroll: onFallbackScroll,
    onMouseEnter: delayHideScrollBar
  }, /* @__PURE__ */ _$3(Filler, {
    prefixCls,
    height: scrollHeight,
    offsetX: offsetLeft,
    offsetY: fillerOffset,
    scrollWidth,
    onInnerResize: collectHeight,
    ref: fillerInnerRef,
    innerProps,
    rtl: isRTL,
    extra: extraContent
  }, listChildren))), inVirtual && scrollHeight > height && /* @__PURE__ */ _$3(ScrollBar, {
    ref: verticalScrollBarRef,
    prefixCls,
    scrollOffset: offsetTop,
    scrollRange: scrollHeight,
    rtl: isRTL,
    onScroll: onScrollBar,
    onStartMove: onScrollbarStartMove,
    onStopMove: onScrollbarStopMove,
    spinSize: verticalScrollBarSpinSize,
    containerSize: size.height,
    style: styles === null || styles === void 0 ? void 0 : styles.verticalScrollBar,
    thumbStyle: styles === null || styles === void 0 ? void 0 : styles.verticalScrollBarThumb,
    showScrollBar
  }), inVirtual && scrollWidth > size.width && /* @__PURE__ */ _$3(ScrollBar, {
    ref: horizontalScrollBarRef,
    prefixCls,
    scrollOffset: offsetLeft,
    scrollRange: scrollWidth,
    rtl: isRTL,
    onScroll: onScrollBar,
    onStartMove: onScrollbarStartMove,
    onStopMove: onScrollbarStopMove,
    spinSize: horizontalScrollBarSpinSize,
    containerSize: size.width,
    horizontal: true,
    style: styles === null || styles === void 0 ? void 0 : styles.horizontalScrollBar,
    thumbStyle: styles === null || styles === void 0 ? void 0 : styles.horizontalScrollBarThumb,
    showScrollBar
  }));
}
var List = /* @__PURE__ */ D(RawList);
List.displayName = "List";
function debounce(func, debounceMs, { signal: signal2, edges } = {}) {
  let pendingThis = void 0;
  let pendingArgs = null;
  const leading = edges != null && edges.includes("leading");
  const trailing = edges == null || edges.includes("trailing");
  const invoke = () => {
    if (pendingArgs !== null) {
      func.apply(pendingThis, pendingArgs);
      pendingThis = void 0;
      pendingArgs = null;
    }
  };
  const onTimerEnd = () => {
    if (trailing) {
      invoke();
    }
    cancel();
  };
  let timeoutId = null;
  const schedule = () => {
    if (timeoutId != null) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      timeoutId = null;
      onTimerEnd();
    }, debounceMs);
  };
  const cancelTimer = () => {
    if (timeoutId !== null) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };
  const cancel = () => {
    cancelTimer();
    pendingThis = void 0;
    pendingArgs = null;
  };
  const flush = () => {
    cancelTimer();
    invoke();
  };
  const debounced = function(...args) {
    if (signal2?.aborted) {
      return;
    }
    pendingThis = this;
    pendingArgs = args;
    const isFirstCall = timeoutId == null;
    schedule();
    if (leading && isFirstCall) {
      invoke();
    }
  };
  debounced.schedule = schedule;
  debounced.cancel = cancel;
  debounced.flush = flush;
  signal2?.addEventListener("abort", cancel, { once: true });
  return debounced;
}
class InstrumentService {
  baseUrl = "http://localhost:5111";
  async searchInstruments(page, pageSize, keyword) {
    try {
      const response = await axios.get(
        `${this.baseUrl}/api/Instrument/find-all`,
        {
          params: {
            pageIndex: page,
            pageSize,
            keyword
          },
          headers: {
            "Authorization": `Bearer ${getBearerToken()}`,
            "Content-Type": "application/json"
          }
        }
      );
      return response.data;
    } catch (error2) {
      if (axios.isAxiosError(error2)) {
        const errorMessage = error2.response?.data?.message || `Failed to search instruments: ${error2.message}`;
        throw new Error(errorMessage);
      }
      throw error2;
    }
  }
}
const instrumentService = new InstrumentService();
const PAGE_SIZE = 25;
const PAGE = 1;
const InstrumentSearch = ({
  activeWatchlistId,
  activeWatchlistInstrumentIds = []
}) => {
  const [instrumentQuery, setInstrumentQuery] = d$3("");
  const [searchInstrumentResults, setSearchInstrumentResults] = d$3([]);
  const [isSearchFocused, setIsSearchFocused] = d$3(false);
  const {
    addInstrumentMutation
  } = useWatchlistQueries();
  const debouncedSearchInstruments = q$2(
    debounce(async (query) => {
      if (!query.trim()) {
        setSearchInstrumentResults([]);
        return;
      }
      try {
        const data = await instrumentService.searchInstruments(PAGE, PAGE_SIZE, query);
        setSearchInstrumentResults(data?.data?.items || []);
      } catch (error2) {
        console.error("Error searching instruments:", error2);
        setSearchInstrumentResults([]);
      }
    }, 500),
    []
  );
  const handleAddInstrument = async (instrument) => {
    if (!activeWatchlistId) return;
    try {
      addInstrumentMutation.mutate({
        instrumentId: parseInt(instrument.instrumentId || "0"),
        watchlistId: activeWatchlistId
      });
      y.success(`${instrument.abbreviation} added to watchlist`);
    } catch {
      y.error(`Failed to add ${instrument.abbreviation}`);
    } finally {
      setInstrumentQuery("");
      setIsSearchFocused(false);
    }
  };
  y$3(() => {
    debouncedSearchInstruments(instrumentQuery);
  }, [instrumentQuery, debouncedSearchInstruments]);
  const isInstrumentInWatchlist = (instrumentId) => {
    return activeWatchlistInstrumentIds.some(
      (id) => id.toString() === instrumentId
    );
  };
  const handleInputChange = (e2) => {
    const value2 = e2.currentTarget.value;
    setInstrumentQuery(value2);
  };
  return /* @__PURE__ */ u$4("div", { className: "add-instrument-section", children: [
    /* @__PURE__ */ u$4("h4", { className: "add-section-title", children: "Add Symbols" }),
    /* @__PURE__ */ u$4("div", { className: "search-container", children: [
      /* @__PURE__ */ u$4(Search, { size: 16, className: "search-icon" }),
      /* @__PURE__ */ u$4(
        "input",
        {
          type: "text",
          value: instrumentQuery,
          onChange: handleInputChange,
          onFocus: () => setIsSearchFocused(true),
          onBlur: () => setIsSearchFocused(false),
          placeholder: "Search instruments to add...",
          className: "search-input"
        }
      )
    ] }),
    isSearchFocused && /* @__PURE__ */ u$4(
      "div",
      {
        className: "search-results",
        onMouseDown: (e2) => e2.preventDefault(),
        children: searchInstrumentResults.length > 0 ? /* @__PURE__ */ u$4(
          List,
          {
            data: searchInstrumentResults,
            height: 200,
            itemHeight: 60,
            itemKey: "instrumentId",
            children: (instrument) => /* @__PURE__ */ u$4("div", { className: "search-result-item", onClick: () => handleAddInstrument(instrument), children: [
              /* @__PURE__ */ u$4("div", { className: "instrument-info", children: [
                /* @__PURE__ */ u$4("div", { className: "symbol-info", children: [
                  /* @__PURE__ */ u$4("span", { className: "symbol", children: instrument.ticker }),
                  /* @__PURE__ */ u$4("span", { className: "name", children: instrument.name })
                ] }),
                /* @__PURE__ */ u$4("span", { className: "abbreviation", children: instrument.abbreviation ?? "--" })
              ] }),
              /* @__PURE__ */ u$4(
                "button",
                {
                  className: "add-instrument-btn",
                  onClick: () => handleAddInstrument(instrument),
                  disabled: isInstrumentInWatchlist(instrument.instrumentId || "0"),
                  children: /* @__PURE__ */ u$4(Plus, { size: 16 })
                }
              )
            ] }, instrument.instrumentId)
          }
        ) : /* @__PURE__ */ u$4("div", { className: "search-results-empty", children: [
          /* @__PURE__ */ u$4("p", { children: "No instruments found" }),
          /* @__PURE__ */ u$4("p", { children: "Try a different search term" })
        ] })
      }
    )
  ] });
};
const InstrumentTableSkeleton = ({ children, type: type2 = "loading" }) => {
  const renderSkeletonRows = () => {
    return Array.from({ length: 5 }, (_2, index2) => /* @__PURE__ */ u$4("tr", { className: "skeleton-row", children: [
      /* @__PURE__ */ u$4("td", { className: "skeleton-cell", children: /* @__PURE__ */ u$4("div", { className: "skeleton-symbol", children: [
        /* @__PURE__ */ u$4("div", { className: "skeleton-line skeleton-symbol-text" }),
        /* @__PURE__ */ u$4("div", { className: "skeleton-line skeleton-market-text" })
      ] }) }),
      /* @__PURE__ */ u$4("td", { className: "skeleton-cell", children: /* @__PURE__ */ u$4("div", { className: "skeleton-line skeleton-price" }) }),
      /* @__PURE__ */ u$4("td", { className: "skeleton-cell", children: /* @__PURE__ */ u$4("div", { className: "skeleton-line skeleton-change" }) }),
      /* @__PURE__ */ u$4("td", { className: "skeleton-cell", children: /* @__PURE__ */ u$4("div", { className: "skeleton-line skeleton-change-percent" }) })
    ] }, index2));
  };
  return /* @__PURE__ */ u$4("div", { className: "instruments-section", children: /* @__PURE__ */ u$4("div", { className: "instruments-table-container", children: /* @__PURE__ */ u$4("table", { className: "instruments-table", children: [
    /* @__PURE__ */ u$4("thead", { children: /* @__PURE__ */ u$4("tr", { children: [
      /* @__PURE__ */ u$4("th", { className: "table-header", children: "Symbol" }),
      /* @__PURE__ */ u$4("th", { className: "table-header", children: "Last" }),
      /* @__PURE__ */ u$4("th", { className: "table-header", children: "Change" }),
      /* @__PURE__ */ u$4("th", { className: "table-header", style: { textAlign: "end" }, children: "Change%" })
    ] }) }),
    /* @__PURE__ */ u$4("tbody", { children: type2 === "loading" ? renderSkeletonRows() : /* @__PURE__ */ u$4("tr", { children: /* @__PURE__ */ u$4("td", { colSpan: 4, children }) }) })
  ] }) }) });
};
const InstrumentTable = ({
  instruments,
  isLoading,
  activeWatchlistId
}) => {
  const { removeInstrumentMutation } = useWatchlistQueries();
  const [selectedInstrument, setSelectedInstrument] = d$3();
  y$3(() => {
    const windowAppContext = window.EurolandAppContext;
    if (!windowAppContext) return;
    windowAppContext.registerCommandHandler("instrument-selected", () => selectedInstrument);
    windowAppContext.emit("instrument-selected", selectedInstrument);
    return () => {
      windowAppContext.unregisterCommandHandler("instrument-selected", () => selectedInstrument);
    };
  }, [selectedInstrument]);
  const handleRemoveInstrument = async (instrumentId) => {
    if (!activeWatchlistId) return;
    try {
      removeInstrumentMutation.mutate({
        watchlistId: activeWatchlistId,
        instrumentId: parseInt(instrumentId)
      });
      y.success("Instrument removed from watchlist");
    } catch {
      y.error("Failed to remove instrument");
    }
  };
  const handleSelectInstrument = (instrument) => {
    const selectedData = { id: instrument.id, symbol: instrument.symbol };
    setSelectedInstrument(selectedData);
  };
  if (isLoading) {
    return /* @__PURE__ */ u$4(
      InstrumentTableSkeleton,
      {
        type: "loading",
        children: /* @__PURE__ */ u$4("div", { className: "loading-state", children: [
          /* @__PURE__ */ u$4("div", { className: "loading-icon", children: "📊" }),
          /* @__PURE__ */ u$4("p", { className: "loading-title", children: "Loading instruments..." }),
          /* @__PURE__ */ u$4("p", { className: "loading-subtitle", children: "Please wait while we fetch your data" })
        ] })
      }
    );
  }
  if (instruments.length === 0) {
    return /* @__PURE__ */ u$4(
      InstrumentTableSkeleton,
      {
        type: "empty",
        children: /* @__PURE__ */ u$4("div", { className: "empty-state", children: [
          /* @__PURE__ */ u$4("div", { className: "empty-icon", children: "📈" }),
          /* @__PURE__ */ u$4("p", { className: "empty-title", children: "No instruments in this watchlist" }),
          /* @__PURE__ */ u$4("p", { className: "empty-subtitle", children: "Use the search below to add instruments" })
        ] })
      }
    );
  }
  return /* @__PURE__ */ u$4("div", { className: "instruments-section", children: /* @__PURE__ */ u$4("div", { className: "instruments-table-container", children: /* @__PURE__ */ u$4("table", { className: "instruments-table", children: [
    /* @__PURE__ */ u$4("thead", { children: /* @__PURE__ */ u$4("tr", { children: [
      /* @__PURE__ */ u$4("th", { className: "table-header", children: "Symbol" }),
      /* @__PURE__ */ u$4("th", { className: "table-header", children: "Last" }),
      /* @__PURE__ */ u$4("th", { className: "table-header", children: "Change" }),
      /* @__PURE__ */ u$4("th", { className: "table-header", style: { textAlign: "end" }, children: "Change%" })
    ] }) }),
    /* @__PURE__ */ u$4("tbody", { children: instruments.map((instrument) => /* @__PURE__ */ u$4(
      "tr",
      {
        className: `table-row ${selectedInstrument?.id === instrument.id ? "selected" : ""}`,
        onClick: () => handleSelectInstrument(instrument),
        children: [
          /* @__PURE__ */ u$4("td", { className: "table-row cell symbol", children: [
            /* @__PURE__ */ u$4("span", { className: "symbol-text", children: instrument.symbol }),
            /* @__PURE__ */ u$4("span", { className: "market-text", children: instrument.market })
          ] }),
          /* @__PURE__ */ u$4("td", { className: "table-row cell price", children: instrument?.last?.toFixed(2) ? instrument?.last?.toFixed(2) + instrument.currency : "--" }),
          /* @__PURE__ */ u$4(
            "td",
            {
              className: `table-row cell change ${instrument.change >= 0 ? "positive" : "negative"}`,
              children: [
                instrument.change >= 0 ? "+" : "",
                instrument.change?.toFixed(2) ?? "--"
              ]
            }
          ),
          /* @__PURE__ */ u$4("td", { className: `table-row cell change-percent ${instrument.changePercent >= 0 ? "positive" : "negative"}`, children: [
            instrument.changePercent >= 0 ? "+" : "",
            instrument.changePercent?.toFixed(2) ? instrument.changePercent?.toFixed(2) + "%" : "--",
            /* @__PURE__ */ u$4(
              "button",
              {
                className: "remove-btn",
                onClick: () => handleRemoveInstrument(instrument.id),
                children: /* @__PURE__ */ u$4(Trash2, { size: 16 })
              }
            )
          ] })
        ]
      },
      instrument.id
    )) })
  ] }) }) });
};
const useWatchlistState = ({ watchlists }) => {
  const [activeWatchlistId, setActiveWatchlistId] = d$3(null);
  const watchlistsArray = T$2(() => {
    return watchlists || [];
  }, [watchlists]);
  y$3(() => {
    if (watchlistsArray.length > 0 && activeWatchlistId === null) {
      setActiveWatchlistId(watchlistsArray[0].id);
    }
    if (activeWatchlistId && watchlistsArray.length > 0) {
      const exists = watchlistsArray.some(
        (w2) => w2.id === activeWatchlistId
      );
      if (!exists) {
        setActiveWatchlistId(watchlistsArray[0].id);
      }
    }
  }, [watchlistsArray, activeWatchlistId]);
  const activeWatchlist = T$2(() => {
    if (!activeWatchlistId || watchlistsArray.length === 0) return null;
    return watchlistsArray.find((w2) => w2.id === activeWatchlistId) || null;
  }, [watchlistsArray, activeWatchlistId]);
  return {
    activeWatchlistId,
    setActiveWatchlistId,
    activeWatchlist,
    watchlistsArray
  };
};
const INSTRUMENTS_QUERY = gql`
query Ticker($ids: [Int!]!, $adjClose: Boolean, $toCurrency: String) {
  instrumentByIds(ids: $ids, exchangeCurrency: $toCurrency, adjClose: $adjClose) {
    shareName
    id
    symbol
    market {
      translation {
        cultureName
        value
      }
      status {
        isOpened
        remainingTime
      }
    }
    currency {
      code
      name
    }
    currentPrice {
      open
      date
      bid
      ask
      high
      low
      volume
      officialClose
      officialCloseDate
      # Chỉ query tickerData nếu cần thiết
      tickerData {
        last
        change
        changePercentage
        prevClose
      }
    }
    fifty_two_weeks: performance(period: FIFTY_TWO_WEEKS) {
      highest
      lowest
      changePercentage
    }
  }
}
`;
const useInstrumentData = ({ activeWatchlist }) => {
  const [result] = useQuery({
    query: INSTRUMENTS_QUERY,
    variables: {
      adjClose: false,
      ids: activeWatchlist?.instrumentIds || []
    }
  });
  const { data: instrumentDatas, fetching } = result;
  const instruments = T$2(() => {
    if (!instrumentDatas?.instrumentByIds) return [];
    return instrumentDatas.instrumentByIds?.filter((instrument) => instrument !== null).map((instrument) => ({
      id: instrument?.id?.toString(),
      instrumentId: instrument?.id?.toString(),
      symbol: instrument?.symbol,
      name: instrument?.shareName,
      price: instrument?.currentPrice?.tickerData?.last,
      change: instrument?.currentPrice?.tickerData?.change,
      last: instrument?.currentPrice?.tickerData?.last,
      high: instrument?.currentPrice?.high,
      low: instrument?.currentPrice?.low,
      hightOfWeek: instrument?.fifty_two_weeks?.highest,
      changePercent: instrument?.currentPrice?.tickerData?.changePercentage,
      volume: instrument?.currentPrice?.volume,
      currency: instrument?.currency?.code,
      market: instrument?.market?.translation?.value,
      marketStatus: instrument?.market?.status,
      fiftyTwoWeeks: instrument?.fifty_two_weeks
    }));
  }, [instrumentDatas]);
  return {
    instruments,
    isLoading: fetching
  };
};
const WatchlistWidget = () => {
  const {
    watchlistsQuery,
    createWatchlistMutation
  } = useWatchlistQueries();
  const { data: watchlists, isLoading, error: error2 } = watchlistsQuery;
  const { activeWatchlistId, setActiveWatchlistId, activeWatchlist, watchlistsArray } = useWatchlistState({ watchlists: watchlists?.data || [] });
  const { instruments, isLoading: instrumentsLoading } = useInstrumentData({ activeWatchlist });
  const handleCreateWatchlist = async (name2) => {
    if (!name2.trim()) {
      y.error("Watchlist name cannot be empty");
      return;
    }
    try {
      createWatchlistMutation.mutate(name2);
      y.success("Watchlist created successfully");
    } catch {
      y.error("Failed to create watchlist");
    }
  };
  if (isLoading) return /* @__PURE__ */ u$4("div", { className: "loading-spinner", children: /* @__PURE__ */ u$4("div", { className: "spinner" }) });
  if (error2) return /* @__PURE__ */ u$4("div", { className: "error-message", children: [
    /* @__PURE__ */ u$4("div", { children: "Error loading watchlists" }),
    /* @__PURE__ */ u$4("div", { children: "Let's try again" })
  ] });
  if (!isLoading && watchlistsArray.length === 0) {
    return /* @__PURE__ */ u$4(EmptyWatchlistState, { onCreateWatchlist: handleCreateWatchlist });
  }
  return /* @__PURE__ */ u$4("div", { className: "watchlist-container", children: [
    /* @__PURE__ */ u$4(
      WatchlistTabs,
      {
        watchlists: watchlistsArray,
        activeWatchlistId,
        onWatchlistSelect: setActiveWatchlistId,
        onCreateWatchlist: handleCreateWatchlist
      }
    ),
    /* @__PURE__ */ u$4(
      InstrumentSearch,
      {
        activeWatchlistId,
        activeWatchlistInstrumentIds: activeWatchlist?.instrumentIds
      }
    ),
    /* @__PURE__ */ u$4("div", { className: "content-area", children: activeWatchlist && /* @__PURE__ */ u$4(
      InstrumentTable,
      {
        instruments,
        isLoading: instrumentsLoading,
        activeWatchlistId
      }
    ) })
  ] });
};
const WatchlistSkeleton = ({ count }) => {
  return /* @__PURE__ */ u$4(k$3, { children: Array.from({ length: count }, (_2, index2) => /* @__PURE__ */ u$4("div", { className: "watchlist-item-skeleton", children: [
    /* @__PURE__ */ u$4("div", { className: "watchlist-info-skeleton" }),
    /* @__PURE__ */ u$4("div", { className: "add-btn-skeleton" })
  ] }, index2)) });
};
function WatchlistModal({
  watchlists,
  onAddToWatchlist,
  onCreateWatchlist,
  isLoadingFetch,
  isLoadingAction
}) {
  const [searchQuery, setSearchQuery] = d$3("");
  const [isCreating, setIsCreating] = d$3(false);
  const [newWatchlistName, setNewWatchlistName] = d$3("");
  const filteredWatchlists = watchlists.filter(
    (watchlist) => watchlist.name.toLowerCase().includes(searchQuery.toLowerCase())
  );
  const handleCreateSubmit = () => {
    if (newWatchlistName.trim()) {
      onCreateWatchlist(newWatchlistName.trim());
      setNewWatchlistName("");
      setIsCreating(false);
    } else {
      y.error("Watchlist name cannot be empty");
    }
  };
  const handleCreateCancel = () => {
    setNewWatchlistName("");
    setIsCreating(false);
  };
  const handleSearchChange = (e2) => {
    setSearchQuery(e2.currentTarget.value);
  };
  const handleNewWatchlistNameChange = (e2) => {
    setNewWatchlistName(e2.currentTarget.value);
  };
  const handleKeyDown = (e2) => {
    if (e2.key === "Enter") handleCreateSubmit();
    if (e2.key === "Escape") handleCreateCancel();
  };
  return /* @__PURE__ */ u$4("div", { className: "watchlist-modal", onClick: (e2) => e2.stopPropagation(), children: /* @__PURE__ */ u$4("div", { className: "modal-content", children: [
    /* @__PURE__ */ u$4("div", { className: "search-section", children: /* @__PURE__ */ u$4("div", { className: "search-container", children: [
      /* @__PURE__ */ u$4(Search, { className: "search-icon", size: 16 }),
      /* @__PURE__ */ u$4(
        "input",
        {
          type: "text",
          placeholder: "Search watchlists...",
          value: searchQuery,
          onChange: handleSearchChange,
          className: "search-input"
        }
      )
    ] }) }),
    /* @__PURE__ */ u$4("div", { className: "watchlists-section", children: [
      /* @__PURE__ */ u$4("div", { className: "section-header", children: [
        /* @__PURE__ */ u$4("h4", { children: "Select Watchlist" }),
        /* @__PURE__ */ u$4(
          "button",
          {
            className: "create-new-btn",
            onClick: () => setIsCreating(true),
            disabled: isCreating || isLoadingAction,
            children: [
              /* @__PURE__ */ u$4(Plus, { size: 14 }),
              "New Watchlist"
            ]
          }
        )
      ] }),
      /* @__PURE__ */ u$4("div", { className: "watchlists-list", children: [
        isCreating && /* @__PURE__ */ u$4("div", { className: "create-watchlist-form", children: [
          /* @__PURE__ */ u$4(
            "input",
            {
              type: "text",
              placeholder: "Watchlist name",
              value: newWatchlistName,
              onChange: handleNewWatchlistNameChange,
              className: "create-input",
              onKeyDown: handleKeyDown,
              autoFocus: true
            }
          ),
          /* @__PURE__ */ u$4("div", { className: "create-actions", children: [
            /* @__PURE__ */ u$4(
              "button",
              {
                className: "confirm-btn",
                onClick: handleCreateSubmit,
                disabled: isLoadingAction,
                children: /* @__PURE__ */ u$4(Check, { size: 14 })
              }
            ),
            /* @__PURE__ */ u$4(
              "button",
              {
                className: "cancel-btn",
                onClick: handleCreateCancel,
                disabled: isLoadingAction,
                children: /* @__PURE__ */ u$4(X, { size: 14 })
              }
            )
          ] })
        ] }),
        isLoadingFetch ? /* @__PURE__ */ u$4(WatchlistSkeleton, { count: 5 }) : filteredWatchlists.map((watchlist) => /* @__PURE__ */ u$4("div", { className: "watchlist-item", onClick: () => onAddToWatchlist(watchlist.id), children: [
          /* @__PURE__ */ u$4("div", { className: "watchlist-info", children: [
            /* @__PURE__ */ u$4("div", { className: "watchlist-name", children: [
              watchlist.isDefault && /* @__PURE__ */ u$4(Star, { className: "default-star", size: 14 }),
              watchlist.name
            ] }),
            /* @__PURE__ */ u$4("div", { className: "watchlist-count", children: [
              watchlist.instrumentIds?.length || 0,
              " instruments"
            ] })
          ] }),
          /* @__PURE__ */ u$4(
            "button",
            {
              className: "add-btn",
              onClick: () => onAddToWatchlist(watchlist.id),
              disabled: isLoadingAction,
              children: isLoadingAction ? /* @__PURE__ */ u$4("div", { className: "loading-spinner small" }) : /* @__PURE__ */ u$4(Plus, { size: 16 })
            }
          )
        ] }, watchlist.id)),
        filteredWatchlists.length === 0 && searchQuery && /* @__PURE__ */ u$4("div", { className: "empty-state", children: /* @__PURE__ */ u$4("p", { children: [
          'No watchlists found matching "',
          searchQuery,
          '"'
        ] }) })
      ] })
    ] })
  ] }) });
}
function RootIframeWidget() {
  const auth = useAuth();
  const { loadingFetchWatchlist, watchlistsQuery, addInstrumentMutation, createWatchlistMutation } = useWatchlistQueries();
  const xpropsInstrumentId = window.xprops?.instrumentId;
  const instrumentId = xpropsInstrumentId || "0";
  const handleAddToWatchlist = async (watchlistId) => {
    try {
      await addInstrumentMutation.mutate({
        watchlistId,
        instrumentId: parseInt(instrumentId)
      });
      y.success("Instrument added to watchlist");
      watchlistsQuery.refetch();
    } catch (error2) {
      y.error("Failed to add instrument");
      console.error("Error adding instrument to watchlist:", error2);
    }
  };
  const handleCreateWatchlist = async (name2) => {
    try {
      await createWatchlistMutation.mutate(name2);
      y.success("Watchlist created successfully");
      watchlistsQuery.refetch();
    } catch (error2) {
      y.error("Failed to create watchlist");
      console.error("Error creating new watchlist:", error2);
    }
  };
  if (!auth.isAuthenticated) {
    return null;
  }
  return /* @__PURE__ */ u$4("div", { className: "root-iframe-widget", children: /* @__PURE__ */ u$4(
    WatchlistModal,
    {
      watchlists: watchlistsQuery.data?.data || [],
      instrumentId,
      onAddToWatchlist: handleAddToWatchlist,
      onCreateWatchlist: handleCreateWatchlist,
      isLoadingFetch: loadingFetchWatchlist,
      isLoadingAction: addInstrumentMutation.isLoading || createWatchlistMutation.isLoading
    }
  ) });
}
const searchParams = new URLSearchParams(window.location.search);
const searchString = searchParams.toString();
window.euroland?.createComponent("WatchlistAddInstrument", {
  tag: "watchlist-add-instrument",
  url: "/watchlist-add-instrument" + (searchString.length ? `?${searchString}` : ""),
  dimensions: {
    width: "500px",
    height: "600px"
  },
  template: {
    name: "modal",
    clickOverlayToClose: false,
    styles: {
      position: "fixed",
      bottom: "0px"
    }
  },
  props: {
    instrumentId: {
      type: "string",
      required: true
    }
  }
});
window.addEventListener("error", (e2) => {
  if (e2.message.includes("ResizeObserver loop")) {
    e2.stopImmediatePropagation();
    return false;
  }
});
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1e3 * 60 * 5,
      cacheTime: 1e3 * 60 * 10
    }
  }
});
class EurolandWatchList extends HTMLElement {
  container = null;
  connectedCallback() {
    this.attachShadow({ mode: "open" });
    const style = document.createElement("style");
    style.textContent = styleString;
    style.textContent += watchlistStyleString;
    style.textContent += watchlistTabsStyleString;
    style.textContent += EmptyWatchlistStateString;
    style.textContent += InstrumentSearchStyleString;
    style.textContent += InstrumentTableStyleString;
    this.container = document.createElement("div");
    this.container.id = "preact-root";
    this.shadowRoot.appendChild(style);
    this.shadowRoot.appendChild(this.container);
    if (this.container) {
      E$2(
        /* @__PURE__ */ u$4(QueryClientProvider, { client: queryClient, children: /* @__PURE__ */ u$4(f$1, { value: client, children: /* @__PURE__ */ u$4(WatchlistWidget, {}) }) }),
        this.container
      );
    }
  }
  disconnectedCallback() {
    if (this.container) {
      E$2(null, this.container);
    }
  }
}
class EurolandAddInstrumentButton extends HTMLElement {
  container = null;
  connectedCallback() {
    this.attachShadow({ mode: "open" });
    const style = document.createElement("style");
    style.textContent = styleString;
    style.textContent += AddInstrumentButtonStyleString;
    style.textContent += AddInstrumentModalStyleString;
    this.container = document.createElement("div");
    this.container.id = "preact-root";
    this.shadowRoot.appendChild(style);
    this.shadowRoot.appendChild(this.container);
    if (this.container) {
      E$2(
        /* @__PURE__ */ u$4(QueryClientProvider, { client: queryClient, children: /* @__PURE__ */ u$4(f$1, { value: client, children: /* @__PURE__ */ u$4(
          AddInstrumentWidget,
          {
            instrumentId: this.getAttribute("instrumentId")
          }
        ) }) }),
        this.container
      );
    }
  }
  disconnectedCallback() {
    if (this.container) {
      E$2(null, this.container);
    }
  }
}
class EurolandAddInstrumentModal extends HTMLElement {
  container = null;
  connectedCallback() {
    this.attachShadow({ mode: "open" });
    const style = document.createElement("style");
    style.textContent = styleString;
    style.textContent += AddInstrumentButtonStyleString;
    style.textContent += AddInstrumentModalStyleString;
    style.textContent += WatchlistModalStyleString;
    this.container = document.createElement("div");
    this.container.id = "preact-root";
    this.shadowRoot.appendChild(style);
    this.shadowRoot.appendChild(this.container);
    if (location.pathname !== "/watchlist-add-instrument") return;
    E$2(
      /* @__PURE__ */ u$4(QueryClientProvider, { client: queryClient, children: /* @__PURE__ */ u$4(f$1, { value: client, children: /* @__PURE__ */ u$4(RootIframeWidget, {}) }) }),
      this.container
    );
  }
  disconnectedCallback() {
    if (this.container) {
      E$2(null, this.container);
    }
  }
}
customElements.define("euroland-watch-list", EurolandWatchList);
customElements.define("euroland-add-instrument-button", EurolandAddInstrumentButton);
customElements.define("euroland-add-instrument-modal", EurolandAddInstrumentModal);
//# sourceMappingURL=watchlist-widget.es.js.map
