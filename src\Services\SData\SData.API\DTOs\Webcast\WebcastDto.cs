using System;
using System.Collections.Generic;
using Euroland.FlipIT.SData.API.Helpers;

namespace Euroland.FlipIT.SData.API.Dto.Webcast;

public class WebcastDto: AuditDto, IDtoObject
{
  public long Id { get; set; }
  public int WebcastTypeId { get; set; }
  public virtual WebcastTypeDto WebcastType { get; set; } = null!;
  public int WebcastHostId { get; set; }
  public virtual WebcastHostDto WebcastHost { get; set; } = null!;
  public int WebcastSourceId { get; set; }
  public virtual WebcastSourceDto WebcastSource { get; set; } = null!;
  public string CompanyCode { get; set; } = null!;
  public string Title { get; set; } = null!;
  private DateTime _publishDate { get; set; }
  public DateTime PublishDate
  {
    get => _publishDate;
    init => _publishDate = value.CestToUtc();
  }
  public string FileType { get; set; } = null!;
  public string? ThumbnailType { get; set; }
  public string? TranscriptType { get; set; }

  public IEnumerable<WebcastVideoDto> Videos { get; set; } = null!;
  public WebcastVimeoDto? Vimeo { get; set; }
  public string? TranscriptUrl { get; set; }
  public string? ThumbnailUrl { get; set; }
}

public class VideoGeneralDto
{
  public string? VideoId { get; set; }
  public string? Url { get; set; }
  public string? HostType { get; set; }
}

public class WebcastVideoDto : VideoGeneralDto
{
}

public class WebcastVimeoDto : VideoGeneralDto
{
  public string? VimeoId { get; set; }
}
