"Deploy QA":
  tags:
    - vietnam-dev-shell
  extends:
    - .default-retry
    - .prevent-fetching
    - .unpack-deployment
  stage: deploy:qa
  dependencies:
    - build-qa
  variables:
    DEPLOY_ENVIRONMENT: QA
    DEPLOY_SERVICE_URL: 'https://BINGO:8172/msdeploy.axd'
  script:
    - 'call ".\build\msdeploy.bat" --source "src/Services/SData/SData.API/bin/Release/net6.0/package" --iisAppPath "tools site/tools/flipit-sdata-api" --usr "%DEV_VN_DEPLOY_USER%" --pwd "%DEV_VN_DEPLOY_PSW%" --url "%DEPLOY_SERVICE_URL%" --env %DEPLOY_ENVIRONMENT%'
    - 'call ".\build\msdeploy.bat" --source "src/Services/Fundamentals/Fundamentals.API/bin/Release/net6.0/package" --iisAppPath "tools site/tools/fundamental-api" --usr "%DEV_VN_DEPLOY_USER%" --pwd "%DEV_VN_DEPLOY_PSW%" --url "%DEPLOY_SERVICE_URL%" --env %DEPLOY_ENVIRONMENT%'
    - 'call ".\build\msdeploy.bat" --source "src/ApiGateway/ApiGateway.API/bin/Release/net6.0/package" --iisAppPath "tools site/tools/ApiGateway" --usr "%DEV_VN_DEPLOY_USER%" --pwd "%DEV_VN_DEPLOY_PSW%" --url "%DEPLOY_SERVICE_URL%" --env %DEPLOY_ENVIRONMENT%'
  only:
    refs:
      - develop
  environment:
    name: $CI_COMMIT_REF_SLUG
    url: https://dev.vn.euroland.com/tools/FlipIT-SData-Api/graphql?companycode=dk-cbg&lang=en-gb#$CI_COMMIT_REF_SLUG
