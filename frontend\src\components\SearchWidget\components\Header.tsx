import { FC } from "react";
import { <PERSON>gin, XMark } from "../../Common";
import { Popover, PopoverContent, PopoverTrigger } from "../../ui/popover";
import { ProfileMenu } from "../../ProfileMenu";
import { i18n } from "@euroland/libs";
import Avatar from "@/components/Avatar/Avatar";
import FollowButton from "./FollowButton";
import { AUTH_ENABLED, FOLLOWING_STATUS } from "@/helpers/constants";
import { useSearchWidgetPropsContext } from "../context/propContext";

export const Header: FC = () => {
  const isLogin = useSearchWidgetPropsContext((s) => s.isLogin);
  const shortName = useSearchWidgetPropsContext((s) => s.shortName);
  const aiSearchSettings = useSearchWidgetPropsContext(
    (s) => s.aiSearchSettings
  );
  const onLogin = useSearchWidgetPropsContext((s) => s.onLogin);
  const onSignOutSuccess = useSearchWidgetPropsContext(
    (s) => s.onSignOutSuccess
  );
  const onCloseWindow = useSearchWidgetPropsContext((s) => s.onCloseWindow);
  const onOpenExternalLink = useSearchWidgetPropsContext(
    (s) => s.onOpenExternalLink
  );
  const avatarUrl = useSearchWidgetPropsContext((s) => s.avatarUrl);
  const userFollowStatus = useSearchWidgetPropsContext(
    (s) => s.userFollowStatus
  );
  const updateStatus = useSearchWidgetPropsContext((s) => s.updateStatus);
  const isFollowed = userFollowStatus === FOLLOWING_STATUS.FOLLOWING;

  const companyName =
    aiSearchSettings.placeholders || i18n.translate("defaultCompany");
  const userMenuElement: React.ReactNode = isLogin ? (
    <div className="relative flex">
      <Popover>
        <PopoverTrigger>
          <div className="flex items-center gap-2 cursor-pointer">
            <Avatar
              avatarUrl={avatarUrl}
              isLogin={isLogin}
              username={shortName}
            />
          </div>
        </PopoverTrigger>
        <PopoverContent className="w-48 bg-white rounded-md shadow-lg p-0 border-0">
          <ProfileMenu
            onLogout={onSignOutSuccess}
            onExternalLink={onOpenExternalLink}
          />
        </PopoverContent>
      </Popover>
    </div>
  ) : (
    <button className="wise-search-widget-login" onClick={() => onLogin()}>
      <Login />
      {i18n.translate("login")}
    </button>
  );

  return (
    <div className="wise-widget-header">
      <h2>{i18n.translate("searchTitle", { companyName })}</h2>

      <div className="flex items-center gap-4">
        {AUTH_ENABLED && (
          <>
            {isLogin && (
              <FollowButton
                isFollowed={isFollowed}
                followAndUnfollowCompany={updateStatus}
              />
            )}
            {userMenuElement}
          </>
        )}
        <div className="wise-widget-close" onClick={() => onCloseWindow()}>
          <XMark />
        </div>
      </div>
    </div>
  );
};
