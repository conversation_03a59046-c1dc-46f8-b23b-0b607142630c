﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
    public class FCalendar
    {
        [Column("fc_ID"), Key]
        public int Id { get; set; }

        [Column("fc_cCode")]
        public string CompanyCode { get; set; }

        [Column("fc_DateTime")]
        public DateTime? DateTime { get; set; }

        [Column("fc_Event"), ForeignKey("FCEvent")]
        public short? Event { get; set; }
        public FCEvent? FCEvent { get; set; }

        [Column("fc_Location")]
        public short? LocationId { get; set; }

        [Column("fc_TimeZone")]
        public short? TimezoneId { get; set; }

        [Column("fc_FYEndMo")]
        public string? FYEndMonth { get; set; }

        [Column("fc_LastUpdated")]
        public DateTime? LastUpdated { get; set; }

        [Column("fc_Market")]
        public short? MarketId { get; set; }

        [Column("fc_MonthOnly")]
        public bool? MonthOnly { get; set; }
    }
}
