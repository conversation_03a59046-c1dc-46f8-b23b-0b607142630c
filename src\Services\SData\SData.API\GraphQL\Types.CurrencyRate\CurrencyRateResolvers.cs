using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure;
using HotChocolate;
using HotChocolate.Resolvers;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.CurrencyRate;

public class CurrencyRateResolvers
{
  public async Task<CurrencyRateDto> GetCurrencyRateByCurrencyRateHistory(
    [Parent] CurrencyRateHistoryDto currencyRateHistory,
    CurrencyRateByIdDataLoader dataLoader,
    IResolverContext resolverContext,
    CancellationToken cancellationToken = default
  ) => await dataLoader.LoadAsync(currencyRateHistory.CurrencyRateId, cancellationToken);
}
