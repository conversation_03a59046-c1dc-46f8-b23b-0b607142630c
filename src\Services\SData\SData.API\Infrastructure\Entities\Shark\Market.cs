using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using HotChocolate;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
    public class Market
    {
        [Key]
        public short MarketNumber { get; set; }
        [Column("Abbreviation")]
        public string? MarketAbbreviation { get; set; }
        public string? MarketOpenTimeLocal { get; set; }
        public string? MarketCloseTimeLocal { get; set; }
        public short TimeDiff { get; set; }
        public string? DataSource { get; set; }
        public string? Delay { get; set; }
        public string? TimeZone { get; set; }
        public int? TimezoneID { get; set; }
        public bool BusinessDaysStoT { get; set; }
        public int? TranslationId { get; set; }

        [ForeignKey("City")]
        public int? CityId { get; set; }

        [GraphQLIgnore]
        public Instrument Instrument { get; set; }

        [GraphQLIgnore]
        public Translation Translation { get; set; }

        [NotMapped]
        [GraphQLIgnore]
        public Timezone? TimezoneObject { get; set; }
    }
}
