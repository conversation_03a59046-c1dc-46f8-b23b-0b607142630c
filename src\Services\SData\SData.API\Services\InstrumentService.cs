using Euroland.FlipIT.SData.API.Constants;
using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.DTOs.Deprecated;
using Euroland.FlipIT.SData.API.Helpers;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System.Globalization;
using Euroland.FlipIT.SData.API.Mappers.Deprecated;
using Instrument = Euroland.FlipIT.SData.API.Infrastructure.Entities.Instrument;

namespace Euroland.FlipIT.SData.API.Services
{
  public class InstrumentService : IInstrumentService
    {
        //private readonly ISharkDbContextFactory _sharkdbContextFactory;
        private readonly IDbContextFactory<DefaultSharkDbContext> _defaultSharkDbContextFactory;
        private readonly IDbContextFactory<RealtimeSharkDbContext> _realtimeSharkDbContextFactory;
        private readonly IDistributedCache _cache;
        private readonly DistributedCachingConfiguration _cacheSettings;
        private readonly IConfiguration _configuration;
        private readonly ICurrencyRateService _currencyRateService;
        private SharkDbContextBase _localSharkDbContext;
        private SharkDbContextBase _realtimeSharkDbContext;

        // public ISharkDbContext LocalSharkDbContext => CreateSharkDbContext(false);

        // public ISharkDbContext RealtimeSharkDbContext => CreateSharkDbContext(true);

        public InstrumentService(
            //ISharkDbContextFactory sharkContextFactory,
            IDbContextFactory<DefaultSharkDbContext> defaultSharkDbContextFactory,
            IDbContextFactory<RealtimeSharkDbContext> realtimeSharkDbContextFactory,
            IDistributedCache cache,
            IOptions<DistributedCachingConfiguration> cacheSettings,
            IConfiguration configuration,
            ICurrencyRateService currencyRateService)
        {
            //_sharkdbContextFactory = sharkContextFactory ?? throw new ArgumentNullException(nameof(sharkContextFactory));
            _defaultSharkDbContextFactory = defaultSharkDbContextFactory ?? throw new ArgumentNullException(nameof(defaultSharkDbContextFactory));
            _realtimeSharkDbContextFactory = realtimeSharkDbContextFactory ?? throw new ArgumentNullException(nameof(realtimeSharkDbContextFactory));
            _cache = cache ?? throw new ArgumentNullException(nameof(cache));
            _cacheSettings = cacheSettings?.Value;
            _configuration = configuration;
            _currencyRateService = currencyRateService;
        }

        private ISharkDbContext CreateSharkDbContext(bool useRealtimeDatabase) {
            if(useRealtimeDatabase) {
                if(_realtimeSharkDbContext == null) {
                    _realtimeSharkDbContext = _realtimeSharkDbContextFactory.CreateDbContext();
                }

                return _realtimeSharkDbContext;
            }
            else {
                if(_localSharkDbContext == null) {
                    _localSharkDbContext = _defaultSharkDbContextFactory.CreateDbContext();
                }

        return _localSharkDbContext;
      }
    }


        public IQueryable<DTOs.Deprecated.Instrument> GetInstruments(List<int> instrumentIds,
            int dayPeriod = 10,
            bool isRT = false,
            string? toCurrency = null,
            CancellationToken ct = default)
        {
            var langCode = CultureInfo.CurrentCulture.Name;

      var query = CreateSharkDbContext(isRT).Instrument
          .AsNoTracking().AsQueryable();

#pragma warning disable CS0618 // Type or member is obsolete
      var today = isRT ? DateTime.UtcNow.Date : DateTime.UtcNow.ToCEST().Date;
#pragma warning restore CS0618 // Type or member is obsolete
      var resultCondition = query.Include(i => i.InstrumentPrice)
          .Include(i => i.InstrumentHistories)
          .Include(i => i.Market)
          .Include(i => i.Currency)
          .Include(i => i.List)
          .Where(i => instrumentIds.Contains(i.InstrumentId))
          .ToInstrumentDTO(dayPeriod, langCode, isRT, toCurrency);

      return resultCondition;
    }

    /// <inheritdoc />
    public async Task<IEnumerable<InstrumentHistory>> GetHistoricalDataAsync(
        int instrumentId,
        bool isIntraday,
        Period period,
        DateTime? fromDate,
        DateTime? toDate,
        bool isRT = false,
        CancellationToken ct = default)
    {
      // try to get data from cache
      //var cacheKey = PrepareCacheKey(instrumentId, period, fromDate, toDate, isIntraday, isRT);
      //var cached = await GetDataFromCacheAsync(cacheKey, ct);
      //if (cached is not null && cached.Any()) return cached;

      // if data not in cache => get data from database
      var histories = isIntraday ?
          await GetIntradayDataAsync(instrumentId, period, fromDate, toDate, isRT, cancellationToken: ct) :
          await GetInstrumentHistoryDataAsync(instrumentId, fromDate, toDate, isRT, ct);

      // set cache
      //_ = Task.Run(async () => { await SetCacheAsync(cacheKey, histories, ct); });

      return histories;
    }

    public async Task<IEnumerable<InstrumentYearlyPerformancePrice>> GetYearlyPerformanceDataAsync(List<int> instrumentIds, int numberOfYears, CancellationToken cancellationToken, string? toCurrency = null)
    {
      var ctx = CreateSharkDbContext(false);
#pragma warning disable CS0618 // Type or member is obsolete
      var todayCET = DateTime.UtcNow.ToCEST();
#pragma warning restore CS0618 // Type or member is obsolete

      // get list date from instrument history
      var instrumentDateQuery = ctx.InstrumentHistory
          .AsNoTracking()
          .Where(ih => instrumentIds.Contains(ih.InstrumentId) &&
                          ih.DateTime >= todayCET.AddYears(-numberOfYears) && // from number of year
                          ih.DateTime <= todayCET.AddMonths(1).AddSeconds(-1)) // to end of month of now
          // .Select(i => new InstrumentHistory {
          //   ID = i.ID,
          //   Close = i.Close,
          //   DateTime = i.DateTime,
          //   High = i.High,
          //   InstrumentId = i.InstrumentId,
          //   Low = i.Close,
          //   Open = i.Open,
          //   Rate = i.Rate,
          //   Volume = i.Volume
          // })
          .GroupBy(ih => new { ih.InstrumentId, ih.DateTime.Year })
          .Select(ih => new YearlyDate
          {
            InstrumentId = ih.Key.InstrumentId,
            Date = ih.Max(grp => grp.DateTime),
          })
          .OrderBy(grp => grp.InstrumentId)
          .ThenBy(grp => grp.Date);

      //get data from date
#pragma warning disable CS8604 // Possible null reference argument.
      var instruments = await ctx.Instrument
          .AsNoTracking()
          .Include(i => i.InstrumentHistories)
          .Where(i => instrumentIds.Contains(i.InstrumentId))
          .Select(i => new InstrumentYearlyPerformancePrice
          {
            InstrumentId = i.InstrumentId,
            ShareName = InstrumentHelper.TruncateMarketAbbreviation(i.ShareName, i.Market.MarketAbbreviation),
            MarketAbbreviation = i.Market.MarketAbbreviation ?? string.Empty,
            CurrencyCode = i.CurrencyCode,
            YearlyPerformances = i.InstrumentHistories
                              .Where(ih => instrumentDateQuery
                                          .Where(d => d.InstrumentId == ih.InstrumentId)
                                          .Select(d => d.Date)
                                          .Contains(ih.DateTime))
                              .Select(ih => new YearlyPerformancePrice
                              {
                                Close = ih.Close * (string.IsNullOrEmpty(toCurrency)
                                    ? 1
                                    : SharkDbContextBase.GetRateForCurrencyExchange(i.CurrencyCode, toCurrency, ih.DateTime)),
                                Year = ih.DateTime.Year
                              })
          })
          .ToListAsync(cancellationToken: cancellationToken);
#pragma warning restore CS8604 // Possible null reference argument.

      var listResult = new List<InstrumentYearlyPerformancePrice>();

      foreach (var item in instruments)
      {
        listResult.Add(CalculateChangePercentage(item, numberOfYears));
      }

      return listResult;
    }

    public async Task<StockData> GetStockOverview(
        int instrumentId,
        Periods period = Periods.ONE_MONTH,
        DateTime? from = null,
        DateTime? to = null,
        string? toCurrency = null)
    {
      var ctx = CreateSharkDbContext(false);

      var baseCurrency = ctx.Instrument.FirstOrDefault(i => i.InstrumentId == instrumentId)?.CurrencyCode;
      var baseQuoteCurrency = $"{baseCurrency}{toCurrency}".ToUpper();

#pragma warning disable CS8604 // Possible null reference argument.
      var rawData = await ctx.InstrumentHistory
          .AsNoTracking()
          .Where(i => i.InstrumentId == instrumentId &&
                      i.DateTime >= from &&
                      i.DateTime <= to)
          .Select(i => new StockRawData
          {
            DateTime = i.DateTime,
            Close = i.Close * (string.IsNullOrEmpty(toCurrency)
                  ? 1
                  : SharkDbContextBase.GetRateForCurrencyExchange(baseCurrency, toCurrency, i.DateTime)),
            Volume = i.Volume
          })
          .OrderBy(i => i.DateTime)
          .ToListAsync();
#pragma warning restore CS8604 // Possible null reference argument.

#pragma warning disable CS8603 // Possible null reference return.
      if (rawData == null || !rawData.Any()) return null;
#pragma warning restore CS8603 // Possible null reference return.

      var firstPrice = rawData.First()?.Close;
      var lastPrice = rawData.Last()?.Close;

      var instrumentPrice = await ctx.InstrumentPrice
          .AsNoTracking()
          .FirstOrDefaultAsync(ip => ip.InstrumentId == instrumentId);
      var currencyRate = await ctx.CurrencyRates
          .AsNoTracking()
          .FirstOrDefaultAsync(cr => cr.Currencies == baseQuoteCurrency);

      var maxPriceStockRawData = rawData.MaxBy(t => t.Close);
      var minPriceStockRawData = rawData.MinBy(t => t.Close);

      var maxPrice = maxPriceStockRawData?.Close;
      var minPrice = minPriceStockRawData?.Close;
      var maxPriceDate = maxPriceStockRawData?.DateTime;
      var minPriceDate = minPriceStockRawData?.DateTime;

      if (instrumentPrice?.Last != null && currencyRate?.Rate != null)
      {
        var lastInstrumentPriceByCurrencyRate = instrumentPrice.Last * currencyRate.Rate;

        if (maxPriceStockRawData?.Close != null &&
            lastInstrumentPriceByCurrencyRate > maxPriceStockRawData.Close.Value)
        {
          maxPrice = lastInstrumentPriceByCurrencyRate;
          maxPriceDate = instrumentPrice.LastUpdatedDate;
        }

        if (minPriceStockRawData?.Close != null &&
            lastInstrumentPriceByCurrencyRate < minPriceStockRawData.Close.Value)
        {
          minPrice = lastInstrumentPriceByCurrencyRate;
          minPriceDate = instrumentPrice.LastUpdatedDate;
        }
      }

      var volumeTotal = 0L;
      var minVolumeDate = DateTime.MaxValue;
      var maxVolumeDate = DateTime.MinValue;

      foreach (var p in rawData)
      {
        volumeTotal += p.Volume ?? 0;
        if (minVolumeDate > p.DateTime)
          minVolumeDate = p.DateTime;
        if (maxVolumeDate < p.DateTime)
          maxVolumeDate = p.DateTime;
      }

      // calc first & last total return
      var dividends = await CalcDividendFactors(ctx, instrumentId, from.Value, to.Value, rawData);
      var firstTTR = (dividends.LastOrDefault(d => d.dDate <= from)?.k ?? 1) * firstPrice;
      var lastTTTR = (dividends.LastOrDefault(d => d.dDate <= to)?.k ?? 1) * lastPrice;

      return new StockData
      {
        InstrumentId = instrumentId,
        FirstPrice = firstPrice,
        LastPrice = lastPrice,

        Change = lastPrice - firstPrice,
        ChangePercentage = (lastPrice - firstPrice) * 100 / firstPrice,

        HighestPrice = maxPrice,
        HighestPriceDate = maxPriceDate,
        LowestPrice = minPrice,
        LowestPriceDate = minPriceDate,

        TotalVolume = volumeTotal,
        HighestVolumeDate = maxVolumeDate,
        LowestVolumeDate = minVolumeDate,

        TotalReturn = 100 * (lastTTTR - firstTTR) / firstTTR
      };
    }

    public async Task<IEnumerable<StockData>?> GetDailyStockData(
        int instrumentId,
        DateTime fromDate,
        DateTime toDate,
        List<int> peers,
        List<int> indices,
        List<int> mas,
        string? toCurrency = null,
        bool isRT = false)
    {
      var compact = IsCompact(peers, indices, mas);
      var ctx = CreateSharkDbContext(false);

      List<StockData>? stockDatas = null;

      var instrument = await ctx.Instrument.FindAsync(instrumentId);
      if (instrument == null) return stockDatas;

      // get daily stock data
      var dailyDt = await GetRawData(ctx, instrument, fromDate, toDate, Historical.DAILY, compact, toCurrency);
      if (!dailyDt.Any()) return stockDatas;

      // get dividend and split events to show daily event
      var dividends = await GetDividendEvents(ctx, instrument.ISIN, instrument.MarketID, fromDate, toDate);
      var splits = await GetSplitEvents(ctx, instrument.ISIN, instrument.MarketID, fromDate, toDate);

      if (compact) // return lastprice, change% only
      {
        // get compareData: peers & indices
        var compareDt = await GetPeersAndIndiciesData(ctx, Historical.DAILY, fromDate, toDate, peers, indices);

        stockDatas = dailyDt
            .Where(i => i.DateTime >= fromDate)
            .Select(i => new StockData
            {
              InstrumentId = i.InstrumentId,
              DateTime = DateTime.SpecifyKind(i.DateTime, DateTimeKind.Utc),
              Date = i.DateTime.ToString("yyyy-MM-dd"),
              LastPrice = i.Close,
              ChangePercentage = (i.Close - i.Open) * 100 / i.Open,
              DividendEvent = dividends?.Any(d => d.dDate == i.DateTime) ?? false,
              SplitEvent = splits?.FirstOrDefault(s => s.EffectiveDate == i.DateTime)?.Ratio,
              Compares = SelectCompareData(compareDt, i.DateTime),
              MA10 = GetMovingAverage(10, i.DateTime, dailyDt, mas),
              MA20 = GetMovingAverage(20, i.DateTime, dailyDt, mas),
              MA50 = GetMovingAverage(50, i.DateTime, dailyDt, mas)
            }).ToList();

        return stockDatas;
      }

      // get dividendFactors to calc totalReturn
      dividends = await CalcDividendFactors(dividends, dailyDt);

      stockDatas = dailyDt.Select(i => new StockData
      {
        InstrumentId = i.InstrumentId,
        DateTime = DateTime.SpecifyKind(i.DateTime, DateTimeKind.Utc),
        Date = i.DateTime.ToString("yyyy-MM-dd"),
        FirstPrice = i.Open,
        LastPrice = i.Close,
        ChangePercentage = (i.Close - i.Open) * 100 / i.Open,
        HighestPrice = i.High,
        LowestPrice = i.Low,
        TotalVolume = i.Volume,
        TotalReturn = (dividends.LastOrDefault(d => d.dDate <= i.DateTime)?.k ?? 1) * i.Close,
        DividendEvent = dividends.Any(d => d.dDate == i.DateTime),
        SplitEvent = splits.FirstOrDefault(s => s.EffectiveDate == i.DateTime)?.Ratio,
      }).ToList();

      return stockDatas;
    }

    public async Task<IEnumerable<StockData>?> GetWeeklyStockData(
        int instrumentId,
        DateTime fromDate,
        DateTime toDate,
        List<int> peers,
        List<int> indices,
        List<int> mas,
        string? toCurrency = null,
        bool isRT = false)
    {
      var compact = IsCompact(peers, indices, mas);
      var ctx = CreateSharkDbContext(false);

      List<StockData>? stockDatas = null;

      var instrument = await ctx.Instrument.FindAsync(instrumentId);
      if (instrument == null) return stockDatas;

      // get weekly stock data
      var weeklyDt = await GetRawData(ctx, instrument, fromDate, toDate, Historical.WEEKLY, compact, toCurrency);
      if (!weeklyDt.Any()) return stockDatas;

      if (compact) // return lastprice, change% only
      {
        // get weekly compare data: peers & indices
        var compareDt = await GetPeersAndIndiciesData(ctx, Historical.WEEKLY, fromDate, toDate, peers, indices);

        stockDatas = weeklyDt
            .Where(i => i.DateTime >= fromDate)
            .ToLookup(r => r.RoundDate).Select(grouped =>
            {
              var list = grouped.ToList();
              var date = list.First().RoundDate;
              var firstPrice = list.FirstOrDefault()?.Close;
              var lastPrice = list.LastOrDefault()?.Close;

              return new StockData
              {
                InstrumentId = instrumentId,
                DateTime = DateTime.SpecifyKind(date, DateTimeKind.Utc),
                Date = date.ToString("yyyy-MM-dd"),
                LastPrice = lastPrice,
                ChangePercentage = (lastPrice - firstPrice) * 100 / firstPrice,
                Compares = SelectCompareData(compareDt, date),
                MA10 = GetMovingAverage(10, date, weeklyDt, mas),
                MA20 = GetMovingAverage(20, date, weeklyDt, mas),
                MA50 = GetMovingAverage(50, date, weeklyDt, mas)
              };
            }).ToList();

        return stockDatas;
      }

      // get dividendFactors to calc totalReturn
      var dividends = await CalcDividendFactors(ctx, instrumentId, fromDate, toDate, weeklyDt);

      stockDatas = weeklyDt.ToLookup(r => r.RoundDate).Select(grouped =>
      {
        var list = grouped.ToList();
        var date = list.First().RoundDate;
        var firstPrice = list.FirstOrDefault()?.Close;
        var lastPrice = list.LastOrDefault()?.Close;

        return new StockData
        {
          InstrumentId = instrumentId,
          DateTime = DateTime.SpecifyKind(date, DateTimeKind.Utc),
          Date = date.ToString("yyyy-MM-dd"),
          FirstPrice = firstPrice,
          LastPrice = lastPrice,
          LowestPrice = list.Min(i => i.Close),
          HighestPrice = list.Max(i => i.Close),
          ChangePercentage = (lastPrice - firstPrice) * 100 / firstPrice,
          TotalReturn = (dividends.LastOrDefault(d => d.dDate <= date)?.k ?? 1) * lastPrice,
          TotalVolume = list.Sum(i => i.Volume)
        };
      }).ToList();

      return stockDatas;
    }

    public async Task<IEnumerable<StockData>?> GetMonthlyStockData(
        int instrumentId,
        DateTime fromDate,
        DateTime toDate,
        List<int> peers,
        List<int> indices,
        List<int> mas,
        string? toCurrency = null,
        bool isRT = false)
    {
      var compact = IsCompact(peers, indices, mas);
      var stockDatas = new List<StockData>();

            var ctx = CreateSharkDbContext(false);

      var instrument = await ctx.Instrument.FindAsync(instrumentId);
      if (instrument == null) return null;

      // get monthly stock data
      var monthlyDt = await GetRawData(ctx, instrument, fromDate, toDate, Historical.MONTHLY, compact, toCurrency);
      if (!monthlyDt.Any()) return null;

      if (compact) // return lastprice, change% only
      {
        // get monthly compare data: peers & indices
        var compareDt = await GetPeersAndIndiciesData(ctx, Historical.MONTHLY, fromDate, toDate, peers, indices);

        for (DateTime runFrom = fromDate; runFrom < toDate; runFrom = runFrom.AddMonths(1))
        {
          var runTo = runFrom.AddMonths(1);
          var monthlyData = monthlyDt.Where(r => r.DateTime >= runFrom && r.DateTime < runTo);

          var firstPrice = monthlyData.FirstOrDefault()?.Close;
          var lastPrice = monthlyData.LastOrDefault()?.Close;
          stockDatas.Add(new StockData
          {
            InstrumentId = instrumentId,
            DateTime = DateTime.SpecifyKind(runFrom, DateTimeKind.Utc),
            Date = runFrom.ToString("yyyy-MM-dd"),
            LastPrice = lastPrice,
            ChangePercentage = (lastPrice - firstPrice) * 100 / firstPrice,
            Compares = SelectCompareData(compareDt, runFrom),
            MA10 = GetMovingAverage(10, runFrom, monthlyDt, mas),
            MA20 = GetMovingAverage(20, runFrom, monthlyDt, mas),
            MA50 = GetMovingAverage(50, runFrom, monthlyDt, mas)
          });
        }

        return stockDatas;
      }

      // get dividendFactors to calc totalReturn
      var dividends = await CalcDividendFactors(ctx, instrumentId, fromDate, toDate, monthlyDt);

      for (DateTime runFrom = fromDate; runFrom < toDate; runFrom = runFrom.AddMonths(1))
      {
        var runTo = runFrom.AddMonths(1);
        var monthlyData = monthlyDt.Where(r => r.DateTime >= runFrom && r.DateTime < runTo);

        var firstPrice = monthlyData.FirstOrDefault()?.Close;
        var lastPrice = monthlyData.LastOrDefault()?.Close;
        stockDatas.Add(new StockData
        {
          InstrumentId = instrumentId,
          DateTime = DateTime.SpecifyKind(runFrom, DateTimeKind.Utc),
          Date = runFrom.ToString("yyyy-MM-dd"),
          FirstPrice = firstPrice,
          LastPrice = lastPrice,
          ChangePercentage = (lastPrice - firstPrice) * 100 / firstPrice,
          HighestPrice = (monthlyData != null && monthlyData.Any()) ? monthlyData.Max(m => m.Close) : null,
          LowestPrice = (monthlyData != null && monthlyData.Any()) ? monthlyData.Min(m => m.Close) : null,
          TotalVolume = (monthlyData != null && monthlyData.Any()) ? monthlyData.Sum(m => m.Volume) : null,
          TotalReturn = dividends != null ? (dividends.LastOrDefault(d => d.dDate <= runFrom)?.k ?? 1) * lastPrice : null
        });
      }

      return stockDatas;
    }


    /*********************************************/
    /* helper services for the InstrumentService */
    /*********************************************/

    private bool IsCompact(
        IEnumerable<int> peers,
        IEnumerable<int> indices,
        IEnumerable<int> mas)
    {
      if ((peers != null && peers.Any()) || (indices != null && indices.Any()) || (mas != null && mas.Any()))
      {
        return true;
      }
      return false;
    }


        private async Task<IEnumerable<DTOs.Deprecated.CompareInfo>> GetPeersAndIndiciesData(
            ISharkDbContext ctx,
            Historical type,
            DateTime fromDate,
            DateTime toDate,
            List<int> peers,
            List<int> indices)
        {
            // get compareData: peers & indices
            var compareDt = new List<DTOs.Deprecated.CompareInfo>();
            var compareIds = GetCompareIds(peers, indices);
            if (compareIds != null)
            {
                foreach (var id in compareIds)
                {
                    var instrument = await ctx.Instrument.FindAsync(id);
                    if (instrument == null) break;

          IEnumerable<CompareRawData> listChangePercentage = new List<CompareRawData>();

          switch (type)
          {
            case Historical.DAILY:
              listChangePercentage = await GetDailyCompareData(ctx, id, fromDate, toDate);
              break;

            case Historical.WEEKLY:
              listChangePercentage = await GetWeeklyCompareData(ctx, id, fromDate, toDate);
              break;

            case Historical.MONTHLY:
              listChangePercentage = await GetMonthlyCompareData(ctx, id, fromDate, toDate);
              break;

            default:
              listChangePercentage = await GetDailyCompareData(ctx, id, fromDate, toDate);
              break;

          }

                    compareDt.Add(new DTOs.Deprecated.CompareInfo
                    {
                        Id = instrument.InstrumentId,
                        Name = instrument.ShareName,
                        Data = listChangePercentage
                    });
                }
            }

      return compareDt;
    }

        private IEnumerable<CompareData> SelectCompareData(
            IEnumerable<DTOs.Deprecated.CompareInfo> data,
            DateTime date)
        {
            var result = new List<CompareData>();
            foreach (var item in data)
            {
                result.Add(new CompareData
                {
                    Id = item.Id,
                    Name = item.Name,
                    DateTime = date,
                    ChangePercentage = item.Data.FirstOrDefault(i => i.DateTime == date)?.ChangePercentage
                });
            }

      return result;
    }

    private async Task<IEnumerable<InstrumentHistory>> GetIntradayDataAsync(
        int instrumentId,
        Period period,
        DateTime? fromDate,
        DateTime? toDate,
        bool isRT = false,
        CancellationToken cancellationToken = default)
    {
      var instrumentIdParameter = new SqlParameter
      {
        ParameterName = "instrumentid",
        SqlDbType = System.Data.SqlDbType.Int,
        Value = instrumentId,
      };

      var datefromParameter = new SqlParameter
      {
        ParameterName = "datefrom",
        SqlDbType = System.Data.SqlDbType.DateTime,
        Value = fromDate,
      };

      var datetoParameter = new SqlParameter
      {
        ParameterName = "dateto",
        SqlDbType = System.Data.SqlDbType.DateTime,
        Value = toDate.HasValue ? toDate : DateTime.UtcNow,
      };

      var periodParameter = new SqlParameter
      {
        ParameterName = "agg_period",
        SqlDbType = System.Data.SqlDbType.VarChar,
        Value = GetPeriodValue(period),
      };

#pragma warning disable S3358 // Ternary operators should not be nested
      var factorParameter = new SqlParameter
      {
        ParameterName = "factor",
        SqlDbType = System.Data.SqlDbType.Int,
        Value = (period == Period.FIVE_MINUTE)
              ? 5
              : (period == Period.TEN_MINUTE)
              ? 10
              : 1
      };
#pragma warning restore S3358 // Ternary operators should not be nested

      var ctx = CreateSharkDbContext(isRT);
      if (isRT)
      {
        var sqlDailyHistoryRaw = GetRawQueryDailyHistory(isRT);
        // prepare params for procedure
        // create a linq query based on a rawQuery
#pragma warning disable CS0618 // Type or member is obsolete
#pragma warning disable CS0618 // Type or member is obsolete
        var dataByPeriod = await ctx.IceTradesHistories
                            .FromSqlRaw(sqlDailyHistoryRaw
                                ,
                                instrumentIdParameter,
                                datefromParameter,
                                datetoParameter,
                                factorParameter)
                            .AsNoTracking()
                            .AsAsyncEnumerable()
                            .Select(i => new InstrumentHistory
                            {
                              InstrumentId = instrumentId,
                              DateTime = i.Date.ToUtc(),
                              Close = i.Close,
                              Volume = i.Size,
                              High = i.High,
                              Low = i.Low,
                              Open = i.Open,
                              Date = i.Date.ToUtc().ToString()
                            })
                            .ToListAsync(cancellationToken);
#pragma warning restore CS0618 // Type or member is obsolete
#pragma warning restore CS0618 // Type or member is obsolete

#pragma warning disable CS0618 // Type or member is obsolete
#pragma warning disable CS0618 // Type or member is obsolete
        var lastPoint = ctx.InstrumentPrice
                            .FromSqlRaw($@"SELECT
	                                                TOP 1 *
                                                FROM
	                                                [InstrumentPrice]
                                                WHERE
	                                                InstrumentID = @instrumentid AND
                                                    [Date] >= @datefrom AND
                                                    [Date] <= @dateto
                                                ORDER BY
	                                                [Date] DESC"
                                ,
                                instrumentIdParameter,
                                datefromParameter,
                                datetoParameter)
                            .AsNoTracking()
                            .Select(i => new InstrumentHistory
                            {
                              InstrumentId = instrumentId,
                              DateTime = (i.LastUpdatedDate ?? new DateTime()).ToKindUtc(),
                              Close = i.Last ?? 0,
                              Volume = i.Volume,
                              High = i.High,
                              Low = i.Low,
                              Open = i.Open,
                              Date = (i.LastUpdatedDate ?? new DateTime()).ToKindUtc().ToString()
                            })
                            .FirstOrDefault();
#pragma warning restore CS0618 // Type or member is obsolete
#pragma warning restore CS0618 // Type or member is obsolete


        if (lastPoint != default
            && (lastPoint.DateTime.Minute % (int)factorParameter.Value != 0)
            )
        {
          // remove data post hours trade
          dataByPeriod.RemoveAll(s => s.DateTime > lastPoint.DateTime);
          if (dataByPeriod.Last().DateTime < lastPoint.DateTime)
          {
            dataByPeriod.Add(lastPoint);
          }
          else
          {
            dataByPeriod.Last().Close = lastPoint.Close;
          }
        }

        return dataByPeriod;
      }
      else
      {
#pragma warning disable CS0618 // Type or member is obsolete
        datefromParameter.Value = fromDate.ToCEST();
#pragma warning restore CS0618 // Type or member is obsolete
#pragma warning disable CS0618 // Type or member is obsolete
#pragma warning disable CS0618 // Type or member is obsolete
        datetoParameter.Value = toDate.HasValue ? toDate.ToCEST() : DateTime.UtcNow.ToCEST();
#pragma warning restore CS0618 // Type or member is obsolete
#pragma warning restore CS0618 // Type or member is obsolete


        // prepare params for procedure

        // create a linq query based on a rawQuery
#pragma warning disable CS0618 // Type or member is obsolete
#pragma warning disable CS0618 // Type or member is obsolete
        return await ctx.DailyHistory
                            .FromSqlRaw(
                                @"EXEC [dbo].[SGH3_GetDailyHistoryForGraph]
                    @instrumentid, @datefrom, @dateto, @agg_period",
                                instrumentIdParameter,
                                datefromParameter,
                                datetoParameter,
                                periodParameter)
                            .AsNoTracking()
                            .AsAsyncEnumerable()
                            .Select(i => new InstrumentHistory
                            {
                              InstrumentId = instrumentId,
                              DateTime = i.Date.ToKindUtc(),
                              Close = i.Close,
                              Volume = i.Volume,
                              High = i.High,
                              Low = i.Low,
                              Open = i.Open,
                              Date = i.Date.ToKindUtc().ToString()
                            })
                            .ToListAsync(cancellationToken);
#pragma warning restore CS0618 // Type or member is obsolete
#pragma warning restore CS0618 // Type or member is obsolete
      }
    }

    private string GetRawQueryDailyHistory(bool isRT)
    {
      if (isRT)
      {
        return $@"
                            SET NOCOUNT ON;
                            WITH CTE AS (
	                            SELECT
		                            aggregatedTime,
		                            [Open],
		                            [Close],
		                            [High],
		                            [Low],
		                            [Volume],
		                            rn1 = ROW_NUMBER ( ) OVER ( PARTITION BY aggregatedTime ORDER BY [Date] ),
		                            rn2 = ROW_NUMBER ( ) OVER ( PARTITION BY aggregatedTime ORDER BY [Date] DESC )
	                            FROM
		                            [rt_daily_history_ohlc] CROSS APPLY ( SELECT DATEADD( MINUTE, DATEDIFF( MINUTE, '1990-01-01T00:00:00', [Date] ) / @factor * @factor, '1990-01-01T00:00:00' ) ) AS x ( aggregatedTime )
	                            WHERE
		                            InstrumentID = @instrumentid
		                            AND [Date] >= @datefrom
		                            AND [Date] <= @dateto
	                            ) SELECT
	                            aggregatedTime AS [Date],
	                            MIN ( CASE WHEN rn1 = 1 THEN [Open] END ) AS [Open],
	                            MAX ( [High] ) AS [High],
	                            MIN ( [Low] ) AS [Low],
	                            MIN ( CASE WHEN rn2 = 1 THEN [Close] END ) AS [Close],
	                            SUM ( [Volume] ) AS [Volume]
                            FROM
	                            CTE
                            GROUP BY
	                            aggregatedTime";
      }
      else
      {
        return @$"SELECT
	                    DATEADD(
                            MINUTE,
                            (DATEDIFF(MINUTE, '1990-01-01T00:00:00', {GetFieldNameEntity("hDate", isRT)}) / @factor) * @factor,
                            '1990-01-01T00:00:00'
                        ) [date]
	                    ,MIN(CASE WHEN rn_asc = 1 THEN [{GetFieldNameEntity("hClose", isRT)}] END) AS [open]
	                    ,MAX([{GetFieldNameEntity("hClose", isRT)}]) AS [high]
	                    ,MIN([{GetFieldNameEntity("hClose", isRT)}]) AS [low]
	                    ,MIN(CASE WHEN rn_desc = 1 THEN [{GetFieldNameEntity("hClose", isRT)}] END) AS [close]
	                    ,SUM({GetFieldNameEntity("hSize", isRT)}) volume
	                    FROM    (
		                    SELECT  ROW_NUMBER() OVER (
			                    PARTITION BY InstrumentId,(DATEDIFF(MINUTE, '1990-01-01T00:00:00', {GetFieldNameEntity("hDate", isRT)}) / @factor)
			                    ORDER BY {GetFieldNameEntity("hDate", isRT)}) AS rn_asc
		                    ,ROW_NUMBER() OVER (
			                    PARTITION BY InstrumentId,(DATEDIFF(MINUTE, '1990-01-01T00:00:00', {GetFieldNameEntity("hDate", isRT)}) / @factor)
			                    ORDER BY {GetFieldNameEntity("hDate", isRT)} DESC) AS rn_desc
		                    ,*
		                    FROM [daily_history] WHERE [{GetFieldNameEntity("hDate", isRT)}]>@datefrom AND [{GetFieldNameEntity("hDate", isRT)}]<=@dateto AND InstrumentID = @instrumentid
		                    ) AS SubQueryAlias
	                    GROUP BY
		                    InstrumentId
		                    ,(DATEDIFF(MINUTE, '1990-01-01T00:00:00', {GetFieldNameEntity("hDate", isRT)}) / @factor)
	                    ORDER BY MAX({GetFieldNameEntity("hDate", isRT)})

                    ";
      }

    }

    private string GetFieldNameEntity(string fieldNameSource, bool isRealtime)
    {
      switch (fieldNameSource)
      {
        case "hDate":
        case "Date":
          return isRealtime ? "Date" : "hDate";
        case "hClose":
        case "Close":
          return isRealtime ? "Close" : "hClose";
        case "hSize":
        case "Volume":
          return isRealtime ? "Volume" : "hSize";
        default:
          return string.Empty;
      }
    }

    private async Task<IEnumerable<DividendEvt>> GetDividendEvents(
        ISharkDbContext ctx,
        string isin,
        short marketId,
        DateTime fromDate,
        DateTime toDate)
    {
      // prepare params for procedure
      var isinParam = new SqlParameter
      {
        ParameterName = "Isin",
        SqlDbType = System.Data.SqlDbType.Char,
        Value = FormatIsin(isin),
      };

      var marketParam = new SqlParameter
      {
        ParameterName = "MarketID",
        SqlDbType = System.Data.SqlDbType.TinyInt,
        Value = marketId,
      };

      var dateFromParam = new SqlParameter
      {
        ParameterName = "dateFrom",
        SqlDbType = System.Data.SqlDbType.DateTime,
        Value = fromDate,
      };

      var dateToParam = new SqlParameter
      {
        ParameterName = "dateTo",
        SqlDbType = System.Data.SqlDbType.DateTime,
        Value = toDate,
      };

      // create a linq query based on a rawQuery
      return await ctx.DividendEvts
          .FromSqlRaw(
              @"EXEC [dbo].[shg_GetDividendForChart]
                    @Isin, @MarketID, @dateFrom, @dateTo",
              isinParam,
              marketParam,
              dateFromParam,
              dateToParam)
          .AsNoTracking()
          .ToListAsync();
    }

    private string FormatIsin(string isin)
    {
      if (isin.Length < 12)
      {
        int spaceNumber = 12 - isin.Length;
        for (int i = 0; i < spaceNumber; i++)
        {
          isin += " ";
        }
      }

      return isin;
    }

    private async Task<IEnumerable<InstrumentHistory>> GetInstrumentHistoryDataAsync(
        int instrumentId,
        DateTime? fromDate,
        DateTime? toDate,
        bool? isRT,
        CancellationToken cancellationToken)
    {
      var currentPriceObj = (InstrumentHistory)null;

      var dateNow = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Utc);
      var dbContext = CreateSharkDbContext(isRT ?? false);
      if (fromDate <= dateNow && (!toDate.HasValue || (toDate >= dateNow)))
      {
        var currentPrice = dbContext.InstrumentPrice.FirstOrDefault(s => s.InstrumentId == instrumentId);

        if (currentPrice != null)
        {
          var GetDateTime = (DateTime? input) =>
          {
#pragma warning disable CS8629 // Nullable value type may be null.
#pragma warning disable CS0618 // Type or member is obsolete
            var result = input.Value.ToKindUtc();
#pragma warning restore CS0618 // Type or member is obsolete
#pragma warning restore CS8629 // Nullable value type may be null.
            return input.HasValue ? result : DateTime.UtcNow;
          };

          currentPriceObj = new InstrumentHistory
          {
            InstrumentId = currentPrice.InstrumentId,
            High = currentPrice.High,
            Close = currentPrice.Last ?? 0,
            Date = GetDateTime(currentPrice.LastUpdatedDate).ToString("yyyy-MM-dd"),
            DateTime = GetDateTime(currentPrice.LastUpdatedDate),
            Low = currentPrice.Low,
            Open = currentPrice.Open,
            Volume = currentPrice.Volume ?? 0
          };
        }
      }


#pragma warning disable CS0618 // Type or member is obsolete
#pragma warning disable CS0618 // Type or member is obsolete
      var result = await dbContext.InstrumentHistory
          .AsNoTracking()
          .Where(i => i.InstrumentId == instrumentId &&
                      (!fromDate.HasValue || i.DateTime >= fromDate) &&
                      (!toDate.HasValue || i.DateTime <= toDate))
          .OrderBy(i => i.DateTime)
          .Select(i => new InstrumentHistory
          {
            InstrumentId = i.InstrumentId,
            High = i.High,
            Low = i.Low,
            Open = i.Open,
            Close = i.Close,
            DateTime = i.DateTime.ToUtc(),
            Date = i.DateTime.ToUtc().ToString("yyyy-MM-dd"),
            Volume = i.Volume
          })
          .ToListAsync(cancellationToken);
#pragma warning restore CS0618 // Type or member is obsolete
#pragma warning restore CS0618 // Type or member is obsolete

      if (currentPriceObj == null) return result;

      if (!result.Any())
      {
        result.Add(currentPriceObj);
        return result;
      }

      if (result.Last().DateTime.Date < currentPriceObj.DateTime.Date)
      {
        result.Add(currentPriceObj);
      }
      else if (result.Last().DateTime.Date == currentPriceObj.DateTime.Date)
      {
        result.Last().Close = currentPriceObj.Close;
      }

      return result;
    }

    private string GetPeriodValue(Period period)
    {
      switch (period)
      {
        case Period.ONE_MINUTE:
          return "1Min";
        case Period.FIVE_MINUTE:
          return "5Min";
        case Period.TEN_MINUTE:
          return "10Min";
        case Period.ONE_HOUR:
          return "1H";
        default:
          return "1Min";
      }
    }

    /// <summary>
    /// suggestedDate's chartIq is smart
    /// and to turning our server performance
    /// ==> no need to check startDate & endDate in cached data
    /// </summary>
    /// <param name="cacheKey"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    private async Task<IEnumerable<InstrumentHistory>> GetDataFromCacheAsync(string cacheKey, CancellationToken ct)
    {
#pragma warning disable CS8603 // Possible null reference return.
      if (string.IsNullOrEmpty(cacheKey)) return null;
#pragma warning restore CS8603 // Possible null reference return.

      var cached = await _cache.GetAsync(cacheKey, ct);
#pragma warning disable CS8604 // Possible null reference argument.
      return ToList<InstrumentHistory>(cached);
#pragma warning restore CS8604 // Possible null reference argument.
    }

    /// <summary>
    /// Set instrument histories to distributed cache service
    /// </summary>
    /// <param name="key"></param>
    /// <param name="data"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    private async Task SetCacheAsync(string key, IEnumerable<InstrumentHistory> data, CancellationToken ct)
    {
      if (string.IsNullOrEmpty(key) || data == null || !data.Any()) return;

      var options = new DistributedCacheEntryOptions()
      {
        AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(_cacheSettings.ExpirationTime)
      };

      var bytes = ToByteArray(data);
      await _cache.SetAsync(key, bytes, options, ct);
    }

    /// <summary>
    /// Get cache info of instrument history
    /// </summary>
    private string PrepareCacheKey(int instrumentId, Period period, DateTime? fromDate, DateTime? toDate, bool isIntraday, bool isRT)
    {
      var type = isIntraday ? 1 : 0;
      var rt = isRT ? 1 : 0;
      var periodType = (int)period;
      var from = fromDate.HasValue ? fromDate.Value.ToString("yyyyMMddHHmm") : string.Empty;
      var to = toDate.HasValue ? toDate.Value.ToString("yyyyMMddHHmm") : string.Empty;
      var cacheKey = $"{instrumentId}_{type}_{periodType}_{from}_{to}_{rt}";
      return cacheKey;
    }

    private IEnumerable<T> ToList<T>(byte[] data)
    {
      try
      {
#pragma warning disable CS8603 // Possible null reference return.
        if (data is null) return default;
#pragma warning restore CS8603 // Possible null reference return.

        var jsonToDeserialize = Encoding.UTF8.GetString(data);
#pragma warning disable CS8603 // Possible null reference return.
        return JsonSerializer.Deserialize<IEnumerable<T>>(jsonToDeserialize);
#pragma warning restore CS8603 // Possible null reference return.
      }
      catch (Exception)
      {
#pragma warning disable CS8603 // Possible null reference return.
        return default;
#pragma warning restore CS8603 // Possible null reference return.
      }
    }

    private byte[] ToByteArray<T>(T data)
    {
      try
      {
        return JsonSerializer.SerializeToUtf8Bytes(data);
      }
      catch (Exception)
      {
#pragma warning disable CS8603 // Possible null reference return.
        return default;
#pragma warning restore CS8603 // Possible null reference return.
      }
    }

    private InstrumentYearlyPerformancePrice CalculateChangePercentage(InstrumentYearlyPerformancePrice instruments, int numberOfYears)
    {
      var listPerformance = new List<YearlyPerformancePrice>();
      for (int i = 0; i < numberOfYears; i++)
      {
        var performanceByYear = new YearlyPerformancePrice();
        int year = DateTime.Now.Year - i;
        performanceByYear.Year = year;

        if (ExistPerformanceByYear(instruments, year))
        {
#pragma warning disable CS8604 // Possible null reference argument.
          var closeCurrentYear = instruments.YearlyPerformances.FirstOrDefault(i => i.Year == year)?.Close ?? 0;
#pragma warning restore CS8604 // Possible null reference argument.
          var closeLastYear = instruments.YearlyPerformances.FirstOrDefault(i => i.Year == year - 1)?.Close ?? 0;
          performanceByYear.ChangePercentage = closeLastYear == 0 ? 0 :
                                              (closeCurrentYear / closeLastYear - 1) * 100;
          performanceByYear.CloseLastYear = closeLastYear;
          performanceByYear.Close = closeCurrentYear;
          listPerformance.Add(performanceByYear);
          continue;
        }

        // if instruments not exist with current year => set to 0
        performanceByYear.ChangePercentage = 0;

        listPerformance.Add(performanceByYear);
      }

      instruments.YearlyPerformances = listPerformance;
      return instruments;
    }

    private bool ExistPerformanceByYear(InstrumentYearlyPerformancePrice instruments, int year)
    {
#pragma warning disable CS8604 // Possible null reference argument.
      return instruments.YearlyPerformances.Any(i => i.Year == year) &&
             instruments.YearlyPerformances.Any(i => i.Year == year - 1);
#pragma warning restore CS8604 // Possible null reference argument.
    }

    private async Task<IEnumerable<DividendEvt>> CalcDividendFactors(
        ISharkDbContext ctx,
        int instrumentId,
        DateTime fromDate,
        DateTime toDate,
        IEnumerable<StockRawData> rawData)
    {
      var instrument = await ctx.Instrument.FindAsync(instrumentId);
#pragma warning disable CS8603 // Possible null reference return.
      if (instrument == null) return null;
#pragma warning restore CS8603 // Possible null reference return.

      var dividends = await GetDividendEvents(ctx, instrument.ISIN, instrument.MarketID, fromDate, toDate);

      decimal? k = 1;
      foreach (var dividend in dividends)
      {
        dividend.k = 1;

        var hClose = rawData.FirstOrDefault(i => i.DateTime == dividend.dDate)?.Close;
        if (hClose != null)
        {
          k = k * (1 + dividend.dDividend / hClose);
          dividend.k = k;
        }
      }

      return dividends;
    }

    private async Task<IEnumerable<DividendEvt>> CalcDividendFactors(
        IEnumerable<DividendEvt> dividends,
        IEnumerable<StockRawData> rawData)
    {
      decimal? k = 1;
      foreach (var dividend in dividends)
      {
        dividend.k = 1;

        var hClose = rawData.FirstOrDefault(i => i.DateTime == dividend.dDate)?.Close;
        if (hClose != null)
        {
          k = k * (1 + dividend.dDividend / hClose);
          dividend.k = k;
        }
      }

      return dividends;
    }

    private decimal? GetMovingAverage(int p, DateTime date, IEnumerable<StockRawData> data, List<int> mas)
    {
      if (mas != null && mas.Contains(p))
      {
        // calculate for p days
        return data
            .Where(i => i.DateTime <= date)
            .TakeLast(p)
            .Sum(i => i.Close) / p;
      }
      return null;
    }

    private List<int> GetCompareIds(List<int> peers, List<int> indies)
    {
      if (peers == null)
        return indies;

      if (indies == null)
        return peers;

      return peers.Concat(indies).ToList();
    }

    private async Task<IEnumerable<StockRawData>> GetRawData(
        ISharkDbContext ctx,
        Instrument instrument,
        DateTime fromDate,
        DateTime toDate,
        Historical type,
        bool compact,
        string? toCurrency = null)
    {
      // to calc moving average (if needed)
      var maDate = compact ? fromDate.AddMonths(-3) : fromDate;

      var baseCurrency = instrument.CurrencyCode?.ToUpper();

#pragma warning disable CS8604 // Possible null reference argument.
      return await ctx.InstrumentHistory
                  .AsNoTracking()
                  .Where(i => i.InstrumentId == instrument.InstrumentId &&
                              i.DateTime >= maDate &&
                              i.DateTime <= toDate)
                  .Select(i => new StockRawData
                  {
                    InstrumentId = i.InstrumentId,
                    DateTime = i.DateTime,
                    Open = i.Open,
                    High = i.High,
                    Low = i.Low,
                    Close = i.Close * (string.IsNullOrEmpty(toCurrency)
                          ? 1
                          : SharkDbContextBase.GetRateForCurrencyExchange(baseCurrency, toCurrency, i.DateTime)),
                    Volume = i.Volume,
                    RoundDate = type == Historical.WEEKLY ? fromDate.AddDays(Math.Floor((i.DateTime - fromDate).TotalDays / 7) * 7) : i.DateTime,
                  })
                  .OrderBy(i => i.DateTime)
                  .ToListAsync();
#pragma warning restore CS8604 // Possible null reference argument.
    }

    private async Task<IEnumerable<CompareRawData>> GetDailyCompareData(
        ISharkDbContext ctx,
        int instrumentId,
        DateTime fromDate,
        DateTime toDate)
    {
      return await ctx.InstrumentHistory
          .AsNoTracking()
          .Where(i => i.InstrumentId == instrumentId &&
                      i.DateTime >= fromDate &&
                      i.DateTime <= toDate)
          .AsAsyncEnumerable()
          .Select(i => new CompareRawData
          {
            DateTime = i.DateTime,
            ChangePercentage = (i.Close - i.Open) * 100 / i.Open
          })
          .OrderBy(i => i.DateTime)
          .ToListAsync();
    }

    private async Task<IEnumerable<CompareRawData>> GetWeeklyCompareData(
        ISharkDbContext ctx,
        int instrumentId,
        DateTime fromDate,
        DateTime toDate)
    {
      var rawData = await ctx.InstrumentHistory
          .AsNoTracking()
          .Where(i => i.InstrumentId == instrumentId &&
                      i.DateTime >= fromDate &&
                      i.DateTime <= toDate)
          .AsAsyncEnumerable()
          .Select(i => new StockRawData
          {
            RoundDate = fromDate.AddDays(Math.Floor((i.DateTime - fromDate).TotalDays / 7) * 7),
            DateTime = i.DateTime,
            Close = i.Close
          })
          .OrderBy(i => i.DateTime)
          .ToListAsync();

#pragma warning disable CS8603 // Possible null reference return.
      return rawData?.ToLookup(r => r.RoundDate).Select(grouped =>
      {
        var list = grouped.ToList();

        return new CompareRawData
        {
          DateTime = list.LastOrDefault().RoundDate,
          ChangePercentage = (list.LastOrDefault()?.Close - list.FirstOrDefault()?.Close) * 100 / list.FirstOrDefault()?.Close
        };
      });
#pragma warning restore CS8603 // Possible null reference return.
    }

    private async Task<IEnumerable<CompareRawData>> GetMonthlyCompareData(
        ISharkDbContext ctx,
        int instrumentId,
        DateTime fromDate,
        DateTime toDate)
    {
      var result = new List<CompareRawData>();

      var rawData = await ctx.InstrumentHistory
                  .AsNoTracking()
                  .Where(i => i.InstrumentId == instrumentId &&
                              i.DateTime >= fromDate &&
                              i.DateTime <= toDate)
                  .OrderBy(i => i.DateTime)
                  .ToListAsync();

      for (DateTime run = fromDate; run < toDate; run = run.AddMonths(1))
      {
        var runTo = run.AddMonths(1);
        var monthlyData = rawData.Where(r => r.DateTime >= run && r.DateTime < runTo);

        result.Add(new CompareRawData
        {
          DateTime = run,
          ChangePercentage = (monthlyData.LastOrDefault()?.Close - monthlyData.FirstOrDefault()?.Close) * 100 / monthlyData.FirstOrDefault()?.Close
        });
      }

      return result;
    }

    private async Task<IEnumerable<Splits>> GetSplitEvents(
        ISharkDbContext ctx,
        string isin,
        short marketId,
        DateTime fromDate,
        DateTime toDate)
    {

      return await ctx.Splits
          .AsNoTracking()
          .Where(s =>
              s.ISIN == isin &&
              s.Market == marketId &&
              s.SplitType == 1 &&
              s.EffectiveDate >= fromDate &&
              s.EffectiveDate <= toDate)
          .ToListAsync();

    }

        public async Task<DateTime> GetStartingDateByInstrument(int instrumentId)
        {
            var ctx = CreateSharkDbContext(false);
            return await ctx.InstrumentHistory.Where(x => x.InstrumentId == instrumentId).MinAsync(x => x.DateTime);
        }

        public async Task<List<LatestShareTradesDto>> GetLatestShareTrades(int instrumentId, int count,
            string? toCurrency = null, bool isRT = false, CancellationToken cancellationToken = default)
        {
            var ctx = CreateSharkDbContext(isRT);

#pragma warning disable CS0618 // Type or member is obsolete
      var trades = await ctx.InstrumentDailyData
                        .AsNoTracking()
                        .Where(s => s.InstrumentID == instrumentId)
                        .OrderByDescending(s => s.Date).ThenByDescending(dh => dh.Close).ThenByDescending(dh => dh.Volume)
                        .Take(count)
                        .Select(s => new LatestShareTradesDto
                        {
                          Close = s.Close,
                          Date = s.Date.ToKindUtc(),
                          Size = s.Volume ?? 0
                        })
                        .ToListAsync(cancellationToken);
#pragma warning restore CS0618 // Type or member is obsolete

      if (!string.IsNullOrEmpty(toCurrency) && trades.Any())
      {
        if (toCurrency.Length != 3)
        {
          throw new ArgumentException("`currency` length must be 3");
        }

        var instrument = await ctx.Instrument.FindAsync(instrumentId);

        var instrumentCurrency = instrument.CurrencyCode.ToUpper();
        toCurrency = toCurrency.ToUpper();
        if (instrumentCurrency != toCurrency)
        {
          var firstDate = trades.Min(h => h.Date);
          var lastDate = trades.Max(h => h.Date);

          var currencyRates = await _currencyRateService.GetRatesAsync(instrumentCurrency, toCurrency, firstDate, lastDate, isRT);

          foreach (var h in trades)
          {
            var closestRate = currencyRates
                .Where(c => c.Date <= h.Date)
                .DefaultIfEmpty()
                .MaxBy(c => c?.Date);

            if (closestRate != null)
            {
              h.Close *= closestRate.Value;
            }
          }
        }
      }

      return trades;
    }

        public async Task<List<LatestShareTradesDto>> GetTodayShareTrades(int instrumentId, string? toCurrency, bool isRT = false, CancellationToken cancellationToken = default)
        {
            var ctx = CreateSharkDbContext(isRT);

      var query = ctx.InstrumentDailyData
                              .Where(dh => dh.InstrumentID == instrumentId);



      var maxDate = query.Max(dh => dh.Date);

#pragma warning disable CS0618 // Type or member is obsolete
      var trades = query.Where(dh => dh.Date.Date == maxDate.Date)
                            .OrderByDescending(dh => dh.Date).ThenByDescending(dh => dh.Close).ThenByDescending(dh => dh.Volume)
                            .Select(dh => new LatestShareTradesDto
                            {
                              Date = dh.Date.ToKindUtc(),
                              Close = dh.Close,
                              Size = dh.Volume ?? 0
                            })
                            .ToList();
#pragma warning restore CS0618 // Type or member is obsolete

      if (!string.IsNullOrEmpty(toCurrency) && trades.Any())
      {
        if (toCurrency.Length != 3)
        {
          throw new ArgumentException("`currency` length must be 3");
        }

        var instrument = await ctx.Instrument.FindAsync(new object?[] { instrumentId }, cancellationToken: cancellationToken);

        var instrumentCurrency = instrument.CurrencyCode.ToUpper();
        toCurrency = toCurrency.ToUpper();
        if (instrumentCurrency != toCurrency)
        {
          var firstDate = trades.Min(h => h.Date);
          var lastDate = trades.Max(h => h.Date);

          var currencyRates = await _currencyRateService.GetRatesAsync(instrumentCurrency, toCurrency, firstDate, lastDate, isRT);

          foreach (var h in trades)
          {
            var closestRate = currencyRates
                .Where(c => c.Date <= h.Date)
                .DefaultIfEmpty()
                .MaxBy(c => c?.Date);

            if (closestRate != null)
            {
              h.Close *= closestRate.Value;
            }
          }
        }
      }

      return trades;
    }

    public async ValueTask DisposeAsync()
    {
      if (_localSharkDbContext != null)
      {
        await _localSharkDbContext.DisposeAsync();
      }
      if (_realtimeSharkDbContext != null)
      {
        await _realtimeSharkDbContext.DisposeAsync();
      }

            if(_currencyRateService != null){
                await _currencyRateService.DisposeAsync();
            }
        }

    public void Dispose()
    {
      DisposeAsync().ConfigureAwait(false).GetAwaiter().GetResult();
    }

    public async Task<Infrastructure.Entities.Instrument?> GetInstrumentByIdAsync̣(int instrumentId, bool isRT = false)
    {
      var ctx = CreateSharkDbContext(isRT);
      return await ctx.Instrument.FirstOrDefaultAsync(i => i.InstrumentId == instrumentId);

    }

    public IQueryable<DividendEvent> GetDividendEvents(int instrumentId, DateTime? fromDate, DateTime? toDate, bool isRT = false)
    {
      var ctx = CreateSharkDbContext(isRT);
      return ctx.Dividends
        .AsNoTracking()
        .Include(i => i.Instrument)
        .Where(i => i.InstrumentId == instrumentId &&
                    i.GrossDivAdj > 0 &&
                    (!fromDate.HasValue || i.Date >= fromDate) &&
                    (!toDate.HasValue || i.Date <= toDate))
        .OrderBy(i => i.Date)
        .ToModel();
    }

    public IQueryable<EarningEvent> GetEarningsEvents(string companyCode, DateTime? fromDate, DateTime? toDate, bool isRT = false)
    {
      var langCode = CultureInfo.CurrentCulture.Name;
      var typeId = 21; // earning event typeId
      var ctx = CreateSharkDbContext(isRT);

      return ctx.FCEvents
        .AsNoTracking()
        .Include(i => i.FCalendar)
        .Where(i => i.TypeId == typeId &&
                    i.FCalendar.CompanyCode == companyCode.ToUpper() &&
                    (!fromDate.HasValue || i.FCalendar.DateTime >= fromDate) &&
                    (!toDate.HasValue || i.FCalendar.DateTime <= toDate))
        .OrderBy(i => i.FCalendar.DateTime)
        .ToModel(langCode);
    }

    public async Task<IEnumerable<InstrumentDailyData>> GetLatestDailyDataAsync(int instrumentId, bool isRT = false)
    {
      var ctx = CreateSharkDbContext(isRT);
      var maxDate = await ctx.InstrumentDailyData.AsNoTracking()
        .Where(i => i.InstrumentID == instrumentId)
        .MaxAsync(i => (DateTime?)i.Date.Date);

#pragma warning disable CS0618 // Type or member is obsolete
      var result = await ctx.InstrumentDailyData.AsNoTracking()
        .Where(i => i.InstrumentID == instrumentId &&
                    EF.Functions.DateDiffDay(i.Date, maxDate) == 0)
        .OrderByDescending(i => i.Date)
        .Select(i => new InstrumentDailyData
        {
          ID = i.ID,
          InstrumentID = i.InstrumentID,
          Date = i.Date.ToKindUtc(),
          Close = i.Close,
          Volume = i.Volume.HasValue ? i.Volume.Value : 0
        })
        .ToListAsync();
#pragma warning restore CS0618 // Type or member is obsolete

      return result;
    }
  }
}
