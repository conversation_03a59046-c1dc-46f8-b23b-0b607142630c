import { useAuth } from "../../customHooks/useAuth";
import { UserMenu } from "./UserMenu";
import { useRef } from "react";
const UserInfo = () => {
  const auth = useAuth();
  const isTriggerLoginRef = useRef(false);
  const isLoggedIn = auth.isAuthenticated;

  const handleLogin = () => {
    isTriggerLoginRef.current = true;
    const login = window.EurolandAppContext?.command("login");
    login?.();
  };

  return (
    <div className="user-info__container">
      {isLoggedIn ? (
        <UserMenu isTriggerLoginRef={isTriggerLoginRef}/>
      ) : (
        <button className="user-info__promode-button" onClick={handleLogin}>Pro-mode</button>
      )}
    </div>
  );
};

export default UserInfo;
