using System;
using System.Linq;
using System.Collections.Generic;
using HotChocolate.Resolvers;
using Microsoft.Extensions.DependencyInjection;
using HotChocolate.Types.Pagination;

namespace Euroland.FlipIT.SData.API.GraphQL.Selections;

public class DefaultSelectionParamContext : ISelectionParamContext
{
  private readonly IResolverContext _resolverContext;
  private IReadOnlyList<IResolverProcessingSelection> _selectionFields;
  private IReadOnlyList<string> _selectionNames;

  public DefaultSelectionParamContext(IResolverContext resolverContext)
  {
    _resolverContext = resolverContext ?? throw new ArgumentNullException(nameof(resolverContext));
  }

  public virtual IResolverContext ResolverContext => _resolverContext;

  public IReadOnlyList<IResolverProcessingSelection> SelectionFields {
    get
    {
      if(_selectionFields == null)
      {
        _selectionFields = _resolverContext.GetResolverProcessingSelections();
      }

      return _selectionFields;
    }
  }

  public IReadOnlyList<string> SelectionNames
  {
    get
    {
      if(_selectionNames == null)
      {
#pragma warning disable CS8601 // Possible null reference assignment.
        _selectionNames = SelectionFields?.Select(s => s.SelectionName).Distinct(StringComparer.OrdinalIgnoreCase).ToList();
#pragma warning restore CS8601 // Possible null reference assignment.
      }

      return _selectionNames;
    }
  }

  public IReadOnlyList<string> ArgumentNames => throw new NotImplementedException();

  public IReadOnlyList<IArgumentValue> Arguments => throw new NotImplementedException();

  public IReadOnlyList<ISortOrderField> SortArguments => throw new NotImplementedException();

  public CursorPagingArguments PagingArgs => throw new NotImplementedException();

  public CursorPagingArguments CursorPagingArgs => throw new NotImplementedException();

  public OffsetPagingArguments OffsetPagingArgs => throw new NotImplementedException();

  public ResolverProcessingSelection TotalCountSelection => throw new NotImplementedException();

  public bool IsTotalCountRequested => throw new NotImplementedException();

  public IReadOnlyList<IResolverProcessingSelection> GetSelectionFieldsFor<TObjectType>()
  {
    throw new NotImplementedException();
  }

  public IEnumerable<string> GetSelectionMappedNamesFor<TObjectType>(SelectionNameFlags flags = SelectionNameFlags.All)
  {
    throw new NotImplementedException();
  }
}
