﻿using Newtonsoft.Json;

namespace Euroland.FlipIT.WiseSearch.Models.Search
{
    public class SearchResponse
    {
        [JsonProperty("source")]
        public string? Source { get; set; }
        [JsonProperty("answer")]
        public string? Answer { get; set; }
        [JsonProperty("key_values")]
        public string? KeyValues { get; set; }
        [JsonProperty("original_response")]
        public string? OriginalResponse { get; set; }
        [JsonProperty("prompt")]
        public string? Prompt { get; set; }
        [JsonProperty("qaLanguage")]
        public string QALanguage { get; set; }
        [JsonProperty("aiResultLanguage")]
        public string AIResultLanguage { get; set; }
    }
}
