using Microsoft.EntityFrameworkCore.Migrations;

namespace Euroland.FlipIT.SData.API.Infrastructure.Migrations
{
  public partial class create_scalar_funcs_to_get_share_information : Migration
    {
		protected override void Up(MigrationBuilder migrationBuilder)
		{
            migrationBuilder.Sql(@"DROP FUNCTION [dbo].[fnGetTimeToMarketOpen]");

            migrationBuilder.Sql(
				@"SET ANSI_NULLS ON
				GO
				SET QUOTED_IDENTIFIER ON
				GO

				-- =============================================
				-- Author:		<V L A D>
				-- Create date: <November 08, 2021>
				-- Description:	Gets time to market open
				-- Returns:
				-- -1: closed
				--	0: opening
				-- >0: time to open in minutes
				-- Example: SELECT dbo.fnGetTimeToMarketOpen(19) -- COP
				-- =============================================
				CREATE FUNCTION [dbo].[fnGetTimeToMarketOpen]
				(
					@MarketId INT
				)
				RETURNS INT
				AS
				BEGIN
					DECLARE @TimeToOpen INT -- in minutes

					DECLARE @MarketOpenTime		INT,
							@MarketCloseTime	INT,
							@CurrentMarketTime	DATETIME,
							@CurrentTime		INT,
							@TimeZone			VARCHAR(50),
							@BusinessDaysStoT	BIT

					SELECT	@MarketOpenTime = DATEPART(HOUR, CAST(ISNULL(MarketOpenTimeLocal, '00:00:00') AS DATETIME)) * 60 + DATEPART(MINUTE, CAST(ISNULL(MarketOpenTimeLocal, '00:00:00') AS DATETIME)),
							@MarketCloseTime = DATEPART(HOUR, CAST(ISNULL(MarketCloseTimeLocal, '23:59:59') AS DATETIME)) * 60 + DATEPART(MINUTE, CAST(ISNULL(MarketCloseTimeLocal, '23:59:59') AS DATETIME)),
							@CurrentMarketTime = DATEADD(MINUTE, DATEPART(tz, GETUTCDATE() AT TIME ZONE Timezone), GETUTCDATE()),
							@CurrentTime = DATEPART(HOUR, @CurrentMarketTime) * 60 + DATEPART(MINUTE, @CurrentMarketTime),
							@TimeZone = TimeZone,
							@BusinessDaysStoT = BusinessDaysStoT
					FROM	Market
					WHERE	MarketNumber = @MarketId

					DECLARE @IsWeekend BIT = 0
					-- market opens from Monday to Friday
					IF @BusinessDaysStoT = 0 AND DATEPART(WEEKDAY, @CurrentMarketTime) IN (1, 7) -- 1: Sunday; 7: Satuday
						SET @IsWeekend = 1

					-- market opens from Sunday to Thursday
					IF @BusinessDaysStoT = 1 AND DATEPART(WEEKDAY, @CurrentMarketTime) IN (6, 7) -- 6: Friday; 7: Satuday
						SET @IsWeekend = 1

					IF @IsWeekend = 1 -- weekends
					BEGIN
						SET @TimeToOpen = -1 -- closed
					END
					ELSE -- weekdays
					BEGIN
						-- normal case: check within trading hours
						IF @CurrentTime BETWEEN @MarketOpenTime AND @MarketCloseTime
							SET @TimeToOpen = 0 -- opening
						ELSE IF @CurrentTime < @MarketOpenTime
							SET @TimeToOpen = @MarketOpenTime - @CurrentTime -- time to open
						ELSE
							SET @TimeToOpen = -1 -- closed

						/* abnormal case: check market events which have been inserted manually by support */
						-- case 1: close all day
						-- fc_Event is 22 with time parts (hour, minute, second) are zero
						IF EXISTS
						(	SELECT	1
							FROM	CS_FCalendar_V4
							WHERE	fc_Event = 22 -- market closes
									AND DATEDIFF(DAY, fc_DateTime, @CurrentTime) = 0 -- in a specific date
									AND DATEPART(HOUR, fc_DateTime) = 0
									AND DATEPART(MINUTE, fc_DateTime) = 0
									AND DATEPART(SECOND, fc_DateTime) = 0
									AND fc_Market = @MarketId -- for a certain market
						)
						BEGIN
							SET @TimeToOpen = -1
						END
						-- case 2: close in a period of time
						-- fc_Event is 22 with time parts are not zero, market close at set time
						-- fc_Event is 149: market open at set time
						--> if exists both events 22 and 149, market temporary closed at event 22, then reopen at event 149
						--> if exists events 22 and not exists event 149, market closes at event 22 til end of day
						-- NOTE: CET time is used in case time part has set
						ELSE
						BEGIN
							DECLARE @CETNow DATETIME,
									@MarketEventClosedTime DATETIME,
									@MarketEventReopenTime DATETIME

							SET @CETNow = DATEADD(MINUTE, DATEPART(tz, GETUTCDATE() AT TIME ZONE 'W. Europe Standard Time'), GETUTCDATE())

							SELECT	TOP 1
									@MarketEventClosedTime = fc_DateTime
							FROM    CS_FCalendar_V4
							WHERE   fc_Event = 22
									AND DATEDIFF(DAY, @CETNow, fc_DateTime) = 0
									AND fc_Market = @MarketId

							SELECT	TOP 1
									@MarketEventReopenTime = fc_DateTime
							FROM    CS_FCalendar_V4
							WHERE   fc_Event = 149
									AND DATEDIFF(DAY, @CETNow, fc_DateTime) = 0
									AND fc_Market = @MarketId

							-- case: close then NOT open again
							IF	@MarketEventClosedTime IS NOT NULL	-- close
								AND @MarketEventReopenTime IS NULL	-- then not open again
								AND @CETNow >= @MarketEventClosedTime  -- after closed time
							BEGIN
								SET @TimeToOpen = -1 -- market closed
							END
							-- case: close then open again
							ELSE IF @MarketEventClosedTime IS NOT NULL		-- close
									AND @MarketEventReopenTime IS NOT NULL	-- then open again
									AND @CETNow BETWEEN @MarketEventClosedTime AND @MarketEventReopenTime
							BEGIN
								SET @TimeToOpen = DATEDIFF(MINUTE, @CETNow, @MarketEventReopenTime) -- time to reopen
							END
						END
					END

					RETURN @TimeToOpen
				END
				GO"
				);


            migrationBuilder.Sql(@"DROP FUNCTION [dbo].[fnGetRelativeVolume]");

            migrationBuilder.Sql(
				@"SET ANSI_NULLS ON
				GO
				SET QUOTED_IDENTIFIER ON
				GO

				-- ========================================================================
				-- Author:		<V L A D>
				-- Create date:	Nov 9, 2021
				-- Description: Gets volume change base on market status
				--		Relative Volume consists of Volume divided by Average Volume where Average Volume is a Simple Moving Average,
				--		calculated on the basis of the past 10 periods (not taking into account the current volume bar).
				--		Relative Volume = volume / average volume
				-- Params:
				--		+ @InstrumentId: Instrument Id
				--		+ @MAPeriod: number of transaction days

				-- Test: select dbo.fnGetRelativeVolume(32865, 10)
				-- ========================================================================
				CREATE FUNCTION [dbo].[fnGetRelativeVolume]
				(
					@InstrumentId	INT,
					@MAPeriod		INT
				)
				RETURNS DECIMAL(21, 10)
				AS
				BEGIN
					DECLARE @RelativeVolume DECIMAL(21, 10), -- [Relative Volume] = [Current Volume] / [Simple Moving Average]
							@LastVolume		BIGINT,
							@SMA			DECIMAL(21, 10), -- Simple Moving Average - calculated on the basis of the past 10 periods
							@LastUpdated	DATETIME

					SELECT	@LastUpdated = [Date],
							@LastVolume = Volume
					FROM	InstrumentPrice
					WHERE	InstrumentId = @InstrumentId

					SELECT	@SMA = AVG(Volume)
					FROM	(
								SELECT	TOP (@MAPeriod) Volume
								FROM	InstrumentHistory
								WHERE	DATEDIFF(DAY, [Date], @LastUpdated) > 0
										AND InstrumentId = @InstrumentId
								ORDER BY [Date] DESC
							) AS sma

					IF ISNULL(@SMA, 0) = 0
						RETURN NULL

					SET @RelativeVolume = @LastVolume / @SMA

					RETURN @RelativeVolume
				END
				GO");

      migrationBuilder.Sql(@"DROP FUNCTION [dbo].[fnGetTotalTrades]");

      migrationBuilder.Sql(
				@"SET ANSI_NULLS ON
				GO
				SET QUOTED_IDENTIFIER ON
				GO

				-- ========================================================================
				-- Author:		<ThinhPV>
				-- Create date:	Nov 9, 2021
				-- Description: Gets TotalTrades
				-- Params:
				--		+ @InstrumentId: Instrument Id
				--		+ @Date: last update date of this InstrumentPrice

				-- Test: select dbo.fnGetTotalTrades(32865, '2017-08-07 12:26:45.000')
				-- ========================================================================
				CREATE FUNCTION [dbo].[fnGetTotalTrades]
				(
					@InstrumentId INT,
					@Date DATE
				)
				RETURNS INT
				AS
				BEGIN
					DECLARE @TotalTrades INT;

					SELECT @TotalTrades = COUNT(InstrumentID) FROM daily_history WHERE InstrumentID = @InstrumentId AND DATEDIFF(DAY, hDate, @Date) = 0

					RETURN(@TotalTrades);
				END
				GO");


		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.Sql(@"DROP FUNCTION [dbo].[fnGetTimeToMarketOpen];");

			migrationBuilder.Sql(@"DROP FUNCTION [dbo].[fnGetRelativeVolume];");

			migrationBuilder.Sql(@"DROP FUNCTION [dbo].[fnGetTotalTrades];");

		}
	}
}
