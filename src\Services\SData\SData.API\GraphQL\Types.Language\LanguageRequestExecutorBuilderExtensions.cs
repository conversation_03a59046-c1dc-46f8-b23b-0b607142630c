using Euroland.FlipIT.SData.API.GraphQL.Types.Language;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class LanguageRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddLanguageType(this IRequestExecutorBuilder builder)
  {
    builder.AddType<LanguageType>();
    builder.AddTypeExtension<QueryTypeExtensions>();
    builder.AddTypeExtension<PressReleaseTypeExtensions>();

    builder.AddDataLoader<LanguageByIdDataLoader>();
    return builder;
  }
}
