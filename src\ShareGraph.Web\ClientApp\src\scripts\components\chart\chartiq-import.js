import { CIQ, timezoneJS } from 'chartiq/js/standard';
import 'chartiq/js/components';
import 'chartiq/js/componentUI';
import 'chartiq/js/addOns';
import 'chartiq/js/deprecated';
import getConfig from 'chartiq/js/defaultConfiguration';
import '../../../lib/chartiq/studies/bars-hlc.chartiq';
import ChartTemplate from './ChartTemplate';
// import getLicenseKey from 'chartiq/key';
import './translation';
import appConfig from '../../services/app-config';
import { CHART, CHART_SECOND_PERIODS } from '../../common';
import { dynamicSort, orderFilterCheckboxMenu, getKeyByCompany } from '../../helper';
import { MarkersCustom } from './marker';
import './timezones';
import './utils';
import './override-functions';
import './custom-web-components';
import './custom-drawing';

import i18n from '../../services/i18n';
import handleToggleInfo from './configuration/handleToggleInfo';
import handleToggleAnnotation from './configuration/handleToggleAnnotation';
import handleTogglePopIn from './configuration/handleTogglePopIn';
import onChartReady from './configuration/onChartReady';

/**
 * Available only on version 9.x.x
 */
// import getLicenseKey from "chartiq/key.js";
window?.getLicenseKey(CIQ);

// Creates a complete customised configuration object
function getCustomConfig({ chartId } = {}) {
  const config = getConfig();
  const shareGraphSettings = appConfig.get();
  const { customStorageKey, tempStorageKey, suffixStorageKey } = getKeyByCompany();
  config.chartId = `${chartId || '_advanced-chart'}-${suffixStorageKey}`;

  const localStorageChartKey = 'myChartLayout' + config.chartId;

  /* Custom toolip type display. If there is DYNAMIC_CALLOUT then dynamic: true, floating: false.
	/* If there is TOOLTIP: then dynamic: false, floating: true
	*/
  const isMobilePhone= CIQ.isMobile && !CIQ.ipad;
  const defaultTooltipType = shareGraphSettings.chart.defaultTooltipType.trim() || 'DYNAMIC_CALLOUT';
  const isDynamicCallout = isMobilePhone ? false : defaultTooltipType.toUpperCase() === CHART.TooltipType.DYNAMIC_CALLOUT;
  const isToolTip = isMobilePhone ? false : defaultTooltipType.toUpperCase() === CHART.TooltipType.TOOLTIP;
  const isStaticHeadsUp = isMobilePhone ? true : defaultTooltipType.toUpperCase() === CHART.TooltipType.STATIC;
  //Get chart type from xml setting
  const chartTypeDefaultSetting = shareGraphSettings.chart.defaultChartType.trim() || 'MOUNTAIN';
  const chartTypeDefault =
    CHART.TYPE.find(type => type.value.toUpperCase() === chartTypeDefaultSetting.trim().toUpperCase()) ||
    CHART.TYPE.find(type => type.value.toUpperCase() === 'MOUNTAIN');

  config.chartEngineParams = {
    layout: {
      chartType: chartTypeDefault.key,
      headsUp: {
        dynamic: isDynamicCallout,
        floating: isToolTip,
        static: isStaticHeadsUp
      },
      crosshair: true,
      chartScale: 'linear'
    },
    preferences: {
      labels: false,
      currentPriceLine: true,
      whitespace: 100
    },
    // chart: {
    // 	percentFormatter: function (parm){
    // 						//console.log(parm);
    // 						//return 11.00;
    // 	}
    // },
    controls: {
      // mSticky: null
    }
  };

  config.drawingTools = [
    {
      type: 'dt',
      tool: 'annotation',
      group: 'text',
      label: 'Annotation',
      shortcut: 'w'
    },
    {
      type: 'dt',
      tool: 'arrow',
      group: 'markings',
      label: 'Arrow',
      shortcut: 'a'
    },
    { type: 'dt', tool: 'line', group: 'lines', label: 'Line', shortcut: 'l' },
    {
      type: 'dt',
      tool: 'horizontal',
      group: 'lines',
      label: 'Horizontal',
      shortcut: 'o'
    },
    {
      type: 'dt',
      tool: 'vertical',
      group: 'lines',
      label: 'Vertical',
      shortcut: 'p'
    },
    { type: 'dt', tool: 'rectangle', group: 'markings', label: 'Rectangle', shortcut: 'r' },
    { type: 'dt', tool: 'segment', group: 'lines', label: 'Segment' }
  ];

  config.drawingToolGrouping = ['All', 'Favorites', 'Text', 'Markings', 'Lines'];

  config.menus.info.content = [
    { type: 'radio', label: 'Show Dynamic Callout', setget: 'Layout.HeadsUp', value: 'dynamic' },
    { type: 'radio', label: 'Show Tooltip', setget: 'Layout.HeadsUp', feature: 'tooltip', value: 'floating' },
    { type: 'radio', label: 'Show Heads-Up Static', setget: 'Layout.HeadsUp', feature: 'tooltip', value: 'static' }
  ];

  config.toggles.info.callbacks = [
    handleToggleInfo
  ];
  config.toggles.annotation = { callbacks: [handleToggleAnnotation] };
  config.toggles.popIn = { callbacks: [handleTogglePopIn] };

  //Custom display chart type from setting
  const enabledChartTypes = shareGraphSettings.chart.enabledChartTypes;
  if (enabledChartTypes && enabledChartTypes.length > 0) {
    const chartTypesOrdered = CHART.TYPE.filter(
      item => enabledChartTypes.indexOf(item.value) !== -1 || item.value === chartTypeDefaultSetting
    )
      .map(item => ({
        type: 'radioIcon',
        iconCls: item.iconCls,
        key: item.key,
        label: item.translationKey,
        cmd: `Layout.ChartType('${item.key}')`,
        order: enabledChartTypes.indexOf(item.value)
      }))
      .sort(dynamicSort('order'));
    config.menuChartStyle = chartTypesOrdered;
    config.menus.display.content = CHART.MENU_DISPLAY.filter(
      item => enabledChartTypes.indexOf(item.value.toUpperCase()) !== -1 || item.value.toUpperCase() === chartTypeDefaultSetting
    )
      .map(item => ({...item, order: enabledChartTypes.indexOf(item.value.toUpperCase())}))
      .sort(dynamicSort('order'));
  } else {
    config.menuChartStyle = CHART.TYPE.filter(item => item.value === chartTypeDefaultSetting).map(item => ({
      type: 'radioIcon',
      iconCls: item.iconCls,
      key: item.key,
      label: item.translationKey,
      cmd: `Layout.ChartType('${item.key}')`,
      order: enabledChartTypes.indexOf(item.value)
    }));
    config.menus.display.content = CHART.MENU_DISPLAY.filter(
      item => item.value.toUpperCase() === chartTypeDefaultSetting
    )
      .map(item => ({...item, order: enabledChartTypes.indexOf(item.value.toUpperCase())}))
      .sort(dynamicSort('order'));
  }

  //Config chart range
  const rangeMenuSetting = shareGraphSettings.chart.enabledPeriods || [];
  const rangeMenu = CHART.RANGE.filter(range =>
    rangeMenuSetting.find(rangeSet => rangeSet.trim().toLowerCase() === range.value.trim().toLowerCase())
  ).map(x => ({
    ...x,
    type: 'range',
    label: i18n.translate(x.translateKey),
    ariaLabel: i18n.translate(x.translateKeyAria) || i18n.translate(x.translateKey),
    cmd: `Layout.SelectionRange(${x.multiplier},'${x.base}',${x.periodicity.period},${x.periodicity.interval},'${x.periodicity.timeUnit}')`
  }));
  config.rangeMenu = rangeMenu;


  config.menuPeriodicity = [
    { type: 'item', label: '1 Min', cmd: 'Layout.setInterval(1,1,\'minute\')' },
    { type: 'item', label: '2 Mins', cmd: 'Layout.setInterval(2,1,\'minute\')' },
		{ type: 'item', label: '5 Mins', cmd: 'Layout.setInterval(1,5,\'minute\')' },
		{ type: 'item', label: '15 Mins', cmd: 'Layout.setInterval(3,5,\'minute\')' },
		{ type: 'item', label: '30 Mins', cmd: 'Layout.setInterval(6,5,\'minute\')' },
		{ type: 'item', label: '1 Hour', cmd: 'Layout.setInterval(12,5,\'minute\')' },
		{ type: 'item', label: '4 Hours', cmd: 'Layout.setInterval(48,5,\'minute\')' },
		{ type: 'item', label: '1 Day', cmd: 'Layout.setInterval(1,1,\'day\')' },
		{ type: 'item', label: '1 Week', cmd: 'Layout.setInterval(1,1,\'week\')' },
		{ type: 'item', label: '1 Month', cmd: 'Layout.setInterval(1,1,\'month\')' },
		{ type: 'item', label: '1 Year', cmd: 'Layout.setInterval(12,1,\'month\')' }
	];

  // // Custom Menu Period
  const tickerDefault = shareGraphSettings.instruments.filter(x => x.default).sort(dynamicSort('order')).find(x => x.default) ?? shareGraphSettings.instruments[0];
  let secondPeriods = [];
  if(tickerDefault.isRT && shareGraphSettings?.intervalOptions?.enabled && shareGraphSettings?.intervalOptions?.options?.length > 0) {
    secondPeriods = shareGraphSettings?.intervalOptions?.options.map(period => CHART_SECOND_PERIODS[period.toUpperCase()])?.filter(item => !!item);
  }
  config.menus.period.content = [
    ...secondPeriods,
    { type: 'item', label: '1 Min', tap: 'Layout.setInterval', value: [1, 1, 'minute'] },
    { type: 'item', label: '3 Mins', tap: 'Layout.setInterval', value: [3, 1, 'minute'] },
    { type: 'item', label: '5 Mins', tap: 'Layout.setInterval', value: [1, 5, 'minute'] },
    { type: 'item', label: '15 Mins', tap: 'Layout.setInterval', value: [3, 5, 'minute'] },
    { type: 'item', label: '30 Mins', tap: 'Layout.setInterval', value: [6, 5, 'minute'] },
    { type: 'item', label: '1 Hour', tap: 'Layout.setInterval', value: [12, 5, 'minute'] },
    { type: 'item', label: '4 Hours', tap: 'Layout.setInterval', value: [48, 5, 'minute'] },
    { type: 'item', label: '1 Day', tap: 'Layout.setInterval', value: [1, 1, 'day'] },
    { type: 'item', label: '1 Week', tap: 'Layout.setInterval', value: [1, 1, 'week'] },
    { type: 'item', label: '1 Month', tap: 'Layout.setInterval', value: [1, 1, 'month'] },
    { type: 'item', label: '1 Year', tap: 'Layout.setInterval', value: [12, 1, 'month'] }
  ];

  // Custom Menu Studies
  config.menus.studies.content = [
    { type: 'component', value: 'cq-study-legend', attributes: { class: 'shaded', heading: 'Current Indicator', 'button-remove': 'true', 'button-clear-all': 'Layout.clearStudies()' } },
    { type: 'component', value: 'cq-studies', attributes: { 'filter-name': 'studylist', favorites: '' } }
  ];

  config.addOns.tooltip = {
    ohl: true,
    series: true,
    studies: true,
    volume: true
  };

  const excludedChartStudies = CHART.STUDIES.filter(item => shareGraphSettings.chart.excludeStudies.includes(item.key));
  const EAStudiesObj = excludedChartStudies.reduce((obj, item) => Object.assign(obj, { [item.value]: true }), {});
  
  config.menuStudiesConfig = {
    // All studies available are by default included in the studies menu
    excludedStudies: {
      'Moving Average': true,
      'Standard Deviation': true,
      'True Range': true,
      'Median Price': true,
      'Price Rate of Change': true,
      'Momentum Indicator': true,
      'Price Relative': true,
      'Anchored VWAP': true,
      VWAP: true,
      ZigZag: true,
      // 'Volume Chart': true,
      ...EAStudiesObj
    },
    // list of studies to exclude
    alwaysDisplayDialog: { ma: true, AVWAP: true }
    /*dialogBeforeAddingStudy: {"rsi": true} // here's how to always show a dialog before adding the study*/
  };

    //document.querySelector('cq-studies').initialize();
  config.onChartReady = onChartReady.bind(undefined, { shareGraphSettings, config });

  config.themes.defaultTheme = 'ciq-day';

  //config.menuChartAggregates = [];

  // Custom ChartPreferences Menu
  config.menuChartPreferences = orderFilterCheckboxMenu(
    CHART.MENU_CHART_PREFERENCES,
    shareGraphSettings.chart.enabledChartPreferences
  );

  // Custom AxisPreferences Menu
  config.menuYAxisPreferences = orderFilterCheckboxMenu(
    CHART.MENU_YAXIS_PREFERENCES,
    shareGraphSettings.chart.enabledYAxisPreferences
  );
  
  // Custom Menu Preferences
  const {enabledChartPreferences, enabledYAxisPreferences} = shareGraphSettings.chart;
  // config.menus.preferences.content = [
  //   { type: 'heading', label: 'Chart Preferences' },
  //   ...CHART.MENU_PREFERENCES.filter(item => enabledChartPreferences.indexOf(item.key.toUpperCase()) !== -1),
  //   { type: 'heading', label: 'Y-Axis Preferences' },
  //   ...CHART.MENU_PREFERENCES.filter(item => enabledYAxisPreferences.indexOf(item.key.toUpperCase()) !== -1),
  //   ...CHART.MENU_PREFERENCES.filter(item => item.key === 'stripe_background')
  // ];

  config.menus.preferences.content = [
    { type: 'heading', label: 'Chart Preferences' },
    ...orderFilterCheckboxMenu(
      CHART.MENU_CHART_PREFERENCES,
      shareGraphSettings.chart.enabledChartPreferences
    ),
    { type: 'separator' },
    { type: 'heading', label: 'Y-Axis Preferences' },
    ...orderFilterCheckboxMenu(
      CHART.MENU_YAXIS_PREFERENCES,
      shareGraphSettings.chart.enabledYAxisPreferences
    )
  ];

  // Custom Events Menu
  config.menuChartEvents = orderFilterCheckboxMenu(CHART.EVENTS, [...shareGraphSettings.chart.enabledEvents, 'NONE']);
  const enabledEvents = shareGraphSettings.chart.enabledEvents;
  config.menus.markers.content = CHART.MENU_MARKERS.filter(item => enabledEvents.indexOf(item.key.toUpperCase()) !== -1);

  /* const {
	   marketDepth,
	   termStructure,
	   tfc,
	   timeSpanEventPanel,
	   visualEarnings
	 } = config.plugins;
   //Select only plugin configurations that needs to be active for this chart
	config.plugins = {
	   marketDepth,
	   termStructure,
	   tfc,
	   timeSpanEventPanel,
	   visualEarnings
	};

	 Enable / disable addOns
	 config.enabledAddOns.tooltip = false;
	 config.enabledAddOns.continuousZoom = true;
	 */

  config.enabledAddOns.fullScreen = false;
  config.enabledAddOns.extendedHours = false;

  config.customStorageKey = customStorageKey;
  config.localStorageChartKey = localStorageChartKey;
  config.tempStorageKey = tempStorageKey;
  config.ariaActive = [
    'cq-chart-instructions',
    'cq-toggle.tableview-ui',
    'cq-lookup',
    '.ciq-data-table-container'
  ];
  config.eventMarkersImplementation = MarkersCustom;
  return config;
}

CIQ.Drawing.prototype.dragToDraw = true;

export { CIQ, timezoneJS, i18n, ChartTemplate, getConfig, getCustomConfig };
