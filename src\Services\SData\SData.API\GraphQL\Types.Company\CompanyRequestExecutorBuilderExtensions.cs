using Euroland.FlipIT.SData.API.GraphQL.Types.Company;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class CompanyRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddCompanyType(this IRequestExecutorBuilder builder)
  {
    builder.AddType<CompanyType>();

    builder.AddTypeExtension<PressReleaseTypeExtensions>();
    builder.AddTypeExtension<InstrumentTypeExtensions>();

    builder.AddDataLoader<CompanyByCodeDataLoader>();

    return builder;
  }
}
