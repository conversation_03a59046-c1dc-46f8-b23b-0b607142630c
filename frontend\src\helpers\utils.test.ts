import { describe, it, expect } from 'vitest'
import { currentRouter } from './utils'

describe('currentRouter', () => {
  it('should return the first path segment when no subPath is provided', () => {
    const href = 'https://example.com/dashboard/users'
    expect(currentRouter(href).at(0)).toBe('dashboard')
  })

  it('should return the first path segment when subPath is provided', () => {
    const href = 'https://example.com/dashboard/users'
    const subPath = '/users'
    expect(currentRouter(href, subPath).at(0)).toBe('dashboard')
  })

  it('should return undefined for root path', () => {
    const href = 'https://example.com/'
    expect(currentRouter(href).at(0)).toBeUndefined()
  })

  it('should handle URLs with query parameters', () => {
    const href = 'https://example.com/dashboard?param=value'
    expect(currentRouter(href).at(0)).toBe('dashboard')
  })

  it('should handle URLs with hash', () => {
    const href = 'https://example.com/dashboard#section'
    expect(currentRouter(href).at(0)).toBe('dashboard')
  })

  it('should handle complex subPath scenarios', () => {
    const href = 'https://example.com/dashboard/users/profile'
    const subPath = '/dashboard'
    expect(currentRouter(href, subPath).at(0)).toBe('users')
  })
})
