using System.Linq;
using AutoMapper;
using Euroland.FlipIT.SData.API.Dto.Webcast;
using Euroland.FlipIT.SData.API.Infrastructure.Entities.Webcast;

namespace Euroland.FlipIT.SData.API.AutoMapper.Profiles;

public class WebcastMapperProfile : Profile
{
  public WebcastMapperProfile()
  {
    CreateMap<CompanyVimeoFolder, CompanyVimeoFolderDto>();

    CreateMap<Webcast, WebcastDto>()
      .ForMember(dest => dest.TranscriptUrl,
        opt =>
          opt.MapFrom(src => $"{src.CompanyCode}/{src.Id}/transcript{src.TranscriptType}"))

      .ForMember(dest => dest.ThumbnailUrl,
        opt =>
          opt.MapFrom(src => $"{src.CompanyCode}/{src.Id}/thumbnail{src.ThumbnailType}"))

      .ForMember(dest => dest.Videos, opt =>
        opt.MapFrom(src => src.WebcastUrls
        .Where(a => a.WebcastHost.Name != WebcastHost.Vimeo)
        .Select(a => new WebcastVideoDto
        {
          Url = $"{src.CompanyCode}/{src.Id}/webcast{src.FileType}",
          HostType = a.WebcastHost.Name
        })))

      .ForMember(dest => dest.Vimeo, opt => opt.MapFrom(src => src.WebcastUrls
        .Where(a => a.WebcastHost.Name == WebcastHost.Vimeo
                    && a.UploadStatus.Name == UploadStatus.Success)
        .Select(a => new WebcastVimeoDto
        {
          VideoId = a.Id.ToString(),
          VimeoId = a.OriginalId,
          HostType = a.WebcastHost.Name
        })
        .FirstOrDefault()));

    CreateMap<UploadStatus, UploadStatusDto>();

    CreateMap<WebcastHost, WebcastHostDto>();

    CreateMap<WebcastSource, WebcastSourceDto>();

    CreateMap<WebcastTranslation, WebcastTranslationDto>();

    CreateMap<WebcastType, WebcastTypeDto>();

    CreateMap<WebcastUrl, WebcastUrlDto>();
  }
}
