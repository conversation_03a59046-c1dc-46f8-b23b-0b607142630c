using Euroland.FlipIT.SData.API.GraphQL.Extensions;
using HotChocolate.Execution.Configuration;
using HotChocolate.Internal;

namespace Microsoft.Extensions.DependencyInjection;

public static class RuntimeSharkDbContextDeciderMiddlewareExtensions
{
  public static IRequestExecutorBuilder RegisterRuntimeSharkDbContext(this IRequestExecutorBuilder builder)
  {
    builder.Services.AddSingleton<IParameterExpressionBuilder>(
            new SharkDbContextParameterExpressionBuilder(HotChocolate.Data.DbContextKind.Pooled));

    return builder;
  }
}
