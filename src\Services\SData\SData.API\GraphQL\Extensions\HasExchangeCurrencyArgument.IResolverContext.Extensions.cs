using HotChocolate.Resolvers;
using HotChocolate.Types;

namespace Microsoft.Extensions.DependencyInjection;

public static class HasExchangeCurrencyArgumentIResolverContextExtensions
{
  public const string EXCHANGE_CURRENCY_ARGUMENT = "exchangeCurrency";
  public static IObjectFieldDescriptor AddExchangeCurrencyArgument(this IObjectFieldDescriptor descriptor)
  {
    return descriptor.Argument(EXCHANGE_CURRENCY_ARGUMENT, desc =>
      desc
        .Description("Quote currency to convert all prices from share's currency to.")
        .Type<StringType>()
        .DefaultValue(null)
    );
  }

  /// <summary>
  /// Determines whether the currency execution tree hierarchy has @exchangeCurrency argument.
  /// </summary>
  /// <param name="resolverContext">The current <see cref="IResolverContext"/></param>
  /// <returns>true if found value of @exchangeCurrency. Otherwise, false.</returns>
  public static bool HasExchangeCurrency(this IResolverContext resolverContext) {
    return resolverContext.ScopedContextData.ContainsKey(EXCHANGE_CURRENCY_ARGUMENT)
      && !string.IsNullOrEmpty(resolverContext.ScopedContextData[EXCHANGE_CURRENCY_ARGUMENT] as string);
  }

  /// <summary>
  /// Gets the value that is associated with the @exchangeCurrencyCode argument.
  /// </summary>
  /// <param name="resolverContext">The current <see cref="IResolverContext"/></param>
  /// <param name="exchangeCurrencyCode">
  /// When this method returns, the value associated with @exchangeCurrencyCode argument, if the key is found;
  /// otherwise, the default value for the type of the value parameter. This parameter is passed uninitialized.
  /// </param>
  /// <returns>true if found value of @exchangeCurrency. Otherwise, false.</returns>
  public static bool TryGetExchangeCurrency(this IResolverContext resolverContext, out string? exchangeCurrencyCode)
  {
    var hasExchangeCurrency = resolverContext.ScopedContextData.ContainsKey(EXCHANGE_CURRENCY_ARGUMENT);

    if(hasExchangeCurrency) {
      exchangeCurrencyCode = resolverContext.ScopedContextData[EXCHANGE_CURRENCY_ARGUMENT] as string;
      if(string.IsNullOrEmpty(exchangeCurrencyCode))
      {
        hasExchangeCurrency = false;
        exchangeCurrencyCode = null;
      }
    }
    else {
      exchangeCurrencyCode = null;
    }
    return hasExchangeCurrency;
  }
}
