﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.WiseSearch.Infrastructure.Entities.WiseSearch
{
    [Table("WiseSearch_IngestedActivities")]
    public class IngestedActivityEntity
    {
        public Guid Id { get; set; }
        public string? CompanyCode { get; set; }
        public string? UserName { get; set; }
        public string? Email { get; set; }
        public string? Name { get; set; }
        public string Ip { get; set; } = null!;
        public string Type { get; set; } = null!;
        public string? ActivityId { get; set; }
        public string? Question { get; set; }
        public string? Answer { get; set; }
        public DateTime Timestamp { get; set; }
        public string? UserAgent { get; set; }
    }
}
