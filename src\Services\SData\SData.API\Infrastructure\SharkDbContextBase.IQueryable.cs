using System.Linq;
using Euroland.FlipIT.SData.API.Infrastructure.Configurations;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.SData.API.Infrastructure;

public static class SharkDbContextBaseIQueryableExtensions
{
  public static IQueryable<TemporaryCurrencyRateHistory> InstrumentCurrencyPairQueryable(this SharkDbContextBase sharkDbContext, string baseQuoteCurrencyPair)
  {
    return sharkDbContext.Set<TemporaryCurrencyRateHistory>()
      .TagWith($"{TemporaryCurrencyRateHistoryConfiguration.TemporaryCurrencyRateHistoryTableName} <currencyPair:{baseQuoteCurrencyPair}><factor:0><fixedRate:0> {TemporaryCurrencyRateHistoryDbCommandInterceptor.END_TAG_PLACEHOLDER}");
  }
}
