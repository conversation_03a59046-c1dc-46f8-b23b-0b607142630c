using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Company;

/// <summary>
/// Extends 'company' field to type <see cref="Pressrelease.PressReleaseType"/>
/// </summary>
public class InstrumentTypeExtensions: ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(Instrument.InstrumentType.Name);

    descriptor.Field("company")
      .Type<CompanyType>()
      .ResolveWith<CompanyResolvers>(
        resolvers => resolvers.GetCompanyByInstrument(default!, default, default!, default)
      );
  }
}
