using System.Text.Json;
using System.Text.RegularExpressions;
using AutoMapper;
using Euroland.FlipIT.Fundamental.API.Dtos.Filters;
using Euroland.FlipIT.Fundamental.API.Dtos.Sorts;
using Euroland.FlipIT.Fundamental.API.Services;
using HotChocolate.Data.Filters;
using HotChocolate.Resolvers;
using Newtonsoft.Json.Linq;

namespace Euroland.FlipIT.Fundamental.API.Dtos;

public class FundamentalDto
{
  public string CompanyCode { get; set; }

  private static async Task<IEnumerable<JToken>> GetData(
    string source,
    string tableName,
    IResolverContext resolverContext,
    FundamentalService fundamentalService,
    CancellationToken cancellationToken)
  {
    resolverContext.SetScopedState(nameof(source), source);
    TimeFilterInputType.SaveFilterContext(resolverContext);

    var root = await fundamentalService.GetTableData(resolverContext, tableName, cancellationToken);
    var data = root as JArray;

    return TimeSortInputType.Sort(data, resolverContext).AsEnumerable();
  }

  [GraphQLType("[IncomeStatement!]")]
  [GraphQLName("incomeStatement")]
  [UseOffsetPaging]
  [UseFiltering(Type = typeof(TimeFilterInputType))]
  [UseSorting(Type = typeof(TimeSortInputType))]
  public async Task<IEnumerable<JToken>> GetIncomeStatement(
    string source,
    IResolverContext resolverContext,
    [Service] FundamentalService fundamentalService,
    CancellationToken cancellationToken = default)
  {
    return await GetData(source, "Income Statement", resolverContext, fundamentalService, cancellationToken);
  }

  [GraphQLType("[BalanceSheet!]")]
  [GraphQLName("balanceSheet")]
  [UseOffsetPaging]
  [UseFiltering(Type = typeof(TimeFilterInputType))]
  [UseSorting(Type = typeof(TimeSortInputType))]
  public async Task<IEnumerable<JToken>> GetBalanceSheet(
    string source,
    IResolverContext resolverContext,
    [Service] FundamentalService fundamentalService,
    CancellationToken cancellationToken = default)
  {
    return await GetData(source, "Balance Sheet", resolverContext, fundamentalService, cancellationToken);
  }

  [GraphQLType("[CashFlowStatement!]")]
  [GraphQLName("cashFlowStatement")]
  [UseOffsetPaging]
  [UseFiltering(Type = typeof(TimeFilterInputType))]
  [UseSorting(Type = typeof(TimeSortInputType))]
  public async Task<IEnumerable<JToken>> GetCashFlowStatement(
    string source,
    IResolverContext resolverContext,
    [Service] FundamentalService fundamentalService,
    CancellationToken cancellationToken = default)
  {
    return await GetData(source, "Cash Flow Statement", resolverContext, fundamentalService, cancellationToken);
  }

  [GraphQLType("[AnalystEstimates!]")]
  [GraphQLName("analystEstimates")]
  [UseOffsetPaging]
  [UseFiltering(Type = typeof(EstimatesFilterInputType))]
  [UseSorting(Type = typeof(TimeSortInputType))]
  public async Task<IEnumerable<JToken>> GetEstimates(
    string source,
    IResolverContext resolverContext,
    [Service] FundamentalService fundamentalService,
    CancellationToken cancellationToken = default)
  {
    resolverContext.SetScopedState(nameof(source), source);
    TimeFilterInputType.SaveFilterContext(resolverContext);

    var root = await fundamentalService.GetTableData(resolverContext, "Analyst Estimates", cancellationToken);
    var data = root as JArray;

    var filterContextDictionary = resolverContext.GetFilterContext()?.ToDictionary();
    if (filterContextDictionary is not null)
    {
#pragma warning disable S1066 // Collapsible "if" statements should be merged
      if (filterContextDictionary.TryGetValue("calculation", out var calculation) &&
        calculation is Dictionary<string, object> calculationDictionary)
      {
        if (calculationDictionary.TryGetValue("eq", out var calculationEq) &&
          calculationEq is string calculationEqString)
        {
          return data.Where(x => x.SelectToken("calculation").Value<string>() == calculationEqString).ToList();
        }
      }
#pragma warning restore S1066 // Collapsible "if" statements should be merged
    }

    return TimeSortInputType.Sort(data, resolverContext);
  }

  [GraphQLType("[Ratios!]")]
  [GraphQLName("ratios")]
  [UseOffsetPaging]
  [UseFiltering(Type = typeof(TimeFilterInputType))]
  [UseSorting(Type = typeof(TimeSortInputType))]
  public async Task<IEnumerable<JToken>> GetRatios(
    string source,
    IResolverContext resolverContext,
    [Service] FundamentalService fundamentalService,
    CancellationToken cancellationToken = default)
  {
    return await GetData(source, "Ratios", resolverContext, fundamentalService, cancellationToken);
  }

  [GraphQLType("[RatiosTTM!]")]
  [GraphQLName("ratiosTTM")]
  [UseOffsetPaging]
  [UseFiltering(Type = typeof(TimeFilterInputType))]
  [UseSorting(Type = typeof(TimeSortInputType))]
  public async Task<IEnumerable<JToken>> GetRatiosTTM(
    string source,
    IResolverContext resolverContext,
    [Service] FundamentalService fundamentalService,
    CancellationToken cancellationToken = default)
  {
    return await GetData(source, "Ratios TTM", resolverContext, fundamentalService, cancellationToken);
  }

  [GraphQLType("[KeyMetrics!]")]
  [GraphQLName("keyMetrics")]
  [UseOffsetPaging]
  [UseFiltering(Type = typeof(TimeFilterInputType))]
  [UseSorting(Type = typeof(TimeSortInputType))]
  public async Task<IEnumerable<JToken>> GetKeyMetrics(
    string source,
    IResolverContext resolverContext,
    [Service] FundamentalService fundamentalService,
    CancellationToken cancellationToken = default)
  {
    return await GetData(source, "Key Metrics", resolverContext, fundamentalService, cancellationToken);
  }

  [GraphQLType("[KeyMetricsTTM!]")]
  [GraphQLName("keyMetricsTTM")]
  [UseOffsetPaging]
  [UseFiltering(Type = typeof(TimeFilterInputType))]
  [UseSorting(Type = typeof(TimeSortInputType))]
  public async Task<IEnumerable<JToken>> GetKeyMetricsTTM(
    string source,
    IResolverContext resolverContext,
    [Service] FundamentalService fundamentalService,
    CancellationToken cancellationToken = default)
  {
    return await GetData(source, "Key Metrics TTM", resolverContext, fundamentalService, cancellationToken);
  }

  [GraphQLType("[IncomeStatementGrowth!]")]
  [GraphQLName("incomeStatementGrowth")]
  [UseOffsetPaging]
  [UseFiltering(Type = typeof(TimeFilterInputType))]
  [UseSorting(Type = typeof(TimeSortInputType))]
  public async Task<IEnumerable<JToken>> GetIncomeStatementGrowth(
    string source,
    IResolverContext resolverContext,
    [Service] FundamentalService fundamentalService,
    CancellationToken cancellationToken = default)
  {
    return await GetData(source, "Income Statement Growth", resolverContext, fundamentalService, cancellationToken);
  }

  [GraphQLType("[BalanceSheetGrowth!]")]
  [GraphQLName("balanceSheetGrowth")]
  [UseOffsetPaging]
  [UseFiltering(Type = typeof(TimeFilterInputType))]
  [UseSorting(Type = typeof(TimeSortInputType))]
  public async Task<IEnumerable<JToken>> GetBalanceSheetGrowth(
    string source,
    IResolverContext resolverContext,
    [Service] FundamentalService fundamentalService,
    CancellationToken cancellationToken = default)
  {
    return await GetData(source, "Balance Sheet Growth", resolverContext, fundamentalService, cancellationToken);
  }

  [GraphQLType("[CashFlowGrowth!]")]
  [GraphQLName("cashFlowGrowth")]
  [UseOffsetPaging]
  [UseFiltering(Type = typeof(TimeFilterInputType))]
  [UseSorting(Type = typeof(TimeSortInputType))]
  public async Task<IEnumerable<JToken>> GetCashFlowGrowth(
    string source,
    IResolverContext resolverContext,
    [Service] FundamentalService fundamentalService,
    CancellationToken cancellationToken = default)
  {
    return await GetData(source, "Cash Flow Growth", resolverContext, fundamentalService, cancellationToken);
  }

  [GraphQLType("[FinancialGrowth!]")]
  [GraphQLName("financialGrowth")]
  [UseOffsetPaging]
  [UseFiltering(Type = typeof(TimeFilterInputType))]
  [UseSorting(Type = typeof(TimeSortInputType))]
  public async Task<IEnumerable<JToken>> GetFinancialGrowth(
    string source,
    IResolverContext resolverContext,
    [Service] FundamentalService fundamentalService,
    CancellationToken cancellationToken = default)
  {
    return await GetData(source, "Financial Growth", resolverContext, fundamentalService, cancellationToken);
  }

  [GraphQLType("[FinancialScore!]")]
  [GraphQLName("financialScore")]
  [UseOffsetPaging]
  [UseFiltering(Type = typeof(TimeFilterInputType))]
  [UseSorting(Type = typeof(TimeSortInputType))]
  public async Task<IEnumerable<JToken>> GetFinancialScore(
    string source,
    IResolverContext resolverContext,
    [Service] FundamentalService fundamentalService,
    CancellationToken cancellationToken = default)
  {
    return await GetData(source, "Financial Score", resolverContext, fundamentalService, cancellationToken);
  }

  [GraphQLType("[OwnerEarnings!]")]
  [GraphQLName("ownerEarnings")]
  [UseOffsetPaging]
  [UseFiltering(Type = typeof(TimeFilterInputType))]
  [UseSorting(Type = typeof(TimeSortInputType))]
  public async Task<IEnumerable<JToken>> GetOwnerEarnings(
    string source,
    IResolverContext resolverContext,
    [Service] FundamentalService fundamentalService,
    CancellationToken cancellationToken = default)
  {
    return await GetData(source, "Owner Earnings", resolverContext, fundamentalService, cancellationToken);
  }

  public async Task<AnalystRecommendationDto?> GetAnalystRecommendation(
    DateTime showDataFrom,
    IResolverContext resolverContext,
    [Service] AnalystEstimatesService analystEstimatesService,
    CancellationToken cancellationToken = default)
  {
    return await analystEstimatesService.GetAnalystRecommendation(resolverContext, showDataFrom, cancellationToken);
  }

  [GraphQLName("segments")]
  [UseOffsetPaging]
  [UseFiltering(Type = typeof(TimeFilterInputType))]
  [UseSorting(Type = typeof(TimeSortInputType))]
  public async Task<IEnumerable<SegmentsDto>> GetSegments(
    string source,
    string? segmentKeyFilter,
    string? detailKeyFilter,
    IResolverContext resolverContext,
    [Service] IMapper mapper,
    [Service] FundamentalService fundamentalService,
    CancellationToken cancellationToken = default)
  {
    // Fetch raw data
    var rawData = await GetData(source, "Segments", resolverContext, fundamentalService, cancellationToken);

    // Get static fields from SegmentsDto
    var staticFields = typeof(SegmentsDto)
        .GetProperties()
        .Select(p => p.Name.ToLower())
        .ToHashSet();

    // Process raw data into SegmentsDto
    var result = rawData.Select(record =>
    {
      // Map static fields
      var segment = mapper.Map<SegmentsDto>(record);

      // Extract dynamic fields
      var recordDict = record.ToObject<Dictionary<string, JToken>>();
      if (recordDict != null)
      {
        // Extract dynamic segments and group by prefix (segment key)
        var dynamicSegments = recordDict
            .Where(kvp => !staticFields.Contains(kvp.Key.ToLower())) // Ignore static fields
            .Select(kvp =>
            {
              var key = kvp.Key;

              if (string.IsNullOrEmpty(key)) return null;

              // Split the key using the pattern
              string pattern = @"segment(s)?";
              string[] splitKey = Regex.Split(key, pattern, RegexOptions.IgnoreCase);
              splitKey = splitKey.Where(s => s.Length > 1).ToArray();

              // Ensure valid split result
              if (splitKey.Length < 2) return null;

              var prefix = splitKey.FirstOrDefault();
              var suffix = splitKey.LastOrDefault();
              var middle = splitKey.Length > 2 ? splitKey[^2] : null;
              // Map the values to SegmentValue
              return new
              {
                Prefix = prefix,
                Middle = middle,
                Suffix = suffix,
                Value = kvp.Value?.ToString()
              };
            })
            .Where(x => x != null) // Remove null entries
            .GroupBy(kvp => kvp?.Prefix) // Group by prefix (segment key)
            .Select(group => new
            {
              Name = group.Key,  // Segment key (e.g., "geo")
              Detail = group
                    .GroupBy(kvp => kvp?.Suffix) // Group by the segment category (e.g., "TotalAssets")
                    .Select(subGroup => new Segment
                    {
                      Name = subGroup.Key,  // Segment category key (e.g., "TotalAssets")
                      Value = subGroup
                            .Select(kvp => new SegmentValue
                            {
                              Name = kvp?.Middle, // The category name (e.g., "Other", "Norway")
                              Value = kvp?.Value   // The value (e.g., "15000000000")
                            })
                            .ToList() // Collect the segment values into a list
                    })
                    .ToList()
            })
            .ToList();

        //Apply Filters for Segments and Details
        segment.Segments = dynamicSegments
        .Where(d => string.IsNullOrEmpty(segmentKeyFilter) || d.Name?.Equals(segmentKeyFilter, StringComparison.OrdinalIgnoreCase) == true) // Check for null or filter by segment key
        .Select(d => new SegmentDetail
        {
          Name = d.Name, // Assign the key (e.g., "business")
          Detail = d.Detail
            .Where(sub => string.IsNullOrEmpty(detailKeyFilter) || sub.Name?.Equals(detailKeyFilter, StringComparison.OrdinalIgnoreCase) == true) // Check for null or filter by detail key
            .ToList() // Filtered details
        })
       .Where(sd => sd.Detail.Count > 0) // Ensure there are remaining details after filtering
       .ToList();
      }
      else
      {
        // Fallback to an empty list if no dynamic data is found
        segment.Segments = new List<SegmentDetail>();
      }
      return segment;
    });
    return result.ToList();
  }
}
