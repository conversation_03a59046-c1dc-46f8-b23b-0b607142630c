using System.Linq;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure;
using HotChocolate;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.CurrencyRateHistory;

public class CurrencyRateHistoryResolvers
{
  public IQueryable<CurrencyRateHistoryDto> GetCurrencyRateHistoryByCurrencyRate(
    [Parent] CurrencyRateDto currencyRate,
    SharkDbContextBase sharkDbContext)
  {
    var result = sharkDbContext.CurrencyRateHistories
      .AsNoTracking()
      .Where(crh => crh.CurrencyRateID == currencyRate.Id)
      .WithAutoMapper()
      .ToProjection<CurrencyRateHistoryDto>();
    return result;
  }
}
