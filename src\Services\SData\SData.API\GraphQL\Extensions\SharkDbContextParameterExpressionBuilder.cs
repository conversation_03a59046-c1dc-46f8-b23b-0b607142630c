using System.Linq.Expressions;
using System.Reflection;
using HotChocolate.Internal;
using HotChocolate.Types.Descriptors;

using Euroland.FlipIT.SData.API.Infrastructure;
using HotChocolate.Data;
using HotChocolate;
using HotChocolate.Resolvers.Expressions.Parameters;
using HotChocolate.Types.Descriptors.Definitions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Diagnostics.CodeAnalysis;
using System.Collections;
using System.Threading.Tasks;
using HotChocolate.Resolvers;
using Euroland.FlipIT.SData.API.Infrastructure.Factories;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;

namespace Euroland.FlipIT.SData.API.GraphQL.Extensions;

internal class ToListMiddleware<TEntity>
{
  private readonly FieldDelegate _next;

  public ToListMiddleware(FieldDelegate next)
  {
    _next = next;
  }

  public async ValueTask InvokeAsync(IMiddlewareContext context)
  {
    await _next(context).ConfigureAwait(false);

    context.Result = context.Result switch
    {
      IQueryable<TEntity> queryable =>
          await queryable
              .ToListAsync(context.RequestAborted)
              .ConfigureAwait(false),
      _ => context.Result
    };
  }
}

internal class ExecutableMiddleware
{
  private readonly FieldDelegate _next;

  public ExecutableMiddleware(FieldDelegate next)
  {
    _next = next;
  }

  public async ValueTask InvokeAsync(IMiddlewareContext context)
  {
    await _next(context).ConfigureAwait(false);

    if (context.Result is IExecutable executable)
    {
      context.Result = await executable
          .ToListAsync(context.RequestAborted)
          .ConfigureAwait(false);
    }
  }
}
/// <summary>
/// Represents object to decide creating an instance of <see cref="SharkDbContextBase"/> at runtime.
/// </summary>
public class SharkDbContextParameterExpressionBuilder : IParameterExpressionBuilder, IParameterFieldConfiguration
{
  private static readonly Type _valueTask = typeof(ValueTask<>);
  private static readonly Type _task = typeof(Task<>);
  public ArgumentKind Kind => ArgumentKind.Service;

  private readonly ServiceKind _kind;

  public bool IsPure => false;

  public bool IsDefaultHandler => false;

  public SharkDbContextParameterExpressionBuilder(DbContextKind kind)
  {
    _kind = kind switch
    {
      DbContextKind.Pooled => ServiceKind.Pooled,
      DbContextKind.Resolver => ServiceKind.Resolver,
      _ => ServiceKind.Synchronized
    };
  }

  public bool CanHandle(ParameterInfo parameter) => parameter.ParameterType == typeof(SharkDbContextBase);

  public void ApplyConfiguration(ParameterInfo parameter, ObjectFieldDescriptor descriptor)
  {
    switch (_kind)
    {
      case ServiceKind.Pooled:
        UseDbContext(descriptor.Extend().Definition);
        break;

      case ServiceKind.Synchronized:
        ServiceExpressionHelper.ApplyConfiguration(parameter, descriptor, _kind);
        break;

      case ServiceKind.Resolver:
        ServiceExpressionHelper.ApplyConfiguration(parameter, descriptor, _kind);
        var definition = descriptor.Extend().Definition;
        var placeholderMiddleware = new FieldMiddlewareDefinition(_ => _ => throw new NotSupportedException(), key: WellKnownMiddleware.ToList);
        var serviceMiddleware = definition.MiddlewareDefinitions.Last(t => t.Key == WellKnownMiddleware.PooledService);
        var index = definition.MiddlewareDefinitions.IndexOf(serviceMiddleware) + 1;
        definition.MiddlewareDefinitions.Insert(index, placeholderMiddleware);
        AddCompletionMiddleware(definition, placeholderMiddleware);
        break;
    }
  }

  public Expression Build(ParameterExpressionBuilderContext context) => ServiceExpressionHelper.Build(context.Parameter, context.ResolverContext, _kind);

  private static void UseDbContext(ObjectFieldDefinition definition)
  {
    var scopedServiceName = typeof(SharkDbContextBase).FullName ?? typeof(SharkDbContextBase).Name;
    FieldMiddlewareDefinition placeholder = new FieldMiddlewareDefinition(_ => _ => throw new System.NotSupportedException(), key: WellKnownMiddleware.ToList);

    FieldMiddlewareDefinition contextMiddleware = new FieldMiddlewareDefinition(next => async context =>
    {
      var executionContext = context.Services.GetRequiredService<IGraphQLExecutionContext>();

      var dbContext = await context.Services
          .GetRequiredService<ISharkDbContextAbstractFactory>()
          .CreateDbContextAsync(executionContext.UseRealtime, executionContext.UseCloud)
          .ConfigureAwait(false);

      context.RegisterForCleanup(() => dbContext.DisposeAsync());

      try
      {
        context.SetLocalState(scopedServiceName, dbContext);
        await next(context).ConfigureAwait(false);
      }
      finally
      {
        context.RemoveLocalState(scopedServiceName);
      }
    },
    key: WellKnownMiddleware.DbContext);

    definition.MiddlewareDefinitions.Insert(0, placeholder);
    definition.MiddlewareDefinitions.Insert(0, contextMiddleware);

    AddCompletionMiddleware(definition, placeholder);
  }

  private static void AddCompletionMiddleware(
        ObjectFieldDefinition definition,
        FieldMiddlewareDefinition placeholderMiddleware)
  {
    if (definition.ResultType is null)
    {
      definition.MiddlewareDefinitions.Remove(placeholderMiddleware);
      return;
    }

    if (TryExtractEntityType(definition.ResultType, out Type? entityType))
    {
      Type middleware = typeof(ToListMiddleware<>).MakeGenericType(entityType);
      var index = definition.MiddlewareDefinitions.IndexOf(placeholderMiddleware);
      definition.MiddlewareDefinitions[index] = new FieldMiddlewareDefinition(FieldClassMiddlewareFactory.Create(middleware), key: WellKnownMiddleware.ToList);
      return;
    }

    if (IsExecutable(definition.ResultType))
    {
      Type middleware = typeof(ExecutableMiddleware);
      var index = definition.MiddlewareDefinitions.IndexOf(placeholderMiddleware);
      definition.MiddlewareDefinitions[index] = new FieldMiddlewareDefinition(FieldClassMiddlewareFactory.Create(middleware), key: WellKnownMiddleware.ToList);
    }

    definition.MiddlewareDefinitions.Remove(placeholderMiddleware);
  }

  private static bool TryExtractEntityType(
        Type resultType,
        [NotNullWhen(true)] out Type? entityType)
  {
    if (!resultType.IsGenericType)
    {
      entityType = null;
      return false;
    }

    if (typeof(IEnumerable).IsAssignableFrom(resultType))
    {
      entityType = resultType.GenericTypeArguments[0];
      return true;
    }

    Type resultTypeDefinition = resultType.GetGenericTypeDefinition();
    if ((resultTypeDefinition == _task || resultTypeDefinition == _valueTask) &&
        typeof(IEnumerable).IsAssignableFrom(resultType.GenericTypeArguments[0]) &&
        resultType.GenericTypeArguments[0].IsGenericType)
    {
      entityType = resultType.GenericTypeArguments[0].GenericTypeArguments[0];
      return true;
    }

    entityType = null;
    return false;
  }

  private static bool IsExecutable(Type resultType)
  {
    if (typeof(IExecutable).IsAssignableFrom(resultType))
    {
      return true;
    }

    if (!resultType.IsGenericType)
    {
      return false;
    }

    var resultTypeDefinition = resultType.GetGenericTypeDefinition();
    return (resultTypeDefinition == _task || resultTypeDefinition == _valueTask) && typeof(IExecutable).IsAssignableFrom(resultType.GenericTypeArguments[0]);
  }
}
