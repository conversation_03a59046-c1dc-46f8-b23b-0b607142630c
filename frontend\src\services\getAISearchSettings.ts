import {LayoutPosition} from "@/config/interface";
import {apiClient} from "./clientInstance";
import {shuffle} from "es-toolkit";

export type AISearchSettings = {
  primaryColor: string;
  gradientColor: {from: string, to: string};
  widgetBehavior: LayoutPosition;
  language: string;
  placeholders: string;
  showSuggestions: boolean;
  suggestedQuestions: string[];
  tickers: Record<string, boolean>;
  suggestedQuestionsRandom: string[];
}

function getDataByLanguage<T>(data: Record<string, T>, language: string) {
  const lowCaseData = Object.fromEntries(Object.entries(data).map(([key, value]) => [key.toLowerCase(), value]));
  return lowCaseData[language.toLocaleLowerCase()] || lowCaseData['en-gb']
}


export async function getAISearchSettings(language: string, signal?: AbortSignal): Promise<AISearchSettings> {
  const { data: { content } } = await apiClient.instance.get<any>('/Setting/wise-search', undefined, {
    signal: signal,
  });
  const result = JSON.parse(content);

  if(result) {
    const suggestedQuestions = getDataByLanguage<string | string[]>(result.suggestedQuestions, language);
    result.suggestedQuestions = Array.isArray(suggestedQuestions) ? suggestedQuestions : [suggestedQuestions];
    result.placeholders = getDataByLanguage<string>(result.placeholders, language);

    const shuffledQuestions = result.suggestedQuestions.length > 6 ? shuffle([...result.suggestedQuestions]) : result.suggestedQuestions ;
    result.suggestedQuestionsRandom = shuffledQuestions.slice(0, 6);
  }

  return result;
}
