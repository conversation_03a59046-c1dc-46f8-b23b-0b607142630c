﻿using Euroland.FlipIT.SData.API.DTOs.Deprecated;
using Euroland.FlipIT.SData.API.Services;
using HotChocolate;
using HotChocolate.Data;
using HotChocolate.Resolvers;
using HotChocolate.Types;
using System.Threading.Tasks;

namespace Euroland.FlipIT.SData.API.GraphQL.Deprecated.MarketDepth
{
    [ExtendObjectType(OperationTypeNames.Query)]
    public class MarketDepthQueries
    {
        public MarketDepthQueries()
        {

        }

        [GraphQLDeprecated("This field will be removed in the next release. Use instrument->orderDepth instead.")]
        [UseProjection]
        public Task<OrderDepth> OrderDepths(IResolverContext context, [Service] IMarketDepthService marketDepthService, int instrumentId, string? toCurrency = null, bool isRT = false)
        {
          return marketDepthService.GetOrderDepth(instrumentId, toCurrency, isRT);
        }
    }
}
