import { cloneDeep, merge } from "es-toolkit";
import { isMatch } from 'es-toolkit/compat';

export const defaultAnimateOptions = {
  layoutPosition: 'top',
  animate: {
    close: {
      duration: 0,
      opacity: [1, 0]
    },
    show: {
      duration: 0,
      opacity: [0, 1]
    }
  }
};

export class ComponentZoidInstance {
  constructor(
    component,
    props,
    options = {}
  ) {
    this.component = component;
    this.props = props;
    this.options = merge(cloneDeep(defaultAnimateOptions), options ?? {});
    this.isAnimating = 0;

    console.log('this.options', this.options);

    this.rendered = new Promise((resolve) => {
      component.event.on(window.euroland.EVENT.RENDERED, () => {
        setTimeout(() => {
          resolve(true);
        }, 10);
      });
    });

    this.displayed = new Promise((resolve) => {
      component.event.once(window.euroland.EVENT.DISPLAY, () => {
        resolve(true);
      });
    });
  }
  
  render() {
    const isIFrame = window.self !== window.top;
    const integrationLayoutPosition = window.xprops
      ? window.xprops.layout[this.options.layoutPosition]
      : "#middleLayout";

    if (isIFrame) {
      this.component.renderTo(window.parent, integrationLayoutPosition);
    } else {
      let middle = document.getElementById("middleLayout");
      if (!middle) {
        middle = document.createElement("div");
        middle.id = "middleLayout";
        document.body.appendChild(middle);
      }
      this.component.renderTo(window.parent, integrationLayoutPosition);
    }
  }

  async animate(rest) {
    try {
      this.isAnimating++;
      await this.component.event.trigger(window.euroland.EVENT.ANIMATE, rest);
    } finally {
      this.isAnimating--;
    }
  }

  async hide() {
    if (this.isShown === false) return;
    this.isShown = false;

    await this.animate(this.options.animate.close);
    await this.component.hide();
  }

  async show(autoFocus = false) {
    if (this.isShown === true) return;
    this.isShown = true;

    await this.component.show();
    await this.rendered;

    await this.animate(this.options.animate.show);
    if (autoFocus) {
      await this.component.focus();
    }
  }

  updateProps(props) {
    if (isMatch(this.props, props)) return;
    
    const mergeProps = {...this.props, ...props};

    // if (import.meta.env.DEV) {
    //   Array.from(Object.entries(mergeProps)).forEach(([keyBy, value]) => {
    //     if(value === undefined) console.warn(`Zoid component does not support update prop "${keyBy}" with value undefined`);
    //   });
    // }

    this.props = mergeProps;
    return this.component.updateProps(mergeProps);
  }

  onBackdropClicked(callback) {
    return this.component.event.on(window.euroland.EVENT.BACKDROP_CLICKED, callback);
  }
}
