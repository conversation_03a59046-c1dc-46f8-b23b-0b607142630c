﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
  [Table("Dividends_ExchDataFeed")]
    public class Dividend
    {
        [Column("rID"), Key]
        public int ID { get; set; }

        public string? ExchangeCurrency { get; set; }

        [Column("currency")]
        public string Currency { get; set; }

        [Column("exDate")]
        public DateTime? Date { get; set; }

        [Column("recDate")]
        public DateTime? RecDate { get; set; }

        [Column("payDate")]
        public DateTime? PayDate { get; set; }

        [Column("FYEDate")]
        public DateTime? FYEDate { get; set; }

        [Column("LastRowChange")]
        public DateTime LastRowChange { get; set; }

        [Column("DivPeriod")]
        public string? Period { get; set; }

        [Column("paytype")]
        public string? PayType { get; set; }

        [Column("GrossDivAdj")]
        public decimal? GrossDivAdj { get; set; }
        public decimal? GrossDividend { get; set; }
        public decimal? NetDividend { get; set; }

        [Column("NetDivAdj")]
        public decimal? NetDivAdj { get; set; }

        [Column("SplitNr")]
        public decimal? SplitNr { get; set; }

        [Column("ShareCapital")]
        public long? ShareCapital { get; set; }

        [Column("DividendAmount")]
        public long? DividendAmount { get; set; }

        [Column("BasicEarningsPerShare")]
        public long? BasicEarningsPerShare { get; set; }

        [Column("PayoutPer")]
        public decimal? PayoutPer { get; set; }

        [Column("isEnabled")]
        public bool Enabled { get; set; }

        [Column("TId"), ForeignKey("Instrument")]
        public int InstrumentId { get; set; }

        public Instrument Instrument { get; set; }
    }

    // Data come from procedure

    public class DividendEvt
    {
        [Key]
        public DateTime dDate { get; set; }

        public decimal? dDividend { get; set; }

        [NotMapped]
        public decimal? k { get; set; } = 1;
    }
}
