using System;
using System.Linq;

namespace Euroland.FlipIT.SData.API.Infrastructure.TempTables;

public class TempTableQuery<T> : ITempTableQuery<T>
{
  private ITempTableReference _tempTableReference;

  public IQueryable<T> Query { get; }

  public string Name => _tempTableReference?.Name ?? throw new ObjectDisposedException(nameof(TempTableQuery<T>));

  public TempTableQuery(IQueryable<T> query, ITempTableReference tempTableReference)
  {
    Query = query ?? throw new ArgumentNullException(nameof(query));
    _tempTableReference = tempTableReference ?? throw new ArgumentNullException(nameof(tempTableReference));
  }

  /// <inheritdoc />
  public void Dispose()
  {
    _tempTableReference?.Dispose();
    _tempTableReference = null;
  }
}
