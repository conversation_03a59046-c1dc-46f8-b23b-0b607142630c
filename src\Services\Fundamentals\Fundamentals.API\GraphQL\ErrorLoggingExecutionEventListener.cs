using HotChocolate.Execution;
using HotChocolate.Execution.Instrumentation;
using HotChocolate.Execution.Processing;
using HotChocolate.Resolvers;

namespace Euroland.FlipIT.Fundamental.API.GraphQL
{
  public class ErrorLoggingExecutionEventListener: ExecutionDiagnosticEventListener
    {
        private readonly ILogger<ErrorLoggingExecutionEventListener> _logger;

        public ErrorLoggingExecutionEventListener(ILogger<ErrorLoggingExecutionEventListener> logger) => _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        public override void ResolverError(
            IMiddlewareContext context,
            IError error)
        {
            _logger.LogError(error.Exception, error.Message);
        }

        public override void TaskError(
            IExecutionTask task,
            IError error)
        {
            _logger.LogError(error.Exception, error.Message);
        }

        public override void RequestError(
            IRequestContext context,
            Exception exception)
        {
            _logger.LogError(exception, "RequestError");
        }

        public override void SubscriptionEventError(
            SubscriptionEventContext context,
            Exception exception)
        {
            _logger.LogError(exception, "SubscriptionEventError");
        }

        public override void SubscriptionTransportError(
            ISubscription subscription,
            Exception exception)
        {
            _logger.LogError(exception, "SubscriptionTransportError");
        }
    }
}
