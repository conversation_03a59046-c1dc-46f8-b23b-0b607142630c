import { OidcAuthAppRoutes } from "./OidcAuthAppRoutes";

const dummyHref = document.createElement('a');

function buildPathName(path: string) {
  if(!path) {
    throw new Error('@path must not be undefined');
  }
  const el = dummyHref.cloneNode() as HTMLAnchorElement;
  el.setAttribute('href', path.replace(/^\/(.+)/, './$1'));
  return el.pathname.toLowerCase();
}

/**
 * Determines if the OIDC (OpenID Connect) authentication is currently opened.
 *
 * This function checks various conditions to determine if the OIDC authentication
 * process is active, including the presence of specific paths in the URL, whether
 * the current window is an iframe, and the presence of certain properties.
 *
 * @returns {boolean} - Returns `true` if the OIDC authentication is opened, otherwise `false`.
 */
export function isOidcAuthOpened() {
  const path = location.pathname.toLowerCase();
  let isInIframe = false;
  try {
    isInIframe = window.self !== window.top;
  } catch {
    isInIframe = false;
  }

  // incase of oidc-client makes a signin-silent
  if (isInIframe && (
      path === buildPathName(OidcAuthAppRoutes.SIGNIN_CALLBACK)
      || path === buildPathName(OidcAuthAppRoutes.SIGNOUT_CALLBACK))
  ) {
    return true;
  }

  // window.xprops is property setted by integration module
  // and must be presented to be integrated with client's site
  // and ensure login popup window works.
  // Check it out for more detail: https://gitlab.euroland.com/tools/flipit/integration/-/blob/master/doc/xprops.md
  if (isInIframe && !window.xprops) return false;

  // Must be as exact as routes defined in OidcAuthApp component.
  return path === buildPathName(OidcAuthAppRoutes.SIGNIN)
    || path === buildPathName(OidcAuthAppRoutes.SIGNIN_CALLBACK)
    || path === buildPathName(OidcAuthAppRoutes.SIGNOUT)
    || path === buildPathName(OidcAuthAppRoutes.SIGNOUT_CALLBACK);
}
