import type {XProps} from '@/zoid';
import { useAuth } from '../core';
import type {AuthPros} from '../core/interfaces';

/**
 * LoginComponent is a React functional component that handles user authentication.
 * It uses the `useAuth` hook to check if the user is authenticated.
 *
 * If the user is not authenticated, it triggers a redirect to the IdentityServer for authentication.
 * While the redirection is in progress, it displays a "Redirecting..." message.
 *
 * If the user is authenticated, it displays a message indicating that the user is logged in.
 *
 * @returns {JSX.Element} The rendered component.
 */
export const LoginComponent = () => {
  const auth = useAuth();

  if(auth.isLoading) return null;

  if (!auth.isAuthenticated) {
    // Redirect to the IdentityServer.
    auth.signinRedirect();
  } else {
    const xprops = window.xprops as unknown as XProps<AuthPros>
    if(typeof(xprops.onLoginSuccess) === 'function') {
      if(auth.user) xprops.onLoginSuccess(auth.user);
    }
    if(typeof(xprops.close) === 'function') {
      xprops.close();
    }
  }

  return null;
};
