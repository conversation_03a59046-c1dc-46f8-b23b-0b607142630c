using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Euroland.FlipIT.SData.API.Infrastructure.Factories;

public class SharkDbContextAbstractFactory : ISharkDbContextAbstractFactory
{
  private readonly ILogger _logger;

  private readonly IDbContextFactory<RealtimeSharkDbContext> _realtimeDbContextFactory;
  private readonly IDbContextFactory<DefaultCloudSharkDbContext> _cloudDbContextFactory;
  private readonly IDbContextFactory<DefaultSharkDbContext> _defaultDbContextFactory;

  /// <summary>
  /// Initializes a new instance of the <see cref="SharkDbContextAbstractFactory"/> class.
  /// </summary>
  /// <param name="logger">
  /// The logger instance used for logging within the factory.
  /// </param>
  public SharkDbContextAbstractFactory(
    ILogger<SharkDbContextAbstractFactory> logger,
    IDbContextFactory<RealtimeSharkDbContext> realtimeDbContextFactory,
    IDbContextFactory<DefaultCloudSharkDbContext> cloudDbContextFactory,
    IDbContextFactory<DefaultSharkDbContext> defaultDbContextFactory
  )
  {
    _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    _realtimeDbContextFactory = realtimeDbContextFactory ?? throw new ArgumentNullException(nameof(realtimeDbContextFactory));
    _cloudDbContextFactory = cloudDbContextFactory ?? throw new ArgumentNullException(nameof(cloudDbContextFactory));
    _defaultDbContextFactory = defaultDbContextFactory ?? throw new ArgumentNullException(nameof(defaultDbContextFactory));
  }

  public async Task<SharkDbContextBase> CreateDbContextAsync(bool enableRealtime, bool useCloudDb)
  {
    return enableRealtime
          ? await _realtimeDbContextFactory.CreateDbContextAsync()
          : !useCloudDb
          ? await _defaultDbContextFactory.CreateDbContextAsync()
          : await _cloudDbContextFactory.CreateDbContextAsync();
  }
}
