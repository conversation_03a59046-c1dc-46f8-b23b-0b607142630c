﻿using AutoMapper;
using Euroland.FlipIT.WiseSearch.Infrastructure.Entities.WiseSearch;
using Euroland.FlipIT.WiseSearch.Models.Setting;

namespace Euroland.FlipIT.WiseSearch
{
    public class AutoMapperProfile : Profile
    {
        public AutoMapperProfile()
        {
            CreateMap<IngestedActivityEntity, SearchHistoryEntity>()
                .ForMember(s => s.AIResult, t => t.MapFrom(d => d.Answer))
                .ForMember(s => s.IpAddress, t => t.MapFrom(d => d.Ip))
                .ForMember(s => s.SearchTime, t => t.MapFrom(d => d.Timestamp))
                .ForMember(s => s.Id, t => t.MapFrom(d => d.ActivityId))
                .ReverseMap();

            CreateMap<SettingEntity, SettingResponse>().ReverseMap();
        }
    }
}
