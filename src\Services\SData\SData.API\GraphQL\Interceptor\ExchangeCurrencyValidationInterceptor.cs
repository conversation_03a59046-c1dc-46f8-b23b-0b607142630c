using System.Linq;
using Euroland.FlipIT.SData.API.GraphQL.Middleware;
using HotChocolate.Configuration;
using HotChocolate.Resolvers;
using HotChocolate.Types.Descriptors.Definitions;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Interceptor;

public class ExchangeCurrencyValidationInterceptor : TypeInterceptor
{
  public override void OnBeforeCompleteType(ITypeCompletionContext completionContext, DefinitionBase definition)
  {
    if (definition is ObjectTypeDefinition objectTypeDefinition)
    {
      foreach (var fieldDefinition in objectTypeDefinition.Fields)
      {
        if (fieldDefinition.HasArguments && fieldDefinition.Arguments.Count > 0)
        {
          if(fieldDefinition.Arguments.Any(arg => arg.Name == HasExchangeCurrencyArgumentIResolverContextExtensions.EXCHANGE_CURRENCY_ARGUMENT))
          {
            var middlewareDefinition = new FieldMiddlewareDefinition(FieldClassMiddlewareFactory.Create<ExchangeCurrencyValidationMiddleware>());
            fieldDefinition.MiddlewareDefinitions.Add(middlewareDefinition);
          }
        }
      }
    }
  }
}
