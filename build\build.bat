@ECHO OFF

:: usage:
:: build.bat <[options]>
::
:: options:
::  --sln <value> -> path to project or solution file. Default empty.
::  --build-client -> build client app along with server side. Default  `true`
::  --output-path -> relative path of the output build result to the project root directory, this will be used later for the deploy job if neccessary. The path is . Default is bin\Release\net6.0\publish
::  --release -> Specify whether publish project/solution to --output-path

SET ROOT=%CD%
ECHO Current working DIR: %ROOT%

:: build.bat --sln soltion_or_project_file

setlocal

:parse
    if "%~1"=="" GOTO endparse
    if "%~1"=="--sln" ( set "_ProjectOrSolution=%~2" )
    if "%~1"=="--build-client" ( set "_BuildClient=%~2" & set "_HasBuildClient=true" )
    if "%~1"=="--output-path" ( set "_OuputDir=%~2" & set "_HasOutputDir=true" )
    if "%~1"=="--release" ( set "_HasPublish=true" )
    shift
    GOTO parse
:endparse

:: Default _BuildClient=true
if "%_HasBuildClient%"=="" ( set "_BuildClient=true" )
if "%_HasBuildClient%"=="true" if "%_BuildClient%"=="" ( set "_BuildClient=true" )
if "%_HasOutputDir%"=="" ( set "_OuputDir=bin\Release\net6.0\publish" )

ECHO Restoring nuget packags...
dotnet restore %_ProjectOrSolution%

if "%_HasPublish%"=="true" (
    ECHO Presemantic-releasing...
)

:: Determines next version to set to src\Version.props
:: in order to apply next version to dotnet build command.
if "%_HasPublish%"=="true" (
    call "./build/release.bat" --dry
)

:: Back to root of project repo if it's changed by release.bat
if not "%ROOT%"=="%CD%" (
    CD %ROOT%
)

dotnet build %_ProjectOrSolution% --no-restore -c Release -p:GitlabBuild=%_BuildClient%
dotnet publish %_ProjectOrSolution% --no-restore --no-build -c Release -p:PublishUrl="%_OuputDir%" -p:EnvironmentName=Production

endlocal

:EOF
