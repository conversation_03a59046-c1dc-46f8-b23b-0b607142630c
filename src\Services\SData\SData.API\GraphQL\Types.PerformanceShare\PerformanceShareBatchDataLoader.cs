using GreenDonut;
using Euroland.FlipIT.SData.API.Dto;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading;

namespace Microsoft.Extensions.DependencyInjection;

public class PerformanceShareBatchDataLoader : BatchDataLoader<string, PerformanceShareDto>
{
  private readonly PerformanceShareGroupDataLoader _performanceShareGroupDataLoader;

  public PerformanceShareBatchDataLoader(
    PerformanceShareGroupDataLoader performanceShareGroupDataLoader,
    IBatchScheduler batchScheduler,
    DataLoaderOptions? options = null)
    : base(batchScheduler, options)
  {
    _performanceShareGroupDataLoader = performanceShareGroupDataLoader ?? throw new ArgumentNullException(nameof(performanceShareGroupDataLoader));
  }

  protected override async Task<IReadOnlyDictionary<string, PerformanceShareDto>> LoadBatchAsync(IReadOnlyList<string> keys, CancellationToken cancellationToken)
  {
    var instrumentIds = keys.Select(key => PerformanceShareGroupDataLoader.DecomputeInstrumentIdFromCacheKey(key)).Distinct().ToList();

    var performances = (await _performanceShareGroupDataLoader.LoadAsync(instrumentIds))
      .SelectMany(shares => shares, (shares, share) => share);

    return performances.ToDictionary(p => PerformanceShareGroupDataLoader.ComputeCacheKey(p.InstrumentId, p.Period));
  }
}
