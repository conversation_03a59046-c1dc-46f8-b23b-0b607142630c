using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Attachment;

public class AttachmentType: ObjectType<AttachmentDto>
{
  public const string Name = "Attachment";
  protected override void Configure(IObjectTypeDescriptor<AttachmentDto> descriptor)
  {
    descriptor.Name(Name);

    descriptor.Field(f => f.PressreleaseId).IsProjected();
  }
}
