using Euroland.FlipIT.Fundamental.API.Dtos;
using HotChocolate.Resolvers;

namespace Euroland.FlipIT.Fundamental.API.GraphQL;

public class QueryType
{
  [GraphQLName("fundamental")]
  public Task<FundamentalDto> GetFundamental(
    string companyCode,
    IResolverContext resolverContext)
  {
    resolverContext.SetScopedState(nameof(companyCode), companyCode);

    return Task.FromResult(new FundamentalDto() { CompanyCode = companyCode});
  }
}
