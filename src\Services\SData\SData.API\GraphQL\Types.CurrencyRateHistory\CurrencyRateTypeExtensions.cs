using HotChocolate.Types;
using HotChocolate.Types.Pagination;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.CurrencyRateHistory;

/// <summary>
/// Extends 'historicals' field to type <see cref="CurrencyRate.CurrencyRateType"/>
/// </summary>
public class CurrencyRateTypeExtensions: ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(CurrencyRate.CurrencyRateType.Name);

    descriptor
      .Field("historicals")
      .UsePaging(
        connectionName: "CurrencyRateHistory",
        options: new PagingOptions
        {
          IncludeTotalCount = true,
          MaxPageSize = int.MaxValue,
          DefaultPageSize = 200
        }
      )
      .UseProjection()
      .UseFiltering()
      .UseSorting(typeof(CurrencyRateHistorySortInputType))
      .ResolveWith<CurrencyRateHistoryResolvers>(resolvers => resolvers.GetCurrencyRateHistoryByCurrencyRate(default!, default!));
  }
}
