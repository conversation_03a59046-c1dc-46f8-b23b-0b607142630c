namespace Euroland.FlipIT.SData.API.Dto;

/// <summary>
/// Contains the information of company.
/// </summary>
public class CompanyNameDto: IDtoObject
{
  /// <summary>
  /// Language name in english (e.g. English, Finnish)
  /// </summary>
  /// <value></value>
  public string Language { get; set; }

  /// <summary>
  /// The translation name of company in appropriate language.
  /// </summary>
  /// <value></value>
  public string? Name { get; set; }

  public string? Address1 { get; set; }
  public string? Address2 { get; set; }
  public string? Zip { get; set; }
  public string? Town { get; set; }
  public string? Country { get; set; }
  public string? Tel { get; set; }
  public string? Fax { get; set; }
  public string? EMail { get; set; }
}
