using System;
using Euroland.FlipIT.SData.API.Helpers;

namespace Euroland.FlipIT.SData.API.Dto;

/// <summary>
/// Represents the exchange rate of a currency exchanged for another currency.
/// </summary>
public class CurrencyRateDto: IDtoObject
{
  public int Id { get; set; }
  public string Pair { get; set; }

  private DateTime? _date;
  public DateTime? Date
  {
    get
    {
      return _date;
    }
    set
    {
      if(value != null) {
        _date = value.Value.Kind != DateTimeKind.Utc ? value.Value.ToUtcKindOnly() : value;
      }
    }
  }
  public decimal? Value { get; set; }
}
