using Euroland.FlipIT.SData.API.Services.Interfaces;
using HotChocolate;
using HotChocolate.Types;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.DTOs.Deprecated.Currencies;

namespace Euroland.FlipIT.SData.API.GraphQL.Deprecated.Instruments
{
  [ExtendObjectType(OperationTypeNames.Query)]
  public class CurrencyQuery
  {
    [GraphQLDeprecated("No longer maintenance and will be removed in the next release.")]
    public CurrencyQueries GetCurrency()
    {
      return new CurrencyQueries();
    }
  }

  [GraphQLName("CurrencyQueries_Deprecated")]
  public class CurrencyQueries
  {
    public async Task<IEnumerable<Currency>> GetCurrenciesAsync([Service] ICurrencyService currencyService)
    {
      return await currencyService.GetCurrencies();
    }

    public async Task<IEnumerable<Currency>> GetCurrenciesByCodesAsync([Service] ICurrencyService currencyService, List<string> codes)
    {
      return await currencyService.GetCurrenciesByCodes(codes);
    }

    public async Task<CurrencyRate> GetCurrentRate(
        [Service] ICurrencyRateService currencyService,
        string baseCurrency,
        string quoteCurrency,
        bool isRT = false)
    {
      return await currencyService.GetCurrentRateAsync(baseCurrency, quoteCurrency, isRT);
    }

    public async Task<IEnumerable<ConvertCurrencyRate>> GetCurrentRates(
        [Service] ICurrencyRateService currencyService,
        List<string> baseCurrencies,
        string quoteCurrency,
        bool isRT = false)
    {
      return await currencyService.GetCurrentRatesAsync(baseCurrencies, quoteCurrency, isRT);
    }

    public async Task<IEnumerable<CurrencyRate>> GetHistoricalRates(
        [Service] ICurrencyRateService currencyService,
        string baseCurrency,
        string quoteCurrency,
        DateTime fromDateUTC,
        DateTime? toDateUTC,
        bool isRT = false)
    {
      if (fromDateUTC.Kind != DateTimeKind.Utc)
      {
        fromDateUTC = DateTime.SpecifyKind(fromDateUTC, DateTimeKind.Utc);
      }
      if (toDateUTC != null && toDateUTC.Value.Kind != DateTimeKind.Utc)
      {
        toDateUTC = DateTime.SpecifyKind(toDateUTC.Value, DateTimeKind.Utc);
      }
      return await currencyService.GetRateHistoryAsync(baseCurrency, quoteCurrency, fromDateUTC, toDateUTC, isRT);
    }
  }
}
