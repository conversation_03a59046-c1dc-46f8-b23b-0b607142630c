
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.DTOs.Deprecated.Currencies;

namespace Euroland.FlipIT.SData.API.Services.Interfaces
{
  /// <summary>
  /// Provides the services to get history of currencies.
  /// </summary>
  public interface ICurrencyRateService: IAsyncDisposable, IDisposable
    {
        /// <summary>
        /// Gets the rate between <paramref name="baseCurrency"/> and <paramref name="quoteCurrency"/>
        /// </summary>
        /// <param name="baseCurrency"></param>
        /// <param name="quoteCurrency"></param>
        /// <returns><see cref="CurrencyRate"/> if found. Otherwise, return null.</returns>
        Task<CurrencyRate> GetCurrentRateAsync(string baseCurrency, string quoteCurrency, bool isRT = false);

        /// <summary>
        /// Gets the rate between each base currency in <paramref name="baseCurrencies"/> and <paramref name="quoteCurrency"/>
        /// </summary>
        /// <param name="baseCurrencies"></param>
        /// <param name="quoteCurrency"></param>
        /// <returns></returns>
        Task<IEnumerable<ConvertCurrencyRate>> GetCurrentRatesAsync(List<string> baseCurrencies, string quoteCurrency, bool isRT = false);

        /// <summary>
        /// Gets the closest rate to the <paramref name="date"/> between <paramref name="baseCurrency"/> and <paramref name="quoteCurrency"/>
        /// </summary>
        /// <param name="baseCurrency"></param>
        /// <param name="quoteCurrency"></param>
        /// <param name="date"></param>
        /// <returns>Closest rate if found. Otherwise, return 1.</returns>
        Task<decimal> GetClosestRateAsync(string baseCurrency, string quoteCurrency, DateTime date, bool isRT = false);

        /// <summary>
        ///
        /// </summary>
        /// <param name="baseCurrency"></param>
        /// <param name="quoteCurrency"></param>
        /// <param name="fromUTC"></param>
        /// <param name="toUTC"></param>
        /// <returns></returns>
        Task<IEnumerable<CurrencyRate>> GetRatesAsync(string baseCurrency, string quoteCurrency, DateTime fromUTC, Nullable<DateTime> toUTC = null, bool isRT = false);

        /// <summary>
        ///
        /// </summary>
        /// <param name="baseCurrency"></param>
        /// <param name="quoteCurrency"></param>
        /// <param name="fromUTC"></param>
        /// <param name="toUTC"></param>
        /// <returns></returns>
        Task<IEnumerable<CurrencyRate>> GetRateHistoryAsync(string baseCurrency, string quoteCurrency, DateTime fromUTC, Nullable<DateTime> toUTC = null, bool isRT = false);
    }
}
