import { Trash2 } from "lucide-preact";
import { useEffect, useState } from "preact/hooks";
import type { InstrumentTableProps } from "./types";
import "./InstrumentTable.css";
import { toast } from "react-toastify";
import { useWatchlistQueries } from "../../services/watchlistQueries";
import type { Instrument } from "../../services/watchlistTypes";

const InstrumentTableSkeleton = ({ children, type = "loading" }: { children: React.ReactNode, type?: "loading" | "empty" }) => {
  const renderSkeletonRows = () => {
    return Array.from({ length: 5 }, (_, index) => (
      <tr key={index} className="skeleton-row">
        <td className="skeleton-cell">
          <div className="skeleton-symbol">
            <div className="skeleton-line skeleton-symbol-text"></div>
            <div className="skeleton-line skeleton-market-text"></div>
          </div>
        </td>
        <td className="skeleton-cell">
          <div className="skeleton-line skeleton-price"></div>
        </td>
        <td className="skeleton-cell">
          <div className="skeleton-line skeleton-change"></div>
        </td>
        <td className="skeleton-cell">
          <div className="skeleton-line skeleton-change-percent"></div>
        </td>
      </tr>
    ));
  };

  return (
    <div className="instruments-section">
      <div className="instruments-table-container">
        <table className="instruments-table">
          <thead>
            <tr>
              <th className="table-header">Symbol</th>
              <th className="table-header">Last</th>
              <th className="table-header">Change</th>
              <th className="table-header" style={{ textAlign: "end" }}>Change%</th>
            </tr>
          </thead>
          <tbody>
            {type === "loading" ? (
              renderSkeletonRows()
            ) : (
              <tr>
                <td colSpan={4}>
                  {children}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}

const InstrumentTable = ({
  instruments,
  isLoading,
  activeWatchlistId,
}: InstrumentTableProps) => {
  const { removeInstrumentMutation } = useWatchlistQueries();  
  const [selectedInstrument, setSelectedInstrument] = useState<{id: string, symbol: string} | null>();

  useEffect(() => {
    const windowAppContext = window.EurolandAppContext;
    if (!windowAppContext) return;

    windowAppContext.registerCommandHandler('instrument-selected', () => selectedInstrument);
    windowAppContext.emit('instrument-selected', selectedInstrument);

    return () => {
      windowAppContext.unregisterCommandHandler('instrument-selected', () => selectedInstrument);
    };
  }, [selectedInstrument]);

  const handleRemoveInstrument = async (instrumentId: string) => {
    if (!activeWatchlistId) return;

    try {
      removeInstrumentMutation.mutate({
        watchlistId: activeWatchlistId,
        instrumentId: parseInt(instrumentId),
      });
      toast.success("Instrument removed from watchlist");
    } catch {
      toast.error("Failed to remove instrument");
    }
  };

  const handleSelectInstrument = (instrument: Instrument) => {
    const selectedData = { id: instrument.id, symbol: instrument.symbol };
    setSelectedInstrument(selectedData as {id: string, symbol: string});
  };

  if (isLoading) {
    return (
      <InstrumentTableSkeleton 
        type="loading"
        children={<div className="loading-state">
          <div className="loading-icon">📊</div>
          <p className="loading-title">Loading instruments...</p>
          <p className="loading-subtitle">Please wait while we fetch your data</p>
        </div>} 
      />
    );
  }

  if (instruments.length === 0) {
    return (
      <InstrumentTableSkeleton 
        type="empty"
        children={<div className="empty-state">
          <div className="empty-icon">📈</div>
          <p className="empty-title">No instruments in this watchlist</p>
          <p className="empty-subtitle">Use the search below to add instruments</p>
        </div>} 
      />
    );
  }

  return (
    <div className="instruments-section">
      <div className="instruments-table-container">
        <table className="instruments-table">
          <thead>
            <tr>
              <th className="table-header">Symbol</th>
              <th className="table-header">Last</th>
              <th className="table-header">Change</th>
              <th className="table-header" style={{ textAlign: "end" }}>Change%</th>
            </tr>
          </thead>
          <tbody>
            {instruments.map((instrument) => (
              <tr 
                key={instrument.id} 
                className={`table-row ${selectedInstrument?.id === instrument.id ? 'selected' : ''}`} 
                onClick={() => handleSelectInstrument(instrument)}
              >
                <td className="table-row cell symbol">
                  <span className="symbol-text">{instrument.symbol}</span>
                  <span className="market-text">{instrument.market}</span>
                </td>
                <td className="table-row cell price">
                  {instrument?.last?.toFixed(2) ? instrument?.last?.toFixed(2) + instrument.currency : "--"} 
                </td>
                <td
                  className={`table-row cell change ${
                    instrument.change >= 0 ? "positive" : "negative"
                  }`}
                >
                  {instrument.change >= 0 ? "+" : ""}
                  {instrument.change?.toFixed(2) ?? "--"}
                </td>
                <td  className={`table-row cell change-percent ${
                    instrument.changePercent >= 0 ? "positive" : "negative"
                  }`}>
                {instrument.changePercent >= 0 ? "+" : ""}
                  {instrument.changePercent?.toFixed(2) ? instrument.changePercent?.toFixed(2) + "%" : "--"}
                <button
                  className="remove-btn"
                  onClick={() => handleRemoveInstrument(instrument.id)}
                >
                  <Trash2 size={16} />
                </button>
                </td>
                
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default InstrumentTable;
