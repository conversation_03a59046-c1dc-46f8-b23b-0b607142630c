import {gql} from '../../../../graphql-queries/utils';

export const INSTRUMENT_DETAIL_QUERY = gql(`query InstrumentDetail($ids: [Int!]!, $adjClose: Boolean, $toCurrency: String) {
  instrumentByIds(ids: $ids, exchangeCurrency: $toCurrency, adjClose: $adjClose) {
    shareName
    id
    symbol
    market {
      translation {
        cultureName
        value
      }
    }
    currency {
      code
      name
    }
    currentPrice {
      open
      date
      bid
      ask
      high
      low
      volume
      officialClose
      officialCloseDate
      tickerData {
        last
        change
        changePercentage
        prevClose
      }
    }
    fifty_two_weeks: performance(period: FIFTY_TWO_WEEKS) {
      highest
      lowest
      changePercentage
    }
  }
}`);
