using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Data.Sorting;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.PressRelease;

/// <summary>
/// Custom sort type to enable ordering on <see cref="InstrumentHistoryDto.DateTime"/> only.
/// </summary>
public class PressReleaseSortType: SortInputType<PressReleaseDto>
{
  protected override void Configure(ISortInputTypeDescriptor<PressReleaseDto> descriptor)
  {
    descriptor.BindFieldsExplicitly();

    descriptor.Field(f => f.DateTime).Type<DefaultSortEnumType>();
    descriptor.Field(f => f.InsertedDate).Type<DefaultSortEnumType>();
  }
}
