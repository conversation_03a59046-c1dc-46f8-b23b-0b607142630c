using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.Types.PerformanceShare;
using HotChocolate;
using HotChocolate.Resolvers;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Middleware;


public class PerformanceShareScopedDataContextMiddleware
{
  private readonly FieldDelegate _next;

  public PerformanceShareScopedDataContextMiddleware(FieldDelegate next)
  {
    _next = next;
  }

  public async ValueTask InvokeAsync(IMiddlewareContext context)
  {
    context.ScopedContextData = context.ScopedContextData.Add(
      nameof(PerformanceShareType), new Dictionary<int, List<PerformanceInput>>());

    await _next(context).ConfigureAwait(false);
  }
}
