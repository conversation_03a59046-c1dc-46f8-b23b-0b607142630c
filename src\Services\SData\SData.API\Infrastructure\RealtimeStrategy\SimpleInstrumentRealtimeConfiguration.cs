
using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;

namespace Euroland.FlipIT.SData.API.Infrastructure;

/// <summary>
/// Simple class querying to table [rt_InstrumentPrice] to
/// get prices to determine whether an instrument has realtime data or not.
/// If no prices return, it means that instrument is not configured to have realtime data.
/// Cache is implemented as well to reduce roundtrip to DB while this configuration is not changed frequently.
/// </summary>
internal class SimpleInstrumentRealtimeConfiguration : IInstrumentRealtimeConfiguration
{
  private readonly IDbContextFactory<RealtimeSharkDbContext> _dbContextFactory;
  private readonly IDistributedCache _cache;
  public SimpleInstrumentRealtimeConfiguration(
    IDbContextFactory<RealtimeSharkDbContext> dbContextFactory,
    IDistributedCache cache)
  {
    _dbContextFactory = dbContextFactory ?? throw new ArgumentNullException(nameof(dbContextFactory));
    _cache = cache ?? throw new ArgumentNullException(nameof(cache));
  }

  public async Task<bool> IsConfigured(int instrumentId)
  {
    if(HasCache(instrumentId, out var isRealtimeConfigured))
    {
      return isRealtimeConfigured;
    }

    using var db = await _dbContextFactory.CreateDbContextAsync();
    var result= await db.InstrumentPrice.AsNoTracking()
      .Where(i => i.InstrumentId == instrumentId)
      .Take(1)
      .CountAsync() > 0;

    SetCache(instrumentId, result);

    return result;
  }

  private bool HasCache(int instrumentId, out bool result)
  {
    var cacheId = $"{nameof(IInstrumentRealtimeConfiguration)}_{instrumentId}";

    var value = _cache.GetString(cacheId);

    if(value != null)
    {
      _ = bool.TryParse(value, out result);
      return true;
    }

    result = false;
    return false;
  }

  private void SetCache(int instrumentId, bool value)
  {
    var cacheId = $"{nameof(IInstrumentRealtimeConfiguration)}_{instrumentId}";
    _cache.SetString(cacheId, value ? bool.TrueString : bool.FalseString);
  }
}
