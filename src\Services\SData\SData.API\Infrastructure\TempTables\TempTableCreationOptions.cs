using System;

namespace Euroland.FlipIT.SData.API.Infrastructure.TempTables;

public class TempTableCreationOptions
{
  private ITempTableNameProvider _tableNameProvider;
  public bool TruncateTableIfExists { get; set; }

  public ITempTableNameProvider TableNameProvider
      {
         get => _tableNameProvider ?? NewGuidTempTableNameProvider.Instance;
         set => _tableNameProvider = value ?? throw new ArgumentNullException(nameof(value), "The table name provider cannot be null.");
      }

   public bool DropTableOnDispose { get; set; } = true;

   public bool UseDefaultDatabaseCollation { get; set; } = false;
}
