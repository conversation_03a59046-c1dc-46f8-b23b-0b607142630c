using System.Reflection;
using Euroland.FlipIT.Fundamental.API.Persistance.FinCalender;
using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.Fundamental.API.Persistance;

public class FinCalendarDbContext : DbContext
{
  public FinCalendarDbContext(DbContextOptions<FinCalendarDbContext> options)
    : base(options)
  {
  }

  protected override void OnModelCreating(ModelBuilder modelBuilder)
  {
    base.OnModelCreating(modelBuilder);
    modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
  }

  public DbSet<FinancialEvent> FinancialEvents { get; set; } = null!;
  public DbSet<FinancialEventDetail> FinancialEventDetails { get; set; } = null!;
  public DbSet<FinancialEventAttachment> FinancialEventAttachments { get; set; } = null!;
  public DbSet<FinancialEventStatistics> FinancialEventStatistics { get; set; } = null!;
  public DbSet<FinancialEventType> FinancialEventTypes { get; set; } = null!;
  public DbSet<FinancialEventTypeDetail> FinancialEventTypeDetails { get; set; } = null!;
}
