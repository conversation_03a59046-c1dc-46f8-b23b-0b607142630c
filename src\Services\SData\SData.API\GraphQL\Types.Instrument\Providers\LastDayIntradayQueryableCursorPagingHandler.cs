using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using HotChocolate;
using HotChocolate.Resolvers;
using HotChocolate.Types.Pagination;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;

internal class LastDayIntradayQueryableCursorPagingHandler<TEntity> : CursorPagingHandler
{
  private readonly LastDayIntradayQueryableCursorPagination<TEntity> _pagination =
        LastDayIntradayQueryableCursorPagination<TEntity>.Instance;

  public LastDayIntradayQueryableCursorPagingHandler(PagingOptions options) : base(options)
  {
  }

  protected override ValueTask<Connection> SliceAsync(IResolverContext context, object source, CursorPagingArguments arguments)
  {
    var ct = context.RequestAborted;
        return source switch
        {
            IQueryable<TEntity> q => ResolveAsync(context, q, arguments, ct),
            IEnumerable<TEntity> e => ResolveAsync(context, e.AsQueryable(), arguments, ct),
            IExecutable<TEntity> ex => SliceAsync(context, ex.Source, arguments),
            _ => throw new GraphQLException("Cannot handle the specified data source.")
        };
  }

  private async ValueTask<Connection> ResolveAsync(
        IResolverContext context,
        IQueryable<TEntity> source,
        CursorPagingArguments arguments = default,
        CancellationToken cancellationToken = default)
    {
        int? totalCount = null;

        if (IncludeTotalCount && context.IsTotalCountSelected())
        {
            totalCount = source.Count();
        }

        return await _pagination
            .ApplyPaginationAsync(source, arguments, totalCount, cancellationToken)
            .ConfigureAwait(false);
    }
}
