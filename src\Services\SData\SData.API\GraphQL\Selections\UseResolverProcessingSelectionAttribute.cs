using System;
using System.Reflection;
using HotChocolate.Internal;
using HotChocolate.Resolvers;
using HotChocolate.Types;
using HotChocolate.Types.Descriptors;
using HotChocolate.Types.Descriptors.Definitions;

namespace Euroland.FlipIT.SData.API.GraphQL.Selections;

public class UseResolverProcessingSelectionAttribute : ObjectFieldDescriptorAttribute
{
  public const string ResolverProcessingSelection = "Euroland.FlipIT.SData.API.GraphQL.Selections.ResolverProcessingSelection";
  protected override void OnConfigure(IDescriptorContext context, IObjectFieldDescriptor descriptor, MemberInfo member)
  {
    FieldMiddlewareDefinition placeholder = new(_ => _ => default, key: ResolverProcessingSelection);

    Type middlewareType = typeof(ResolverProcessingSelectionsMiddleware);

    descriptor
      .Extend()
      .Definition
      .MiddlewareDefinitions
      .Add(placeholder);

    descriptor
      .Extend()
      .OnBeforeCreate(
        (ctx, def) => {
          IExtendedType extendedType = ctx.TypeInspector.GetType(def.ResultType);

          //def.Type =
          def.Configurations.Add(new CompleteConfiguration<ObjectFieldDefinition>(
            (_, objFieldDefinition) => {
              CompileMiddleware(objFieldDefinition, placeholder, typeof(int), null, null);
            },
            def,
            ApplyConfigurationOn.BeforeCompletion
          ));
        }
      );
    //descriptor.Use<ResolverProcessingSelectionsMiddleware>();
  }

  private static void CompileMiddleware(
        ObjectFieldDefinition definition,
        FieldMiddlewareDefinition placeholder,
        Type keyType,
        Type valueType,
        Type dataLoaderType)
    {
        Type middlewareType = typeof(ResolverProcessingSelectionsMiddleware);

        FieldMiddleware middleware = FieldClassMiddlewareFactory.Create(middlewareType);
        var index = definition.MiddlewareDefinitions.IndexOf(placeholder);
        definition.MiddlewareDefinitions[index] = new(middleware, key: ResolverProcessingSelection);
    }
}
