import Markdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import {
  faFilePdf,
  faSquareArrowUpRight,
} from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import clsx from "clsx";
import { useMemo } from "react";

const isPdf = (url: string) => {
  if(url.includes("api/pdf/download")) return true;
  return new URL(url).pathname.endsWith(".pdf");
};

const ReferenceLink = ({
  href,
  supNumber,
  text,
}: {
  href: string;
  supNumber: string;
  text: string;
}) => {
  const isPdfLink = useMemo(() => isPdf(href), [href]);

  return (
    <div className="reference-card">
      <a
        href={href}
        className="reference-link"
        target="_blank"
        rel="noopener noreferrer"
      >
        <FontAwesomeIcon
          icon={isPdfLink ? faFilePdf : faSquareArrowUpRight}
          className={clsx("reference-icon", {
            "text-primary": !isPdfLink,
            "text-red-500": isPdfLink,
          })}
        />
        <div className="reference-title">
          <sup>{supNumber}</sup> {text}
        </div>
      </a>
    </div>
  );
};

// Process the markdown to transform references
const processReferences = (markdown: string) => {
  // This regex looks for patterns like: <sup>1</sup> [text](link)
  const refRegex = /<sup>(\d+)<\/sup>\s*\[(.*?)\]\((.*?)\)/g;

  return markdown.replace(refRegex, (_, supNumber, text, link) => {
    // Transform to a special format we can detect
    return `[${supNumber}:${text}](${link})`;
  });
};

const MarkdownSetup = ({ children }: { children: string }) => {
  const processedMarkdown = useMemo(() => {
    return processReferences(children ?? "");
  }, [children]);

  return (
    <Markdown
      className={"markdown-content"}
      remarkPlugins={[remarkGfm]}
      rehypePlugins={[rehypeRaw]}
      components={{
        a: ({ children, href, ...props }) => {
          const childrenStr = String(children);
          const refMatch = childrenStr.match(/^(\d+):(.*)/);

          if (href && refMatch) {
            const [, supNumber, text] = refMatch;
            return (
              <ReferenceLink
                href={href}
                supNumber={supNumber}
                text={text}
              />
            );
          }

          return (
            <a href={href} target="_blank" rel="noopener noreferrer" {...props}>
              {children}
            </a>
          );
        },
      }}
    >
      {processedMarkdown}
    </Markdown>
  );
};

export default MarkdownSetup;
