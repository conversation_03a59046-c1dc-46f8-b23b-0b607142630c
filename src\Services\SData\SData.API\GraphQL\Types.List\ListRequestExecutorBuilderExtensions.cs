using Euroland.FlipIT.SData.API.GraphQL.Types.List;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class ListRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddListType(this IRequestExecutorBuilder builder)
  {
    builder.AddType<ListType>();
    builder.AddTypeExtension<InstrumentTypeExtensions>();

    return builder;
  }
}
