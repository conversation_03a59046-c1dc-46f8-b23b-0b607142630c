﻿using AutoMapper;
using Euroland.FlipIT.SData.API.DTOs.Deprecated;
using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using System;

namespace Euroland.FlipIT.SData.API.Mappers.Deprecated.Profiles
{
  public class DividendMapperProfile : Profile
    {
        public DividendMapperProfile()
        {
            // Dividend to DividendDTO
            _ = CreateMap<Dividend, DividendEvent>(MemberList.Destination)

                .ForMember(dto => dto.Currency, conf =>
                    conf.MapFrom(ol => ol.Instrument.CurrencyCode))

                .ForMember(dto => dto.DateTime, conf =>
                    conf.MapFrom(ol => DateTime.SpecifyKind(ol.Date.Value, DateTimeKind.Utc)))

                .ForMember(dto => dto.Date, conf =>
                    conf.MapFrom(ol => ol.Date.Value.ToString("yyyy-MM-dd")))

                .ForMember(dto => dto.Dividend, conf =>
                    conf.MapFrom(ol => ol.Currency == ol.Instrument.CurrencyCode ? ol.GrossDivAdj :
                                       SharkDbContextBase.GetRateForCurrencyExchange(ol.Currency, ol.Instrument.CurrencyCode, ol.Date.Value) * ol.GrossDivAdj));


        }
    }
}
