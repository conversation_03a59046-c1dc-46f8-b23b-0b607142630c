using Euroland.FlipIT.SData.API.Constants;
using Euroland.FlipIT.SData.API.DTOs.Deprecated;
using Euroland.FlipIT.SData.API.Helpers;
using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Mappers.Deprecated;
using Euroland.FlipIT.SData.API.Services.Interfaces;
using HotChocolate;
using HotChocolate.Data;
using HotChocolate.Execution;
using HotChocolate.Types;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;


namespace Euroland.FlipIT.SData.API.GraphQL.Deprecated.Instruments
{
  [ExtendObjectType(OperationTypeNames.Query)]
  public class InstrumentQueries
  {
    private readonly IConfiguration _configuration;

    private readonly ILogger _logger;

    public InstrumentQueries(
        IConfiguration configuration,
        ILogger<InstrumentQueries> logger)
    {
      _configuration = configuration;
      _logger = logger;
    }

    /// <summary>
    /// Get instrument information by instrumentIds
    /// </summary>
    /// <param name="instrumentIds"></param>
    /// <param name="dayPeriod"></param>
    /// <returns></returns>
    [GraphQLDeprecated("No longer maintenance and will be removed in the next release.")]
    [UseProjection]
    public IQueryable<DTOs.Deprecated.Instrument> GetInstruments(
        [Service] IInstrumentService instrumentService,
        List<int> instrumentIds,
        int dayPeriod = 10,
        bool isRT = false,
        string? toCurrency = null,
        CancellationToken cancellationToken = default)
    {
      if (instrumentIds?.Any() == false)
      {
        throw new QueryException("Provide at least one instrument-id");
      }

      using (_logger.BeginScope(new Dictionary<string, object>
      {
        ["InstrumentIds"] = string.Join(", ", instrumentIds),
        ["UseRealTime"] = isRT
      }))
      {
        return instrumentService.GetInstruments(instrumentIds, dayPeriod, isRT, toCurrency, cancellationToken);
      }
    }

    /// <summary>
    /// Get Latest Share Trades By InstrumentId
    /// </summary>
    /// <param name="instrumentService"></param>
    /// <param name="isin"></param>
    /// <param name="marketId"></param>
    /// <param name="count"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [GraphQLDeprecated("No longer maintenance and will be removed in the next release.")]
    [UseProjection]
    public async Task<List<LatestShareTradesDto>> GetLatestShareTrades(
        [Service] IInstrumentService instrumentService,
        int instrumentId,
        int count = 10,
        string? toCurrency = null,
        bool isRT = false,
        CancellationToken cancellationToken = default)
    {
      return await instrumentService.GetLatestShareTrades(instrumentId, count, toCurrency, isRT, cancellationToken);
    }

    /// <summary>
    /// Get Today Share Trades By InstrumentId
    /// </summary>
    /// <param name="instrumentService"></param>
    /// <param name="isin"></param>
    /// <param name="marketId"></param>
    /// <param name="count"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [GraphQLDeprecated("No longer maintenance and will be removed in the next release.")]
    [UseProjection]
    public async Task<List<LatestShareTradesDto>> GetTodayShareTrades(
        [Service] IInstrumentService instrumentService,
        int instrumentId,
        string? toCurrency = null,
        bool isRT = false,
        CancellationToken cancellationToken = default)
    {
      return await instrumentService.GetTodayShareTrades(instrumentId, toCurrency, isRT, cancellationToken);
    }

    /// <summary>
    /// Get instrument histories by instrumentId
    /// </summary>
    /// <param name="instrumentId">The ID of instrument</param>
    /// <param name="isIntraday"></param>
    /// <param name="fromDate"></param>
    /// <param name="toDate"></param>
    /// <param name="period">T</param>
    /// <param name="toCurrency">
    /// The quote currency code to convert from share's base currency to. The format follows the ISO 4217.
    /// Default null.
    /// </param>
    /// <returns></returns>
    [GraphQLDeprecated("No longer maintenance and will be removed in the next release.")]
    public async Task<IEnumerable<InstrumentHistory>> GetInstrumentHistoricalAsync(
        [Service] IInstrumentService instrumentService,
        [Service] ICurrencyRateService currencyService,
        CancellationToken cancellationToken,
        int instrumentId,
        bool isIntraday,
        bool isRT = false,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        Period period = Period.ONE_MINUTE,
        string? toCurrency = null)
    {
      var histories = await instrumentService.GetHistoricalDataAsync(
          instrumentId,
          isIntraday,
          period,
          fromDate,
          toDate,
          isRT: isRT,
          cancellationToken);
      if (!string.IsNullOrEmpty(toCurrency) && histories.Any())
      {
        if (toCurrency.Length != 3)
        {
          throw new ArgumentException("`currency` length must be 3");
        }

        var instrument = await instrumentService.GetInstrumentByIdAsync̣(instrumentId);

        var instrumentCurrency = instrument?.CurrencyCode?.ToUpper();
        toCurrency = toCurrency.ToUpper();
        if (instrumentCurrency != toCurrency && !string.IsNullOrEmpty(instrumentCurrency))
        {
          var firstDate = histories.Min(h => h.DateTime);
          var lastDate = histories.Max(h => h.DateTime);

          var currencyRates = await currencyService.GetRatesAsync(instrumentCurrency, toCurrency, firstDate, lastDate, isRT);

          foreach (var h in histories)
          {
            var closestRate = currencyRates
                .Where(c => c.Date <= h.DateTime.Date)
                .DefaultIfEmpty()
                .MaxBy(c => c?.Date);

            if (closestRate != null)
            {
              h.Open *= closestRate.Value;
              h.Close *= closestRate.Value;
              h.High *= closestRate.Value;
              h.Low *= closestRate.Value;
            }
          }
        }
      }

      return histories;
    }

    /// <summary>
    /// Get instrument histories by instrumentId
    /// </summary>
    /// <param name="instrumentIds"></param>
    /// <param name="numberOfYears"></param>
    /// <returns></returns>
    [GraphQLDeprecated("No longer maintenance and will be removed in the next release.")]
    public async Task<IEnumerable<InstrumentYearlyPerformancePrice>> GetInstrumentPerformanceAsync(
        [Service] IInstrumentService service,
        List<int> instrumentIds,
        CancellationToken cancellationToken,
        int numberOfYears = 5,
        string? toCurrency = null)
    {
      var result = await service.GetYearlyPerformanceDataAsync(instrumentIds, numberOfYears, cancellationToken, toCurrency);
      return result;
    }

    /// <summary>
    /// Get list Devidend events by instrumentId
    /// </summary>
    /// <param name="instrumentId"></param>
    /// <param name="fromDate"></param>
    /// <param name="toDate"></param>
    /// <returns></returns>
    [UseProjection]
    [GraphQLDeprecated("No longer maintenance and will be removed in the next release.")]
    public IQueryable<DividendEvent> GetDividendEvents(
        [Service] IInstrumentService instrumentService,
        int instrumentId,
        DateTime? fromDate,
        DateTime? toDate,
        bool isRT = false)
    {
      return instrumentService.GetDividendEvents(instrumentId, fromDate, toDate, isRT);
    }

    /// <summary>
    /// Get list earnings event
    /// </summary>
    /// <param name="companyCode"></param>
    /// <param name="fromDate"></param>
    /// <param name="toDate"></param>
    /// <returns></returns>
    [UseProjection]
    [GraphQLDeprecated("No longer maintenance and will be removed in the next release.")]
    public IQueryable<EarningEvent> GetEarningsEvents(
        [Service] IInstrumentService instrumentService,
        string companyCode,
        DateTime? fromDate,
        DateTime? toDate,
        bool isRT = false)
    {
      return instrumentService.GetEarningsEvents(companyCode, fromDate, toDate, isRT);
    }

    /// <summary>
    /// Get Press Releases by companyCode
    /// </summary>
    /// <param name="companyCode"></param>
    /// <param name="fromDate"></param>
    /// <param name="toDate"></param>
    /// <param name="prSources">list pr sourceId, format: sourceId></param>
    /// <param name="languageForwarding">format: souceId|sourceLang|destLang;...</param>
    /// <param name="includeMessageTypeIDs">format: typeId1,typeId2,...</param>
    /// <param name="excludeMessageTypeIDs">format: typeId1,typeId2,...</param>
    /// <param name="ct"></param>
    /// <returns></returns>
    [GraphQLDeprecated("No longer maintenance and will be removed in the next release.")]
    [UseProjection]
    public IQueryable<PressRelease> GetPressReleases(
        NewsContext dbContext,
        string companyCode,
        DateTime? fromDate,
        DateTime? toDate,
        string? prSources, // "1,14"
        string? languageForwarding, // "5|ar-AE|en-GB;6|zh-TW|zh-CN"
        string? includeMessageTypeIDs, // "1,2,3"
        string? excludeMessageTypeIDs)
    {
      var lang = LangHelpers.GetLang(CultureInfo.CurrentCulture.Name);
      var predicate = BuildPredicateWithPrReferences(
              companyCode,
              fromDate, toDate,
              lang.LangId, languageForwarding,
              prSources,
              includeMessageTypeIDs,
              excludeMessageTypeIDs);

      return dbContext.PressReleases
          .AsNoTracking()
          .Where(predicate)
          .ToModel();
    }

    /// <summary>
    /// Get press release detail by prID
    /// </summary>
    /// <param name="prId">press release id</param>
    /// <param name="ct">cancellation token</param>
    /// <returns></returns>
    [GraphQLDeprecated("No longer maintenance and will be removed in the next release.")]
    [UseProjection]
    public IQueryable<PressRelease> GetPressReleaseDetail(NewsContext dbContext, long prId)
    {
      return dbContext.PressReleases
          .AsNoTracking()
          .Include(i => i.Attachment.Where(a => a.IsInBlob))
          .Where(i => i.Id == prId)
          .ToModel();
    }

    #region Time Series Stock Data APIs

    /// <summary>
    /// TIME_SERIES_INTRADAY
    /// This API returns intraday time series of the equity specified, covering extended trading hours
    /// where applicable (e.g., 4:00am to 8:00pm Eastern Time for the US market)
    /// This API returns the most recent 2-3 weeks of intraday data
    /// and is best suited for short-term/medium-term charting and trading strategy development.
    /// </summary>
    /// <param name="instrumentId">The ID of the equity of your choice.</param>
    /// <param name="type">The time series of your choice. Default is INTRADAY_ONEDAY</param>
    /// <param name="interval">Time interval between two consecutive data points in the time series. The following values are supported: 1min, 5min, 10min, 60min</param>
    /// <param name="from"></param>
    /// <param name="to"></param>
    /// <returns></returns>
    [GraphQLDeprecated("No longer maintenance and will be removed in the next release.")]
    public async Task<IEnumerable<InstrumentDailyData>> GetIntradayData(
        [Service] ICurrencyRateService currencyService,
        [Service] IInstrumentService instrumentService,
        int instrumentId,
        string? toCurrency = null,
        bool isRT = false
        //INTRADAY type               = INTRADAY.ONE_DAY,
        //INTRADAY_INTERVAL interval  = INTRADAY_INTERVAL.ONE_MINUTE,
        //DateTime? from              = null,
        //DateTime? to                = null
        )
    {
      var result = await instrumentService.GetLatestDailyDataAsync(instrumentId, isRT);

      if (!string.IsNullOrEmpty(toCurrency) && result.Any())
      {
        if (toCurrency.Length != 3)
        {
          throw new ArgumentException("`currency` length must be 3");
        }

        var instrument = await instrumentService.GetInstrumentByIdAsync̣(instrumentId);

        var instrumentCurrency = instrument?.CurrencyCode?.ToUpper();
        toCurrency = toCurrency.ToUpper();
        if (instrumentCurrency != toCurrency && !string.IsNullOrEmpty(instrumentCurrency))
        {
          var firstDate = result.Min(h => h.Date);
          var lastDate = result.Max(h => h.Date);

          var currencyRates = await currencyService.GetRatesAsync(instrumentCurrency, toCurrency, firstDate, lastDate, isRT);

          foreach (var h in result)
          {
            var closestRate = currencyRates
                .Where(c => c.Date <= h.Date)
                .DefaultIfEmpty()
                .MaxBy(c => c?.Date);

            if (closestRate != null)
            {
              h.Close *= closestRate.Value;
            }
          }
        }
      }

      return result;
    }

    /// <summary>
    /// This API returns overview time series of the equity specified
    /// </summary>
    /// <param name="instrumentId">The ID of the equity of your choice.</param>
    /// <param name="period">The time series of your choice.</param>
    /// <param name="from"></param>
    /// <param name="to"></param>
    /// <returns></returns>
    [GraphQLDeprecated("No longer maintenance and will be removed in the next release.")]
    public async Task<StockData?> GetStockOverview(
        [Service] IInstrumentService service,
        [Service] ICurrencyRateService currencyRateService,
        int instrumentId,
        Periods period = Periods.ONE_MONTH,
        DateTime? from = null,
        DateTime? to = null,
        string? toCurrency = null)
    {
      if (!from.HasValue || !to.HasValue)
      {
        from = await GetFromDateByPeriod(period, from, instrumentId, service);
        to = DateTime.UtcNow;
      }

      var fromDate = from.Value.Date;
      var toDate = to.Value.Date;

      var stockData = await service.GetStockOverview(instrumentId, period, fromDate, toDate, toCurrency);

      return stockData;
    }

    /// <summary>
    /// TIME_SERIES_DAILY|WEEKLY|MONTHLY
    /// This API returns daily|weekly|monthly time series of the equity specified
    /// </summary>
    /// <param name="instrumentId">The ID of the equity of your choice.</param>
    /// <param name="type">The time series of your choice.</param>
    /// <param name="period">The time series of your choice.</param>
    /// <param name="from"></param>
    /// <param name="to"></param>
    /// <param name="peers">The list of ID of peer.</param>
    /// <param name="indices">The list of ID of index.</param>
    /// <returns></returns>
    [GraphQLDeprecated("No longer maintenance and will be removed in the next release.")]
    public async Task<IEnumerable<StockData>?> GetHistoricalData(
        [Service] IInstrumentService service,
        int instrumentId,
        Historical type = Historical.MONTHLY,
        Periods period = Periods.THREE_MONTHS,
        DateTime? from = null,
        DateTime? to = null,
        List<int> peers = null,
        List<int> indices = null,
        List<int> mas = null,
        string? toCurrency = null,
        bool isRT = false)
    {
      if (!string.IsNullOrEmpty(toCurrency) && toCurrency.Length != 3)
      {
        throw new ArgumentException("`toCurrency` length must be 3");
      }

      if (!from.HasValue || !to.HasValue)
      {
        to = DateTime.UtcNow;
        from = await GetFromDateByPeriod(period, fromDateParam: null, instrumentId: instrumentId, instrumentService: service);
      }

      var fromDate = from.Value.Date;
      var toDate = to.Value.Date;

      switch (type)
      {
        case Historical.DAILY:
          return await service.GetDailyStockData(instrumentId, fromDate, toDate, peers, indices, mas, toCurrency, isRT);

        case Historical.WEEKLY:
          return await service.GetWeeklyStockData(instrumentId, fromDate, toDate, peers, indices, mas, toCurrency, isRT);

        case Historical.MONTHLY:
        default:
          return await service.GetMonthlyStockData(instrumentId, fromDate, toDate, peers, indices, mas, toCurrency, isRT);
      }
    }

    #endregion Time Series Stock Data APIs

    /***********************************************/
    /* helper services for the InstrumentResolvers */
    /***********************************************/

    private Expression<Func<PressRelease, bool>> BuildPredicateWithPrReferences(
        string companyCode,
        DateTime? fromDate, DateTime? toDate,
        int curLangId,
        string? langForwarding,
        string? prSourceIds,
        string? includeMsgTypeIds,
        string? excludeMsgTypeIds
        )
    {
      var predicateBuilder = PredicateBuilder.New<PressRelease>();
      Expression<Func<PressRelease, bool>> predicate = null;

      // where(prLangId = @LangId || {@LangForwarding})
      var langsFw = GetLangForwarding(langForwarding, curLangId);
      predicate = predicateBuilder.And(langsFw);

      // where(companyCode = @cCode & @fromDate <= prDate <= @toDate)
      Expression<Func<PressRelease, bool>> condition = i =>
                          i.CompanyCode == companyCode &&
                          (!fromDate.HasValue || i.DateTime >= fromDate) &&
                          (!toDate.HasValue || i.DateTime <= toDate);

      predicate = predicateBuilder.And(condition);

      // where(prSourceId in @sourceIds)
      if (!string.IsNullOrEmpty(prSourceIds))
      {
        var sourceIds = ToListInt(prSourceIds, ',');
        if (sourceIds.Any())
          predicate = predicateBuilder.And(i => sourceIds.Contains(i.SourceId));
      }

      // where(msgTypeId in @includeMsgTypeIds)
      if (!string.IsNullOrEmpty(includeMsgTypeIds))
      {
        var msgTypeIds = ToListInt(includeMsgTypeIds, ',');
        if (msgTypeIds.Any())
          predicate = predicateBuilder.And(i => msgTypeIds.Contains(i.MessageTypeId.Value));
      }

      // where(msgTypeId <> @excludeMsgTypeIds)
      if (!string.IsNullOrEmpty(excludeMsgTypeIds))
      {
        var msgTypeIds = ToListInt(excludeMsgTypeIds, ',');
        if (msgTypeIds.Any())
          predicate = predicateBuilder.And(i => !msgTypeIds.Contains(i.MessageTypeId.Value));
      }

      return predicate;
    }

    private Expression<Func<PressRelease, bool>> GetLangForwarding(string langFw, int curLangId)
    {
      Expression<Func<PressRelease, bool>> predicate = null;
      Expression<Func<PressRelease, bool>> condition = i => i.LanguageId == curLangId;

      var predicateBuilder = PredicateBuilder.New<PressRelease>();
      predicate = predicateBuilder.Or(condition);

      if (!string.IsNullOrEmpty(langFw))
      {
        try
        {
          var langsForwardings = langFw.Split(';', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
          foreach (var item in langsForwardings)
          {
            var temp = item.Split('|', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);

            var prSourceId = Int32.Parse(temp[0]);
            var sourceLang = LangHelpers.GetLang(temp[1]);
            var destLang = LangHelpers.GetLang(temp[2]);

            if (destLang.LangId == curLangId)
            {
              Expression<Func<PressRelease, bool>> _forward = i => i.SourceId == prSourceId && i.LanguageId == sourceLang.LangId;
              predicate = predicateBuilder.Or(_forward);
            }
          }
        }
        catch (Exception)
        {
          return predicate;
        }
      }

      return predicate;
    }

    private List<int> ToListInt(string str, char c)
    {
      try
      {
        var strArr = str.Split(c, StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);

        return Array.ConvertAll(strArr, s => int.Parse(s)).ToList();
      }
      catch (Exception)
      {
        return new List<int>();
      }
    }

    private async Task<DateTime> GetFromDateByPeriod(Periods period, DateTime? fromDateParam = null, int instrumentId = 0, IInstrumentService instrumentService = null)
    {
      var toDate = DateTime.UtcNow;

      switch (period)
      {
        case Periods.ONE_MONTH:
          return toDate.AddMonths(-1);

        case Periods.THREE_MONTHS:
          return toDate.AddMonths(-3);

        case Periods.SIX_MONTHS:
          return toDate.AddMonths(-6);

        case Periods.YTD:
          return new DateTime(toDate.Year, 1, 1);

        case Periods.ONE_YEAR:
          return toDate.AddYears(-1);

        case Periods.THREE_YEARS:
          return toDate.AddYears(-3);

        case Periods.FIVE_YEARS:
          return toDate.AddYears(-5);

        case Periods.CUSTOM_RANGE:
        case Periods.ALL:
          var startingDate = await instrumentService.GetStartingDateByInstrument(instrumentId);
          return (DateTime)(fromDateParam.HasValue ? fromDateParam : startingDate);

        default:
          return toDate.AddYears(-1);
      }
    }
  }
}
