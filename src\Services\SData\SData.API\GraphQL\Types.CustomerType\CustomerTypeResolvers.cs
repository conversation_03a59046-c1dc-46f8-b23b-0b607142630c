using System.Linq;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure;
using HotChocolate;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.CustomerType;

public class CustomerTypeResolvers
{
  public IQueryable<CustomerTypeDto> GetCustomerTypeByCompany(
    [Parent] CompanyDto company,
    SharkDbContextBase sharkDbContext)
  {
    return sharkDbContext.CustomerType
      .AsNoTracking()
      .Where(ct => ct.Id == company.CustomerTypeId)
      .WithAutoMapper()
      .ToProjection<CustomerTypeDto>();
  }

  public IQueryable<CustomerTypeDto> GetCustomerTypeByInstrument(
    [Parent] InstrumentDto instrument,
    SharkDbContextBase sharkDbContext)
  {
    return sharkDbContext.CustomerType
      .AsNoTracking()
      .Where(ct => ct.Id == instrument.Customer)
      .WithAutoMapper()
      .ToProjection<CustomerTypeDto>();
  }
}
