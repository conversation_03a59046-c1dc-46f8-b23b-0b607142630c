/**
 * An object containing the routes used for OIDC (OpenID Connect) authentication.
 *
 * @property {string} SIGNIN - The route for the sign-in page.
 * @property {string} SIGNIN_CALLBACK - The route for the sign-in callback page.
 * @property {string} SIGNOUT - The route for the sign-out page.
 * @property {string} SIGNOUT_CALLBACK - The route for the sign-out callback page.
 */
export const OidcAuthAppRoutes = {
  SIGNIN: '/signin',
  SIGNIN_CALLBACK: '/signin-oidc',
  SIGNOUT: '/signout',
  SIGNOUT_CALLBACK: '/signout-oidc'
};
