using System.Security.Principal;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Euroland.FlipIT.SData.API.Infrastructure.Configurations;

public class CountryConfiguration: IEntityTypeConfiguration<Country>
{
    public void Configure(EntityTypeBuilder<Country> builder)
    {
        builder.ToTable("CountryCode");
        builder.Property(e => e.Id).HasColumnName("ccID");
        builder.Property(e => e.Name).HasColumnName("ccCountry");
        builder.Property(e => e.CallingCode).HasColumnName("ccPhoneCode");
        builder.Property(e => e.Code).HasColumnName("ccCode");
    }
}
