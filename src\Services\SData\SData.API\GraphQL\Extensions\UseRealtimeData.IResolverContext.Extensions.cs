using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Infrastructure;
using HotChocolate.Resolvers;
using Microsoft.AspNetCore.Http;

namespace Microsoft.Extensions.DependencyInjection;

internal static class UseRealtimeDataIResolverContextExtensions
{
  public static Task<bool> UseRealtimeDataAsync(this IResolverContext resolverContext)
  {
    var strategy = resolverContext.RequestServices.GetRequiredService<IRealtimeDataStrategy>();
    var httpcontextAccessor = resolverContext.RequestServices.GetRequiredService<IHttpContextAccessor>();

    return strategy.IsAllowedAsync(httpcontextAccessor.HttpContext);
  }
}
