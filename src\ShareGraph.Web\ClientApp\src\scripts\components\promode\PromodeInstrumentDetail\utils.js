export const getSelectedInstrumentId = () => {
  const instrumentId =
    window.EurolandAppContext?.command("instrument-selected")?.id ||
    window?.xprops?.data?.mainInstrumentId;
  return instrumentId ? parseInt(instrumentId) : undefined;
};

export const formatNumber = (value, decimals = 2) => {
  if (value === null || value === undefined) return "--";
  return Number(value).toLocaleString(undefined, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  });
};

export const formatPercentage = (value) => {
  if (value === null || value === undefined) return "--";
  const formatted = Number(value).toFixed(2);
  return `${formatted >= 0 ? "+" : ""}${formatted}%`;
};

export const formatVolume = (value) => {
  if (value === null || value === undefined) return "--";
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`;
  }
  return value.toLocaleString();
};