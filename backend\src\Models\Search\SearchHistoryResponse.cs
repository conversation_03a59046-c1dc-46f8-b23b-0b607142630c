﻿namespace Euroland.FlipIT.WiseSearch.Models.Search
{
    public class SearchHistoryResponse
    {
        public Guid Id { get; set; }
        public string? Question { get; set; }
        public string? AIResult { get; set; }
        public DateTime SearchTime { get; set; }
        public string CompanyCode { get; set; }
        public string CompanyName { get; set; }
        public string QALanguage { get; set; }
        public string AIResultLanguage { get; set; }
        public string ResultShow { get; set; }
    }
}
