import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faInfoCircle } from "@fortawesome/free-solid-svg-icons";
import { useState, useEffect } from "react";
import {useSearchWidgetPropsContext} from "./context/propContext";
import { i18n } from "@euroland/libs";

const DisclaimerPopup = () => {
  const [isVisible, setIsVisible] = useState(false);
  const companyCode = useSearchWidgetPropsContext(s => s.companyInfo.companyCode);

  useEffect(() => {
    // Check if user has already accepted the disclaimer for this specific company
    const hasAccepted = localStorage.getItem(`disclaimerAccepted_${companyCode}`);
    if (!hasAccepted) {
      setIsVisible(true);
    }
  }, [companyCode]);

  const handleAccept = () => {
    // Store in localStorage that user has accepted for this specific company
    localStorage.setItem(`disclaimerAccepted_${companyCode}`, "true");
    setIsVisible(false);
  };

  // Don't render anything if popup should be hidden
  if (!isVisible) {
    return null;
  }

  return (
    <div className="disclaimer-popup">
      <div className="disclaimer-popup__container">
        <div className="disclaimer-popup__header">
          <FontAwesomeIcon icon={faInfoCircle} className="disclaimer-popup__icon" />
          <h3 className="disclaimer-popup__title">{i18n.translate("disclaimerTitle")}</h3>
        </div>
        <div className="disclaimer-popup__content">
          {i18n.translate("disclaimerContent")}
        </div>
        <div className="disclaimer-popup__actions">
          <button 
            className="disclaimer-popup__button disclaimer-popup__button--accept"
            onClick={handleAccept}
          >
            {i18n.translate("disclaimerAccept")}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DisclaimerPopup;
