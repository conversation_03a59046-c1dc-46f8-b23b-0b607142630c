using HotChocolate;

namespace Euroland.FlipIT.SData.API.Dto;

/// <summary>
/// Contains the information of company.
/// </summary>
public class CompanyDto: IDtoObject
{
  public int Id { get; set; }
  public string Code { get; set; }
  public short MarketId { get; set; }
  public string? Name { get; set; }
  public string? HomePage { get; set; }
  public string? Country { get; set; }
  public string? Town { get; set; }
  public string? Tel { get; set; }
  public string? Fax { get; set; }
  public string? Email { get; set; }
  public short CustomerTypeId { get; set; }

#region deprecated

  [GraphQLDeprecated($"This field might be removed in the next release. Use field {nameof(Id)} instead.")]
  public int CompanyId => Id;

  // Always Project Field
  [GraphQLDeprecated($"This field might be removed in the next release. Use field {nameof(Code)} instead")]
  public string? CompanyCode => Code;

  [GraphQLDeprecated($"This field might be removed in the next release. Use field {nameof(MarketId)} instead")]
  public short CompanyMarket => MarketId;

  [GraphQLDeprecated($"This field might be removed in the next release. Use field {nameof(Name)} instead")]
  public string? CompanyName { get; set;}

  [GraphQLDeprecated($"This field might be removed in the next release. Use field {nameof(HomePage)} instead")]
  public string? CompanyHomePage => HomePage;

  [GraphQLDeprecated($"This field might be removed in the next release. Use field {nameof(Country)} instead")]
  public string? CompanyCountry => Country;

  [GraphQLDeprecated($"This field might be removed in the next release. Use field {nameof(Town)} instead")]
  public string? CompanyTown => Town;

  [GraphQLDeprecated($"This field might be removed in the next release. Use field {nameof(Tel)} instead")]
  public string? CompanyTel => Tel;

  [GraphQLDeprecated($"This field might be removed in the next release. Use field {nameof(Fax)} instead")]
  public string? CompanyFax => Fax;

  [GraphQLDeprecated($"This field might be removed in the next release. Use field {nameof(Email)} instead")]
  public string? CompanyEMail  => Email;

  [GraphQLDeprecated($"This field might be removed in the next release. Use field {nameof(CustomerTypeId)} instead")]
  public short CompanyCustomer => CustomerTypeId;

  #endregion deprecated
}
