#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:5.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:5.0 AS build
WORKDIR /src
COPY ["nuget.config", "."]
COPY ["src/Services/SData/SData.API/SData.API.csproj", "src/Services/SData/SData.API/"]
RUN dotnet restore "src/Services/SData/SData.API/SData.API.csproj"
COPY . .
WORKDIR "/src/src/Services/SData/SData.API"
RUN dotnet build "SData.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "SData.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Euroland.FlipIT.SData.API.dll"]
