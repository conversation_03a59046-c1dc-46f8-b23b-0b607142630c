using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace Euroland.FlipIT.SData.API.Infrastructure;

internal class HttpRequestHeaderRealtimeDataStrategy : IRealtimeDataStrategy
{
  public const string REALTIME_HEADER = "X-Db-RTData";
  public Task<bool> IsAllowedAsync(HttpContext httpContext)
  {
    return Task.Run(() =>
      httpContext.Request.Headers.TryGetValue(REALTIME_HEADER, out var allowRealtime)
      && string.Equals(allowRealtime, Boolean.TrueString, StringComparison.OrdinalIgnoreCase)
    );
  }
}
