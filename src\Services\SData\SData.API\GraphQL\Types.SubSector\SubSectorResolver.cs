using System.Linq;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure;
using HotChocolate;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.SubSector;

public class SubSectorResolver
{
  public IQueryable<SubSectorDto> GetSubSectorByInstrument(
    [Parent] InstrumentDto instrument,
    SharkDbContextBase sharkDbContext,
    string cultureName)
  {
    return sharkDbContext.SubSector
      .AsNoTracking()
      .Where(s => s.Id == instrument.MarCat && s.MarketId == instrument.MarketID)
      .WithAutoMapper()
      .ToProjection<SubSectorDto>(new { cultureName });
  }
}
