import { useState } from "react";
import styles from "./tesla-dashboard.module.scss";
import { Search, Share, Heart, Info, ChevronRight } from "lucide-react";

export default function TeslaDashboard() {
  const [activeTab, setActiveTab] = useState("Overview");
  const tabs = ["Overview", "Statements", "Statistics", "Dividends", "Earnings", "Revenue"];

  return (
    <div className={styles.dashboard}>
      {/* Header */}
      <header className={styles.header}>
        <div className={styles.headerLeft}>
          <div className={styles.logo}>
            <Heart className={styles.heartIcon} />
            <span>TSLA</span>
            <span className={styles.exchange}>NASDAQ</span>
            <span className={styles.currency}>USD</span>
          </div>
        </div>
        <div className={styles.headerRight}>
          <span>Your Watchlist</span>
          <ChevronRight size={16} />
        </div>
      </header>

      {/* Title */}
      <h1 className={styles.title}>TSLA fundamentals</h1>

      {/* Navigation */}
      <nav className={styles.navigation}>
        <div className={styles.navLeft}>
          <Search className={styles.searchIcon} />
          {tabs.map((tab) => (
            <button
              key={tab}
              className={`${styles.navTab} ${activeTab === tab ? styles.active : ""}`}
              onClick={() => setActiveTab(tab)}
            >
              {tab}
            </button>
          ))}
        </div>
        <Share className={styles.shareIcon} />
      </nav>

      {/* Key Facts */}
      <section className={styles.keyFacts}>
        <h2>Key facts</h2>
        <div className={styles.factsGrid}>
          <div className={styles.factItem}>
            <span className={styles.factLabel}>Market capitalization</span>
            <span className={styles.factValue}>
              1.03T <span className={styles.currency}>USD</span>
            </span>
          </div>
          <div className={styles.factItem}>
            <span className={styles.factLabel}>
              Dividend yield (indicated) <ChevronRight size={12} />
            </span>
            <span className={styles.factValue}>—</span>
          </div>
          <div className={styles.factItem}>
            <span className={styles.factLabel}>
              Price to earnings Ratio (TTM) <ChevronRight size={12} />
            </span>
            <span className={styles.factValue}>170.91</span>
          </div>
          <div className={styles.factItem}>
            <span className={styles.factLabel}>
              Basic EPS (TTM) <ChevronRight size={12} />
            </span>
            <span className={styles.factValue}>
              1.99 <span className={styles.currency}>USD</span>
            </span>
          </div>
          <div className={styles.factItem}>
            <span className={styles.factLabel}>Founded</span>
            <span className={styles.factValue}>2003</span>
          </div>
          <div className={styles.factItem}>
            <span className={styles.factLabel}>
              Employees (FY) <ChevronRight size={12} />
            </span>
            <span className={styles.factValue}>125.67K</span>
          </div>
          <div className={styles.factItem}>
            <span className={styles.factLabel}>CEO</span>
            <span className={styles.factValue}>Elon Reeve Musk</span>
          </div>
          <div className={styles.factItem}>
            <span className={styles.factLabel}>Website</span>
            <span className={styles.factValue}>
              tesla.com <ChevronRight size={12} />
            </span>
          </div>
        </div>
      </section>

      {/* About */}
      <section className={styles.about}>
        <h3>About</h3>
        <p>
          Tesla, Inc. engages in the design, development, manufacture, and sale of electric vehicles, energy generation
          and storage systems. The company operates through Automotive and Energy Generation and Storage. The Autom...{" "}
          <button className={styles.showMore}>Show more</button>
        </p>
      </section>

      {/* Ownership and Capital Structure */}
      <section className={styles.ownershipSection}>
        <div className={styles.ownership}>
          <h3>
            Ownership <Info size={14} />
          </h3>
          <div className={styles.ownershipChart}>
            <div className={styles.pieChart}>
              <div className={styles.centerValue}>3.22B</div>
            </div>
            <div className={styles.ownershipLegend}>
              <div className={styles.legendItem}>
                <span className={styles.legendColor} style={{ backgroundColor: "#4285f4" }}></span>
                <span>Free float shares</span>
                <span>2.80B (87.09%)</span>
              </div>
              <div className={styles.legendItem}>
                <span className={styles.legendColor} style={{ backgroundColor: "#ff9800" }}></span>
                <span>Closely held shares</span>
                <span>415.19M (12.91%)</span>
              </div>
            </div>
          </div>
        </div>
        <div className={styles.capitalStructure}>
          <h3>Capital structure</h3>
          <div className={styles.capitalChart}>
            <div className={styles.capitalBar}>
              <div className={styles.barSegment} style={{ backgroundColor: "#00bcd4", width: "60%" }}></div>
              <div className={styles.barSegment} style={{ backgroundColor: "#e91e63", width: "5%" }}></div>
              <div className={styles.barSegment} style={{ backgroundColor: "#2196f3", width: "35%" }}></div>
            </div>
            <div className={styles.capitalLegend}>
              <div className={styles.legendItem}>
                <span className={styles.legendColor} style={{ backgroundColor: "#00bcd4" }}></span>
                <span>Market cap</span>
                <span>1.03T</span>
              </div>
              <div className={styles.legendItem}>
                <span className={styles.legendColor} style={{ backgroundColor: "#4caf50" }}></span>
                <span>Debt</span>
                <span>12.11B</span>
              </div>
              <div className={styles.legendItem}>
                <span className={styles.legendColor} style={{ backgroundColor: "#ff9800" }}></span>
                <span>Minority Interest</span>
                <span>765.00M</span>
              </div>
              <div className={styles.legendItem}>
                <span className={styles.legendColor} style={{ backgroundColor: "#2196f3" }}></span>
                <span>Cash & equivalents</span>
                <span>37.41B</span>
              </div>
              <div className={styles.legendItem}>
                <span className={styles.legendColor} style={{ backgroundColor: "#9c27b0" }}></span>
                <span>Enterprise value</span>
                <span>1.01T</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Valuation */}
      <section className={styles.valuation}>
        <h2>
          Valuation <ChevronRight size={16} />
        </h2>
        <p>Fundamental metrics to determine fair value of the stock</p>

        <div className={styles.valuationContent}>
          <div className={styles.valuationLeft}>
            <h3>
              Summary <Info size={14} />
            </h3>
            <div className={styles.valuationMetrics}>
              <div className={styles.metricItem}>
                <span>Price to earning ratio (P/E)</span>
                <span>170.91x</span>
              </div>
              <div className={styles.metricItem}>
                <span>Price to sales ratio (P/S)</span>
                <span>11.43x</span>
              </div>
            </div>
            <div className={styles.valuationIndicator}>
              <span>1.03T</span>
              <div className={styles.indicatorBar}>
                <div className={styles.indicatorPointer}></div>
              </div>
            </div>
            <div className={styles.indicatorLabels}>
              <span>Market Cap</span>
              <span>Net Income</span>
              <span>Revenue</span>
            </div>
          </div>

          <div className={styles.valuationRight}>
            <h3>
              Valuation ratios <Info size={14} />
            </h3>
            <div className={styles.chartTabs}>
              <span>Annual</span>
              <span className={styles.active}>Quarterly</span>
            </div>
            <div className={styles.lineChart}>
              <div className={styles.chartArea}>
                {/* Chart visualization would go here */}
                <svg viewBox="0 0 400 200" className={styles.chartSvg}>
                  <polyline points="0,150 100,120 200,80 300,60 400,90" fill="none" stroke="#ff9800" strokeWidth="2" />
                  <polyline
                    points="0,180 100,160 200,140 300,100 400,120"
                    fill="none"
                    stroke="#2196f3"
                    strokeWidth="2"
                  />
                </svg>
              </div>
              <div className={styles.chartLegend}>
                <span>
                  <span className={styles.legendDot} style={{ backgroundColor: "#ff9800" }}></span>P/E
                </span>
                <span>
                  <span className={styles.legendDot} style={{ backgroundColor: "#2196f3" }}></span>P/S
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Growth and Profitability */}
      <section className={styles.growthSection}>
        <h2>
          Growth and Profitability <ChevronRight size={16} />
        </h2>
        <p>Company's recent performance and margins</p>

        <div className={styles.growthContent}>
          <div className={styles.performance}>
            <h3>
              Performance <Info size={14} />
            </h3>
            <div className={styles.performanceChart}>
              <div className={styles.barChart}>
                {/* Bar chart visualization */}
                <div className={styles.chartBars}>
                  <div className={styles.barGroup}>
                    <div className={styles.bar} style={{ height: "60%", backgroundColor: "#2196f3" }}></div>
                    <span>Q1 '24</span>
                  </div>
                  <div className={styles.barGroup}>
                    <div className={styles.bar} style={{ height: "80%", backgroundColor: "#2196f3" }}></div>
                    <span>Q2 '24</span>
                  </div>
                  <div className={styles.barGroup}>
                    <div className={styles.bar} style={{ height: "70%", backgroundColor: "#2196f3" }}></div>
                    <span>Q3 '24</span>
                  </div>
                  <div className={styles.barGroup}>
                    <div className={styles.bar} style={{ height: "85%", backgroundColor: "#2196f3" }}></div>
                    <span>Q4 '24</span>
                  </div>
                  <div className={styles.barGroup}>
                    <div className={styles.bar} style={{ height: "65%", backgroundColor: "#2196f3" }}></div>
                    <span>Q1 '25</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className={styles.revenueProfit}>
            <h3>
              Revenue to profit conversion <Info size={14} />
            </h3>
            <div className={styles.conversionChart}>
              {/* Waterfall chart visualization */}
              <div className={styles.waterfallChart}>
                <div className={styles.waterfallBar} style={{ backgroundColor: "#00bcd4", height: "100%" }}>
                  Revenue
                </div>
                <div className={styles.waterfallBar} style={{ backgroundColor: "#e91e63", height: "60%" }}>
                  COGS
                </div>
                <div className={styles.waterfallBar} style={{ backgroundColor: "#2196f3", height: "40%" }}>
                  Gross profit
                </div>
                <div className={styles.waterfallBar} style={{ backgroundColor: "#e91e63", height: "30%" }}>
                  Op expenses
                </div>
                <div className={styles.waterfallBar} style={{ backgroundColor: "#2196f3", height: "20%" }}>
                  Op income
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Revenue Breakdown */}
      <section className={styles.revenueBreakdown}>
        <h2>
          Revenue breakdown <ChevronRight size={16} />
        </h2>
        <p>Revenue streams and regions a business earns money from</p>

        <div className={styles.breakdownContent}>
          <div className={styles.bySource}>
            <h3>
              By source/business <Info size={14} />
            </h3>
            <span className={styles.period}>Period: 2024</span>
            <div className={styles.donutChart}>
              <svg viewBox="0 0 200 200" className={styles.donutSvg}>
                <circle
                  cx="100"
                  cy="100"
                  r="80"
                  fill="none"
                  stroke="#2196f3"
                  strokeWidth="40"
                  strokeDasharray="200 100"
                />
                <circle
                  cx="100"
                  cy="100"
                  r="80"
                  fill="none"
                  stroke="#00bcd4"
                  strokeWidth="40"
                  strokeDasharray="100 200"
                  strokeDashoffset="-200"
                />
              </svg>
            </div>
            <div className={styles.donutLegend}>
              <div className={styles.legendItem}>
                <span className={styles.legendColor} style={{ backgroundColor: "#2196f3" }}></span>
                <span>Automotive</span>
              </div>
              <div className={styles.legendItem}>
                <span className={styles.legendColor} style={{ backgroundColor: "#00bcd4" }}></span>
                <span>Energy Generation and Storage</span>
              </div>
            </div>
          </div>

          <div className={styles.byCountry}>
            <h3>
              By country <Info size={14} />
            </h3>
            <span className={styles.period}>Period: 2024</span>
            <div className={styles.donutChart}>
              <svg viewBox="0 0 200 200" className={styles.donutSvg}>
                <circle
                  cx="100"
                  cy="100"
                  r="80"
                  fill="none"
                  stroke="#2196f3"
                  strokeWidth="40"
                  strokeDasharray="150 150"
                />
                <circle
                  cx="100"
                  cy="100"
                  r="80"
                  fill="none"
                  stroke="#ff9800"
                  strokeWidth="40"
                  strokeDasharray="100 200"
                  strokeDashoffset="-150"
                />
                <circle
                  cx="100"
                  cy="100"
                  r="80"
                  fill="none"
                  stroke="#00bcd4"
                  strokeWidth="40"
                  strokeDasharray="50 250"
                  strokeDashoffset="-250"
                />
              </svg>
            </div>
            <div className={styles.donutLegend}>
              <div className={styles.legendItem}>
                <span className={styles.legendColor} style={{ backgroundColor: "#2196f3" }}></span>
                <span>United States</span>
              </div>
              <div className={styles.legendItem}>
                <span className={styles.legendColor} style={{ backgroundColor: "#ff9800" }}></span>
                <span>Other International</span>
              </div>
              <div className={styles.legendItem}>
                <span className={styles.legendColor} style={{ backgroundColor: "#00bcd4" }}></span>
                <span>China</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Estimates */}
      <section className={styles.estimates}>
        <h2>
          Estimates <ChevronRight size={16} />
        </h2>
        <p>Revenue and Earnings forecasts and estimates accuracy</p>

        <div className={styles.estimatesContent}>
          <div className={styles.revenueEstimates}>
            <h3>
              Revenue <Info size={14} />
            </h3>
            <div className={styles.estimateChart}>
              {/* Dot plot chart */}
              <div className={styles.dotPlot}>
                <div className={styles.dotRow}>
                  <span className={styles.dot} style={{ backgroundColor: "#00bcd4" }}></span>
                  <span className={styles.dot} style={{ backgroundColor: "#e91e63" }}></span>
                  <span className={styles.dot} style={{ backgroundColor: "#e91e63" }}></span>
                  <span className={styles.dot} style={{ backgroundColor: "#e91e63" }}></span>
                  <span className={styles.dotEmpty}></span>
                </div>
              </div>
            </div>
          </div>

          <div className={styles.earningsEstimates}>
            <h3>
              Earnings <Info size={14} />
            </h3>
            <span>Next: Jul 24, 2025</span>
            <div className={styles.estimateChart}>
              {/* Dot plot chart */}
              <div className={styles.dotPlot}>
                <div className={styles.dotRow}>
                  <span className={styles.dot} style={{ backgroundColor: "#00bcd4" }}></span>
                  <span className={styles.dot} style={{ backgroundColor: "#e91e63" }}></span>
                  <span className={styles.dotEmpty}></span>
                  <span className={styles.dot} style={{ backgroundColor: "#e91e63" }}></span>
                  <span className={styles.dotEmpty}></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Dividends */}
      <section className={styles.dividends}>
        <h2>
          Dividends <ChevronRight size={16} />
        </h2>
        <p>Dividend yield, history and sustainability</p>

        <div className={styles.noDividends}>
          <div className={styles.dividendIcon}>%</div>
          <div>
            <h4>No dividends</h4>
            <p>TSLA has never paid dividends and has no current plans to do so.</p>
          </div>
        </div>
      </section>

      {/* Financial Health */}
      <section className={styles.financialHealth}>
        <h2>
          Financial health <ChevronRight size={16} />
        </h2>
        <p>Financial position and solvency of the company</p>

        <div className={styles.healthContent}>
          <div className={styles.debtLevel}>
            <h3>
              Debt level and coverage <Info size={14} />
            </h3>
            <div className={styles.debtChart}>
              {/* Debt level chart */}
              <div className={styles.barChart}>
                <div className={styles.chartBars}>
                  <div className={styles.barGroup}>
                    <div className={styles.bar} style={{ height: "40%", backgroundColor: "#e91e63" }}></div>
                    <div className={styles.bar} style={{ height: "60%", backgroundColor: "#2196f3" }}></div>
                    <span>Q1 '24</span>
                  </div>
                  <div className={styles.barGroup}>
                    <div className={styles.bar} style={{ height: "50%", backgroundColor: "#e91e63" }}></div>
                    <div className={styles.bar} style={{ height: "70%", backgroundColor: "#2196f3" }}></div>
                    <span>Q2 '24</span>
                  </div>
                  <div className={styles.barGroup}>
                    <div className={styles.bar} style={{ height: "45%", backgroundColor: "#e91e63" }}></div>
                    <div className={styles.bar} style={{ backgroundColor: "#2196f3" }}></div>
                    <span>Q3 '24</span>
                  </div>
                  <div className={styles.barGroup}>
                    <div className={styles.bar} style={{ height: "55%", backgroundColor: "#e91e63" }}></div>
                    <div className={styles.bar} style={{ height: "80%", backgroundColor: "#2196f3" }}></div>
                    <span>Q4 '24</span>
                  </div>
                  <div className={styles.barGroup}>
                    <div className={styles.bar} style={{ height: "50%", backgroundColor: "#e91e63" }}></div>
                    <div className={styles.bar} style={{ height: "85%", backgroundColor: "#2196f3" }}></div>
                    <span>Q1 '25</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className={styles.financialPosition}>
            <h3>
              Financial position analysis <Info size={14} />
            </h3>
            <div className={styles.positionChart}>
              <div className={styles.stackedBars}>
                <div className={styles.stackedBar}>
                  <div className={styles.barSegment} style={{ backgroundColor: "#2196f3", height: "70%" }}>
                    Short term
                  </div>
                </div>
                <div className={styles.stackedBar}>
                  <div className={styles.barSegment} style={{ backgroundColor: "#00bcd4", height: "50%" }}>
                    Long term
                  </div>
                </div>
              </div>
              <div className={styles.positionLabels}>
                <span>Assets</span>
                <span>Liabilities</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
