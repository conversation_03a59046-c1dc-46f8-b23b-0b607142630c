import { useContext, useEffect } from 'react';
import { Routes, Route, BrowserRouter } from 'react-router-dom';
import { useSelector, useStore } from 'react-redux';

import Home from './scripts/pages/Home';
import Share from './scripts/pages/Share';
import PrintDialog from './scripts/pages/PrintDialog';
import ErrorDialog from './scripts/pages/ErrorDialog';
import PressRelease from './scripts/pages/PressRelease';
import Popout from './scripts/pages/Popout';
import Print from './scripts/pages/Print';
import Accessibility from './scripts/pages/Accessibility';
import AccessibleHome from './scripts/pages/accessibles/AccessibleHome';
import AccessibleOrderDepth from './scripts/pages/accessibles/AccessibleOrderDepth';
import AccessibleShareDetails from './scripts/pages/accessibles/AccessibleShareDetails';
import AccessibleSharePerformance from './scripts/pages/accessibles/AccessibleSharePerformance';
import { AppContext } from './scripts/AppContext';
import { getInstrumentSettingById, updateDefault } from './scripts/utils/format-number';
import { convertFormatNumber } from './scripts/helper';
import { useSelectedCurrency } from './scripts/customHooks/useSelectedCurrency';
import appConfig from './scripts/services/app-config';
import { ZoidPromodeModal } from './scripts/components/promode/promode-manager-modal/PromodeModal';
import CreateAlertModal from './scripts/pages/promode/create-alert/CreateAlertModal';


//const initRealtime = lazy(() => import('./scripts/real-time/init-realtime'));


export default function App() {
  const settings = useContext(AppContext);
  const selectedInstrumentId = useSelector(state => state.tickers.selectedInstrumentId); 
  const selectedCurrency = useSelectedCurrency();
  const store = useStore();

  function getBaseName() {
    const baseEl = document.querySelector('base[href]');
    if (baseEl) {
      return baseEl.getAttribute('href').replace(/\/$/, '');
    }
    return '';
  }

  const isRealtimeEnabled = () => {
    var settings = appConfig.get();
    //peers and tickers and indices. There is only one of them has IsRT=true then it's must enable RealTime
    var isEnabledRTForTicker = (settings.instruments || []).find(x => x.isRT) !== undefined;
    var isEnabledRTPeer = isEnabledRTForPeer(settings);
    var isEnabledRTIndicies = isEnabledRTForIndicies(settings);
    return isEnabledRTForTicker || isEnabledRTPeer || isEnabledRTIndicies;
  };  

  const isEnabledRTForPeer = (settings) => {
     const peers = settings.peers.peers || [];
     if (settings.peers.enabled === false || !peers || peers.length === 0) return false;
     return peers.find(x =>x.isRT) !== undefined;
  };

  const isEnabledRTForIndicies = (settings) => {
    const indicies = settings.indices.indices || [];
    if (settings.indices.enabled === false || !indicies || indicies.length === 0) return false;
    return indicies.find(x =>x.isRT) !== undefined;
  };


  useEffect(() => {
    document.body.setAttribute('dir', window.appSettings?.isRtl ? 'rtl' : 'ltr');

    const loadRealtime = async () => {
      const realtimeInit = (await import('./scripts/real-time/init-realtime')).default;
      realtimeInit(store);
    };

    if(isRealtimeEnabled()) {
      loadRealtime();
    }

    return () => {
      document.body.removeAttribute('dir');
    };
  }, []);

  useEffect(() => {
    if(!selectedInstrumentId) return;

    
    const instrumentSetting = getInstrumentSettingById(selectedInstrumentId);
    updateDefault({
      format: convertFormatNumber({decimalDigits: selectedCurrency?.decimalDigits ?? instrumentSetting.decimalDigits})
    });
  }, [selectedInstrumentId, selectedCurrency?.decimalDigits]);


  return (
    <BrowserRouter basename={getBaseName()}>
      <Routes>
        <Route path="/" element={<Home />} />
        {settings.accessibilities.enabled && (
          <Route path="/accessibility" element={<Accessibility />}>
            <Route index element={<AccessibleHome />} />
            <Route path="order-depth" element={<AccessibleOrderDepth />} />
            <Route path="share-details" element={<AccessibleShareDetails />} />
            <Route path="share-performance" element={<AccessibleSharePerformance />} />
            <Route path="*" element={<AccessibleHome />} />
          </Route>
        )}
        <Route path="/social" element={<Share />} />
        <Route path="/print-dialog" element={<PrintDialog />} />
        <Route path={ZoidPromodeModal.path} element={<ZoidPromodeModal.ComponentWrapper />} />
        <Route path="/promode-create-alert" element={<CreateAlertModal />} />
        <Route path="/press-release" element={<PressRelease />} />
        <Route path="/popout" element={<Popout />} />
        <Route path="/print" element={<Print />} />
        <Route path="/error-dialog" element={<ErrorDialog />} />
      </Routes>
    </BrowserRouter>
  );
}
