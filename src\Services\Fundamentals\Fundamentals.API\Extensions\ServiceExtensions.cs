using Euroland.NetCore.ToolsFramework.Localization;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.FileProviders.Physical;
using Microsoft.Extensions.Localization;

namespace Euroland.FlipIT.Fundamental.API.Extensions;

public static class ServiceExtensions
{
  public static void AddEurolandTranslation(this IServiceCollection services,
    IConfiguration configuration)
  {
    IFileInfo languageMapFileInfo = !string.IsNullOrEmpty(configuration["LanguageMapFilePath"])
      ? new PhysicalFileInfo(new FileInfo(configuration["LanguageMapFilePath"]))
      : new NotFoundFileInfo("LanguageMap.xml");

    // The order is important. Must add before any default localizations.
    services.AddEurolandJsonLocalization(cfg =>
    {
      cfg.DefaultCulture = new System.Globalization.CultureInfo("en-GB");
      cfg.ResourcePath = "Translations";

      if (languageMapFileInfo.Exists)
      {
        cfg.LanguageToCultureProvider = new CompabilityXmlLanguageToCultureProvider(languageMapFileInfo, null);
      }
    });

    services.AddTransient<ILanguageToCultureProvider>(sp =>
      (sp.GetRequiredService<IStringLocalizerFactory>() as JsonStringLocalizerFactory)?.LanguageToCultureProvider
    );
  }
}
