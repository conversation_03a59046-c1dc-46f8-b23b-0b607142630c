using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Euroland.FlipIT.SData.API.Infrastructure.Configurations;

public class DividendEvtConfiguration: IEntityTypeConfiguration<DividendEvt>
{
    public void Configure(EntityTypeBuilder<DividendEvt> builder)
    {
        builder.Property(o=>o.dDividend).HasColumnType("money");
    }
}