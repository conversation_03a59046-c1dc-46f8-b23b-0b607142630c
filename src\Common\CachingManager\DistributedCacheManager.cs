using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Euroland.FlipIT.Shared.CachingManager;

/// <summary>
/// Manages distributed cache operations, providing methods to get, set, and remove cache entries.
/// </summary>
/// <remarks>
/// This class implements the <see cref="IConfigurableCacheManager"/> interface and utilizes
/// <see cref="IDistributedCache"/> for caching operations. It supports both synchronous and asynchronous
/// methods for cache retrieval and manipulation. Default cache settings include a one-hour expiration
/// time span and an option to use a rolling interval.
/// </remarks>
public class DistributedCacheManager : IConfigurableCacheManager
{
  public TimeSpan DefaultTimeSpan { get; set; }
  public bool UseRollingIntervalAsDefault { get; set; }
  private readonly IDistributedCache _cache;

  private static readonly JsonSerializerSettings serializerSettings = new JsonSerializerSettings
  {
    DateTimeZoneHandling = DateTimeZoneHandling.RoundtripKind,
    ReferenceLoopHandling = ReferenceLoopHandling.Ignore
  };

  private const string ENTRY_SIZE_EXCEPTION_MESSAGE = "Unable to set entry size on distributed cache";

  /// <summary>
  /// Initializes a new instance of the <see cref="DistributedCacheManager"/> class.
  /// </summary>
  /// <param name="distributedCache">The distributed cache instance to be used for caching operations.</param>
  /// <remarks>
  /// Sets the default cache expiration time to 1 hour and disables the use of a rolling interval by default.
  /// </remarks>
  public DistributedCacheManager(
    IDistributedCache distributedCache)
  {
    _cache = distributedCache;
    DefaultTimeSpan = TimeSpan.FromHours(1);
    UseRollingIntervalAsDefault = false;
  }

  /// <inheritdoc/>
  public T GetCache<T, T2>(T2 key)
  {
    string value = _cache.GetString(key.ToString());

    if (value == null) return default(T);

    return JsonConvert.DeserializeObject<T>(value, serializerSettings);
  }

  /// <inheritdoc/>
  public async Task<T> GetCacheAsync<T, T2>(T2 key)
  {
    string value = await _cache.GetStringAsync(key.ToString());
    if (value == null) return default;

    return JsonConvert.DeserializeObject<T>(value, serializerSettings);
  }

  /// <inheritdoc/>
  public void RemoveCache<T>(params T[] keys)
  {
    foreach (var key in keys)
      _cache.Remove(key.ToString());
  }

  /// <inheritdoc/>
  public T SetCache<T, T2>(T obj, T2 key, TimeSpan? timeSpan = null, bool useRollingInterval = false, int? entrySize = null)
  {
    if (entrySize != null)
      throw new ArgumentException(ENTRY_SIZE_EXCEPTION_MESSAGE);

    _cache.SetStringAsync(
      key.ToString(),
      SerialiseObject(obj),
      GetCachingOption(
        useRollingInterval,
        timeSpan == null ? DefaultTimeSpan : (TimeSpan)timeSpan));

    return obj;
  }

  /// <inheritdoc/>
  public T SetCache<T, T2>(T obj, T2 key)
  {
    _cache.SetStringAsync(
      key.ToString(),
      SerialiseObject(obj),
        GetCachingOption(
          UseRollingIntervalAsDefault,
          DefaultTimeSpan));

    return obj;
  }

  private static DistributedCacheEntryOptions GetCachingOption(bool useRollingInterval, TimeSpan timeSpan)
  {
    var cacheEntryOptions = new DistributedCacheEntryOptions();

    if (useRollingInterval)
    {
      cacheEntryOptions.SetSlidingExpiration(timeSpan);
    }
    else
    {
      var expiration = DateTime.Now + timeSpan;
      cacheEntryOptions.SetAbsoluteExpiration(expiration);
    }

    return cacheEntryOptions;
  }

  private static string SerialiseObject<T>(T obj) =>
    JsonConvert.SerializeObject(obj, serializerSettings);

}
