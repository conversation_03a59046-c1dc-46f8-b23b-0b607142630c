using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Helpers;
using HotChocolate.Execution;
using HotChocolate.Language;
using HotChocolate.Resolvers;
using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Middleware;

/// <summary>
/// DateTime colum in some table of database is stored in CEST timezone.
/// Use this middleware to convert the DateTime filter input to CEST timezone for generating SQL query by entity framework core.
/// </summary>
public class ConvertDateTimeOffsetFilterInputToCestTimezoneMiddleware
{
  private readonly FieldDelegate _next;

  public ConvertDateTimeOffsetFilterInputToCestTimezoneMiddleware(FieldDelegate next)
  {
    _next = next;
  }

  public async Task InvokeAsync(IMiddlewareContext context)
  {
    var arguments = context.Selection.Arguments;
    foreach (var argument in arguments)
    {
      if (argument.Name == "where")
      {
        var objectFieldNodes = (List<ObjectFieldNode>?)argument.ValueLiteral?.Value;
        if (objectFieldNodes != null)
        {
          foreach (var objectFieldNode in objectFieldNodes.ToList())
          {
            if ((objectFieldNode.Name.Value.Equals("dateTime", StringComparison.InvariantCultureIgnoreCase) ||
                 objectFieldNode.Name.Value.Equals("date", StringComparison.InvariantCultureIgnoreCase))
                &&
                objectFieldNode.Value is ObjectValueNode dateTimeValueNode)
            {
              var dateTimeFields = dateTimeValueNode.Fields.ToList();

              foreach (var dateTimeField in dateTimeFields.ToList())
              {
                switch (dateTimeField.Value)
                {
                  case StringValueNode stringValueNode:
                    ProcessDateTimeField(stringValueNode.Value, dateTimeField, dateTimeFields);
                    break;
                  case VariableNode variableNode when
                    context.Variables.TryGetVariable<StringValueNode>(variableNode.Name.Value, out var variableValue) &&
                    variableValue?.Value != null:
                    ProcessDateTimeField(variableValue.Value, dateTimeField, dateTimeFields);
                    break;
                }
              }

              var newDateTimeNode =
                objectFieldNode.Name.Value.Equals("dateTime", StringComparison.InvariantCultureIgnoreCase)
                  ? new ObjectFieldNode("dateTime", new ObjectValueNode(dateTimeFields))
                  : new ObjectFieldNode("date", new ObjectValueNode(dateTimeFields));

              objectFieldNodes.Remove(objectFieldNode);
              objectFieldNodes.Add(newDateTimeNode);
            }
          }

          context.ReplaceArgument("where", new ArgumentValue(
            argument,
            argument.Kind ?? ValueKind.Object,
            argument.IsFullyCoerced,
            argument.IsDefaultValue,
            argument.Value,
            new ObjectValueNode(objectFieldNodes)
          ));
        }
      }
    }

    await _next(context);
  }

  private void ProcessDateTimeField(string dateTimeValue, ObjectFieldNode dateTimeField, List<ObjectFieldNode> dateTimeFields)
  {
    var dateTimeOffset = DateTimeOffset.Parse(dateTimeValue, CultureInfo.InvariantCulture, DateTimeStyles.RoundtripKind);
    var cestDateTime = dateTimeOffset.DateTimeOffsetToCest();
    dateTimeFields.Remove(dateTimeField);
    dateTimeFields.Add(new ObjectFieldNode(
      dateTimeField.Name.Value,
      new StringValueNode(cestDateTime.ToString("o"))
    ));
  }
}
