using Euroland.FlipIT.SData.API.GraphQL.Deprecated.DataLoaders;
using Euroland.FlipIT.SData.API.GraphQL.Deprecated.Instruments;
using Euroland.FlipIT.SData.API.GraphQL.Deprecated.Webcast;
using Euroland.FlipIT.SData.API.GraphQL.Deprecated.MarketDepth;
using Euroland.FlipIT.SData.API.Helpers;
using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using Euroland.FlipIT.SData.API.Infrastructure.Repositories;
using Euroland.FlipIT.SData.API.Services;
using Euroland.FlipIT.SData.API.Services.Interfaces;
using Euroland.NetCore.ToolsFramework.Localization;
using Euroland.FlipIT.SData.API.GraphQL.Interceptor;
using Euroland.FlipIT.SData.API.GraphQL.Types.CompanyName;
using Euroland.FlipIT.SData.API.GraphQL.Types.CustomerType;
using Euroland.FlipIT.SData.API.GraphQL.Types.Dividend;
using Euroland.FlipIT.SData.API.GraphQL.Types.FCEvent;
using Euroland.FlipIT.SData.API.GraphQL.Types.SubSector;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.SData.API.GraphQL.Middleware;
using Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;
using Euroland.FlipIT.SData.API.GraphQL.Types.CurrencyRate;

using HotChocolate.Data;
using HotChocolate.Diagnostics;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.FileProviders.Physical;
using System.IO;
using Microsoft.Extensions.Localization;
using Euroland.FlipIT.SData.API.GraphQL;
using HotChocolate.Types;
using System;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Euroland.FlipIT.SData.API
{
  public class Startup
  {
    public Startup(IConfiguration configuration, IHostEnvironment environment)
    {
      Configuration = configuration;
      Environment = environment;
    }

    public IConfiguration Configuration { get; }
    public IHostEnvironment Environment { get; }

    // This method gets called by the runtime. Use this method to add services to the container.
    public void ConfigureServices(IServiceCollection services)
    {
      services.AddHttpContextAccessor();
      services.TryAddScoped<IRealtimeDataStrategy, HttpRequestHeaderRealtimeDataStrategy>();

      IFileInfo languageMapFileInfo = !string.IsNullOrEmpty(Configuration["LanguageMapFilePath"])
        ? new PhysicalFileInfo(new FileInfo(Configuration["LanguageMapFilePath"]))
        : new NotFoundFileInfo("LanguageMap.xml");

      services.AddHealthChecks();
      services.RegisterDbContextFactories(Configuration);
      services.AddCurrencyService();
      services.AddCurrencyRateService(Configuration);

      // Service to get dynamically translation from db [shark].[Translation] table
      services.AddEurolandTranslationService(cfg => {
        cfg.ConnectionString = Configuration.GetConnectionString("SharkDb");
      });

      // The order is important. Must add before any default localizations.
      services.AddEurolandJsonLocalization(cfg =>
      {
        cfg.DefaultCulture = new System.Globalization.CultureInfo("en-GB");
        cfg.ResourcePath = "Translations";

        if(languageMapFileInfo.Exists) {
          cfg.LanguageToCultureProvider = new CompabilityXmlLanguageToCultureProvider(languageMapFileInfo, null);
        }
      });

      services.AddTransient<ILanguageToCultureProvider>(sp =>
        (sp.GetRequiredService<IStringLocalizerFactory>() as JsonStringLocalizerFactory)?.LanguageToCultureProvider
      );

      services
          .AddGraphQLServer()
          //.AddApolloTracing() // Use "GraphQL-Tracing=1" header to enable tracing
          .TryAddTypeInterceptor<PerformanceShareScopedDataContextInterceptor>()
          .TryAddTypeInterceptor<AdjustedCloseArgumentInterceptor>()
          .TryAddTypeInterceptor<ExchangeCurrencyValidationInterceptor>()
          .AddDiagnosticEventListener<ErrorLoggingExecutionEventListener>()
          .AddQueryType<QueryType>(c => c.Name(OperationTypeNames.Query))
            .AddCompanyType()
            .AddCompanyNameType()
            .AddInstrumentType()
            .AddTranslationType()
            .AddCurrencyType()
            .AddCurrencyRateType()
            .AddCurrencyRateHistoryType()
            .AddPressReleaseType()
            .AddAttachmentType()
            .AddTimezoneType()
            .AddCountryType()
            .AddCityType()
            .AddMarketType()
            .AddOrderDepthType()
            .AddCustomerTypeType()
            .AddDividendType()
            .AddFCEventType()
            .AddPerformanceShareType()
            .AddListType()
            .AddSubSectorType()
            .AddLanguageType()
            .AddMessageTypeType()
            .AddWebcastType()
#region Deprecated
          .AddTypeExtension<CurrencyQuery>()
          .AddTypeExtension<InstrumentQueries>()
          .AddTypeExtension<WebcastQueries>()
          .AddDataLoader<WebcastDataLoader>()
          .AddTypeExtension<MarketDepthQueries>()

          .RegisterDbContext<DefaultSharkDbContext>(kind: DbContextKind.Pooled)
          .RegisterDbContext<RealtimeSharkDbContext>(kind: DbContextKind.Pooled)
#endregion Deprecated
          .UseField<QueryContextMiddleware>()
          .RegisterRuntimeSharkDbContext()
          .RegisterDbContext<NewsContext>(kind: DbContextKind.Pooled)
          .RegisterDbContext<WebcastContext>(kind: DbContextKind.Pooled)
          .RegisterDbContext<TimescaleDbContext>(kind: DbContextKind.Pooled)

          .AddTypeConverter<DateTimeOffset, DateTime>(t => t.UtcDateTime)
          .AddTypeConverter<DateTime, DateTimeOffset>(
            t => t.Kind is DateTimeKind.Unspecified
              ? DateTime.SpecifyKind(t, DateTimeKind.Utc)
              : t
          )
          .AddProjections()
          .AddFiltering()
          .AddSorting()
          .AddInstrumentation(o => // add the activity instrumentation to our schema.
          {
            o.Scopes = ActivityScopes.All;
          })
          .PublishSchemaDefinition(c =>
            c.SetName("sdata")
          );

      // Define some important constants and the activity source
      // var serviceName = "CoreAPI.SData";
      // var serviceVersion = "1.0.0";

      // configure OpenTelemetry for our service
      // to quickly inspect our traces, we will use a Jaeger exported.
      /*
      services.AddOpenTelemetryTracing(i =>
      {
        i
              .AddSource(serviceName)
              .SetResourceBuilder(
                  ResourceBuilder.CreateDefault()
                      .AddService(serviceName: serviceName, serviceVersion: serviceVersion))
              .AddHttpClientInstrumentation()
              .AddAspNetCoreInstrumentation()
              .AddHotChocolateInstrumentation() // register the Hot Chocolate instrumentation events with OpenTelemetry.
              .AddSqlClientInstrumentation();

        if (Environment.IsDevelopment())
        {
          i.AddJaegerExporter();
          //.AddJaegerExporter(o =>
          //{
          //    o.AgentHost = "jaeger";
          //    o.AgentPort = 6831;
          //});
        }

      });
*/
      services.AddOptions<DistributedCachingConfiguration>()
              .Bind(Configuration.GetSection(nameof(DistributedCachingConfiguration)));

      services.AddInMemoryDistributedCachingManager();
      services.AddScoped<IGraphQLExecutionContext, GraphQLExecutionContext>();

      services.AddTransient<ICurrencyRateService, CurrencyRateService>();

#region Deprecated
      services.AddScoped<IUnitOfWork, UnitOfWork<DefaultSharkDbContext>>();
      services.AddTransient<IInstrumentService, InstrumentService>();
      services.AddTransient<IWebcastService, WebcastService>();
      services.AddTransient<IMarketDepthService, MarketDepthService>();
      services.AddControllers();
#endregion Deprecated
    }

    // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
      if (env.IsDevelopment())
      {
        app.UseDeveloperExceptionPage();
      }

      app.UseRouting();

      app.UseEndpoints(endpoints =>
      {
        endpoints.MapGraphQL();
        endpoints.MapHealthChecks("/health");
      });

      //app.UseGraphQLVoyager(path: "/graphql-ui");
    }
  }
}
