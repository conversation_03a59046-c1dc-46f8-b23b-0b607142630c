using HotChocolate.Types;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.List;

/// <summary>
/// Extends 'list' field to type <see cref="Instrument.InstrumentType"/>
/// </summary>
public class InstrumentTypeExtensions : ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(Instrument.InstrumentType.Name);

    descriptor.Field("list")
      .UseFirstOrDefault()
      .UseProjection()
      .AddCultureNameArgument()
      .ResolveWith<ListResolvers>(
        resolvers => resolvers.GetListByInstrument(default!, default!, default!)
      );
  }
}
