using HotChocolate.Data.Filters;
using HotChocolate.Resolvers;

namespace Euroland.FlipIT.Fundamental.API.Dtos.Filters;

public class TimeFilterInputType : FilterInputType<TimeFilterInput>
{
  protected override void Configure(IFilterInputTypeDescriptor<TimeFilterInput> descriptor)
  {
    descriptor.AllowAnd(false).AllowOr(false);
    descriptor.Field(x => x.Year).Type<TimeIntFieldFilterInputType>();
    descriptor.Field(x => x.Period).Type<TimeStringFieldFilterInputType>();
  }

  public static void SaveFilterContext(IResolverContext resolverContext)
  {
    var filterContextDictionary = resolverContext.GetFilterContext()?.ToDictionary();
    if (filterContextDictionary is not null)
    {
      if (filterContextDictionary.TryGetValue("year", out var year) &&
        year is Dictionary<string, object> yearDictionary)
      {
#pragma warning disable S1066 // Collapsible "if" statements should be merged
        if (yearDictionary.TryGetValue("in", out var yearIn) &&
          yearIn is List<int?> yearInList &&
          yearInList.Count > 0)
        {
          resolverContext.SetScopedState("year", yearInList);
        }
#pragma warning restore S1066 // Collapsible "if" statements should be merged
      }

      if (filterContextDictionary.TryGetValue("period", out var period) &&
        period is Dictionary<string, object> periodDictionary)
      {
#pragma warning disable S1066 // Collapsible "if" statements should be merged
        if (periodDictionary.TryGetValue("in", out var periodIn) &&
          periodIn is List<string> periodInList &&
          periodInList.Count > 0)
        {
          resolverContext.SetScopedState("period", periodInList);
        }
#pragma warning restore S1066 // Collapsible "if" statements should be merged
      }
    }
  }
}

public class TimeIntFieldFilterInputType : ComparableOperationFilterInputType<int>
{
  protected override void Configure(IFilterInputTypeDescriptor descriptor)
  {
    descriptor.AllowAnd(false).AllowOr(false);
    descriptor.Operation(DefaultFilterOperations.In).Type<ListType<IntType>>();
  }
}

public class TimeStringFieldFilterInputType : StringOperationFilterInputType
{
  protected override void Configure(IFilterInputTypeDescriptor descriptor)
  {
    descriptor.AllowAnd(false).AllowOr(false);
    descriptor.Operation(DefaultFilterOperations.In).Type<ListType<StringType>>();
  }
}
