USE [shark]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		HuyDQ
-- Create date: June 04 2024
-- Description:	Get close price from date by currency rate 
-- =============================================
CREATE FUNCTION [dbo].[fn_SGH3_getCloseInPeriodTimeByCurrencyRate] 
(
	@InstrumentId int,
	@CurrencyBase AS VARCHAR(3),
	@CurrencyQuote AS VARCHAR(3),
	@StartDate datetime,
	@EndDate datetime
)
RETURNS DECIMAL(18,4)
AS
BEGIN
	DECLARE @Result decimal(18,4)
	DECLARE @Close decimal(18,4)
	DECLARE @Date datetime

	SELECT TOP 1 
    @Close = [Close],
    @Date = [Date]
	FROM dbo.InstrumentHistory
	WHERE [InstrumentId] = @InstrumentId 
	AND [DATE] >= @StartDate
	AND [DATE] <= @EndDate
	ORDER BY [Date] ASC

	SET @Result = @Close * dbo.fn_shgGetRateForCurrencyExchange(@CurrencyBase, @CurrencyQuote, @Date)

	RETURN @Result
END
GO

