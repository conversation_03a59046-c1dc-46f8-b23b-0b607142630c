import { i18n } from "@euroland/libs";
import clsx from "clsx";
import { FC } from "react";
import { Login } from "../Common";

const Avatar: FC<{
  isLogin: boolean;
  username: string;
  onClick?: () => void;
  avatarUrl: string
}> = ({ isLogin, onClick, username, avatarUrl }) => {
  let content: React.ReactNode = <>
    <Login id="login-icon" />
    <span>{i18n.translate("login")}</span>
  </>

  if(isLogin) {
    if (avatarUrl) {
      content = <img className="block rounded-full h-full object-cover" src={avatarUrl} alt="avatar" />
    } else {
      content = username
    }
  }
  return (
    <div
      style={{ userSelect: "none" }}
      className={clsx({
        "wise-widget-login uppercase": isLogin,
        "wise-widget-initials": !isLogin,
      })}
      onClick={onClick}
    >
      {content}
    </div>
  );
};

export default Avatar;
