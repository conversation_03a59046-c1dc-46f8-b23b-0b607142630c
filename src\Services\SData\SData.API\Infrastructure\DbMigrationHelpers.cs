using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.Threading.Tasks;

namespace Euroland.FlipIT.SData.API.Infrastructure
{
  public static class DbMigrationHelpers
    {
        public static async Task ApplyDbMigrationsAsync(IHost host)
        {
            using (var serviceScope = host.Services.CreateScope())
            {
                var services = serviceScope.ServiceProvider;

                await EnsureDatabasesMigratedAsync(services);
            }
        }

        public static async Task EnsureDatabasesMigratedAsync(IServiceProvider services)
        {
            using (var scope = services.GetRequiredService<IServiceScopeFactory>().CreateScope())
            {
                using (var context = scope.ServiceProvider.GetRequiredService<DefaultSharkDbContext>())
                {
                    await context.Database.MigrateAsync();
                }
            }
        }
    }
}
