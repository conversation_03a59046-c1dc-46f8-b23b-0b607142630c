using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Currency;

/// <summary>
/// Contains deprecated fields to be compatible with previous release.
/// </summary>
public class CurrencyTypeDeprecatedExtensions: ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name("Currency");

    descriptor.Field("currencies")
      .Deprecated()
      .ResolveWith<Deprecated.Instruments.CurrencyQueries>(p => p.GetCurrenciesAsync(default));

    descriptor.Field("currenciesByCodes")
      .Argument("codes", cfg => cfg.Type<NonNullType<ListType<StringType>>>())
      .Deprecated()
      .ResolveWith<Deprecated.Instruments.CurrencyQueries>(p => p.GetCurrenciesByCodesAsync(default, default));

    descriptor.Field("currentRate")
      .Argument("baseCurrency", cfg => cfg.Type<NonNullType<StringType>>())
      .Argument("quoteCurrency", cfg => cfg.Type<NonNullType<StringType>>())
      .Argument("isRT", cfg => cfg.Type<NonNullType<BooleanType>>().DefaultValue(false))
      .Deprecated()
      .ResolveWith<Deprecated.Instruments.CurrencyQueries>(p => p.GetCurrentRate(default, default, default, default));

    descriptor.Field("currentRates")
      .Argument("baseCurrency", cfg => cfg.Type<NonNullType<StringType>>())
      .Argument("quoteCurrency", cfg => cfg.Type<NonNullType<StringType>>())
      .Argument("isRT", cfg => cfg.Type<NonNullType<BooleanType>>().DefaultValue(false))
      .Deprecated()
      .ResolveWith<Deprecated.Instruments.CurrencyQueries>(p => p.GetCurrentRates(default, default, default, default));

    descriptor.Field("historicalRates")
      .Argument("baseCurrency", cfg => cfg.Type<NonNullType<StringType>>())
      .Argument("quoteCurrency", cfg => cfg.Type<NonNullType<StringType>>())
      .Argument("fromDateUTC", cfg => cfg.Type<NonNullType<DateTimeType>>())
      .Argument("toDateUTC", cfg => cfg.Type<DateTimeType>())
      .Argument("isRT", cfg => cfg.Type<NonNullType<BooleanType>>().DefaultValue(false))
      .Deprecated()
      .ResolveWith<Deprecated.Instruments.CurrencyQueries>(p => p.GetHistoricalRates(default, default, default, default, default, default));
  }
}
