using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Serilog;

var builder = WebApplication.CreateBuilder(args);
builder.Host.ConfigureAppConfiguration((hostingContext, config) => {
    config.Sources.Clear();
    var env = hostingContext.HostingEnvironment;
    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
        .AddJsonFile("currencyTransformations.json", optional: true, reloadOnChange: true)
        .AddJsonFile($"appsettings.{env.EnvironmentName}.json", optional: true)
        .AddEnvironmentVariables();

    if(args != null) {
        config.AddCommandLine(args);
    }
});

var logger = new LoggerConfiguration()
          .WriteTo.Console()
          .ReadFrom.Configuration(builder.Configuration)
          .CreateLogger();
var startup = new Euroland.FlipIT.SData.API.Startup(builder.Configuration, builder.Environment);

startup.ConfigureServices(builder.Services);

//builder.Logging.ClearProviders();
builder.Logging.AddSerilog(logger);

var app = builder.Build();
startup.Configure(app, app.Environment);

app.Run();
