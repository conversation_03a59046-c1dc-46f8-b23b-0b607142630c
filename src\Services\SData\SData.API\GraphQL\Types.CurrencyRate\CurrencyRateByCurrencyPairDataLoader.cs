using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using Euroland.FlipIT.SData.API.Infrastructure.Factories;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.Shared.CachingManager;
using GreenDonut;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.CurrencyRate;

public class CurrencyRateByCurrencyPairDataLoader : BatchDataLoader<string, CurrencyRateDto>
{
  private readonly ISharkDbContextAbstractFactory _factory;
  private readonly IGraphQLExecutionContext _queryRequestContext;
  private readonly IConfigurableCacheManager _cache;

  public CurrencyRateByCurrencyPairDataLoader(
    ISharkDbContextAbstractFactory factory,
    IGraphQLExecutionContext queryRequestContext,
    IConfigurableCacheManager configurableCacheManager,
    IBatchScheduler batchScheduler,
    DataLoaderOptions? options = null)
    : base(batchScheduler, options)
  {
    _factory = factory ?? throw new ArgumentNullException(nameof(factory));
    _queryRequestContext = queryRequestContext ?? throw new ArgumentNullException(nameof(queryRequestContext));
    _cache = configurableCacheManager ?? throw new ArgumentNullException(nameof(configurableCacheManager));
  }

  protected override async Task<IReadOnlyDictionary<string, CurrencyRateDto>> LoadBatchAsync(IReadOnlyList<string> keys, CancellationToken cancellationToken)
  {
    var dtoFromCache = new List<CurrencyRateDto>();
    var needToFetchNew = new List<string>();

    foreach (var k in keys.Distinct())
    {
      if (string.IsNullOrEmpty(k) || k.Length != 6)
      {
        continue;
      }

      var dto = _cache.GetCache<CurrencyRateDto, string>(
        CacheKeyHelper.GenerateKeyByPropOfDtoObject<CurrencyRateDto, string>(k, cr => cr.Pair)
      );

      if (dto != null)
      {
        dtoFromCache.Add(dto);
      }
      else if (IsSameCurrency(k))
      {
        dtoFromCache.Add(new CurrencyRateDto
        {
          Date = DateTime.UtcNow,
          Id = -keys.GetHashCode(),
          Pair = k,
          Value = 1m
        });
      }
      else
      {
        needToFetchNew.Add(k);
      }
    }

    await using var sharkDbContext = await _factory.CreateDbContextAsync(_queryRequestContext.UseRealtime, _queryRequestContext.UseCloud);

    var data = needToFetchNew.Count > 0
      ? await sharkDbContext
        .CurrencyRates
        .AsNoTracking()
        .Where(c => needToFetchNew.Contains(c.Currencies))
        .WithAutoMapper()
        .ToProjection<CurrencyRateDto>()
        .ToListAsync(cancellationToken)
      : Enumerable.Empty<CurrencyRateDto>();

    foreach (var currencyRate in data)
    {
      _cache.SetCache(
        currencyRate,
        CacheKeyHelper.GenerateKeyByPropOfDtoObject<CurrencyRateDto, string>(currencyRate.Pair, cr => cr.Pair)
      );

      dtoFromCache.Add(currencyRate);
    }

    return dtoFromCache.ToDictionary(cr => cr.Pair.ToLower()!);
  }

  private static bool IsSameCurrency(string currencyPair)
  {
    return !string.IsNullOrEmpty(currencyPair)
      && currencyPair.Length == 6
      && currencyPair.Substring(0, 3).ToLower() == currencyPair.Substring(3).ToLower();
  }
}
