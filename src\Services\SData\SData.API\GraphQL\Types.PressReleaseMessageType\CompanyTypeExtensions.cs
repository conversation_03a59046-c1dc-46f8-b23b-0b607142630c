using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.PressReleaseMessageType;


public class CompanyTypeExtensions: ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(Company.CompanyType.Name);

    descriptor.Field("pressReleaseMessageTypes")
      .Description("Get all available types of announcements of a company.")
      .Argument("groupByMessageType", cfg =>
        cfg.Type<BooleanType>()
          .DefaultValue(false)
          .Description("If false, The message types will show up instead of the groups. Default to false.")
      )
      .Argument("languageId", cfg =>
        cfg.Type<NonNullType<IntType>>()
          .DefaultValue(32)
          .Description("ID of language. Default 32 for English.")
      )
      .Argument("sourceId", cfg =>
        cfg.Type<IntType>()
          .DefaultValue(null)
          .Description("ID of data source where to get announcement. Default null.")
      )
      .Argument("from", cfg =>
        cfg.Type<DateType>()
          .DefaultValue(null)
      )
      .Argument("to", cfg =>
        cfg.Type<DateType>()
          .DefaultValue(null)
      )
      .UseProjection()
      .ResolveWith<MessageTypeResolvers>(
        resolvers => resolvers.GetMessageTypeByCompany(
          default!,
          default!,
          default!,
          default,
          default,
          default,
          default,
          default,
          default
        )
      );
  }
}
