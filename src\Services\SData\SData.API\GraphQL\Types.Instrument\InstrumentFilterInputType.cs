using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Data.Filters;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;

public class InstrumentFilterInputType: FilterInputType<InstrumentDto>
{
  protected override void Configure(IFilterInputTypeDescriptor<InstrumentDto> descriptor)
  {
    descriptor.BindFieldsExplicitly();

    descriptor.Field(p => p.Id);
    descriptor.Field(p => p.InstrumentType);
    descriptor.Field(p => p.Market<PERSON>);
  }
}
