using System;
using System.Collections.Generic;

namespace Euroland.FlipIT.SData.API.Dto;

public class OrderDepthDto: IDtoObject
{
  public int InstrumentId { get; set; }
  public DateTime RowUpdated { get; set; }

  /// <summary>
  /// Gets list of <see cref="MarketOrderDto"/>.
  /// </summary>
  /// <typeparam name="MarketOrderDto"></typeparam>
  /// <returns></returns>
  private List<MarketDepthDto>? _marketDepths;

  public List<MarketDepthDto> MarketDepths
  {
    get { return _marketDepths ??= new List<MarketDepthDto>(); }
  }
}
