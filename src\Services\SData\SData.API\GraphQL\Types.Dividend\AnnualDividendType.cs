using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Dividend;

public class AnnualDividendYieldType: ObjectType<AnnualDividendDto>
{
  public const string TypeName = "AnnualDividend";
  protected override void Configure(IObjectTypeDescriptor<AnnualDividendDto> descriptor)
  {
    descriptor.Name(TypeName);
    descriptor.Field(p => p.InstrumentId).IsProjected();
    descriptor.Field(p => p.Currency)
      .Description("Original dividend currency");
    descriptor.Field(p => p.ExchangeCurrency)
      .Description("Exchange currency (if provided) that price is converted from original dividend currency into this");

    descriptor.Field("instrument")
      .ResolveWith<DividendResolvers>(p => p.InstrumentByAnnualDividend(default!, default!, default!, default));
  }
}
