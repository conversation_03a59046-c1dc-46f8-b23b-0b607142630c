using System;

namespace Euroland.FlipIT.Shared.CachingManager;

/// <summary>
/// Interface for managing caching operations.
/// </summary>
/// <typeparam name="T">The type of the cached object.</typeparam>
/// <typeparam name="T2">The type of the key used for caching.</typeparam>
/// <remarks>
/// Provides methods to get, set, and remove cache entries asynchronously and synchronously.
/// </remarks>
public interface IConfigurableCacheManager : ICachingManager
{
  /// <summary>
  /// Adds or updates an object in the cache with the specified key and optional settings.
  /// </summary>
  /// <typeparam name="T">The type of the object to cache.</typeparam>
  /// <typeparam name="T2">The type of the key used to identify the cache entry.</typeparam>
  /// <param name="obj">The object to be cached.</param>
  /// <param name="key">The key associated with the cached object.</param>
  /// <param name="timeSpan">Optional expiration time for the cache entry. If null, a default expiration is 1 hour.</param>
  /// <param name="useRollingInterval">Indicates whether to use a rolling expiration interval.</param>
  /// <param name="entrySize">Optional size of the cache entry, used for managing cache capacity.</param>
  /// <returns>The cached object.</returns>
  T SetCache<T, T2>(T obj, T2 key, TimeSpan? timeSpan = null, bool useRollingInterval = false, int? entrySize = null);
}
