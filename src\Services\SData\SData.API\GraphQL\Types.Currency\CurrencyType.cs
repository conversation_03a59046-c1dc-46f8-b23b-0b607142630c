using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Types;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Currency;

public class CurrencyType: ObjectType<CurrencyDto>
{
  protected override void Configure(IObjectTypeDescriptor<CurrencyDto> descriptor)
  {
    descriptor.Name($"Currency");
    descriptor.Field(f => f.Code).IsProjected(true);

    descriptor.TranslationField();
  }
}
