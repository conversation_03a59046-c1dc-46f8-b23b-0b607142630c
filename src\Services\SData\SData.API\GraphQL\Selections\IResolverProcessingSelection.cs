using System;
using System.Reflection;
using HotChocolate.Data.Projections.Context;

namespace Euroland.FlipIT.SData.API.GraphQL.Selections;

public interface IResolverProcessingSelection
{
  ISelectedField SelectedField { get; }
  public Type RuntimeType { get; }
  MemberInfo? ClassMemberInfo { get; }
  string SelectionName { get; }
  string SelectionMemberName { get; }
  /// <summary>
  /// Select the MemberName if possible otherwise retrieve the SelectionName
  /// because technically the underlying IFieldSelection.Member is a nullable field.
  /// </summary>
  string SelectionMemberNameOrDefault { get; }
}
