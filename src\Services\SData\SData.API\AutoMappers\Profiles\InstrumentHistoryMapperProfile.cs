using AutoMapper;

using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure.Entities.Shark;
using Euroland.FlipIT.SData.API.Infrastructure.Entities.Timescale;

namespace Euroland.FlipIT.SData.API.AutoMapper.Profiles;

public class InstrumentHistoryMapperProfile: Profile
{
  public InstrumentHistoryMapperProfile()
  {
    CreateMap<InstrumentHistory, InstrumentHistoryDto>();
    CreateMap<AdjustedInstrumentHistory, InstrumentHistoryDto>();
    CreateMap<InstrumentHistoryTimescale, InstrumentHistoryDto>();
  }
}
