import React from 'react';
import { useAuth } from './useAuth';
import {OidcAuthContextProps} from './OidcAuthContext';

/**
 * Higher-order component to access the imperative API
 * @param {import('react').ComponentType} Component
 * @returns {import('react').ComponentType}
 */
export const withAuth = (Component: React.ComponentType<{
  auth: OidcAuthContextProps
}>) => {
     
    const Comp = (props: any) => {
        const auth = useAuth();

        return <Component {...props} auth={auth} />;
    };

    Comp.displayName = `withAuth(${Component.displayName || Component.name})`;

    return Comp;
};
