﻿using AISearch.Models;
using Euroland.FlipIT.WiseSearch.Common;
using Euroland.FlipIT.WiseSearch.Models;
using Euroland.FlipIT.WiseSearch.Models.Authentication;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.Extensions.Options;
using System.Net;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using static Euroland.FlipIT.WiseSearch.Models.AppSettings;

namespace Euroland.FlipIT.WiseSearch.Middlewares;

public class ApiKeyAuthenticationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ApiKeyAuthenticationMiddleware> _logger;
    private readonly List<ApiKeyAuthOption> _authOptions;

    public ApiKeyAuthenticationMiddleware(
        RequestDelegate next,
        ILogger<ApiKeyAuthenticationMiddleware> logger,
        IOptions<AppSettings> appSettings)
    {
        _next = next;
        _logger = logger;
        _authOptions = appSettings.Value.ApiKeyAuthentication;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var requestHeaders = context.Request.Headers;
        var requestInfo = APIKeyAuthenticationContextHelper.FromHeaders(requestHeaders);

        var client = _authOptions.FirstOrDefault(x => x.ApiKey == requestInfo.APIKey);
        if (client == null)
        {
            await _next(context);
            return;
        }

        if (requestInfo.HasMissingRequiredFields(out var missingFields))
        {
            await WriteErrorResponse(context, HttpStatusCode.BadRequest, $"Missing required headers: {string.Join(", ", missingFields)}");
            return;
        }

        if (!ValidateTimestamp(requestInfo.Timestamp, client.AllowedTimeSkewSeconds, out var errorMsg))
        {
            await WriteErrorResponse(context, HttpStatusCode.RequestTimeout, errorMsg);
            return;
        }

        if (!ValidateSignature(requestInfo, client.Secret, out var computedSig))
        {
            _logger.LogWarning("Invalid signature. Computed: {Computed}, Provided: {Provided}", computedSig, requestInfo.Signature);
            await WriteErrorResponse(context, HttpStatusCode.Forbidden, "Invalid signature");
            return;
        }

        // Attach claims for Mobile client
        var claims = new List<Claim>
        {
            new("user_name", requestInfo.Username),
            new("auth_type", "mobile")
        };

        context.User = new ClaimsPrincipal(new ClaimsIdentity(claims, "MobileAuth"));
        context.Items["MobileUser"] = requestInfo;

        await _next(context);
    }

    private static bool ValidateTimestamp(string timestamp, int allowedSkewSeconds, out string error)
    {
        error = string.Empty;

        if (!long.TryParse(timestamp, out var requestTime))
        {
            error = "Invalid timestamp format";
            return false;
        }

        var currentEpoch = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        var delta = Math.Abs(currentEpoch - requestTime);

        if (delta > allowedSkewSeconds)
        {
            error = "Request expired";
            return false;
        }

        return true;
    }

    private static bool ValidateSignature(APIKeyAuthenticationContext request, string secret, out string computedSig)
    {
        var dataToSign = $"{request.Username}:{request.Timestamp}";

        using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secret));
        computedSig = Convert.ToBase64String(hmac.ComputeHash(Encoding.UTF8.GetBytes(dataToSign)));

        return computedSig == request.Signature;
    }

    private static async Task WriteErrorResponse(HttpContext context, HttpStatusCode originalStatusCode, string message)
    {
        var response = new GlobalResponseModel
        {
            RequestUrl = context.Request.GetDisplayUrl(),
            IsSuccess = false,
            HttpStatusCode = (int)originalStatusCode,
            Error = originalStatusCode.ToString(),
            Message = message
        };

        context.Response.StatusCode = (int)HttpStatusCode.OK;
        context.Response.ContentType = "application/json";
        await JsonSerializer.SerializeAsync(context.Response.Body, response);
    }
}
