﻿using System.Collections.Generic;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities.Webcast
{
    public class WebcastHost
    {
        public const string AzureBlob = "AzureBlob";
        public const string Vimeo = "Vimeo";

        public int Id { get; set; }
        public string Name { get; set; } = null!;

        public virtual ICollection<Webcast> Webcasts { get; set; } = new List<Webcast>();
        public virtual ICollection<WebcastUrl> WebcastUrls { get; set; } = new List<WebcastUrl>();
    }
}
