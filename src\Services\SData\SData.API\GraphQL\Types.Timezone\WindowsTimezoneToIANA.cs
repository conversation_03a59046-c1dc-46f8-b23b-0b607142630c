using System.Linq;
using System.Collections.Generic;
using System.Globalization;
using System;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Timezone;

/// <summary>
/// Provides functionality to convert Windows timezone names to IANA timezone names.
/// </summary>
public static class WindowsTimezoneToIANA
{
  static List<RegionInfo> regions;
  const string GOLDEN_ZONE_CODE = "001";

  /// <summary>
  /// Converts a Windows timezone name to its corresponding IANA timezone identifier.
  /// </summary>
  /// <param name="windowsTimezoneName">The name of the Windows timezone to convert.</param>
  /// <param name="englishCountryName">The English name of the country to assist in conversion, if applicable.</param>
  /// <param name="countryCode">Two letter ISO region name of the provided country.</param>
  /// <returns>The IANA timezone identifier if conversion is successful; otherwise, null.</returns>
  /// <exception cref="ArgumentNullException">Thrown when the <paramref name="windowsTimezoneName"/> is null or empty.</exception>
  public static string? Convert(string windowsTimezoneName, string? englishCountryName, string? countryCode)
  {
    if (string.IsNullOrEmpty(windowsTimezoneName))
    {
      throw new ArgumentNullException(nameof(windowsTimezoneName));
    }

    if (regions == null && !string.IsNullOrEmpty(englishCountryName))
    {
      regions = CultureInfo.GetCultures(
        CultureTypes.SpecificCultures
      )
      .Select(x => new RegionInfo(x.Name))
      .ToList();
    }
    var countryCodeISO3166 = !string.IsNullOrEmpty(countryCode) ? countryCode : GOLDEN_ZONE_CODE;

#pragma warning disable S2486 // Generic exceptions should not be ignored
    if (string.IsNullOrEmpty(countryCode) && !string.IsNullOrEmpty(englishCountryName) && regions != null)
    {
      try
      {
        var englishRegion = regions.FirstOrDefault(
          region => region.EnglishName.Contains(englishCountryName, System.StringComparison.OrdinalIgnoreCase)
        );

        if (englishRegion != null)
        {
          countryCodeISO3166 = englishRegion.TwoLetterISORegionName;
        }
      }
      catch { }
    }
#pragma warning restore S2486 // Generic exceptions should not be ignored

    return TimeZoneConverter.TZConvert.WindowsToIana(windowsTimezoneName, countryCodeISO3166);
  }
}
