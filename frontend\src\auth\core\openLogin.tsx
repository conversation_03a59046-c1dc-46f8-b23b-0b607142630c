import { User } from "oidc-client-ts";
import { OpenLoginProps } from "./interfaces.ts";

/**
 * Opens a login component and returns a promise that resolves on successful login
 * or rejects on login failure.
 *
 * @param {OpenLoginProps} props - The properties for opening the login component.
 * @param {React.ComponentType} props.LoginComponent - The login component to be rendered.
 * @returns {Promise<void>} A promise that resolves on successful login or rejects on login failure.
 *
 * @example
 * openLogin({ LoginComponent })
 *   .then(() => {
 *     console.log('Login successful');
 *   })
 *   .catch((error) => {
 *     console.error('Login failed', error);
 *   });
 */
export function openLogin({ LoginComponent }: OpenLoginProps) {
  if (!LoginComponent) {
    Promise.reject('Integration LoginComponent is required');
  }

  const renderPromise = new Promise<User>((resolve, reject) => {
    const isIFrame = window.top !== window.self;
    const onError = (error: Error) => {
      reject(error);
    };

     
    const componentInstance = (LoginComponent as any)({
      onLoginSuccess: resolve,
      onLoginFail: onError
    });

    componentInstance.event.on(window.euroland.EVENT.ERROR, (error: Error) => {
      // Something went wrong in login window
      reject(error);
      return;
    });

    if (isIFrame) {
      componentInstance.renderTo(window.parent, window.xprops.layout.middle, 'popup');
    } else {
      let middle = document.getElementById('middleLayout');
      if (!middle) {
        middle = document.createElement('div');
        middle.id = 'middleLayout';
        document.body.appendChild(middle);
      }

      componentInstance.renderTo(window.parent, '#middleLayout', 'popup');
    }
  });

  // Check if component can render to parent window, otherwise, host window is blocking
  // popup. In this case, we should has a warning message out to the screen.
  const renderableCheckPromise = LoginComponent.canRenderTo(window.parent);

  return Promise.all([renderableCheckPromise, renderPromise]).then((results) => results[1]);
}
