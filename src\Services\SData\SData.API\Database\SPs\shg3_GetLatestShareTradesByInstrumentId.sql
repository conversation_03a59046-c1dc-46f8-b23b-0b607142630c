/*----------------------------------------------------------------------------------*/
/* Created [AnhNT] [Feb 20 2024] : Create a new SP based on dbo.shg3_GetLatestShareTradesByInstrumentId store (reference to store shg_GetLatestShareTradesByIsinAndMarket) */
/*----------------------------------------------------------------------------------*/

USE [shark]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [dbo].[shg3_GetLatestShareTradesByInstrumentId]
	-- Add the parameters for the stored procedure here
@InstrumentId INT,
@COUNT int = 10
AS
BEGIN
	SELECT TOP (@COUNT)
				hDate [Date],
				hClose [Close],
				COALESCE(hSize,0) [Size]				
	FROM dbo.daily_history daily
	WHERE InstrumentId =  @InstrumentId
	ORDER BY hDate DESC
	
END