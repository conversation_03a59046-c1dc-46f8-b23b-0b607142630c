using Euroland.FlipIT.Fundamental.API.Persistance.FinCalender;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Euroland.FlipIT.Fundamental.API.Persistance.Configurations;

public class FinancialEventTypeDetailConfiguration : IEntityTypeConfiguration<FinancialEventTypeDetail>
{
  public void Configure(EntityTypeBuilder<FinancialEventTypeDetail> builder)
  {
    builder.HasKey(fetd => new { fetd.FinancialEventTypeId, fetd.LanguageId });
  }
}
