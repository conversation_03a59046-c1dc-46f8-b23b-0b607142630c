using System;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

[Keyless]
public class IceTradesHistory_RT
{
    public DateTime Date { get; set; }
    [Column(TypeName = "decimal(18,4)")]
    public decimal Open { get; set; }
    [Column(TypeName = "decimal(18,4)")]
    public decimal High { get; set; }
    [Column(TypeName = "decimal(18,4)")]
    public decimal Low { get; set; }
    [Column(TypeName = "decimal(18,4)")]
    public decimal Close { get; set; }
    [Column("Volume", TypeName = "bigint")]
    public long? Size { get; set; }
}