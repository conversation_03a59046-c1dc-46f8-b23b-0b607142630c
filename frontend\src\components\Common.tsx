import { FC } from "react";
import { FontAwesomeIcon, FontAwesomeIconProps } from "@fortawesome/react-fontawesome";
import {
  faBell,
  faBellSlash,
  faRobot,
  faXmark,
  faTrash,
  faChevronDown,
  faArrowTrendUp,
  faChartLine,
  faArrowTrendDown,
  faUpRightFromSquare,
  faUser,
  faCircleInfo,
  faRightFromBracket,
  faCalculator,
  faGaugeHigh,
  faLaptop,
  faGlobe,
  faChess,
  faHandHoldingDollar,
} from "@fortawesome/free-solid-svg-icons";

export const SearchIcon: FC<{className?: string}> = ({className = ""}) => {
  return (
    <svg
  data-name="Layer 1"
  id="Layer_1"
  viewBox="0 0 32 32"
  xmlns="http://www.w3.org/2000/svg"
  className={className}
>
  <path
    fill="white"
    d="M21.7406,17.02355a.485.485,0,0,0-.08227-.02136l-.007-.00537-.064-.04352a1.42145,1.42145,0,0,0-.16736-.08655,3.03133,3.03133,0,0,0-.48169-.15985A7.71413,7.71413,0,0,1,17.74957,15.144,11.45622,11.45622,0,0,1,14.003,7.96954a11.83659,11.83659,0,0,1-.14953-1.20331l-.027-.30493-.01361-.15277L13.806,6.23205,13.8042,6.213a.74808.74808,0,0,0-.02832-.16236.663.663,0,0,0-1.2251-.12573.74154.74154,0,0,0-.08044.29675l-.00672.07019c-.05651.00367-.06341.00428-.00006.0008l-.00055.00555-.01458.1532-.02912.30682A15.59152,15.59152,0,0,1,12.00952,9.188a12.263,12.263,0,0,1-1.9721,4.29437,8.50086,8.50086,0,0,1-3.16993,2.62958l-.6358.25025c-.30835.10986-.73444.19354-.9646.29248l-.79816.28039-.38812.13642c-.06561.03479-.03107.053-.05133.08l-.04291.14594-.02069.07055a.71852.71852,0,0,0-.00409.08765.12673.12673,0,0,0,.00763.05408l.07678.2586a4.96659,4.96659,0,0,0,.54383.3664,8.00334,8.00334,0,0,1,1.91791.475l.28057.10906a1.54321,1.54321,0,0,1,.22687.10639l.481.24841A8.75756,8.75756,0,0,1,9.51788,20.714a12.27308,12.27308,0,0,1,2.71662,5.67475,16.66048,16.66048,0,0,1,.25921,1.7547l.044.44061.00549.05493a.74735.74735,0,0,0,.02307.14295.58986.58986,0,0,0,1.11561.07763.66164.66164,0,0,0,.04888-.213l.00232-.02747.01862-.21936.07446-.87091a15.79543,15.79543,0,0,1,.73786-3.20617,10.47,10.47,0,0,1,2.4469-4.00592,8.044,8.044,0,0,1,3.25317-2.00043c.82928-.27387,1.31128-.29975,1.3952-.38751.0503-.03577-.04339-.185.0575-.18311a.35641.35641,0,0,0,.28247-.40942.38463.38463,0,0,0-.1117-.21985A.41507.41507,0,0,0,21.7406,17.02355Z"
  />
  <path
    fill="white"
    d="M27.899,8.73254a.38338.38338,0,0,0-.21955-.17786c-.1156-.02576-.03747-.01794-.0661-.0285l-.02343-.03675a.45369.45369,0,0,0-.06165-.069.576.576,0,0,0-.17841-.10968,4.52358,4.52358,0,0,1-.57745-.201,3.165,3.165,0,0,1-1.16235-.95582,4.03659,4.03659,0,0,1-.277-.41217L25.20416,6.512l-.15692-.33875a4.98155,4.98155,0,0,1-.19671-.64233c-.05884-.24231-.12909-.42383-.1792-.73206q-.08148-.4269-.16449-.86133c-.01239-.09722-.031-.08929-.048-.10717l-.05194-.04566-.10455-.09155-.10138-.08624-.05451-.024-.10052-.0426-.04657-.01874-.01172-.00433c-.00732-.00251-.04407-.00019-.03418.00055l-.0235.00555c-.01385.00006-.09442.02484-.17407.04791a1.72278,1.72278,0,0,0-.20544.15143.56831.56831,0,0,0-.11249.09894l-.127.353a7.48636,7.48636,0,0,1-.41589,1.91363c-.12866.29309-.1477.32678-.20563.43726l-.16656.28412a3.40451,3.40451,0,0,1-.81445.89306.8207.8207,0,0,1-.11322.08039l-.1789.11816a3.65131,3.65131,0,0,1-.731.25366,3.0034,3.0034,0,0,0-.36248.11725l-.31655.12336c-.16467.09344-.28489.20849-.25242.25811l-.061.1684-.007.07037a.72551.72551,0,0,0,.008.1568l.05438.1441.022.05683.0127.03277.08459.0531c.12043.07459.26319.16309.43476.26947.26263.05848.51758.14588.83642.23493a1.23147,1.23147,0,0,1,.39191.19183l.15625.10028a3.66039,3.66039,0,0,1,.65247.62451,4.09263,4.09263,0,0,1,.30182.43695,3.68913,3.68913,0,0,1,.34552.70942c.27807.70691.35191,2.06729.73529,2.21856a.27712.27712,0,0,0,.2547.1557c.14515,0,.40147.0704.59393-.84448.17064-.8112.233-1.241.3772-1.70911.1029-.24621.14361-.31622.1842-.407l.14569-.25458a3.38383,3.38383,0,0,1,.5553-.69641,5.38175,5.38175,0,0,1,1.7926-.966c.01825-.0304-.04059-.18646.04431-.17889A.32311.32311,0,0,0,27.899,8.73254Z"
  />
</svg>

  );
};

export type ForwardFontAwesomeIconProps = Omit<FontAwesomeIconProps, "icon">;

export const Bell: FC<{ id: string; onClick?: () => void }> = ({
  id,
  onClick,
}) => {
  return (
    <FontAwesomeIcon
      icon={faBell}
      id={id}
      onClick={onClick}
      style={{ pointerEvents: "none" }}
    />
  );
};

export const BellSlash: FC<{ id: string; onClick: () => void }> = ({
  id,
  onClick,
}) => {
  return (
    <FontAwesomeIcon
      icon={faBellSlash}
      id={id}
      onClick={onClick}
      style={{ pointerEvents: "none" }}
    />
  );
};

export const NotificationToggle: FC<{
  id: string;
  value: boolean;
  onToggle: (toggleValue: boolean) => void;
  buttonProps?: React.ButtonHTMLAttributes<HTMLButtonElement>;
  svgProps?: Partial<React.ComponentProps<typeof FontAwesomeIcon>>;
}> = ({ id, value, onToggle, buttonProps = {}, svgProps = {} }) => {
  return (
    <button {...buttonProps} onClick={() => onToggle(!value)}>
      <FontAwesomeIcon
        icon={value ? faBell : faBellSlash}
        id={id}
        style={{ pointerEvents: "none", ...svgProps.style }}
        {...svgProps}
      />
    </button>
  );
};

export const Robot: FC = () => {
  return <FontAwesomeIcon icon={faRobot} />;
};

export const CalculatorIcon: FC = () => {
  return <FontAwesomeIcon icon={faCalculator} />;
};

export const GaugeHighIcon: FC = () => {
  return <FontAwesomeIcon icon={faGaugeHigh} />;
};

export const LaptopIcon: FC = () => {
  return <FontAwesomeIcon icon={faLaptop} />;
};

export const GlobeIcon: FC = () => {
  return <FontAwesomeIcon icon={faGlobe} />;
};

export const ChessIcon: FC = () => {
  return <FontAwesomeIcon icon={faChess} />;
};

export const HoldingDollarIcon: FC = () => {
  return <FontAwesomeIcon icon={faHandHoldingDollar} />;
};

export const XMark: FC<{ className?: string; onClick?: () => void }> = ({
  className = "",
  onClick,
}) => {
  return (
    <FontAwesomeIcon icon={faXmark} className={className} onClick={onClick} />
  );
};

export const TrashCan: FC = () => {
  return <FontAwesomeIcon icon={faTrash} />;
};

export const ChevronDown: FC = () => {
  return <FontAwesomeIcon icon={faChevronDown} />;
};

export const ArrowTrendUp: FC<{ className?: string }> = ({ className = "" }) => {
  return <FontAwesomeIcon icon={faArrowTrendUp} className={className} />;
};

export const ChartLine: FC<{ className?: string }> = ({ className = "" }) => {
  return <FontAwesomeIcon icon={faChartLine} className={className} />;
};

export const ArrowTrendDown: FC<{ className?: string }> = ({
  className = "",
}) => {
  return <FontAwesomeIcon icon={faArrowTrendDown} className={className} />;
};

export const Trash: FC<ForwardFontAwesomeIconProps> = (props) => {
  return <FontAwesomeIcon icon={faTrash} {...props} />;
};

export const ExternalLink: FC<ForwardFontAwesomeIconProps> = (props) => {
  const classes = `lucide lucide-external-link ${props.className || ""}`;
  return (
    <FontAwesomeIcon
      icon={faUpRightFromSquare}
      {...props}
      className={classes}
    />
  );
};

export const UserIcon: FC<ForwardFontAwesomeIconProps> = (props) => {
  const classes = `lucide lucide-user text-gray-600 ${props.className || ""}`;
  return <FontAwesomeIcon icon={faUser} {...props} className={classes} />;
};

export const Info: FC<ForwardFontAwesomeIconProps> = (props) => {
  const classes = `lucide lucide-info ${props.className || ""}`;
  return <FontAwesomeIcon icon={faCircleInfo} {...props} className={classes} />;
};

export const LogOut: FC<ForwardFontAwesomeIconProps> = (props) => {
  const classes = `lucide lucide-log-out ${props.className || ""}`;
  return <FontAwesomeIcon icon={faRightFromBracket} {...props} className={classes} />;
};

export const Login: FC<ForwardFontAwesomeIconProps> = (props) => {
  const classes = `lucide lucide-log-in ${props.className || ""}`;
  return <FontAwesomeIcon icon={faRightFromBracket} {...props} className={classes} />;
};


