using System;

namespace Euroland.FlipIT.SData.API.Helpers
{
    public static class LangHelpers
    {
        public static (string LangCode, string TwoLetterOfLang, int LangId, string ListTR) GetLang(string langCode)
        {
            langCode = langCode.Trim().ToLower();

            var tuple = ("en-US", "EN", 32, "LIST");

            switch (langCode)
            {
                case "en-us":
                    tuple = ("en-US", "EN", 32, "LIST");
                    break;
                case "fr-fr":
                    tuple = ("fr-FR", "FR", 65, "French");
                    break;
                case "ar-sa":
                    tuple = ("ar-SA", "AR", 1, "Arabic");
                    break;
                case "ar-ae":
                    tuple = ("ar-AE", "AR", 1, "Arabic");
                    break;
                case "fi-fi":
                    tuple = ("fi-FI", "FI", 64, "Finnish");
                    break;
                case "sv-se":
                    tuple = ("sv-SE", "SV", 92, "Swedish");
                    break;
                case "de-de":
                    tuple = ("de-DE", "DE", 26, "German");
                    break;
                case "es-mx":
                    tuple = ("es-MX", "ES", 45, "Spanish");
                    break;
                case "it-it":
                    tuple = ("it-IT", "IT", 74, "Italian");
                    break;
                case "nl-nl":
                    tuple = ("nl-NL", "NL", 78, "Dutch");
                    break;
                case "ru-ru":
                    tuple = ("ru-RU", "RU", 86, "Russian");
                    break;
                case "pl-pl":
                    tuple = ("pl-PL", "PL", 82, "Polish");
                    break;
                case "zh-tw":
                    tuple = ("zh-TW", "TW", 19, "Taiwanese");
                    break;
                case "zh-cn":
                    tuple = ("zh-CN", "CN", 20, "Chinese");
                    break;
                case "ko-kr":
                    tuple = ("ko-KR", "KR", 77, "korean");
                    break;
                case "da-dk":
                    tuple = ("da-DK", "DK", 25, "Danish");
                    break;
                case "is-is":
                    tuple = ("is-IS", "IE", 73, "Icelandic");
                    break;
                case "vi-vn":
                    tuple = ("vi-VN", "VI", 105, "Vietnamese");
                    break;
                case "ja-jp":
                    tuple = ("ja-JP", "JP", 76, "Japanese");
                    break;
                case "pt-pt":
                    tuple = ("pt-PT", "PT", 83, "Portuguese");
                    break;
                case "et-ee":
                  tuple = ("et-EE", "ET", 101, "Estonian");
                  break;
                case "pt-br":
                  tuple = ("pt-BR", "PT", 84, "Portuguese");
                  break;
                case "he-il":
                  tuple = ("he-IL", "HE", 71, "Hebrew");
                  break;
                case "ku-arab":
                  tuple = ("ku-Arab", "KU", 135, "Kurdish");
                  break;
                case "ca-es":
                  tuple = ("ca-ES", "CA", 18, "Catalan");
                  break;
                case "ro-ro":
                  tuple = ("ro-RO", "RO", 85, "Romanian");
                  break;
                case "lt-lt":
                  tuple = ("lt-LT", "LT", 103, "Lithuanian");
                  break;
                case "el-gr":
                  tuple = ("el-GR", "EL", 31, "Greek");
                  break;
            }

            return tuple;
        }

        public static bool IsCulture(this string currentCulture, string langCode)
        {
            currentCulture = currentCulture.Trim();
            return string.Equals(currentCulture, langCode, StringComparison.OrdinalIgnoreCase);
        }
    }
}
