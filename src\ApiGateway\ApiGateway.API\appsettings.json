{"Serilog": {"Using": ["Serilog.Sinks.Syslog"], "MinimumLevel": "Warning", "WriteTo": [{"Name": "UdpSyslog", "Args": {"host": "*************", "port": "514", "appName": "FlipIT.ApiGateway", "restrictedToMinimumLevel": "Warning", "outputTemplate": "[{Timestamp:HH:mm:ss}] {Message:lj}{NewLine}{Exception}"}}], "Properties": {"Application": "FlipIT.ApiGateway"}}, "AllowedHosts": "*", "ForwaredHeaders": ["X-Db-Connection-Kind", "X-Db-RTData"], "WellKnownSchemas": [{"IsRootType": true, "Name": "sdata", "RemoteUrl": "http://localhost/tools/sdata-api/graphql"}, {"Name": "fundamental", "RemoteUrl": "http://localhost/tools/fundamental-api/graphql"}]}