.switcher {
  &__wrapper {
    display: flex;
    margin-bottom: pxToRem(27);
    justify-content: space-between;
    &--currency {
      display: flex;
      align-items: center;
      gap: pxToRem(23);
      margin-left: auto;
      @include onRTL{
        margin-left: unset;
        margin-right: auto;
      }
    }
  }
  &__ticker {
    margin-left: auto;
  }
}

.tooltip.right {
  left: auto;
  right: 0;
  z-index: 99;

  &:after {
    left: auto;
    right: pxToRem(10);
  }
}

@include onTextRevert {
  .tooltip.right {
    right: auto;
    left: 0;

    &:after {
      right: auto;
      left: pxToRem(10);
    }
  }
}

.switcher {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  &__btn {
    background: $color-white;
    box-shadow: pxToRem(0) pxToRem(0) pxToRem(4) rgba(0, 0, 0, 0.25);
    border-radius: pxToRem(2);
    border: 0;
    width: pxToRem(36);
    height: pxToRem(36);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--secondary-color);
  }

  &__item {
    + * {
      margin-left: pxToRem(14);
    }

    .btn {
      font-weight: 700;
      color: var(--secondary-color);
      padding-left: 0;
      padding-right: 0;
    }

    &.active {
      .switcher__btn {
        background: var(--secondary-color);
        color: $color-white;
      }

      .switcher__item-title {
        color: var(--primary-color);
      }
    }


  }

  &--tab {
    justify-content: flex-start;
    margin-bottom: pxToRem(43);
    white-space: nowrap;
    overflow-x: auto;
    position: relative;

    @include media-breakpoint-down(sm) {
      // justify-content: space-between;
      margin: 0;
      // gap: pxToRem(15);
    }

    &::before {
      content: '';
      border-bottom: pxToRem(1) $color-gray-light solid;
      width: 100%;
      position: absolute;
      bottom: pxToRem(1);
      left: 0;
      right: 0;
    }


    // @include hideScrollbar;

    .switcher {
      padding: 0;
      &__item {
        // min-width: pxToRem(100);
        position: relative;
        padding-bottom: pxToRem(6);
        &:not(:first-child) {
          margin-left: pxToRem(30);
          @include media-breakpoint-down(sm) {
            margin-left: pxToRem(15);
          }
        }

        &-title {
        display: block;
        color: $color-gray-dark-800;
        background: $color-white;
        font-size: pxToRem(20);
        border: none;
        font-weight: 400;
        @include media-breakpoint-down(lg) {
          font-size: pxToRem(18);
          width: 100%;
        }
        @include media-breakpoint-down(sm) {
          font-size: pxToRem(16);
          flex: 1;
        }

        &:hover {
          background: $color-white;
          border: none;
        }
      }

        &.active .switcher__item-title {
          color: $color-blue-400;
          background: $color-white;
          &:hover {
            color: $color-blue-400;
            background: $color-white;
          }

          &::before {
            content: '';
            background: $color-blue-400;
            height: pxToRem(3);
            width: 100%;
            position: absolute;
            bottom: 0;
            right: 0;
            left: 0;
            border-radius: pxToRem(3);
            @include onMobile {
              height: pxToRem(2);
              bottom: pxToRem(1);
            }
          }
        }
      }


    }

    &::after {
      display: none;
    }
  }

  @include media-breakpoint-down(xl) {
    .tooltip {
      left: auto;
      right: 0;

      &:after {
        left: auto;
        right: pxToRem(10);
      }
    }
  }

  @include media-breakpoint-down(sm) {
    &__item-title {
      font-size: pxToRem(20);
    }
  }
  .switcher-title {
    font-size: pxToRem(24);
    font-weight: 700;
    color: var(--secondary-color);
  }

  @include media-breakpoint-down(sm) {
    .switcher-title {
      font-size: pxToRem(20);
    }
  }

  .switcher-title-active {
    color: var(--primary-color);
  }
}

.tab-contents {
  opacity: 0;
  animation: fadeIn ease-in 1;
  animation-fill-mode: forwards;
  animation-duration: 1s;
}
@include onTextRevert {
  .switcher {
    &__ticker {
      margin-right: auto;
      margin-left: unset;
      .switcher__item {
        + * {
          margin-left: unset;
        }
      }
    }
    &--tab {
      .switcher {
        &__item {
          + * {
            margin-right: pxToRem(20);
            margin-left: unset;
            @include media-breakpoint-down(sm) {
              margin-right: pxToRem(15);
            }
          }
        }
      }
      .active {
        &:after {
          left: unset;
          right: 0;
        }
      }
    }
  }
}


