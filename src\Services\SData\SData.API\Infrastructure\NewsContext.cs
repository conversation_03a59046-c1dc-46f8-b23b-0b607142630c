﻿using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;
namespace Euroland.FlipIT.SData.API.Infrastructure
{
  public class NewsContext : DbContext
  {
    public NewsContext(DbContextOptions<NewsContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
      modelBuilder.Entity<PressRelease>()
          .<PERSON><PERSON><PERSON>(i => new { i.Id, i.LanguageId });

      modelBuilder.Entity<Attachment>()
          .<PERSON><PERSON><PERSON>(a => new { a.Id, a.PressreleaseId, a.LanguageId });

      modelBuilder.Entity<PressRelease>()
          .HasMany(p => p.Attachment)
          .WithOne(a => a.PressRelease)
          .HasForeignKey(f => new { f.PressreleaseId, f.LanguageId });

      modelBuilder.Entity<MessageType>()
        .ToView(nameof(MessageType));
    }

    public virtual DbSet<PressRelease> PressReleases { get; set; }
    public virtual DbSet<Attachment> Attachment { get; set; }
    public virtual DbSet<MessageType> MessageTypes { get; set; }

  }
}

