﻿using Euroland.FlipIT.WiseSearch.Infrastructure.Repositories;

namespace Euroland.FlipIT.WiseSearch.Infrastructure.UnitOfWorks
{
    public interface IWiseSearchUoW : IUnitOfWorkBase<WiseSearchDbContext>
    {
        IWsSearchHistoryRepository WsSearchHistoryRepository { get; }
        IWsSettingRepository WsSettingRepository { get; }
        IWsIngestRepository WsIngestRepository { get; }
    }

    public class WiseSearchUoW(WiseSearchDbContext wiseSearchDbContext,
                          IWsSearchHistoryRepository wsSearchHistoryRepository,
                          IWsSettingRepository wsSettingRepository,
                          IWsIngestRepository wsIngestRepository) : UnitOfWorkBase<WiseSearchDbContext>(wiseSearchDbContext), IWiseSearchUoW

    {

        public IWsSearchHistoryRepository WsSearchHistoryRepository => wsSearchHistoryRepository;

        public IWsSettingRepository WsSettingRepository => wsSettingRepository;

        public IWsIngestRepository WsIngestRepository => wsIngestRepository;
    }
}
