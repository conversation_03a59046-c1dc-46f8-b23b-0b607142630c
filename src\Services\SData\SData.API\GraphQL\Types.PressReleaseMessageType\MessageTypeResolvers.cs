using System;
using System.Linq;
using System.Threading;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure;
using HotChocolate;
using HotChocolate.Resolvers;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.PressReleaseMessageType;

public class MessageTypeResolvers
{
  public IQueryable<MessageTypeDto> GetMessageTypeByCompany(
    [Parent] CompanyDto company,
    NewsContext newsDbContext,
    IResolverContext resolverContext,
    bool groupByMessageType = false,
    int languageId = 32,
    int? sourceId = null,
    DateTime? from = null,
    DateTime? to = null,
    CancellationToken cancellationToken = default)
  {
    var ungroupGroup = !groupByMessageType;

    var query = newsDbContext.MessageTypes.FromSqlRaw($@"
      SELECT
        t.*
      FROM
        [MessageType] t
      INNER JOIN (
        SELECT DISTINCT
          CASE
            WHEN t.mtMessageGroup IS NULL THEN t.mtID
            ELSE g.mtID
          END AS TypeId
          ,CASE
            WHEN t.mtMessageGroup IS NULL THEN t.mtSourceID
            ELSE g.mtSourceID
          END AS SourceId
          ,p.prCompanyCode AS CompanyCode
          ,p.prLanguageID AS LanguageId
        FROM Pressreleases AS p
        INNER JOIN MessageType AS t
          ON t.mtID = p.prMessageTypeId
            AND t.mtSourceID = p.prSourceID
        LEFT JOIN MessageType AS g
          ON g.mtMessageGroup = t.mtMessageGroup
            AND g.mtSourceID = t.mtSourceID
            AND g.mtMessageGroupType IS NULL
        WHERE p.prCompanyCode = @companyCode
          AND p.prLanguageID = @languageId
          AND (@ungroupGroup = 1 OR (@ungroupGroup = 0 AND t.mtMessageGroup IS NOT NULL))
          AND (@sourceId IS NULL OR p.prSourceID = @sourceId)
          AND (@from IS NULL OR CONVERT(DATE, p.prDate) >= CONVERT(DATE, @from))
          AND (@to IS NULL OR CONVERT(DATE, p.prDate) <= CONVERT(DATE, @to))
      ) AS mt
      ON t.mtID = mt.TypeId AND t.mtSourceID = mt.SourceId
    ",
    new SqlParameter("ungroupGroup", System.Data.SqlDbType.Bit) { Value = ungroupGroup },
    new SqlParameter("companyCode", System.Data.SqlDbType.VarChar, 12) { Value = company.Code.Trim()},
    new SqlParameter("languageId", System.Data.SqlDbType.Int) { Value = languageId},
    new SqlParameter("sourceId", System.Data.SqlDbType.Int) { Value = sourceId != null ? sourceId.Value : DBNull.Value },
    new SqlParameter("from", System.Data.SqlDbType.SmallDateTime) { Value = from != null ? from.Value : DBNull.Value },
    new SqlParameter("to", System.Data.SqlDbType.SmallDateTime) { Value = to != null ? to.Value : DBNull.Value }
    );

    return query.WithAutoMapper().ToProjection<MessageTypeDto>();
  }
}
