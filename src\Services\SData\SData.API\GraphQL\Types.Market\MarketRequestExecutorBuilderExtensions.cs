using Euroland.FlipIT.SData.API.GraphQL.Types.Market;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class MarketRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddMarketType(this IRequestExecutorBuilder builder)
  {
    builder.AddType<MarketType>()
      .AddType<MarketStatusType>()
      .AddTypeExtension<CompanyTypeExtensions>()
      .AddTypeExtension<InstrumentTypeExtensions>()
      .AddTypeExtension<EventTypeExtensions>();

    builder.AddDataLoader<MarketByIdDataLoader>();
    builder.AddDataLoader<MarketStatusByMarketIdDataLoader>();

    return builder;
  }
}
