import {XProps} from '@/zoid';
import { useAuth } from '../core';
import {AuthPros} from '../core/interfaces';
import { i18n } from "@euroland/libs";

const isIframe = window.self !== window.top;

/**
 * LoginCallbackComponent is a React functional component that handles the login callback process.
 * It uses the `useAuth` hook to get the authentication status and user information.
 *
 * If the user is authenticated and `window.xprops` is defined, it checks for the presence of
 * `onLoginSuccess` and `close` functions in `window.xprops` and calls them accordingly.
 *
 * @returns A JSX element that displays the authentication status.
 */
export const LoginCallbackComponent = () => {
  const auth = useAuth();
  const xprops = window.xprops as unknown as XProps<AuthPros>

  // If the page is not loaded in an iframe and not opened by zoid component,
  // we assume it's being accessed directly from an email verification link after registration
  if(!isIframe && !('xprops' in window)) {
    return <>
      <div className="p-4 text-center">
        <h2 className="text-xl font-bold mb-2">{i18n.translate('login.callback.title')}</h2>
        <p className="mb-4">{i18n.translate('login.callback.verified')}</p>
        <p>{i18n.translate('login.callback.close')}</p>
        <p className="mt-4 text-sm text-gray-600">{i18n.translate('login.callback.hint')}</p>
      </div>
    </>
  }
  if (auth.isAuthenticated &&xprops) {
    if(typeof(xprops.onLoginSuccess) === 'function') {
     if (auth.user) xprops.onLoginSuccess(auth.user);
    }
    if(typeof(xprops.close) === 'function') {
     xprops.close();
    }
  }


  return null
  // return (
  //   <>
  //     <div>{auth.isAuthenticated ? 'authenticated!' : 'unauthenticated!'}</div>
  //   </>
  // );
};
