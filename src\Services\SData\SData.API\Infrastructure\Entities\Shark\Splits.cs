﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
  public class Splits
    {
        [Key]
        public int ID { get; set; }
        public DateTime EffectiveDate { get; set; }
        public string ISIN { get; set; }
        public string Ratio { get; set; }
        public byte Market { get; set; }
        public DateTime? InsertedTime { get; set; }
        public int SplitType { get; set; }
    }
}
