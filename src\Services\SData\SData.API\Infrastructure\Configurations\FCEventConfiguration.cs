using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Euroland.FlipIT.SData.API.Infrastructure.Configurations;

public class FCEventConfiguration: IEntityTypeConfiguration<FCEvent>
{
  private const string ViewName = "CS_FCEvents";

  public void Configure(EntityTypeBuilder<FCEvent> builder)
  {
    builder.ToView(ViewName);
  }
}
