using Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class InstrumentRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddInstrumentType(this IRequestExecutorBuilder builder)
  {
    builder
      .AddType<InstrumentType>()
      .AddType<InstrumentHistoryType>()
      .AddType<InstrumentPriceType>()
      .AddType<InstrumentType2Type>();

    builder.AddTypeExtension<CompanyTypeExtensions>();

    builder.AddDataLoader<InstrumentByIdDataLoader>();
    builder.AddDataLoader<InstrumentPriceByIdDataLoader>();

    return builder;
  }
}
