using Euroland.FlipIT.SData.API.GraphQL.Types.PerformanceShare;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class PerformanceShareExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddPerformanceShareType(this IRequestExecutorBuilder builder)
  {
    builder.AddType<PerformancePeriodType>()
      .AddType<PerformanceShareType>()
      .AddTypeExtension<InstrumentTypeExtensions>();

    builder.AddDataLoader<PerformanceShareBatchDataLoader>();
    builder.AddDataLoader<PerformanceShareGroupDataLoader>();

    return builder;
  }
}
