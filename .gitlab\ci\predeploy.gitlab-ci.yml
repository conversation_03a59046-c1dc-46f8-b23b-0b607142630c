.shared-scripts:
  script:
    - ECHO PreDeploy job...

package-qa:
  tags:
    - vietnam-dev-shell
  extends:
    - .prevent-fetching
    - .disable-artifact-passing
    - .default-retry
    - .shared-scripts
  stage: pre:deploy
  only:
    refs:
      - develop

package-staging:
  tags:
    - ee-buildtest-shell
  extends:
    - .prevent-fetching
    - .disable-artifact-passing
    - .default-retry
    - .shared-scripts
  stage: pre:deploy
  only:
    refs:
      - next

package-prod:
  tags:
    - ee-buildtest-shell
  extends:
    - .prevent-fetching
    - .disable-artifact-passing
    - .default-retry
    - .shared-scripts
  stage: pre:deploy
  only:
    refs:
      - master

