using Microsoft.EntityFrameworkCore;
using Euroland.FlipIT.WiseSearch.Infrastructure.Entities.Shark;

namespace Euroland.FlipIT.WiseSearch.Infrastructure.Repositories
{
  public interface ISharkCompanySettingsLanguageRepository : IRepositoryBase<SharkDbContext, CompanySettingsLanguage, int>
  {
    Task<CompanySettingsLanguage?> GetByCompanyCodeAsync(string companyCode);
    Task<Dictionary<string, string>?> GetNameByCompanyCodesAsync(List<string> companyCodes, string language);
  }

  public class SharkCompanySettingsLanguageRepository : RepositoryBase<SharkDbContext, CompanySettingsLanguage, int>, ISharkCompanySettingsLanguageRepository
  {
    public SharkCompanySettingsLanguageRepository(SharkDbContext context) : base(context)
    {

    }

    public async Task<CompanySettingsLanguage?> GetByCompanyCodeAsync(string companyCode)
    {
      var query = await _dbContext.CompanySettingLanguages.FirstOrDefaultAsync(s => s.CompanyCode == companyCode);
      return query;
    }

    public async Task<Dictionary<string, string>?> GetNameByCompanyCodesAsync(List<string> companyCodes, string language)
    {
      var query = _dbContext.CompanySettingLanguages
                            .AsNoTracking()
                            .Where(s => companyCodes.Contains(s.CompanyCode) && s.Language == language);

      var queryResult = await query.ToDictionaryAsync(s => s.CompanyCode, s => s.Name);
      return queryResult;
    }
  }
}
