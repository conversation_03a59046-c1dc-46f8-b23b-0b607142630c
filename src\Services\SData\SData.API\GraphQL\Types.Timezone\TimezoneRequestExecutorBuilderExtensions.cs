using Euroland.FlipIT.SData.API.GraphQL.Types.Timezone;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class TimezoneRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddTimezoneType(this IRequestExecutorBuilder builder)
  {
    builder.AddType<TimezoneType>();
    //builder.AddTypeExtension<MarketTypeExtensions>();
    builder.AddTypeExtension<EventTypeExtensions>();

    builder.AddDataLoader<TimezoneByIdDataLoader>();
    builder.AddDataLoader<TimezoneByNameDataLoader>();
    return builder;
  }
}
