﻿using AutoMapper;
using Euroland.FlipIT.SData.API.DTOs.Deprecated;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Mappers.Deprecated.Profiles;
using System.Linq;

namespace Euroland.FlipIT.SData.API.Mappers.Deprecated
{
    public static class DividendMappers
    {
        internal static IMapper Mapper { get; }
        static DividendMappers()
        {
            Mapper = new MapperConfiguration(cfg => {
                cfg.AddProfile<DividendMapperProfile>();
            }).CreateMapper();
        }

        public static IQueryable<DividendEvent> ToModel(this IQueryable<Dividend> evt)
        {
            return Mapper.ProjectTo<DividendEvent>(evt);
        }
    }
}
