﻿using Euroland.FlipIT.SData.API.DTOs.Deprecated;
using Euroland.FlipIT.SData.API.Helpers;
using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Infrastructure.Factories;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Services.Interfaces;

namespace Euroland.FlipIT.SData.API.Services
{
    public interface IMarketDepthService: IAsyncDisposable
    {
        Task<OrderDepth> GetOrderDepth(int instrumentId, string? toCurrency, bool isRT = false);
    }

    public class MarketDepthService : IMarketDepthService
    {
        private readonly ISharkDbContext _localSharkDbContext;
        private readonly ICurrencyRateService _currencyRateService;

        public MarketDepthService(ISharkDbContextFactory sharkDbContextFactory, ICurrencyRateService currencyRateService)
        {
            _currencyRateService = currencyRateService;
            _localSharkDbContext = sharkDbContextFactory?.CreateDbContext(false) ?? throw new ArgumentNullException(nameof(sharkDbContextFactory));
        }

        public ValueTask DisposeAsync()
        {
            return _localSharkDbContext.DisposeAsync();
        }

        public async Task<OrderDepth> GetOrderDepth(int instrumentId, string? toCurrency, bool isRT = false)
        {
                var orderDepth = new OrderDepth();

                var marketDepth = _localSharkDbContext.MarketDepth
                    .AsNoTracking()
                    .FirstOrDefault(s => s.InstrumentId == instrumentId);

                if (marketDepth != null)
                {
                    var fromCurrency = _localSharkDbContext.Instrument
                        .AsNoTracking()
                        .FirstOrDefault(s => s.InstrumentId == instrumentId)
                        ?.CurrencyCode;

                    var currencyRate = 1m;
                    if (!string.IsNullOrEmpty(fromCurrency) && !string.IsNullOrEmpty(toCurrency))
                    {
                        currencyRate = await _currencyRateService.GetClosestRateAsync(fromCurrency, toCurrency, marketDepth.FiRowUpdated, isRT);
                    }

                    orderDepth.RowUpdated = marketDepth.FiRowUpdated.ToKindUtc();

                    List<MarketOrder> lstMarketOrder = new List<MarketOrder>();

                    var askPrices = new[]
                    {
                        marketDepth.Fid_Best_Ask_1, marketDepth.Fid_Best_Ask_2, marketDepth.Fid_Best_Ask_3,
                        marketDepth.Fid_Best_Ask_4, marketDepth.Fid_Best_Ask_5, marketDepth.Fid_Best_Ask_6,
                        marketDepth.Fid_Best_Ask_7, marketDepth.Fid_Best_Ask_8, marketDepth.Fid_Best_Ask_9,
                        marketDepth.Fid_Best_Ask_10
                    };

                    var askSizes = new[]
                    {
                        marketDepth.Fid_Best_Ask_1_Size, marketDepth.Fid_Best_Ask_2_Size, marketDepth.Fid_Best_Ask_3_Size,
                        marketDepth.Fid_Best_Ask_4_Size, marketDepth.Fid_Best_Ask_5_Size, marketDepth.Fid_Best_Ask_6_Size,
                        marketDepth.Fid_Best_Ask_7_Size, marketDepth.Fid_Best_Ask_8_Size, marketDepth.Fid_Best_Ask_9_Size,
                        marketDepth.Fid_Best_Ask_10_Size
                    };

                    var bidPrices = new[]
                    {
                        marketDepth.Fid_Best_Bid_1, marketDepth.Fid_Best_Bid_2, marketDepth.Fid_Best_Bid_3,
                        marketDepth.Fid_Best_Bid_4, marketDepth.Fid_Best_Bid_5, marketDepth.Fid_Best_Bid_6,
                        marketDepth.Fid_Best_Bid_7, marketDepth.Fid_Best_Bid_8, marketDepth.Fid_Best_Bid_9,
                        marketDepth.Fid_Best_Bid_10
                    };

                    var bidSizes = new[]
                    {
                        marketDepth.Fid_Best_Bid_1_Size, marketDepth.Fid_Best_Bid_2_Size, marketDepth.Fid_Best_Bid_3_Size,
                        marketDepth.Fid_Best_Bid_4_Size, marketDepth.Fid_Best_Bid_5_Size, marketDepth.Fid_Best_Bid_6_Size,
                        marketDepth.Fid_Best_Bid_7_Size, marketDepth.Fid_Best_Bid_8_Size, marketDepth.Fid_Best_Bid_9_Size,
                        marketDepth.Fid_Best_Bid_10_Size
                    };

                    for (var i = 0; i < 10; i++)
                    {
                        if (askPrices[i].HasValue && askPrices[i].Value > 0 &&
                            askSizes[i].HasValue && askSizes[i].Value > 0 &&
                            bidPrices[i].HasValue && bidPrices[i].Value > 0 &&
                            bidSizes[i].HasValue && bidSizes[i].Value > 0)
                        {
                            lstMarketOrder.Add(new MarketOrder()
                            {
                                BuyPrice = bidPrices[i].Value * currencyRate,
                                BuyVolume = bidSizes[i].Value,
                                SellPrice = askPrices[i].Value * currencyRate,
                                SellVolume = askSizes[i].Value
                            });
                        }
                    }

                    orderDepth.MarketDepth = lstMarketOrder;
                }

                return orderDepth;
        }
    }
}
