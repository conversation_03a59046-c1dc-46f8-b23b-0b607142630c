using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Dividend;

public class DividendType: ObjectType<DividendDto>
{
  public const string TypeName = "Dividend";
  protected override void Configure(IObjectTypeDescriptor<DividendDto> descriptor)
  {
    descriptor.Name(TypeName).Description("Dividend per share");
    descriptor.Field(p => p.InstrumentId).IsProjected();
    descriptor.Field(p => p.Currency)
      .Description("Original dividend currency");
    descriptor.Field(p => p.ExchangeCurrency)
      .Description("Exchange currency (if provided) that price is converted from original dividend currency into this");

    descriptor.Field("instrument")
      .ResolveWith<DividendResolvers>(p => p.InstrumentByDividendPerShare(default!, default!, default!, default));
  }
}
