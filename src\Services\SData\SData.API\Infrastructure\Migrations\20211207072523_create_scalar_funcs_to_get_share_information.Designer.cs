﻿// <auto-generated />
using System;
using Euroland.FlipIT.SData.API.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace Euroland.FlipIT.SData.API.Infrastructure.Migrations
{
    [DbContext(typeof(DefaultSharkDbContext))]
    [Migration("20211207072523_create_scalar_funcs_to_get_share_information")]
    partial class create_scalar_funcs_to_get_share_information
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("Relational:MaxIdentifierLength", 128)
                .HasAnnotation("ProductVersion", "5.0.11")
                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Instrument", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime?>("Agentupdate")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("AllTimeHigh")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("AllTimeLow")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("BetaFactor")
                        .HasColumnType("decimal(18,2)");

                    b.Property<double?>("BookValueOfShare")
                        .HasColumnType("float");

                    b.Property<decimal?>("Change52W")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("52wChange");

                    b.Property<int?>("CompanyID")
                        .HasColumnType("int");

                    b.Property<decimal?>("Correlation")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CurrencyCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte?>("Customer")
                        .HasColumnType("tinyint");

                    b.Property<decimal?>("DPS")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("DataSourceID")
                        .HasColumnType("int");

                    b.Property<decimal?>("EPS")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("EurCat")
                        .HasColumnType("int");

                    b.Property<decimal?>("FiveYearsChange")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("5YearsChange");

                    b.Property<decimal?>("High52W")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("52wHigh");

                    b.Property<decimal?>("HighYTD")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("Highest52w")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("52Highest");

                    b.Property<string>("ISIN")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("InstrumentType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("LastRowChange")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ListID")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ListedFrom")
                        .HasColumnType("datetime2");

                    b.Property<int?>("LotSize")
                        .HasColumnType("int");

                    b.Property<decimal?>("Low52W")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("52wLow");

                    b.Property<decimal?>("LowYTD")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("Lowest52w")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("52Lowest");

                    b.Property<bool?>("MainIndex")
                        .HasColumnType("bit");

                    b.Property<int?>("MarCat")
                        .HasColumnType("int");

                    b.Property<decimal?>("MarketCapEUR")
                        .HasColumnType("decimal(18,2)");

                    b.Property<short>("MarketID")
                        .HasColumnType("smallint");

                    b.Property<string>("MarketSpecSign")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Month")
                        .HasColumnType("decimal(18,2)");

                    b.Property<double?>("NetInComeGrowth")
                        .HasColumnType("float");

                    b.Property<decimal?>("NetIncome")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("NoShares")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("NumSharesDate")
                        .HasColumnType("datetime2");

                    b.Property<long?>("NumberOfUnlistedShares")
                        .HasColumnType("bigint");

                    b.Property<double?>("PayoutRatio")
                        .HasColumnType("float");

                    b.Property<decimal?>("PrevMid")
                        .HasColumnType("decimal(18,2)");

                    b.Property<byte?>("PrimaryMarket")
                        .HasColumnType("tinyint");

                    b.Property<int?>("RegionID")
                        .HasColumnType("int");

                    b.Property<string>("RicCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("SPS")
                        .HasColumnType("float");

                    b.Property<string>("ShareName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Name");

                    b.Property<double?>("SplitRatio")
                        .HasColumnType("float");

                    b.Property<decimal?>("ThreeMonthChange")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("3MonthChange");

                    b.Property<decimal?>("ThreeMonthHigh")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("3MonthHigh");

                    b.Property<decimal?>("ThreeMonthLow")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("3MonthLow");

                    b.Property<string>("Ticker")
                        .HasColumnType("nvarchar(max)");

                    b.Property<short?>("TokyoEurope")
                        .HasColumnType("smallint");

                    b.Property<decimal?>("TotalMarketCap")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("Turnover")
                        .HasColumnType("decimal(18,2)");

                    b.Property<double?>("TurnoverGrowth")
                        .HasColumnType("float");

                    b.Property<decimal?>("TwoWeek")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("2Week");

                    b.Property<bool>("Visible")
                        .HasColumnType("bit");

                    b.Property<decimal?>("Volatility")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("VolumeTurnover")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("VolumeTurnoverUSD")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("WKN")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Week")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("YTD")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("YahooSymbol")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ListID");

                    b.HasIndex("MarketID")
                        .IsUnique();

                    b.ToTable("Instrument");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.InstrumentPrice", b =>
                {
                    b.Property<int>("InstrumentId")
                        .HasColumnType("int");

                    b.Property<decimal?>("Ask")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("AskSize")
                        .HasColumnType("int");

                    b.Property<decimal?>("Bid")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("BidSize")
                        .HasColumnType("int");

                    b.Property<decimal?>("Change")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ChangePercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("High")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("Last")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("LastRowChange")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("LastUpdateDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("Date");

                    b.Property<decimal?>("Low")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("Mid")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("OfficialClose")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("OfficialCloseDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("Open")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrevClose")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TodayTurnover")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("VWAP")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long?>("Volume")
                        .HasColumnType("bigint");

                    b.HasKey("InstrumentId");

                    b.ToTable("InstrumentPrice");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.List", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Arabic")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Chinese")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Danish")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Dutch")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Finnish")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("French")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("German")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Icelandic")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Italian")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Japanese")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Korean")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ListName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("List");

                    b.Property<byte?>("MarketID")
                        .HasColumnType("tinyint");

                    b.Property<string>("Norwegian")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Polish")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Russian")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Spanish")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Swedish")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Taiwanese")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Vietnamese")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.ToTable("List");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Market", b =>
                {
                    b.Property<short>("MarketNumber")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("smallint")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<bool>("BusinessDaysStoT")
                        .HasColumnType("bit");

                    b.Property<int>("CityId")
                        .HasColumnType("int");

                    b.Property<string>("DataSource")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Delay")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MarketAbbreviation")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Abbreviation");

                    b.Property<string>("MarketCloseTimeLocal")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MarketOpenTimeLocal")
                        .HasColumnType("nvarchar(max)");

                    b.Property<short>("TimeDiff")
                        .HasColumnType("smallint");

                    b.Property<string>("TimeZone")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TimezoneID")
                        .HasColumnType("int");

                    b.Property<int>("TranslationId")
                        .HasColumnType("int");

                    b.HasKey("MarketNumber");

                    b.HasIndex("TranslationId")
                        .IsUnique();

                    b.ToTable("Market");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Translation", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("AR")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CA")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CN")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DE")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DK")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EN")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ES")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ET")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FI")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FO")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FR")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HE")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IE")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IT")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("JP")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("KR")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("KU")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LT")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NO")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PT")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RO")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RU")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SV")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TW")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VI")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.ToTable("Translation");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Instrument", b =>
                {
                    b.HasOne("Euroland.FlipIT.SData.API.Infrastructure.Entities.List", "List")
                        .WithMany()
                        .HasForeignKey("ListID");

                    b.HasOne("Euroland.FlipIT.SData.API.Infrastructure.Entities.Market", "Market")
                        .WithOne("Instrument")
                        .HasForeignKey("Euroland.FlipIT.SData.API.Infrastructure.Entities.Instrument", "MarketID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("List");

                    b.Navigation("Market");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.InstrumentPrice", b =>
                {
                    b.HasOne("Euroland.FlipIT.SData.API.Infrastructure.Entities.Instrument", null)
                        .WithOne("InstrumentPrice")
                        .HasForeignKey("Euroland.FlipIT.SData.API.Infrastructure.Entities.InstrumentPrice", "InstrumentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Market", b =>
                {
                    b.HasOne("Euroland.FlipIT.SData.API.Infrastructure.Entities.Translation", "Translation")
                        .WithOne("Market")
                        .HasForeignKey("Euroland.FlipIT.SData.API.Infrastructure.Entities.Market", "TranslationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Translation");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Instrument", b =>
                {
                    b.Navigation("InstrumentPrice");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Market", b =>
                {
                    b.Navigation("Instrument");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Translation", b =>
                {
                    b.Navigation("Market");
                });
#pragma warning restore 612, 618
        }
    }
}
