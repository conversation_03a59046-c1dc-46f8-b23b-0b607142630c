using System.Text.Json;
using System.Text;
using Euroland.FlipIT.WiseSearch.Models;
using Newtonsoft.Json;
using Euroland.FlipIT.WiseSearch.Models.Search;
using Euroland.FlipIT.WiseSearch.Extensions;
using Microsoft.EntityFrameworkCore;
using Euroland.FlipIT.WiseSearch.Constants;
using AutoMapper;
using Euroland.FlipIT.WiseSearch.Constants.Enums;
using Euroland.FlipIT.WiseSearch.Infrastructure.UnitOfWorks;
using Euroland.FlipIT.WiseSearch.Services;
using Euroland.FlipIT.WiseSearch.Models.Authentication;
using Newtonsoft.Json.Linq;
using Euroland.FlipIT.WiseSearch.Infrastructure.Entities.WiseSearch;
using Microsoft.Extensions.Options;
using System.Text.Json.Nodes;

namespace AISearch.Services
{
  public interface ISearchService
  {
    Task<IEnumerable<SearchHistoryResponse>> GetSearchHistoryAsync(string companyCode, string userName, string language);
    Task<bool> ClearSearchHistoryAsync(string companyCode, string userName, CancellationToken cancellationToken = default);
    Task<bool> DeleteSearchHistoryAsync(string userName, Guid historyId, CancellationToken cancellationToken = default);
    Task<List<ProfileSearchHistoryOutput>?> GetProfileSearchHistoryAsync(string userName, string language);
    Task<bool> ProfileClearSearchHistoryAsync(string userName, CancellationToken cancellationToken = default);
    IAsyncEnumerable<SearchStepMessage> StreamSearchSteps(SearchInput input,
                                                          TokenInfoDto token,
                                                          CancellationToken cancellationToken = default);
    Task<PagedResult<SearchHistoryResponse>> GetSearchHistoryPagingAsync(string companyCode, string? userName, string language, int pageNumber, int pageSize);
  }

  public class SearchService : ISearchService
  {
    private readonly string _openAIKey;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ISharkUoW _sharkUoW;
    private readonly IWiseSearchUoW _wiseSearchUoW;
    private readonly ILogger<SearchService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly IMapper _mapper;
    private readonly IIngestActivityService _ingestActivityService;
    private readonly AppSettings _settings;

    public SearchService(ISharkUoW sharkUoW,
                         IWiseSearchUoW wiseSearchUoW,
                         ILogger<SearchService> logger,
                         IHttpClientFactory httpClientFactory,
                         IServiceProvider serviceProvider,
                         IMapper mapper,
                         IIngestActivityService ingestActivityService,
                         IOptions<AppSettings> options)
    {
      _httpClientFactory = httpClientFactory;
      _sharkUoW = sharkUoW;
      _wiseSearchUoW = wiseSearchUoW;
      _logger = logger;
      _serviceProvider = serviceProvider;
      _mapper = mapper;
      _ingestActivityService = ingestActivityService;
      _settings = options.Value;
    }

    public async Task<IEnumerable<SearchHistoryResponse>> GetSearchHistoryAsync(string companyCode, string userName, string language)
    {
      var searchHistories = await _wiseSearchUoW.WsSearchHistoryRepository.GetSearchHistoriesByUserAndCompanyAsync(userName, companyCode);
      var companyName = await _sharkUoW.SharkCompanySettingsLanguageRepository.GetByCompanyCodeAsync(companyCode);

      if (companyName == default)
      {
        throw new InvalidDataException($"Not found company name by code {companyCode}, lang {language}");
      }

      var searchRespones = searchHistories.Select(history => new SearchHistoryResponse
      {
        Id = history.Id,
        Question = history.Question,
        AIResult = history.AIResult,
        ResultShow = history.ResultShow,
        SearchTime = history.SearchTime,
        CompanyCode = history.CompanyCode,
        CompanyName = companyName.Name,
        QALanguage = history.QALanguage,
        AIResultLanguage = history.AIResultLanguage,
      }).ToList();

      return searchRespones;
    }

    public async Task<bool> ClearSearchHistoryAsync(string companyCode, string userName, CancellationToken cancellationToken = default)
    {
      var existingSearchHistory = await _wiseSearchUoW.WsSearchHistoryRepository.GetSearchHistoriesByUserAndCompanyAsync(userName, companyCode);

      _wiseSearchUoW.WsSearchHistoryRepository.RemoveRange(existingSearchHistory);

      var saveResult = await _wiseSearchUoW.SaveChangesAsync(cancellationToken) > 0;

      var ingestActivity = new IngestedActivityEntity
      {
        UserName = userName,
        CompanyCode = companyCode,
        Type = CIWiseSearchIngestActivityTypeEnum.DeleteByCompany.ToString(),
      };

      await _ingestActivityService.IngestActivityAsync(ingestActivity, cancellationToken);

      return saveResult;
    }

    public async Task<bool> DeleteSearchHistoryAsync(string userName, Guid historyId, CancellationToken cancellationToken = default)
    {
      SearchHistoryEntity existingSearchHistory = await _wiseSearchUoW.WsSearchHistoryRepository.GetByKeyAsync(historyId);

      if (existingSearchHistory == null)
      {
        throw new InvalidDataException($"SearchHistory  with id: {historyId} is not found.");
      }

      if (existingSearchHistory.UserName != userName)
      {
        throw new InvalidDataException("Cannot delete search history of another");
      }

      _wiseSearchUoW.WsSearchHistoryRepository.Remove(existingSearchHistory);

      var result = await _wiseSearchUoW.SaveChangesAsync() > 0;

      var ingestActivity = new IngestedActivityEntity
      {
        UserName = userName,
        ActivityId = historyId.ToString(),
        Type = CIWiseSearchIngestActivityTypeEnum.Delete.ToString(),
      };

      await _ingestActivityService.IngestActivityAsync(ingestActivity, cancellationToken);

      return result;
    }

    private async Task<SearchResponse> GetWiseResultAsync(SearchInput searchInput, string? paraphrasedQuery)
    {
      var wiseEndpoint = _settings.HttpClientRequest.WiseApi.Endpoints.Query;
      using (var client = _httpClientFactory.CreateClient(HttpClientFactoryNameConst.WiseAPIRequest))
      {
        searchInput.Question = paraphrasedQuery ?? searchInput.Question;
        var jsonObject = JObject.FromObject(searchInput);
        jsonObject["response_model"] = _settings.HttpClientRequest.WiseApi.Query.ResponseModel;
        jsonObject["semantic_count"] = _settings.HttpClientRequest.WiseApi.Query.SemanticCount;

        string jsonPayload = jsonObject.ToString(Formatting.None);

        var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

        HttpResponseMessage? response = null;
        string? responseBody = null;
        string? requestId = null;

        try
        {
          response = await client.PostAsync(wiseEndpoint, content);
          responseBody = await response.Content.ReadAsStringAsync();

          if (response.Headers.TryGetValues("X-Request-Id", out var requestIds))
          {
            requestId = requestIds.FirstOrDefault();
          }

          _logger.LogInformation("WiseAPI response received. StatusCode: {StatusCode}, RequestId: {RequestId}, Response: {ResponseBody}",
              response.StatusCode, requestId ?? "N/A", responseBody);

          response.EnsureSuccessStatusCode();

          if (!string.IsNullOrWhiteSpace(responseBody))
          {
            var searchResponse = JsonConvert.DeserializeObject<SearchResponse>(responseBody);
            if (searchResponse != null && !string.IsNullOrEmpty(searchResponse.Answer))
            {
              return searchResponse;
            }
          }

          throw new InvalidDataException($"Invalid response from WiseAPI. RequestId: {requestId ?? "N/A"}");
        }
        catch (Exception ex)
        {
          _logger.LogError(ex, "Error calling WiseAPI. RequestId: {RequestId}, Response: {ResponseBody}", requestId ?? "N/A", responseBody ?? "No response");
          throw;
        }
      }
    }

    private async Task<string?> DetectQuery(string query, string companyName)
    {
      if (string.IsNullOrWhiteSpace(query))
      {
        throw new ArgumentException("Text cannot be null or empty.", nameof(query));
      }

      var utcNow = DatetimeExts.DateTimeUtcNow();
      var formattedDate = utcNow.ToString("MMMM d, yyyy", System.Globalization.CultureInfo.CreateSpecificCulture("en-US"));

      var requestBody = new
      {
        model = _settings.HttpClientRequest.OpenAI.Model,
        messages = new[]
          {
                    new
                    {
                        role = "system",
                        content = $@"You are an expert in language processing and financial analysis. Your task is to process a user query about{companyName} by:
1. Identifying the language of the query
2. Translating it to English if it's not already in English
3. Paraphrasing the English query to be more precise for vector search in a financial database

Current date: {formattedDate}
Company: {companyName}

For paraphrasing, follow these conservative guidelines:
- Only paraphrase if the query is too vague or general for effective vector search
- Keep the original query if it is already specific and well-formed
- Limited transformations allowed:
1. For overly casual language:
 - ""how do you make money"" → ""what is your business model""
 - ""how much did you earn"" → ""what was your net income""
2. For news queries:
 - ""news about X"" → ""Press Release about X""
 - ""announcements about X"" → ""Press Release about X""

Return the original query unchanged if:
- It contains specific dates, quarters, or years
- It uses proper financial/business terminology
- It is already well-structured for vector search
- It contains specific numbers or metrics
- It references specific events, products, or initiatives

Always preserve:
- Company names and specific references
- Financial metrics and KPIs
- Time periods when specifically mentioned
- Industry-specific terminology
- Geographic references

The goal is minimal transformation - only paraphrase when the original query would clearly perform poorly in vector search.

Return a JSON object with the following structure:
{{
""originalLanguage"": ""Language name in English (e.g., English, Spanish, French)"",
""originalQuery"": ""The original query exactly as provided"",
""translatedQuery"": ""The query translated to English (same as original if already English)"",
""paraphrasedQuery"": ""The English query paraphrased for better vector search""
}}

Only return the JSON object, nothing else."
                    },
                    new
                    {
                        role = "user",
                        content = query
                    }
                },
        temperature = _settings.HttpClientRequest.OpenAI.Temperature,
        response_format = new { type = "json_object" }
      };

      var jsonContent = new StringContent(
      JsonConvert.SerializeObject(requestBody),
      Encoding.UTF8,
      "application/json");


      using HttpClient client = _httpClientFactory.CreateClient(HttpClientFactoryNameConst.OpenAIRequest);

      var response = await client.PostAsync(_settings.HttpClientRequest.OpenAI.Endpoints.ChatCompletions, jsonContent);
      response.EnsureSuccessStatusCode();

      var responseContent = await response.Content.ReadAsStringAsync();
      using var document = JsonDocument.Parse(responseContent);
      var messageContent = document.RootElement
          .GetProperty("choices")[0]
          .GetProperty("message")
          .GetProperty("content")
      .GetString();

      return messageContent;
    }

    private async Task<string> TranslateText(string text, string targetLang = "English")
    {
      if (string.IsNullOrWhiteSpace(text))
      {
        throw new ArgumentException("Text cannot be null or empty.", nameof(text));
      }

      var requestBody = new
      {
        model = _settings.HttpClientRequest.OpenAI.Model,
        messages = new[]
          {
                new { role = "system", content = $"You are a professional translator. Translate the following text to {targetLang}. Maintain the tone and context. DO NOT REMOVE ANYTHING FROM THE ORIGINAL TEXT. MAINTAIN THE STRUCTURE AND LINKS. This is for Investor Relations and target audience is retail investor. Only return the translation, nothing else." },
                new { role = "user", content = text }
            },
        temperature = _settings.HttpClientRequest.OpenAI.Temperature
      };

      var jsonContent = new StringContent(
      JsonConvert.SerializeObject(requestBody),
      Encoding.UTF8,
      "application/json");

      using HttpClient client = _httpClientFactory.CreateClient(HttpClientFactoryNameConst.OpenAIRequest);
      var response = await client.PostAsync(_settings.HttpClientRequest.OpenAI.Endpoints.ChatCompletions, jsonContent);
      response.EnsureSuccessStatusCode();

      var responseContent = await response.Content.ReadAsStringAsync();
      using var document = JsonDocument.Parse(responseContent);
      var translation = document.RootElement
          .GetProperty("choices")[0]
          .GetProperty("message")
          .GetProperty("content")
          .GetString();

      return translation?.Trim() ?? string.Empty;
    }

    public async Task<List<ProfileSearchHistoryOutput>?> GetProfileSearchHistoryAsync(string userName, string language)
    {
      var searchHistories = _wiseSearchUoW.WsSearchHistoryRepository.GetSearchHistoriesByUser(userName);

      var companyCodes = await searchHistories.Select(s => s.CompanyCode).ToListAsync();
      var companyNames = await _sharkUoW.SharkCompanySettingsLanguageRepository.GetNameByCompanyCodesAsync(companyCodes, language)
                                  ?? throw new InvalidDataException($"Not found company name by lang {language}");
      var query = await
                         searchHistories.Select(sh => new ProfileSearchHistoryOutput
                         {
                           Id = sh.Id,
                           Question = sh.Question,
                           AIResult = sh.AIResult,
                           SearchTime = sh.SearchTime,
                           CompanyCode = sh.CompanyCode,
                           CompanyName = companyNames.ContainsKey(sh.CompanyCode) ? companyNames[sh.CompanyCode] : string.Empty
                         }).ToListAsync();
      return query;
    }

    public async Task<bool> ProfileClearSearchHistoryAsync(string userName, CancellationToken cancellationToken = default)
    {
      var existingSearchHistory = _wiseSearchUoW.WsSearchHistoryRepository.GetSearchHistoriesByUser(userName);

      _wiseSearchUoW.WsSearchHistoryRepository.RemoveRange(existingSearchHistory);

      var ingestActivity = new IngestedActivityEntity
      {
        UserName = userName,
        Type = CIWiseSearchIngestActivityTypeEnum.DeleteAll.ToString(),
      };

      await _ingestActivityService.IngestActivityAsync(ingestActivity, cancellationToken);

      return await _wiseSearchUoW.SaveChangesAsync() > 0;
    }

    public async IAsyncEnumerable<SearchStepMessage> StreamSearchSteps(SearchInput input,
                                                                       TokenInfoDto token,
                                                                       CancellationToken cancellationToken = default)
    {
      if (input == null)
      {
        yield return new SearchStepMessage("Error", $"Input of service StreamSearchSteps is null");
        yield break;
      }

      if (string.IsNullOrEmpty(input.Question))
      {
        yield return new SearchStepMessage("Error", $"Question cannot be null or empty");
        yield break;
      }

      var wiseLanguage = _settings.HttpClientRequest.WiseApi.DefaultLanguageToTranslate.ToLower();

      var companyName = string.Empty;
      var cSettingLanguage = await _sharkUoW.SharkCompanySettingsLanguageRepository.GetNameByCompanyCodesAsync(new List<string> { input.CompanyCode }, input.Language);

      if (!cSettingLanguage.Any())
      {
        companyName = (await _sharkUoW.SharkCompanyNameRepository.GetByKeyAsync(input.CompanyCode)).CName;
      }
      else
      {
        companyName = cSettingLanguage.First().Value;
      }

      if (string.IsNullOrEmpty(companyName))
      {
        yield return new SearchStepMessage("Error", $"Not found company name with code {input.CompanyCode}");
        yield break;
      }

      yield return new SearchStepMessage("Message", "detectingLanguage");
      var queryDetected = await DetectQuery(input.Question, companyName);

      if (string.IsNullOrEmpty(queryDetected))
        throw new Exception("Invalid response format from OpenAI API");

      var parsedResponse = JsonNode.Parse(queryDetected);

      var originalLanguage = parsedResponse["originalLanguage"]?.ToString()?.ToLower();

      var isSameLang = originalLanguage.Equals(wiseLanguage, StringComparison.CurrentCultureIgnoreCase);

      string questionShow = input.Question;
      if (!isSameLang)
      {
        yield return new SearchStepMessage("Message", "translating");
        input.Question = await TranslateText(input.Question, wiseLanguage);
      }

      yield return new SearchStepMessage("Message", "processingQuery");
      await Task.Delay(1500, cancellationToken);
      yield return new SearchStepMessage("Message", "searching");

      var paraphrasedQuery = parsedResponse["paraphrasedQuery"]?.ToString();
      SearchResponse? wiseResponse = null;
      string? errorMessage = "Wise API response no content";
      try
      {
        wiseResponse = await GetWiseResultAsync(input, paraphrasedQuery);
        errorMessage = null;
      }
      catch (InvalidDataException ex)
      {
        errorMessage = ex.Message;
      }
      catch (Exception ex)
      {
        errorMessage = $"search_failed: {ex.Message}";
        _logger.LogError(ex, "Error occurred while getting wise result.");
      }

      if (!string.IsNullOrEmpty(errorMessage) || wiseResponse == null)
      {
        yield return new SearchStepMessage("Error", errorMessage);
        yield break;
      }

      string? translatedAnswer = null;
      if (!isSameLang)
      {
        translatedAnswer = await TranslateText(wiseResponse.Answer, originalLanguage);
      }

      var company = await _sharkUoW.SharkCompanySettingRepository.GetByKeyAsync(input.CompanyCode);
      if (company == default)
        throw new InvalidDataException($"Company {input.CompanyCode} not found");

      var searchHistory = new SearchHistoryEntity
      {
        Id = Guid.NewGuid(),
        UserName = token.UserName,
        Question = questionShow,
        QALanguage = originalLanguage,
        AIResultLanguage = wiseLanguage,
        CompanyCode = input.CompanyCode,
        AIResult = wiseResponse.Answer,
        ResultShow = translatedAnswer ?? wiseResponse.Answer,
        SearchTime = DatetimeExts.DateTimeUtcNow()
      };

      await _wiseSearchUoW.WsSearchHistoryRepository.AddAsync(searchHistory, cancellationToken);
      await _wiseSearchUoW.SaveChangesAsync(cancellationToken);

      wiseResponse.AIResultLanguage = searchHistory.AIResultLanguage;
      wiseResponse.QALanguage = searchHistory.QALanguage;
      wiseResponse.Answer = searchHistory.ResultShow;

      var ingestActivity = _mapper.Map<IngestedActivityEntity>(searchHistory);
      ingestActivity.Type = CIWiseSearchIngestActivityTypeEnum.Create.ToString();
      ingestActivity.Name = token.FullName;
      ingestActivity.Email = token.Email;
      ingestActivity.UserAgent = token.UserAgent;
      ingestActivity.Ip = token.IPAddress ?? string.Empty;

      await _ingestActivityService.IngestActivityAsync(ingestActivity, cancellationToken);

      yield return new SearchStepMessage("Result", JsonConvert.SerializeObject(wiseResponse));
    }

    public async Task<PagedResult<SearchHistoryResponse>> GetSearchHistoryPagingAsync(string companyCode, string? userName, string language, int pageNumber, int pageSize)
    {
      var (searchHistories, totalCount) = await _wiseSearchUoW.WsSearchHistoryRepository
                                                              .GetSearchHistoriesByUserAndCompanyAsync(userName,
                                                                                                       companyCode,
                                                                                                       pageNumber,
                                                                                                       pageSize);

      var companyName = await _sharkUoW.SharkCompanySettingsLanguageRepository
                                       .GetByCompanyCodeAsync(companyCode);

      if (companyName == default)
      {
        throw new InvalidDataException($"Not found company name by code {companyCode}, lang {language}");
      }

      var searchResponses = searchHistories.Select(history => new SearchHistoryResponse
      {
        Id = history.Id,
        Question = history.Question,
        AIResult = history.AIResult,
        ResultShow = history.ResultShow,
        SearchTime = history.SearchTime,
        CompanyCode = history.CompanyCode,
        CompanyName = companyName.Name,
        QALanguage = history.QALanguage,
        AIResultLanguage = history.AIResultLanguage,
      }).ToList();

      return new PagedResult<SearchHistoryResponse>
      {
        Items = searchResponses,
        TotalCount = totalCount,
        PageNumber = pageNumber,
        PageSize = pageSize
      };
    }
  }
}
