using Euroland.FlipIT.Shared.CachingManager;
using HotChocolate.Resolvers;

namespace Microsoft.Extensions.DependencyInjection;

public static class CachingManagerIResolverContextExtensions
{
  /// <summary>
  /// Retrieves an instance of <see cref="IConfigurableCacheManager"/> from the current
  /// <see cref="IResolverContext"/>.
  /// </summary>
  /// <param name="ctx">The resolver context from which to obtain the cache manager.</param>
  /// <returns>An instance of <see cref="IConfigurableCacheManager"/>.</returns>
  public static IConfigurableCacheManager GetConfigurableCacheManager(this IResolverContext ctx)
  {
    return ctx.RequestServices.GetRequiredService<IConfigurableCacheManager>();
  }

  /// <summary>
  /// Retrieves an instance of <see cref="ICachingManager"/> from the current
  /// <see cref="IResolverContext"/>.
  /// </summary>
  /// <param name="ctx">The resolver context from which to obtain the caching manager.</param>
  /// <returns>An instance of <see cref="ICachingManager"/>.</returns>
  public static ICachingManager GetCachingManager(this IResolverContext ctx)
  {
    return ctx.RequestServices.GetRequiredService<ICachingManager>();
  }
}
