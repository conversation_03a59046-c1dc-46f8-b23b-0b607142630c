using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using Euroland.FlipIT.Shared.CachingManager;
using GreenDonut;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Attachment;

/// <summary>
/// A data loader that batches and caches the retrieval of <see cref="AttachmentDto"/> objects by their IDs.
/// Utilizes a configurable cache manager to minimize database queries and improve performance.
/// </summary>
public class AttachmentsByIdDataLoader : BatchDataLoader<long, AttachmentDto>
{
  private readonly IDbContextFactory<NewsContext> _dbContextFactory;
  private readonly IConfigurableCacheManager _configurableCacheManager;

  public AttachmentsByIdDataLoader(
    IDbContextFactory<NewsContext> dbContextFactory,
    IConfigurableCacheManager configurableCacheManager,
    IBatchScheduler batchScheduler,
    DataLoaderOptions? options = null)
    : base(batchScheduler, options)
  {
    _dbContextFactory = dbContextFactory ?? throw new ArgumentNullException(nameof(dbContextFactory));
    _configurableCacheManager = configurableCacheManager ?? throw new ArgumentNullException(nameof(configurableCacheManager));
  }

  protected override async Task<IReadOnlyDictionary<long, AttachmentDto>> LoadBatchAsync(
    IReadOnlyList<long> keys,
    CancellationToken cancellationToken)
  {

    var filterKeys = keys.Where(k =>
        _configurableCacheManager.GetCache<AttachmentDto, string>(
          CacheKeyHelper.GenerateKeyByPropOfDtoObject<AttachmentDto, long>(k.ToString(), a => a.Id)) == null)
      .ToList();

    await using var newsContext = await _dbContextFactory.CreateDbContextAsync();

    var data = filterKeys.Count > 0
      ? await newsContext.Attachment
        .AsNoTracking()
        .Where(a => a.IsInBlob && filterKeys.Contains(a.Id))
        .WithAutoMapper()
        .ToProjection<AttachmentDto>()
        .ToListAsync(cancellationToken)
      : Enumerable.Empty<AttachmentDto>();

    foreach (var attachment in data)
    {
      _configurableCacheManager.SetCache(
        attachment,
        CacheKeyHelper.GenerateKeyByPropOfDtoObject<AttachmentDto, long>(attachment.Id.ToString(), a => a.Id)
      );
    }

    var result = keys.Select(k =>
      _configurableCacheManager.GetCache<AttachmentDto, string>(
        CacheKeyHelper.GenerateKeyByPropOfDtoObject<AttachmentDto, long>(k.ToString(), a => a.Id))).ToList();

    return result.ToDictionary(p => p.Id);
  }
}
