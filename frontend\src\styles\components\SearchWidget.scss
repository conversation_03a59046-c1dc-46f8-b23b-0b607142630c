@use "../_rtl-mixins" as *;

.wise-search-widget {
  @apply flex;

  &.layout-bottom {
    flex-direction: column;
  }

  @include ltr {
    &.layout-left {
      flex-direction: row-reverse;
    }
  }

  @include rtl {
    &.layout-right {
      flex-direction: row-reverse;
    }
  }
}
/* Drawer styles */
.wise-widget-container {
  position: static;
  width: 100%;
  height: 100vh;
  background: white;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;
  box-shadow: -5px 0 30px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  flex: 1;
}

.wise-widget-backdrop {
  position: static;
  width: 0%;
  height: 100vh;
  opacity: 0;
}

.wise-search-widget.layout-bottom {
  width: 100%;
  height: 100vh;

  .wise-widget-backdrop {
    width: 100%;
    height: 0%;

    @include breakpoint($breakpoint-small) {
      height: 25%;
    }
  
    @include breakpoint($breakpoint-large) {
      height: 33%;
    }
  }

  .wise-widget-container {
    height: 100%;

    @include breakpoint($breakpoint-small) {
      height: 75%;
    }
  
    @include breakpoint($breakpoint-large) {
      height: 66%;
    }
  }
}

.wise-search-widget:not(.layout-bottom) {
  .wise-widget-backdrop {
    @include breakpoint($breakpoint-small) {
      width: 33%;
    }
  
    @include breakpoint($breakpoint-large) {
      width: 50%;
    }
  }
}

.wise-widget-backdrop.active {
  opacity: 1;
  visibility: visible;
}

.wise-widget-header {
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;

  @apply bg-primary;

  font-size: 16px;
  color: #ffffff;
  font-weight: 600;
}

.wise-widget-search {
  position: sticky;
  padding: 20px;
  border-bottom: 1px solid #eee;
  // border-bottom: 1px solid #eee;
  // background: white;

  &__container {
    position: relative;
  }

  &__input {
    width: 100%;
    padding: 16px 150px 16px 16px;
    border: 2px solid #eee;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;

    &:focus {
      @apply border-primary;
      box-shadow: 0 0 0 3px var(--primary-color-light);
    }
  }

  &__button {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    @apply bg-primary;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s ease;

    &:hover {
      @apply bg-primary;
    }

    > img {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }

  &__clear {
    position: absolute;
    right: 120px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 5px;
    transition: all 0.2s ease;
    line-height: 1;
  }
}

.wise-widget-results {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
  background: white;
  font-family:
    "Inter",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    sans-serif;
  line-height: 1.6;
  color: #2c3e50;
}

.wise-widget-result {
  color: #2c3e50;
  line-height: 1.5;
  font-size: 13px;
}

.wise-widget-close {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: #eee;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  & > svg {
    color: #000;
  }
}

.wise-widget-close:hover {
  background: #e0e0e0;
  transform: rotate(90deg);
}

.wise-widget-loading {
  display: none;
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.wise-widget-loading.active {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.wise-widget-loading .loading-spinner {
  width: 40px;
  height: 40px;
  @apply border-t-primary border-[3px] border-solid border-[#f3f3f3];
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.wise-widget-loading .loading-text {
  display: flex;
  flex-direction: column;
  gap: 8px;
  color: #666;
  font-size: 13px;
}

.wise-widget-loading .loading-dots {
  display: inline-block;
}

.wise-widget-loading .loading-dots::after {
  display: inline-block;
  content: "";
  animation: dots 1.4s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes dots {
  0% {
    content: "";
  }

  25% {
    content: ".";
  }

  50% {
    content: "..";
  }

  75% {
    content: "...";
  }

  100% {
    content: "";
  }
}

.wise-search-widget-login {
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.wise-search-widget-login:hover {
  background: #0000cd11;
}

.wise-widget-history {
  padding: 20px;
}

.wise-widget-history-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.wise-widget-history-item {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
}

.history-header {
  padding: 16px;
  background: #f8f9fa;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s ease;

  svg {
    width: 16px;
    height: 16px;
  }
}

.history-header:hover {
  background: #f0f0f0;
}

.history-summary {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.history-query {
  font-weight: 500;
  color: #333;
  font-size: 16px;
  line-height: 1;
}

.history-time {
  font-size: 0.8em;
  color: #666;
}

.history-content {
  padding: 0;
  transition: all 0.3s ease;
  will-change: height;
  height: 0;
}

.history-content-inner {
  padding: 16px;
}

.wise-widget-history-item.expanded .history-content {
  height: var(--history-content-height);
}

.wise-widget-history-item.expanded .history-header svg {
  transform: rotate(180deg);
}

.wise-widget-history-item .history-header svg {
  transition: transform 0.3s ease;
}

.wise-widget-pagination {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 20px;
  padding: 16px;
}

.page-item {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #f8f9fa;
  color: #333;
}

.page-item:hover {
  background: #e9ecef;
}

.page-item.active {
  @apply bg-primary text-white;
}

.page-item.dots {
  cursor: default;
  background: transparent;
}

.wise-widget-tabs {
  display: flex;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
}

.wise-widget-tabs .tab {
  padding: 12px 20px;
  cursor: pointer;
  color: #666;
  font-weight: 500;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
  font-size: 16px;
  line-height: 1;
}

.wise-widget-tabs .tab:hover {
  @apply text-primary;
}

.wise-widget-tabs .tab.active {
  @apply text-primary border-b-primary;
}

.wise-widget-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.tab-panel {
  display: none;
  height: 100%;
  overflow-y: auto;
}

.tab-panel.active {
  display: block;
}

.wise-widget-history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.wise-widget-history-header h3 {
  margin: 0;
  color: #333;
  font-size: 15px;
  font-weight: 500;
}

.wise-widget-clear-history {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  background: #f8f9fa;
  color: #dc3545;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;

  svg {
    fill: #dc3545;
    height: 15px;
  }
}

.wise-widget-clear-history:hover {
  background: #dc3545;
  color: white;

  svg {
    fill: white;
  }
}

.wise-widget-no-history {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  font-size: 13px;
  background: #f8f9fa;
  border-radius: 8px;
}

/* update footer styles */
.wise-widget-footer {
  padding: 20px;
  background: #f8f9fa;
  border-top: 1px solid #eee;
  font-size: 11px;
  color: #666;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.wise-widget-footer-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
}

.wise-widget-footer-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.wise-widget-footer-logo {
  width: 80px;
  height: 20px;
  object-fit: contain;
}

.wise-widget-footer-links {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.wise-widget-footer-links a {
  color: #000;
  text-decoration: none;
  transition: color 0.2s ease;
}

.wise-widget-footer-links a:hover {
  @apply text-primary;
}

.wise-widget-footer-separator {
  color: #ddd;
}

.wise-widget-footer-disclaimer {
  padding-top: 8px;
  border-top: 1px solid #eee;
  color: #000;
  font-size: 10px;
  line-height: 1.4;
}

@media (max-width: 480) {
  .wise-widget-footer-main {
    flex-direction: column;
    align-items: flex-start;
  }

  .wise-widget-footer-links {
    flex-direction: column;
    align-items: flex-start;
  }

  .wise-widget-footer-separator {
    display: none;
  }
}

.wise-widget-footer i {
  @apply text-primary;
  font-size: 14px;
}

.wise-widget-footer span {
  opacity: 0.7;
}

.wise-widget-footer strong {
  color: #333;
  font-weight: 600;
}

/* local warning styles */
.history-info-icon {
  fill: #666;
  width: 14px;
  height: 14px;
  // cursor: help;
  position: relative;
}

.wise-widget-local-warning {
  background: #fff3cd;
  border: 1px solid #ffeeba;
  color: #856404;
  padding: 12px 16px;
  margin: 0 0 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 13px;
  line-height: 1.4;
}

.wise-widget-local-warning svg {
  width: 16px;
  height: 16px;
  fill: #856404;
}

/* profile styles */
.wise-widget-profile {
  position: relative;
  cursor: pointer;
}

.wise-widget-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  @apply bg-primary;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.wise-widget-avatar:hover {
  // @apply bg-primary/80;
}

.wise-widget-profile-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 180px;
  display: none;
  z-index: 1000;
}

.wise-widget__suggestions {
  // padding: 0 20px;
  margin-top: 16px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 12px;
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease;

  &.hidden {
    display: none;
  }

  &__item {
    background: #f8f9fa;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    color: #333;

    & > svg {
      @apply text-primary;
      font-size: 14px;
    }

    > span {
      text-align: left;
    }

    &:hover {
      // @apply bg-primary/10 border-primary/30;

    }
  }
}

/* Ensure the container has border-collapse behavior */
.flex-collapse {
  display: flex;
  flex-wrap: wrap;
}

/* Collapse borders between items */
.flex-collapse > div {
  border: 1px solid #333;
  margin-left: -1px; /* Overlap left border with previous item's right border */
  margin-top: -1px;  /* Overlap top border if items wrap to multiple rows */
}

/* Reset margin for the first item in a row */
.flex-collapse > div:nth-child(odd) {
  margin-left: 0;
}
