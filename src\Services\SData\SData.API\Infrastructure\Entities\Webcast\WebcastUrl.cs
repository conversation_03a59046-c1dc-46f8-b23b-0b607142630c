namespace Euroland.FlipIT.SData.API.Infrastructure.Entities.Webcast;

public class WebcastUrl
{
    public long Id { get; set; }
    public long WebcastId { get; set; }
    public virtual Webcast Webcast { get; set; } = null!;
    public int WebcastHostId { get; set; }
    public virtual WebcastHost WebcastHost { get; set; } = null!;
    public int UploadStatusId { get; set; }
    public virtual UploadStatus UploadStatus { get; set; } = null!;
    public string? OriginalId { get; set; }
}
