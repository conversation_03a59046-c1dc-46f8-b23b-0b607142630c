using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Euroland.FlipIT.WiseSearch.Infrastructure.Entities.Shark.Views;

#nullable enable

[Table("CompanyName")]
public class CompanyNameView
{
  [Key]
  [Column("cCode")]
  [Required]
  public string CCode { get; set; } = null!;

  [Column("cLang")]
  [Required]
  public string CLang { get; set; } = null!;

  [Column("cName")]
  public string? CName { get; set; }

  [Column("cAdr1")]
  public string? CAdr1 { get; set; }

  [Column("cAdr2")]
  public string? CAdr2 { get; set; }

  [Column("cZip")]
  public string? CZip { get; set; }

  [Column("cTown")]
  public string? CTown { get; set; }

  [Column("cCountry")]
  public string? CCountry { get; set; }

  [Column("cTel")]
  public string? CTel { get; set; }

  [Column("cFax")]
  public string? CFax { get; set; }

  [Column("cEMail")]
  public string? CEmail { get; set; }
}
