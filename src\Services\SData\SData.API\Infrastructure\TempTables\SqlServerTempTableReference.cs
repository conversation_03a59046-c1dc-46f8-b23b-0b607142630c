using System;
using System.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.Logging;

namespace Euroland.FlipIT.SData.API.Infrastructure.TempTables;

public class SqlServerTempTableReference: ITempTableReference
{
   private readonly IDiagnosticsLogger<DbLoggerCategory.Query> _logger;
      private readonly ISqlGenerationHelper _sqlGenerationHelper;
      private readonly DatabaseFacade _database;
      private readonly ITempTableNameLease _nameLease;
      private readonly bool _dropTableOnDispose;

      public string Name { get; }


      public SqlServerTempTableReference(
         IDiagnosticsLogger<DbLoggerCategory.Query> logger,
         ISqlGenerationHelper sqlGenerationHelper,
         string tableName,
         DatabaseFacade database,
         ITempTableNameLease nameLease,
         bool dropTableOnDispose)
      {
         Name = tableName ?? throw new ArgumentNullException(nameof(tableName));
         _logger = logger ?? throw new ArgumentNullException(nameof(logger));
         _sqlGenerationHelper = sqlGenerationHelper ?? throw new ArgumentNullException(nameof(sqlGenerationHelper));
         _database = database ?? throw new ArgumentNullException(nameof(database));
         _nameLease = nameLease ?? throw new ArgumentNullException(nameof(nameLease));
         _dropTableOnDispose = dropTableOnDispose;
      }

      public void Dispose()
      {
         try
         {
            if (!_dropTableOnDispose || _database.GetDbConnection().State != ConnectionState.Open)
               return;

            // ReSharper disable once RedundantCast because the "name" should not be sent as a parameter.
            _database.ExecuteSql($"DROP TABLE IF EXISTS {_sqlGenerationHelper.DelimitIdentifier(Name)}");
            _database.CloseConnection();
         }
         catch (ObjectDisposedException ex)
         {
            _logger.Logger.LogWarning(ex, $"Trying to dispose of the temp table reference '{Name}' after the corresponding DbContext has been disposed.");
         }
         finally
         {
            _nameLease.Dispose();
         }
      }
}
