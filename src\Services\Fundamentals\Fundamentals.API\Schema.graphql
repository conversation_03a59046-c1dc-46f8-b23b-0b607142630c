type IncomeStatement {
  cik: String @fromNewtonJson
  eps: Float @fromNewtonJson
  fye: Date @fromNewtonJson
  fys: Date @fromNewtonJson
  date: Date @fromNewtonJson
  link: String @fromNewtonJson
  ebitda: Float @fromNewtonJson
  period: String @fromNewtonJson
  symbol: String @fromNewtonJson
  revenue: Float @fromNewtonJson
  finalLink: String @fromNewtonJson
  netIncome: Float @fromNewtonJson
  periodEnd: Date @fromNewtonJson
  epsdiluted: Float @fromNewtonJson
  ebitdaratio: Float @fromNewtonJson
  fillingDate: Date @fromNewtonJson
  grossProfit: Float @fromNewtonJson
  periodStart: Date @fromNewtonJson
  acceptedDate: Date @fromNewtonJson
  calendarYear: String @fromNewtonJson
  costOfRevenue: Float @fromNewtonJson
  otherExpenses: Float @fromNewtonJson
  interestIncome: Float @fromNewtonJson
  netIncomeRatio: Float @fromNewtonJson
  periodEndMonth: String @fromNewtonJson
  costAndExpenses: Float @fromNewtonJson
  incomeBeforeTax: Float @fromNewtonJson
  interestExpense: Float @fromNewtonJson
  operatingIncome: Float @fromNewtonJson
  grossProfitRatio: Float @fromNewtonJson
  incomeTaxExpense: Float @fromNewtonJson
  periodStartMonth: String @fromNewtonJson
  reportedCurrency: String @fromNewtonJson
  operatingExpenses: Float @fromNewtonJson
  incomeBeforeTaxRatio: Float @fromNewtonJson
  operatingIncomeRatio: Float @fromNewtonJson
  weightedAverageShsOut: Float @fromNewtonJson
  weightedAverageShsOutDil: Float @fromNewtonJson
  depreciationAndAmortization: Float @fromNewtonJson
  sellingAndMarketingExpenses: Float @fromNewtonJson
  totalOtherIncomeExpensesNet: Float @fromNewtonJson
  researchAndDevelopmentExpenses: Float @fromNewtonJson
  generalAndAdministrativeExpenses: Float @fromNewtonJson
  sellingGeneralAndAdministrativeExpenses: Float @fromNewtonJson
}

type BalanceSheet {
  cik: String @fromNewtonJson
  fye: Date @fromNewtonJson
  fys: Date @fromNewtonJson
  date: Date @fromNewtonJson
  link: String @fromNewtonJson
  period: String @fromNewtonJson
  symbol: String @fromNewtonJson
  netDebt: Float @fromNewtonJson
  goodwill: Float @fromNewtonJson
  finalLink: String @fromNewtonJson
  inventory: Float @fromNewtonJson
  periodEnd: Date @fromNewtonJson
  taxAssets: Float @fromNewtonJson
  totalDebt: Float @fromNewtonJson
  commonStock: Float @fromNewtonJson
  fillingDate: Date @fromNewtonJson
  otherAssets: Float @fromNewtonJson
  periodStart: Date @fromNewtonJson
  taxPayables: Float @fromNewtonJson
  totalAssets: Float @fromNewtonJson
  totalEquity: Float @fromNewtonJson
  acceptedDate: Date @fromNewtonJson
  calendarYear: String @fromNewtonJson
  longTermDebt: Float @fromNewtonJson
  shortTermDebt: Float @fromNewtonJson
  netReceivables: Float @fromNewtonJson
  periodEndMonth: String @fromNewtonJson
  preferredStock: Float @fromNewtonJson
  accountPayables: Float @fromNewtonJson
  deferredRevenue: Float @fromNewtonJson
  intangibleAssets: Float @fromNewtonJson
  minorityInterest: Float @fromNewtonJson
  otherLiabilities: Float @fromNewtonJson
  periodStartMonth: String @fromNewtonJson
  reportedCurrency: String @fromNewtonJson
  retainedEarnings: Float @fromNewtonJson
  totalInvestments: Float @fromNewtonJson
  totalLiabilities: Float @fromNewtonJson
  otherCurrentAssets: Float @fromNewtonJson
  totalCurrentAssets: Float @fromNewtonJson
  longTermInvestments: Float @fromNewtonJson
  shortTermInvestments: Float @fromNewtonJson
  otherNonCurrentAssets: Float @fromNewtonJson
  totalNonCurrentAssets: Float @fromNewtonJson
  cashAndCashEquivalents: Float @fromNewtonJson
  capitalLeaseObligations: Float @fromNewtonJson
  otherCurrentLiabilities: Float @fromNewtonJson
  totalCurrentLiabilities: Float @fromNewtonJson
  totalStockholdersEquity: Float @fromNewtonJson
  deferredRevenueNonCurrent: Float @fromNewtonJson
  propertyPlantEquipmentNet: Float @fromNewtonJson
  otherNonCurrentLiabilities: Float @fromNewtonJson
  totalNonCurrentLiabilities: Float @fromNewtonJson
  cashAndShortTermInvestments: Float @fromNewtonJson
  goodwillAndIntangibleAssets: Float @fromNewtonJson
  othertotalStockholdersEquity: Float @fromNewtonJson
  totalLiabilitiesAndTotalEquity: Float @fromNewtonJson
  deferredTaxLiabilitiesNonCurrent: Float @fromNewtonJson
  totalLiabilitiesAndStockholdersEquity: Float @fromNewtonJson
  accumulatedOtherComprehensiveIncomeLoss: Float @fromNewtonJson
}

type CashFlowStatement {
  cik: String @fromNewtonJson
  fye: Date @fromNewtonJson
  fys: Date @fromNewtonJson
  date: Date @fromNewtonJson
  link: String @fromNewtonJson
  period: String @fromNewtonJson
  symbol: String @fromNewtonJson
  finalLink: String @fromNewtonJson
  inventory: Float @fromNewtonJson
  netIncome: Float @fromNewtonJson
  periodEnd: Date @fromNewtonJson
  fillingDate: Date @fromNewtonJson
  periodStart: Date @fromNewtonJson
  acceptedDate: Date @fromNewtonJson
  calendarYear: String @fromNewtonJson
  freeCashFlow: Float @fromNewtonJson
  debtRepayment: Float @fromNewtonJson
  dividendsPaid: Float @fromNewtonJson
  periodEndMonth: String @fromNewtonJson
  acquisitionsNet: Float @fromNewtonJson
  netChangeInCash: Float @fromNewtonJson
  accountsPayables: Float @fromNewtonJson
  periodStartMonth: String @fromNewtonJson
  reportedCurrency: String @fromNewtonJson
  cashAtEndOfPeriod: Float @fromNewtonJson
  commonStockIssued: Float @fromNewtonJson
  deferredIncomeTax: Float @fromNewtonJson
  operatingCashFlow: Float @fromNewtonJson
  otherNonCashItems: Float @fromNewtonJson
  capitalExpenditure: Float @fromNewtonJson
  accountsReceivables: Float @fromNewtonJson
  otherWorkingCapital: Float @fromNewtonJson
  changeInWorkingCapital: Float @fromNewtonJson
  commonStockRepurchased: Float @fromNewtonJson
  purchasesOfInvestments: Float @fromNewtonJson
  stockBasedCompensation: Float @fromNewtonJson
  cashAtBeginningOfPeriod: Float @fromNewtonJson
  otherFinancingActivites: Float @fromNewtonJson
  otherInvestingActivites: Float @fromNewtonJson
  effectOfForexChangesOnCash: Float @fromNewtonJson
  depreciationAndAmortization: Float @fromNewtonJson
  salesMaturitiesOfInvestments: Float @fromNewtonJson
  netCashUsedForInvestingActivites: Float @fromNewtonJson
  netCashProvidedByOperatingActivities: Float @fromNewtonJson
  investmentsInPropertyPlantAndEquipment: Float @fromNewtonJson
  netCashUsedProvidedByFinancingActivities: Float @fromNewtonJson
}

type AnalystEstimates {
  fye: Date @fromNewtonJson
  fys: Date @fromNewtonJson
  date: Date @fromNewtonJson
  period: String @fromNewtonJson
  symbol: String @fromNewtonJson
  periodEnd: Date @fromNewtonJson
  fillingDate: String @fromNewtonJson
  periodStart: Date @fromNewtonJson
  calendarYear: Int @fromNewtonJson
  cumulativeType: String @fromNewtonJson
  periodEndMonth: String @fromNewtonJson
  estimatedEpsAvg: Float @fromNewtonJson
  estimatedEpsLow: Float @fromNewtonJson
  estimatedEbitAvg: Float @fromNewtonJson
  estimatedEbitLow: Float @fromNewtonJson
  estimatedEpsHigh: Float @fromNewtonJson
  periodStartMonth: String @fromNewtonJson
  reportedCurrency: String @fromNewtonJson
  estimatedEbitHigh: Float @fromNewtonJson
  estimatedEbitdaAvg: Float @fromNewtonJson
  estimatedEbitdaLow: Float @fromNewtonJson
  estimatedEbitdaHigh: Float @fromNewtonJson
  estimatedRevenueAvg: Float @fromNewtonJson
  estimatedRevenueLow: Float @fromNewtonJson
  estimatedRevenueHigh: Float @fromNewtonJson
  estimatedNetIncomeAvg: Float @fromNewtonJson
  estimatedNetIncomeLow: Float @fromNewtonJson
  estimatedNetIncomeHigh: Float @fromNewtonJson
  estimatedSgaExpenseAvg: Float @fromNewtonJson
  estimatedSgaExpenseLow: Float @fromNewtonJson
  estimatedSgaExpenseHigh: Float @fromNewtonJson
  estimatedNumberAnalystsEps: Float @fromNewtonJson
  estimatedNumberAnalystRevenue: Float @fromNewtonJson
}

type Ratios {
  fye: Date @fromNewtonJson
  fys: Date @fromNewtonJson
  date: Date @fromNewtonJson
  period: String @fromNewtonJson
  symbol: String @fromNewtonJson
  cashRatio: Float @fromNewtonJson
  debtRatio: Float @fromNewtonJson
  periodEnd: Date @fromNewtonJson
  ebtPerEbit: Float @fromNewtonJson
  quickRatio: Float @fromNewtonJson
  fillingDate: String @fromNewtonJson
  payoutRatio: Float @fromNewtonJson
  periodStart: Date @fromNewtonJson
  calendarYear: String @fromNewtonJson
  cashPerShare: Float @fromNewtonJson
  currentRatio: Float @fromNewtonJson
  assetTurnover: Float @fromNewtonJson
  dividendYield: Float @fromNewtonJson
  ebitPerRevenue: Float @fromNewtonJson
  operatingCycle: String @fromNewtonJson
  periodEndMonth: String @fromNewtonJson
  priceFairValue: Float @fromNewtonJson
  returnOnAssets: Float @fromNewtonJson
  returnOnEquity: Float @fromNewtonJson
  debtEquityRatio: Float @fromNewtonJson
  netIncomePerEBT: Float @fromNewtonJson
  netProfitMargin: Float @fromNewtonJson
  priceSalesRatio: Float @fromNewtonJson
  effectiveTaxRate: Float @fromNewtonJson
  interestCoverage: Float @fromNewtonJson
  payablesTurnover: Float @fromNewtonJson
  periodStartMonth: String @fromNewtonJson
  priceToBookRatio: Float @fromNewtonJson
  reportedCurrency: String @fromNewtonJson
  grossProfitMargin: Float @fromNewtonJson
  inventoryTurnover: Float @fromNewtonJson
  priceToSalesRatio: Float @fromNewtonJson
  fixedAssetTurnover: Float @fromNewtonJson
  pretaxProfitMargin: Float @fromNewtonJson
  priceCashFlowRatio: Float @fromNewtonJson
  priceEarningsRatio: Float @fromNewtonJson
  cashConversionCycle: String @fromNewtonJson
  cashFlowToDebtRatio: Float @fromNewtonJson
  dividendPayoutRatio: Float @fromNewtonJson
  priceBookValueRatio: Float @fromNewtonJson
  receivablesTurnover: Float @fromNewtonJson
  freeCashFlowPerShare: Float @fromNewtonJson
  operatingProfitMargin: Float @fromNewtonJson
  cashFlowCoverageRatios: Float @fromNewtonJson
  daysOfSalesOutstanding: Float @fromNewtonJson
  companyEquityMultiplier: Float @fromNewtonJson
  enterpriseValueMultiple: Float @fromNewtonJson
  returnOnCapitalEmployed: Float @fromNewtonJson
  shortTermCoverageRatios: Float @fromNewtonJson
  daysOfPayablesOutstanding: Float @fromNewtonJson
  operatingCashFlowPerShare: Float @fromNewtonJson
  priceToFreeCashFlowsRatio: Float @fromNewtonJson
  totalDebtToCapitalization: Float @fromNewtonJson
  daysOfInventoryOutstanding: Float @fromNewtonJson
  priceEarningsToGrowthRatio: Float @fromNewtonJson
  operatingCashFlowSalesRatio: Float @fromNewtonJson
  longTermDebtToCapitalization: Float @fromNewtonJson
  priceToOperatingCashFlowsRatio: Float @fromNewtonJson
  capitalExpenditureCoverageRatio: Float @fromNewtonJson
  dividendPaidAndCapexCoverageRatio: Float @fromNewtonJson
  freeCashFlowOperatingCashFlowRatio: Float @fromNewtonJson
}

type RatiosTTM {
  date: Date @fromNewtonJson
  period: String @fromNewtonJson
  peRatioTTM: Float @fromNewtonJson
  fillingDate: Date @fromNewtonJson
  pegRatioTTM: Float @fromNewtonJson
  calendarYear: String @fromNewtonJson
  cashRatioTTM: Float @fromNewtonJson
  debtRatioTTM: Float @fromNewtonJson
  ebtPerEbitTTM: Float @fromNewtonJson
  quickRatioTTM: Float @fromNewtonJson
  payoutRatioTTM: Float @fromNewtonJson
  cashPerShareTTM: Float @fromNewtonJson
  currentRatioTTM: Float @fromNewtonJson
  dividendYielTTM: Float @fromNewtonJson
  assetTurnoverTTM: Float @fromNewtonJson
  reportedCurrency: String @fromNewtonJson
  ebitPerRevenueTTM: Float @fromNewtonJson
  operatingCycleTTM: Float @fromNewtonJson
  priceFairValueTTM: Float @fromNewtonJson
  returnOnAssetsTTM: Float @fromNewtonJson
  returnOnEquityTTM: Float @fromNewtonJson
  debtEquityRatioTTM: Float @fromNewtonJson
  netIncomePerEBTTTM: Float @fromNewtonJson
  netProfitMarginTTM: Float @fromNewtonJson
  priceSalesRatioTTM: Float @fromNewtonJson
  dividendPerShareTTM: Float @fromNewtonJson
  effectiveTaxRateTTM: Float @fromNewtonJson
  interestCoverageTTM: Float @fromNewtonJson
  payablesTurnoverTTM: Float @fromNewtonJson
  priceToBookRatioTTM: Float @fromNewtonJson
  grossProfitMarginTTM: Float @fromNewtonJson
  inventoryTurnoverTTM: Float @fromNewtonJson
  priceToSalesRatioTTM: Float @fromNewtonJson
  fixedAssetTurnoverTTM: Float @fromNewtonJson
  pretaxProfitMarginTTM: Float @fromNewtonJson
  priceCashFlowRatioTTM: Float @fromNewtonJson
  priceEarningsRatioTTM: Float @fromNewtonJson
  cashConversionCycleTTM: Float @fromNewtonJson
  cashFlowToDebtRatioTTM: Float @fromNewtonJson
  priceBookValueRatioTTM: Float @fromNewtonJson
  receivablesTurnoverTTM: Float @fromNewtonJson
  freeCashFlowPerShareTTM: Float @fromNewtonJson
  operatingProfitMarginTTM: Float @fromNewtonJson
  cashFlowCoverageRatiosTTM: Float @fromNewtonJson
  daysOfSalesOutstandingTTM: Float @fromNewtonJson
  dividendYielPercentageTTM: Float @fromNewtonJson
  companyEquityMultiplierTTM: Float @fromNewtonJson
  enterpriseValueMultipleTTM: Float @fromNewtonJson
  returnOnCapitalEmployedTTM: Float @fromNewtonJson
  shortTermCoverageRatiosTTM: Float @fromNewtonJson
  daysOfPayablesOutstandingTTM: Float @fromNewtonJson
  operatingCashFlowPerShareTTM: Float @fromNewtonJson
  priceToFreeCashFlowsRatioTTM: Float @fromNewtonJson
  totalDebtToCapitalizationTTM: Float @fromNewtonJson
  daysOfInventoryOutstandingTTM: Float @fromNewtonJson
  priceEarningsToGrowthRatioTTM: Float @fromNewtonJson
  operatingCashFlowSalesRatioTTM: Float @fromNewtonJson
  longTermDebtToCapitalizationTTM: Float @fromNewtonJson
  priceToOperatingCashFlowsRatioTTM: Float @fromNewtonJson
  capitalExpenditureCoverageRatioTTM: Float @fromNewtonJson
  dividendPaidAndCapexCoverageRatioTTM: Float @fromNewtonJson
  freeCashFlowOperatingCashFlowRatioTTM: Float @fromNewtonJson
}

type KeyMetrics {
  fye: Date @fromNewtonJson
  fys: Date @fromNewtonJson
  roe: Float @fromNewtonJson
  date: Date @fromNewtonJson
  roic: Float @fromNewtonJson
  period: String @fromNewtonJson
  symbol: String @fromNewtonJson
  pbRatio: Float @fromNewtonJson
  peRatio: Float @fromNewtonJson
  ptbRatio: Float @fromNewtonJson
  evToSales: Float @fromNewtonJson
  marketCap: Float @fromNewtonJson
  periodEnd: Date @fromNewtonJson
  pfcfRatio: Float @fromNewtonJson
  pocfratio: Float @fromNewtonJson
  fillingDate: String @fromNewtonJson
  payoutRatio: Float @fromNewtonJson
  periodStart: Date @fromNewtonJson
  calendarYear: String @fromNewtonJson
  cashPerShare: Float @fromNewtonJson
  currentRatio: Float @fromNewtonJson
  debtToAssets: Float @fromNewtonJson
  debtToEquity: Float @fromNewtonJson
  grahamNetNet: Float @fromNewtonJson
  grahamNumber: Float @fromNewtonJson
  capexPerShare: Float @fromNewtonJson
  dividendYield: Float @fromNewtonJson
  earningsYield: Float @fromNewtonJson
  incomeQuality: Float @fromNewtonJson
  capexToRevenue: Float @fromNewtonJson
  periodEndMonth: String @fromNewtonJson
  workingCapital: Float @fromNewtonJson
  averagePayables: Float @fromNewtonJson
  enterpriseValue: Float @fromNewtonJson
  investedCapital: Float @fromNewtonJson
  netDebtToEBITDA: Float @fromNewtonJson
  revenuePerShare: Float @fromNewtonJson
  averageInventory: Float @fromNewtonJson
  evToFreeCashFlow: Float @fromNewtonJson
  interestCoverage: Float @fromNewtonJson
  payablesTurnover: Float @fromNewtonJson
  periodStartMonth: String @fromNewtonJson
  reportedCurrency: String @fromNewtonJson
  bookValuePerShare: Float @fromNewtonJson
  freeCashFlowYield: Float @fromNewtonJson
  inventoryTurnover: Float @fromNewtonJson
  netIncomePerShare: Float @fromNewtonJson
  priceToSalesRatio: Float @fromNewtonJson
  averageReceivables: Float @fromNewtonJson
  tangibleAssetValue: Float @fromNewtonJson
  capexToDepreciation: Float @fromNewtonJson
  receivablesTurnover: Float @fromNewtonJson
  daysSalesOutstanding: Float @fromNewtonJson
  freeCashFlowPerShare: Float @fromNewtonJson
  interestDebtPerShare: Float @fromNewtonJson
  netCurrentAssetValue: Float @fromNewtonJson
  daysOfInventoryOnHand: Float @fromNewtonJson
  evToOperatingCashFlow: Float @fromNewtonJson
  returnOnTangibleAssets: Float @fromNewtonJson
  daysPayablesOutstanding: Float @fromNewtonJson
  capexToOperatingCashFlow: Float @fromNewtonJson
  intangiblesToTotalAssets: Float @fromNewtonJson
  enterpriseValueOverEBITDA: Float @fromNewtonJson
  operatingCashFlowPerShare: Float @fromNewtonJson
  tangibleBookValuePerShare: Float @fromNewtonJson
  shareholdersEquityPerShare: Float @fromNewtonJson
  stockBasedCompensationToRevenue: Float @fromNewtonJson
  researchAndDdevelopementToRevenue: Float @fromNewtonJson
  salesGeneralAndAdministrativeToRevenue: Float @fromNewtonJson
}

type KeyMetricsTTM {
  date: Date @fromNewtonJson
  period: String @fromNewtonJson
  roeTTM: Float @fromNewtonJson
  roicTTM: Float @fromNewtonJson
  pbRatioTTM: Float @fromNewtonJson
  peRatioTTM: Float @fromNewtonJson
  fillingDate: Date @fromNewtonJson
  ptbRatioTTM: Float @fromNewtonJson
  calendarYear: String @fromNewtonJson
  evToSalesTTM: Float @fromNewtonJson
  marketCapTTM: Float @fromNewtonJson
  pfcfRatioTTM: Float @fromNewtonJson
  pocfratioTTM: Float @fromNewtonJson
  payoutRatioTTM: Float @fromNewtonJson
  cashPerShareTTM: Float @fromNewtonJson
  currentRatioTTM: Float @fromNewtonJson
  debtToAssetsTTM: Float @fromNewtonJson
  debtToEquityTTM: Float @fromNewtonJson
  grahamNetNetTTM: Float @fromNewtonJson
  grahamNumberTTM: Float @fromNewtonJson
  capexPerShareTTM: Float @fromNewtonJson
  dividendYieldTTM: Float @fromNewtonJson
  earningsYieldTTM: Float @fromNewtonJson
  incomeQualityTTM: Float @fromNewtonJson
  reportedCurrency: String @fromNewtonJson
  capexToRevenueTTM: Float @fromNewtonJson
  workingCapitalTTM: Float @fromNewtonJson
  averagePayablesTTM: Float @fromNewtonJson
  debtToMarketCapTTM: Float @fromNewtonJson
  enterpriseValueTTM: Float @fromNewtonJson
  investedCapitalTTM: Float @fromNewtonJson
  netDebtToEBITDATTM: Float @fromNewtonJson
  revenuePerShareTTM: Float @fromNewtonJson
  averageInventoryTTM: Float @fromNewtonJson
  dividendPerShareTTM: Float @fromNewtonJson
  evToFreeCashFlowTTM: Float @fromNewtonJson
  interestCoverageTTM: Float @fromNewtonJson
  payablesTurnoverTTM: Float @fromNewtonJson
  bookValuePerShareTTM: Float @fromNewtonJson
  freeCashFlowYieldTTM: Float @fromNewtonJson
  inventoryTurnoverTTM: Float @fromNewtonJson
  netIncomePerShareTTM: Float @fromNewtonJson
  priceToSalesRatioTTM: Float @fromNewtonJson
  averageReceivablesTTM: Float @fromNewtonJson
  tangibleAssetValueTTM: Float @fromNewtonJson
  capexToDepreciationTTM: Float @fromNewtonJson
  receivablesTurnoverTTM: Float @fromNewtonJson
  daysSalesOutstandingTTM: Float @fromNewtonJson
  freeCashFlowPerShareTTM: Float @fromNewtonJson
  interestDebtPerShareTTM: Float @fromNewtonJson
  netCurrentAssetValueTTM: Float @fromNewtonJson
  daysOfInventoryOnHandTTM: Float @fromNewtonJson
  evToOperatingCashFlowTTM: Float @fromNewtonJson
  returnOnTangibleAssetsTTM: Float @fromNewtonJson
  daysPayablesOutstandingTTM: Float @fromNewtonJson
  dividendYieldPercentageTTM: Float @fromNewtonJson
  capexToOperatingCashFlowTTM: Float @fromNewtonJson
  intangiblesToTotalAssetsTTM: Float @fromNewtonJson
  enterpriseValueOverEBITDATTM: Float @fromNewtonJson
  operatingCashFlowPerShareTTM: Float @fromNewtonJson
  tangibleBookValuePerShareTTM: Float @fromNewtonJson
  shareholdersEquityPerShareTTM: Float @fromNewtonJson
  stockBasedCompensationToRevenueTTM: Float @fromNewtonJson
  researchAndDevelopementToRevenueTTM: Float @fromNewtonJson
  salesGeneralAndAdministrativeToRevenueTTM: Float @fromNewtonJson
}

type IncomeStatementGrowth {
  fye: Date @fromNewtonJson
  fys: Date @fromNewtonJson
  date: Date @fromNewtonJson
  period: String @fromNewtonJson
  symbol: String @fromNewtonJson
  growthEPS: Float @fromNewtonJson
  periodEnd: Date @fromNewtonJson
  fillingDate: String @fromNewtonJson
  periodStart: Date @fromNewtonJson
  calendarYear: String @fromNewtonJson
  growthEBITDA: Float @fromNewtonJson
  growthRevenue: Float @fromNewtonJson
  periodEndMonth: String @fromNewtonJson
  growthNetIncome: Float @fromNewtonJson
  growthEPSDiluted: Float @fromNewtonJson
  periodStartMonth: String @fromNewtonJson
  reportedCurrency: String @fromNewtonJson
  growthEBITDARatio: Float @fromNewtonJson
  growthGrossProfit: Float @fromNewtonJson
  growthCostOfRevenue: Float @fromNewtonJson
  growthOtherExpenses: Float @fromNewtonJson
  growthNetIncomeRatio: Float @fromNewtonJson
  growthCostAndExpenses: Float @fromNewtonJson
  growthIncomeBeforeTax: Float @fromNewtonJson
  growthInterestExpense: Float @fromNewtonJson
  growthOperatingIncome: Float @fromNewtonJson
  growthGrossProfitRatio: Float @fromNewtonJson
  growthIncomeTaxExpense: Float @fromNewtonJson
  growthOperatingExpenses: Float @fromNewtonJson
  growthIncomeBeforeTaxRatio: Float @fromNewtonJson
  growthOperatingIncomeRatio: Float @fromNewtonJson
  growthWeightedAverageShsOut: Float @fromNewtonJson
  growthWeightedAverageShsOutDil: Float @fromNewtonJson
  growthDepreciationAndAmortization: Float @fromNewtonJson
  growthSellingAndMarketingExpenses: Float @fromNewtonJson
  growthTotalOtherIncomeExpensesNet: Float @fromNewtonJson
  growthResearchAndDevelopmentExpenses: Float @fromNewtonJson
  growthGeneralAndAdministrativeExpenses: Float @fromNewtonJson
}

type BalanceSheetGrowth {
  fye: Date @fromNewtonJson
  fys: Date @fromNewtonJson
  date: Date @fromNewtonJson
  period: String @fromNewtonJson
  symbol: String @fromNewtonJson
  periodEnd: Date @fromNewtonJson
  fillingDate: String @fromNewtonJson
  periodStart: Date @fromNewtonJson
  calendarYear: String @fromNewtonJson
  growthNetDebt: Float @fromNewtonJson
  growthGoodwill: Float @fromNewtonJson
  periodEndMonth: String @fromNewtonJson
  growthInventory: Float @fromNewtonJson
  growthTaxAssets: Float @fromNewtonJson
  growthTotalDebt: Float @fromNewtonJson
  periodStartMonth: String @fromNewtonJson
  reportedCurrency: String @fromNewtonJson
  growthCommonStock: Float @fromNewtonJson
  growthOtherAssets: Float @fromNewtonJson
  growthTaxPayables: Float @fromNewtonJson
  growthTotalAssets: Float @fromNewtonJson
  growthLongTermDebt: Float @fromNewtonJson
  growthShortTermDebt: Float @fromNewtonJson
  growthNetReceivables: Float @fromNewtonJson
  growthAccountPayables: Float @fromNewtonJson
  growthDeferredRevenue: Float @fromNewtonJson
  growthIntangibleAssets: Float @fromNewtonJson
  growthOtherLiabilities: Float @fromNewtonJson
  growthRetainedEarnings: Float @fromNewtonJson
  growthTotalInvestments: Float @fromNewtonJson
  growthTotalLiabilities: Float @fromNewtonJson
  growthOtherCurrentAssets: Float @fromNewtonJson
  growthTotalCurrentAssets: Float @fromNewtonJson
  growthLongTermInvestments: Float @fromNewtonJson
  growthShortTermInvestments: Float @fromNewtonJson
  growthOtherNonCurrentAssets: Float @fromNewtonJson
  growthTotalNonCurrentAssets: Float @fromNewtonJson
  growthCashAndCashEquivalents: Float @fromNewtonJson
  growthOtherCurrentLiabilities: Float @fromNewtonJson
  growthTotalCurrentLiabilities: Float @fromNewtonJson
  growthTotalStockholdersEquity: Float @fromNewtonJson
  growthDeferredRevenueNonCurrent: Float @fromNewtonJson
  growthPropertyPlantEquipmentNet: Float @fromNewtonJson
  growthOtherNonCurrentLiabilities: Float @fromNewtonJson
  growthTotalNonCurrentLiabilities: Float @fromNewtonJson
  growthCashAndShortTermInvestments: Float @fromNewtonJson
  growthGoodwillAndIntangibleAssets: Float @fromNewtonJson
  growthOthertotalStockholdersEquity: Float @fromNewtonJson
  growthDeferrredTaxLiabilitiesNonCurrent: Float @fromNewtonJson
  growthTotalLiabilitiesAndStockholdersEquity: Float @fromNewtonJson
  growthAccumulatedOtherComprehensiveIncomeLoss: Float @fromNewtonJson
}

type CashFlowGrowth {
  fye: Date @fromNewtonJson
  fys: Date @fromNewtonJson
  date: Date @fromNewtonJson
  period: String @fromNewtonJson
  symbol: String @fromNewtonJson
  periodEnd: Date @fromNewtonJson
  fillingDate: String @fromNewtonJson
  periodStart: Date @fromNewtonJson
  calendarYear: String @fromNewtonJson
  periodEndMonth: String @fromNewtonJson
  growthInventory: Float @fromNewtonJson
  growthNetIncome: Float @fromNewtonJson
  periodStartMonth: String @fromNewtonJson
  reportedCurrency: String @fromNewtonJson
  growthFreeCashFlow: Float @fromNewtonJson
  growthDebtRepayment: Float @fromNewtonJson
  growthDividendsPaid: Float @fromNewtonJson
  growthAcquisitionsNet: Float @fromNewtonJson
  growthNetChangeInCash: Float @fromNewtonJson
  growthAccountsPayables: Float @fromNewtonJson
  growthCashAtEndOfPeriod: Float @fromNewtonJson
  growthCommonStockIssued: Float @fromNewtonJson
  growthDeferredIncomeTax: Float @fromNewtonJson
  growthOperatingCashFlow: Float @fromNewtonJson
  growthOtherNonCashItems: Float @fromNewtonJson
  growthCapitalExpenditure: Float @fromNewtonJson
  growthAccountsReceivables: Float @fromNewtonJson
  growthOtherWorkingCapital: Float @fromNewtonJson
  growthChangeInWorkingCapital: Float @fromNewtonJson
  growthCommonStockRepurchased: Float @fromNewtonJson
  growthPurchasesOfInvestments: Float @fromNewtonJson
  growthStockBasedCompensation: Float @fromNewtonJson
  growthCashAtBeginningOfPeriod: Float @fromNewtonJson
  growthOtherFinancingActivites: Float @fromNewtonJson
  growthOtherInvestingActivites: Float @fromNewtonJson
  growthEffectOfForexChangesOnCash: Float @fromNewtonJson
  growthDepreciationAndAmortization: Float @fromNewtonJson
  growthSalesMaturitiesOfInvestments: Float @fromNewtonJson
  growthNetCashUsedForInvestingActivites: Float @fromNewtonJson
  growthNetCashProvidedByOperatingActivites: Float @fromNewtonJson
  growthInvestmentsInPropertyPlantAndEquipment: Float @fromNewtonJson
  growthNetCashUsedProvidedByFinancingActivities: Float @fromNewtonJson
}

type FinancialGrowth {
  fye: Date @fromNewtonJson
  fys: Date @fromNewtonJson
  date: Date @fromNewtonJson
  period: String @fromNewtonJson
  symbol: String @fromNewtonJson
  epsgrowth: Float @fromNewtonJson
  periodEnd: Date @fromNewtonJson
  debtGrowth: Float @fromNewtonJson
  ebitgrowth: Float @fromNewtonJson
  assetGrowth: Float @fromNewtonJson
  fillingDate: String @fromNewtonJson
  periodStart: Date @fromNewtonJson
  calendarYear: String @fromNewtonJson
  revenueGrowth: Float @fromNewtonJson
  periodEndMonth: String @fromNewtonJson
  inventoryGrowth: Float @fromNewtonJson
  netIncomeGrowth: Float @fromNewtonJson
  rdexpenseGrowth: Float @fromNewtonJson
  epsdilutedGrowth: Float @fromNewtonJson
  periodStartMonth: String @fromNewtonJson
  reportedCurrency: String @fromNewtonJson
  grossProfitGrowth: Float @fromNewtonJson
  receivablesGrowth: Float @fromNewtonJson
  sgaexpensesGrowth: Float @fromNewtonJson
  freeCashFlowGrowth: Float @fromNewtonJson
  operatingIncomeGrowth: Float @fromNewtonJson
  bookValueperShareGrowth: Float @fromNewtonJson
  dividendsperShareGrowth: Float @fromNewtonJson
  operatingCashFlowGrowth: Float @fromNewtonJson
  tenYRevenueGrowthPerShare: Float @fromNewtonJson
  fiveYRevenueGrowthPerShare: Float @fromNewtonJson
  tenYNetIncomeGrowthPerShare: Float @fromNewtonJson
  threeYRevenueGrowthPerShare: Float @fromNewtonJson
  weightedAverageSharesGrowth: Float @fromNewtonJson
  fiveYNetIncomeGrowthPerShare: Float @fromNewtonJson
  tenYOperatingCFGrowthPerShare: Float @fromNewtonJson
  threeYNetIncomeGrowthPerShare: Float @fromNewtonJson
  fiveYOperatingCFGrowthPerShare: Float @fromNewtonJson
  threeYOperatingCFGrowthPerShare: Float @fromNewtonJson
  tenYDividendperShareGrowthPerShare: Float @fromNewtonJson
  weightedAverageSharesDilutedGrowth: Float @fromNewtonJson
  fiveYDividendperShareGrowthPerShare: Float @fromNewtonJson
  tenYShareholdersEquityGrowthPerShare: Float @fromNewtonJson
  threeYDividendperShareGrowthPerShare: Float @fromNewtonJson
  fiveYShareholdersEquityGrowthPerShare: Float @fromNewtonJson
  threeYShareholdersEquityGrowthPerShare: Float @fromNewtonJson
}

type FinancialScore {
  date: Date @fromNewtonJson
  ebit: Float @fromNewtonJson
  period: String @fromNewtonJson
  symbol: String @fromNewtonJson
  revenue: Float @fromNewtonJson
  marketCap: Float @fromNewtonJson
  fillingDate: Date @fromNewtonJson
  totalAssets: Float @fromNewtonJson
  altmanZScore: Float @fromNewtonJson
  calendarYear: String @fromNewtonJson
  piotroskiScore: Float @fromNewtonJson
  workingCapital: Float @fromNewtonJson
  reportedCurrency: String @fromNewtonJson
  retainedEarnings: Float @fromNewtonJson
  totalLiabilities: Float @fromNewtonJson
}

type OwnerEarnings {
  date: Date @fromNewtonJson
  period: String @fromNewtonJson
  symbol: String @fromNewtonJson
  averagePPE: Float @fromNewtonJson
  fillingDate: Date @fromNewtonJson
  growthCapex: Float @fromNewtonJson
  calendarYear: String @fromNewtonJson
  ownersEarnings: Float @fromNewtonJson
  maintenanceCapex: Float @fromNewtonJson
  reportedCurrency: String @fromNewtonJson
  ownersEarningsPerShare: Float @fromNewtonJson
}
