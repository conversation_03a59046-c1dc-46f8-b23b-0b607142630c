using AutoMapper;
using Euroland.FlipIT.SData.API.Constants;
using Euroland.FlipIT.SData.API.Helpers;
using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using System;

namespace Euroland.FlipIT.SData.API.Mappers.Deprecated.Profiles
{
  public class InstrumentMapperProfile : Profile
  {
    /// <summary>
    /// AutoMapper Profile: <see cref="InstrumentInfo"/>
    /// </summary>
    public InstrumentMapperProfile()
    {
      // automapper parameterization
      // see: https://docs.automapper.org/en/stable/Queryable-Extensions.html
      var langCode = "en-gb";
      int dayPeriod = 10;
      bool isRT = false;
      string toCurrency = string.Empty;

      var today = DateTime.UtcNow.UtcToCest().Date;

      var ytd = today.Date.GetFirstDateOfYear().Date;
      var date1YearAgo = today.Date.GetDateYearsAgo(1).Date;
      var date2YearsAgo = today.Date.GetDateYearsAgo(2).Date;
      var date3YearsAgo = today.Date.GetDateYearsAgo(3).Date;
      var date5YearsAgo = today.Date.GetDateYearsAgo(5).Date;
      var date10YearsAgo = today.Date.GetDateYearsAgo(10).Date;

      var date1MonthAgo = today.Date.GetDateMonthsAgo(1).Date;
      var date3MonthsAgo = today.Date.GetDateMonthsAgo(3).Date;
      var date6MonthsAgo = today.Date.GetDateMonthsAgo(6).Date;

      var date1WeekAgo = today.Date.GetDateWeeksAgo(1).Date;
      var date2WeeksAgo = today.Date.GetDateWeeksAgo(2).Date;
      var date52WeeksAgo = today.Date.GetDateWeeksAgo(52).Date;

      var language = LangHelpers.GetLang(langCode);

      var ConvertToDateOnly = (DateTime? datetime) =>
      {
        if (!datetime.HasValue)
        {
          return (DateOnly?)null;
        }
        return DateOnly.FromDateTime(datetime.Value);
      };

      // Instrument to InstrumentInfo
#pragma warning disable S3358 // Ternary operators should not be nested
      _ = CreateMap<Instrument, DTOs.Deprecated.Instrument>(MemberList.Destination)

    // -----------------------------
    // resolver for Instrument
    // -----------------------------

    .ForMember(dto => dto.VolumeChange, conf =>
        conf.MapFrom(ol => SharkDbContextBase.GetVolumeChange(ol.InstrumentId, dayPeriod)))

    .ForMember(dto => dto.TotalTrades, conf =>
        conf.MapFrom(ol => SharkDbContextBase.GetTotalTrades(ol.InstrumentId, ol.InstrumentPrice.LastUpdatedDate.Value)))

    .ForMember(dto => dto.PE, conf =>
        conf.MapFrom(ol => ol.EPS == 0 ? 0 :
                           ol.EPS == null ? 0 : ol.InstrumentPrice.Last / ol.EPS))

    .ForMember(dto => dto.Industry, conf =>
        conf.MapFrom(ol => SharkDbContextBase.GetIndustry(ol.MarCat.Value, ol.MarketID, language.LangId)))

    .ForMember(dto => dto.IndustryTranslationID, conf =>
        conf.MapFrom(ol => SharkDbContextBase.GetIndustryTranslationID(ol.EurCat.Value, language.LangId)))

    .ForMember(dto => dto.YTD, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodChangeByCurrencyRateRealTime(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, ytd)
                : SharkDbContextBase.GetPeriodChangeByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, ytd)))

    .ForMember(dto => dto.TwoYearsChange, conf =>
      conf.MapFrom(ol =>
        isRT
          ? SharkDbContextBase.GetPeriodChangeByCurrencyRateRealTime(ol.InstrumentId, ol.CurrencyCode,
            toCurrency, date2YearsAgo)
          : SharkDbContextBase.GetPeriodChangeByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
            toCurrency, date2YearsAgo)))

    .ForMember(dto => dto.ThreeYearsChange, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodChangeByCurrencyRateRealTime(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date3YearsAgo)
                : SharkDbContextBase.GetPeriodChangeByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date3YearsAgo)))

    .ForMember(dto => dto.FiveYearsChange, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodChangeByCurrencyRateRealTime(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date5YearsAgo)
                : SharkDbContextBase.GetPeriodChangeByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date5YearsAgo)))

    .ForMember(dto => dto.TenYearsChange, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodChangeByCurrencyRateRealTime(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date10YearsAgo)
                : SharkDbContextBase.GetPeriodChangeByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date10YearsAgo)))

    .ForMember(dto => dto.Month, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodChangeByCurrencyRateRealTime(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date1MonthAgo)
                : SharkDbContextBase.GetPeriodChangeByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date1MonthAgo)))

    .ForMember(dto => dto.ThreeMonthChange, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodChangeByCurrencyRateRealTime(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date3MonthsAgo)
                : SharkDbContextBase.GetPeriodChangeByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date3MonthsAgo)))

    .ForMember(dto => dto.SixMonthsChange, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodChangeByCurrencyRateRealTime(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date6MonthsAgo)
                : SharkDbContextBase.GetPeriodChangeByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date6MonthsAgo)))

    .ForMember(dto => dto.Week, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodChangeByCurrencyRateRealTime(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date1WeekAgo)
                : SharkDbContextBase.GetPeriodChangeByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date1WeekAgo)))

    .ForMember(dto => dto.TwoWeek, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodChangeByCurrencyRateRealTime(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date2WeeksAgo)
                : SharkDbContextBase.GetPeriodChangeByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date2WeeksAgo)))

    .ForMember(dto => dto.Percent52W, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodChangeByCurrencyRateRealTime(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date52WeeksAgo)
                : SharkDbContextBase.GetPeriodChangeByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date52WeeksAgo)))

    .ForMember(dto => dto.MarketCap, conf =>
        conf.MapFrom(ol => (ol.InstrumentPrice.Last) * ol.NoShares * SharkDbContextBase.GetCurrencyRateByDate(ol.CurrencyCode, toCurrency, ol.InstrumentPrice.LastUpdatedDate)))

    .ForMember(dto => dto.StartingDate, conf =>
        conf.MapFrom(ol => SharkDbContextBase.GetStartingDate(ol.InstrumentId).ToUtc()))

    .ForMember(dto => dto.LatestDate, conf =>
        conf.MapFrom(ol => SharkDbContextBase.GetLatestDate(ol.InstrumentId).ToUtc()))

    .ForMember(dto => dto.ListName, conf =>
        conf.MapFrom(ol => (
            langCode.IsCulture("en-us") ? ol.List.ListName :
            langCode.IsCulture("fr-fr") ? ol.List.French :
            langCode.IsCulture("ar-sa") ? ol.List.Arabic :
            langCode.IsCulture("ar-ae") ? ol.List.Arabic :
            langCode.IsCulture("fi-fi") ? ol.List.Finnish :
            langCode.IsCulture("sv-se") ? ol.List.Swedish :
            langCode.IsCulture("de-de") ? ol.List.German :
            langCode.IsCulture("es-mx") ? ol.List.Spanish :
            langCode.IsCulture("it-it") ? ol.List.Italian :
            langCode.IsCulture("nl-nl") ? ol.List.Dutch :
            langCode.IsCulture("ru-ru") ? ol.List.Russian :
            langCode.IsCulture("pl-pl") ? ol.List.Polish :
            langCode.IsCulture("zh-tw") ? ol.List.Taiwanese :
            langCode.IsCulture("zh-cn") ? ol.List.Chinese :
            langCode.IsCulture("ko-kr") ? ol.List.Korean :
            langCode.IsCulture("da-dk") ? ol.List.Danish :
            langCode.IsCulture("is-is") ? ol.List.Icelandic :
            langCode.IsCulture("vi-vn") ? ol.List.Vietnamese :
            langCode.IsCulture("ja-jp") ? ol.List.Japanese :
            ol.List.ListName) ?? ol.List.ListName))


    .ForMember(dto => dto.Currency, conf =>
        conf.MapFrom(ol => (
            langCode.IsCulture("en-us") ? ol.Currency.Translation.EN :
            langCode.IsCulture("fr-fr") ? ol.Currency.Translation.FR :
            langCode.IsCulture("ar-sa") ? ol.Currency.Translation.AR :
            langCode.IsCulture("ar-ae") ? ol.Currency.Translation.AR :
            langCode.IsCulture("fi-fi") ? ol.Currency.Translation.FI :
            langCode.IsCulture("sv-se") ? ol.Currency.Translation.SV :
            langCode.IsCulture("de-de") ? ol.Currency.Translation.DE :
            langCode.IsCulture("es-mx") ? ol.Currency.Translation.ES :
            langCode.IsCulture("it-it") ? ol.Currency.Translation.IT :
            langCode.IsCulture("nl-nl") ? ol.Currency.Translation.NL :
            langCode.IsCulture("ru-ru") ? ol.Currency.Translation.RU :
            langCode.IsCulture("pl-pl") ? ol.Currency.Translation.PL :
            langCode.IsCulture("zh-tw") ? ol.Currency.Translation.TW :
            langCode.IsCulture("zh-cn") ? ol.Currency.Translation.CN :
            langCode.IsCulture("ko-kr") ? ol.Currency.Translation.KR :
            langCode.IsCulture("da-dk") ? ol.Currency.Translation.DK :
            langCode.IsCulture("is-is") ? ol.Currency.Translation.IE :
            langCode.IsCulture("vi-vn") ? ol.Currency.Translation.VI :
            langCode.IsCulture("ja-jp") ? ol.Currency.Translation.JP :
            ol.Currency.Translation.EN) ?? ol.Currency.Translation.EN))
    .ForMember(dto => dto.Symbol, conf => conf.MapFrom(f => f.Currency.Symbol))

    // -----------------------------
    // resolver for InstrumentPrice
    // -----------------------------
    .ForMember(dto => dto.Bid, conf =>
        conf.MapFrom(ol => ol.InstrumentPrice.Bid * SharkDbContextBase.GetCurrencyRateByDate(ol.CurrencyCode, toCurrency, ol.InstrumentPrice.LastUpdatedDate)))

    .ForMember(dto => dto.Ask, conf =>
        conf.MapFrom(ol => ol.InstrumentPrice.Ask * SharkDbContextBase.GetCurrencyRateByDate(ol.CurrencyCode, toCurrency, ol.InstrumentPrice.LastUpdatedDate)))

    .ForMember(dto => dto.Open, conf =>
        conf.MapFrom(ol => ol.InstrumentPrice.Open * SharkDbContextBase.GetCurrencyRateByDate(ol.CurrencyCode, toCurrency, ol.InstrumentPrice.LastUpdatedDate)))

    .ForMember(dto => dto.Last, conf =>
        conf.MapFrom(ol => isRT
      ? SharkDbContextBase.GetLatestPriceOfInstrumentByCurrencyRateRealTime(ol.InstrumentId, ol.CurrencyCode, toCurrency)
      : SharkDbContextBase.GetLatestPriceOfInstrumentByCurrencyRate(ol.InstrumentId, ol.CurrencyCode, toCurrency)))

    .ForMember(dto => dto.High, conf =>
        conf.MapFrom(ol => ol.InstrumentPrice.High * SharkDbContextBase.GetCurrencyRateByDate(ol.CurrencyCode, toCurrency, ol.InstrumentPrice.LastUpdatedDate)))

    .ForMember(dto => dto.Low, conf =>
        conf.MapFrom(ol => ol.InstrumentPrice.Low * SharkDbContextBase.GetCurrencyRateByDate(ol.CurrencyCode, toCurrency, ol.InstrumentPrice.LastUpdatedDate)))

    .ForMember(dto => dto.Volume, conf =>
        conf.MapFrom(ol => ol.InstrumentPrice.Volume))

    .ForMember(dto => dto.Mid, conf =>
        conf.MapFrom(ol => ol.InstrumentPrice.Mid * SharkDbContextBase.GetCurrencyRateByDate(ol.CurrencyCode, toCurrency, ol.InstrumentPrice.LastUpdatedDate)))

    .ForMember(dto => dto.PrevClose, conf =>
        conf.MapFrom(ol => SharkDbContextBase.GetPrevCloseOfInstrumentByCurrencyRate(ol.InstrumentId, ol.CurrencyCode, toCurrency)))

    .ForMember(dto => dto.Change, conf =>
      conf.MapFrom(ol => isRT
      ?
        SharkDbContextBase.GetLatestPriceOfInstrumentByCurrencyRateRealTime(ol.InstrumentId, ol.CurrencyCode, toCurrency)
        - SharkDbContextBase.GetPrevCloseOfInstrumentByCurrencyRateRealTime(ol.InstrumentId, ol.CurrencyCode, toCurrency)
      :
        SharkDbContextBase.GetLatestPriceOfInstrumentByCurrencyRate(ol.InstrumentId, ol.CurrencyCode, toCurrency)
        - SharkDbContextBase.GetPrevCloseOfInstrumentByCurrencyRate(ol.InstrumentId, ol.CurrencyCode, toCurrency)
      ))

    .ForMember(dto => dto.ChangePercentage, conf =>
      conf.MapFrom(ol => isRT
        ? ((
            SharkDbContextBase.GetLatestPriceOfInstrumentByCurrencyRateRealTime(ol.InstrumentId, ol.CurrencyCode, toCurrency)
            - SharkDbContextBase.GetPrevCloseOfInstrumentByCurrencyRateRealTime(ol.InstrumentId, ol.CurrencyCode, toCurrency)
          )
          /
          SharkDbContextBase.GetPrevCloseOfInstrumentByCurrencyRateRealTime(ol.InstrumentId, ol.CurrencyCode, toCurrency)) * 100
        : ((
            SharkDbContextBase.GetLatestPriceOfInstrumentByCurrencyRate(ol.InstrumentId, ol.CurrencyCode, toCurrency)
           - SharkDbContextBase.GetPrevCloseOfInstrumentByCurrencyRate(ol.InstrumentId, ol.CurrencyCode, toCurrency)
            )
          /
          SharkDbContextBase.GetPrevCloseOfInstrumentByCurrencyRate(ol.InstrumentId, ol.CurrencyCode, toCurrency)) * 100))

    .ForMember(dto => dto.TodayTurnover, conf =>
        conf.MapFrom(ol => ol.InstrumentPrice.TodayTurnover * SharkDbContextBase.GetCurrencyRateByDate(ol.CurrencyCode, toCurrency, ol.InstrumentPrice.LastUpdatedDate)))

    .ForMember(dto => dto.VWAP, conf =>
        conf.MapFrom(ol => ol.InstrumentPrice.VWAP * SharkDbContextBase.GetCurrencyRateByDate(ol.CurrencyCode, toCurrency, ol.InstrumentPrice.LastUpdatedDate)))

    .ForMember(dto => dto.BidSize, conf =>
        conf.MapFrom(ol => ol.InstrumentPrice.BidSize))

    .ForMember(dto => dto.AskSize, conf =>
        conf.MapFrom(ol => ol.InstrumentPrice.AskSize))

    .ForMember(dto => dto.OfficialCloseDate, conf =>
            conf.MapFrom(ol => ConvertToDateOnly(ol.InstrumentPrice.OfficialCloseDate)))

    .ForMember(dto => dto.OfficialClose, conf =>
      conf.MapFrom(ol =>
        ol.InstrumentPrice.OfficialClose * (ol.InstrumentPrice.OfficialCloseDate != null
          ? SharkDbContextBase.GetCurrencyRateByDate(ol.CurrencyCode, toCurrency,
            ol.InstrumentPrice.OfficialCloseDate.Value)
          : SharkDbContextBase.GetCurrentCurrencyRate(ol.CurrencyCode, toCurrency))))

    .ForMember(dto => dto.LastUpdatedDate, conf =>
            conf.MapFrom(ol => ol.InstrumentPrice.LastUpdatedDate.ToKindUtc()))


    // -------------------------------
    // resolver for Market Information
    // -------------------------------
    .ForMember(dto => dto.MarketNumber, conf =>
        conf.MapFrom(ol => ol.Market.MarketNumber))

    .ForMember(dto => dto.MarketStatus, conf =>
        conf.MapFrom(ol => GetMarketStatus(SharkDbContextBase.GetMarketStatus(ol.MarketID, default))))

    .ForMember(dto => dto.CountdownToTheOpeningBell, conf =>
        conf.MapFrom(ol => CountdownToOpeningBell(SharkDbContextBase.GetMarketStatus(ol.MarketID, default))))

    .ForMember(dto => dto.CountdownToTheClosingBell, conf =>
        conf.MapFrom(ol => CountdownToClosingBell(SharkDbContextBase.GetMarketStatus(ol.MarketID, default))))

    .ForMember(dto => dto.MarketTimeZone, conf =>
        conf.MapFrom(ol => ol.Market.TimeZone))

    .ForMember(dto => dto.BusinessDaysStoT, conf =>
        conf.MapFrom(ol => ol.Market.BusinessDaysStoT))

    .ForMember(dto => dto.NormalDailyOpen, conf =>
        conf.MapFrom(ol => ol.Market.MarketOpenTimeLocal))

    .ForMember(dto => dto.NormalDailyClose, conf =>
        conf.MapFrom(ol => ol.Market.MarketCloseTimeLocal))

    .ForMember(dto => dto.MarketAbbreviation, conf =>
        conf.MapFrom(ol => ol.Market.MarketAbbreviation))

    .ForMember(dto => dto.MarketName, conf =>
        conf.MapFrom(ol => (
            langCode.IsCulture("en-us") ? ol.Market.Translation.EN :
            langCode.IsCulture("fr-fr") ? ol.Market.Translation.FR :
            langCode.IsCulture("ar-sa") ? ol.Market.Translation.AR :
            langCode.IsCulture("ar-ae") ? ol.Market.Translation.AR :
            langCode.IsCulture("fi-fi") ? ol.Market.Translation.FI :
            langCode.IsCulture("sv-se") ? ol.Market.Translation.SV :
            langCode.IsCulture("de-de") ? ol.Market.Translation.DE :
            langCode.IsCulture("es-mx") ? ol.Market.Translation.ES :
            langCode.IsCulture("it-it") ? ol.Market.Translation.IT :
            langCode.IsCulture("nl-nl") ? ol.Market.Translation.NL :
            langCode.IsCulture("ru-ru") ? ol.Market.Translation.RU :
            langCode.IsCulture("pl-pl") ? ol.Market.Translation.PL :
            langCode.IsCulture("zh-tw") ? ol.Market.Translation.TW :
            langCode.IsCulture("zh-cn") ? ol.Market.Translation.CN :
            langCode.IsCulture("ko-kr") ? ol.Market.Translation.KR :
            langCode.IsCulture("da-dk") ? ol.Market.Translation.DK :
            langCode.IsCulture("is-is") ? ol.Market.Translation.IE :
            langCode.IsCulture("vi-vn") ? ol.Market.Translation.VI :
            langCode.IsCulture("ja-jp") ? ol.Market.Translation.JP :
            ol.Market.Translation.EN) ?? ol.Market.Translation.EN))

    .ForMember(dto => dto.PriceBeginOfYear, conf =>
      conf.MapFrom(ol => SharkDbContextBase.GetCloseInPeriodTimeByCurrencyRate(ol.InstrumentId, ol.CurrencyCode, toCurrency, ytd, today)))
    .ForMember(dto => dto.Price52WeekAgo, conf =>
        conf.MapFrom(ol => SharkDbContextBase.GetCloseInPeriodTimeByCurrencyRate(ol.InstrumentId, ol.CurrencyCode, toCurrency, date52WeeksAgo, today)))
    .ForMember(dto => dto.PriceMonthAgo, conf =>
        conf.MapFrom(ol => SharkDbContextBase.GetCloseInPeriodTimeByCurrencyRate(ol.InstrumentId, ol.CurrencyCode, toCurrency, date1MonthAgo, today)))
    .ForMember(dto => dto.PriceWeekAgo, conf =>
        conf.MapFrom(ol => SharkDbContextBase.GetCloseInPeriodTimeByCurrencyRate(ol.InstrumentId, ol.CurrencyCode, toCurrency, date1WeekAgo, today)))
    .ForMember(dto => dto.PriceThreeMonthAgo, conf =>
        conf.MapFrom(ol => SharkDbContextBase.GetCloseInPeriodTimeByCurrencyRate(ol.InstrumentId, ol.CurrencyCode, toCurrency, date3MonthsAgo, today)))
    .ForMember(dto => dto.AYearAgo, conf =>
        conf.MapFrom(ol => SharkDbContextBase.GetCloseInPeriodTimeByCurrencyRate(ol.InstrumentId, ol.CurrencyCode, toCurrency, date1YearAgo, today)))
    .ForMember(dto => dto.Price2YearsAgo, conf =>
      conf.MapFrom(ol => SharkDbContextBase.GetCloseInPeriodTimeByCurrencyRate(ol.InstrumentId, ol.CurrencyCode, toCurrency, date2YearsAgo, today)))
    .ForMember(dto => dto.Price5YearAgo, conf =>
        conf.MapFrom(ol => SharkDbContextBase.GetCloseInPeriodTimeByCurrencyRate(ol.InstrumentId, ol.CurrencyCode, toCurrency, date5YearsAgo, today)))
    .ForMember(dto => dto.High52W, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodHighestOfHighByCurrencyRateRealTime(ol.InstrumentId,
                    ol.CurrencyCode, toCurrency, date52WeeksAgo)
                : SharkDbContextBase.GetPeriodHighestOfHighByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date52WeeksAgo)))
    .ForMember(dto => dto.Low52W, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodLowestOfLowByCurrencyRateRealTime(ol.InstrumentId,
                    ol.CurrencyCode, toCurrency, date52WeeksAgo)
                : SharkDbContextBase.GetPeriodLowestOfLowByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date52WeeksAgo)))

    .ForMember(dto => dto.AllTimeHigh, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodHighestOfHighByCurrencyRateRealTime(ol.InstrumentId,
                    ol.CurrencyCode, toCurrency, null)
                : SharkDbContextBase.GetPeriodHighestOfHighByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, null)))

    .ForMember(dto => dto.AllTimeLow, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodLowestOfLowByCurrencyRateRealTime(ol.InstrumentId,
                    ol.CurrencyCode, toCurrency, null)
                : SharkDbContextBase.GetPeriodLowestOfLowByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, null)))

    .ForMember(dto => dto.HighYTD, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodHighestOfHighByCurrencyRateRealTime(ol.InstrumentId,
                    ol.CurrencyCode, toCurrency, ytd)
                : SharkDbContextBase.GetPeriodHighestOfHighByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, ytd)))

    .ForMember(dto => dto.LowYTD, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodLowestOfLowByCurrencyRateRealTime(ol.InstrumentId,
                    ol.CurrencyCode, toCurrency, ytd)
                : SharkDbContextBase.GetPeriodLowestOfLowByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, ytd)))

    .ForMember(dto => dto.ThreeMonthHigh, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodHighestOfHighByCurrencyRateRealTime(ol.InstrumentId,
                    ol.CurrencyCode, toCurrency, date3MonthsAgo)
                : SharkDbContextBase.GetPeriodHighestOfHighByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date3MonthsAgo)))

    .ForMember(dto => dto.ThreeMonthLow, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodLowestOfLowByCurrencyRateRealTime(ol.InstrumentId,
                    ol.CurrencyCode, toCurrency, date3MonthsAgo)
                : SharkDbContextBase.GetPeriodLowestOfLowByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date3MonthsAgo)))

    .ForMember(dto => dto.Highest52w, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodHighestOfHighByCurrencyRateRealTime(ol.InstrumentId,
                    ol.CurrencyCode, toCurrency, date52WeeksAgo)
                : SharkDbContextBase.GetPeriodHighestOfHighByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date52WeeksAgo)))

    .ForMember(dto => dto.Lowest52w, conf =>
        conf.MapFrom(ol =>
            isRT
                ? SharkDbContextBase.GetPeriodLowestOfLowByCurrencyRateRealTime(ol.InstrumentId,
                    ol.CurrencyCode, toCurrency, date52WeeksAgo)
                : SharkDbContextBase.GetPeriodLowestOfLowByCurrencyRate(ol.InstrumentId, ol.CurrencyCode,
                    toCurrency, date52WeeksAgo)))

    .ForMember(dto => dto.ShareName,
               conf => conf.MapFrom(ol => (ol.Market == null || string.IsNullOrEmpty(ol.ShareName))
               ? ol.ShareName
               : InstrumentHelper.TruncateMarketAbbreviation(ol.ShareName, ol.Market.MarketAbbreviation)));
#pragma warning restore S3358 // Ternary operators should not be nested
    }

    private static string GetMarketStatus(int timeToMarketOpen)
    {
      return timeToMarketOpen < 0 ?
          Constants.MarketStatus.Close.ToString() :
          Constants.MarketStatus.Open.ToString();
    }

    private static int? CountdownToOpeningBell(int countdown)
    {
      return countdown < 0 ? -countdown : null;
    }

    private static int? CountdownToClosingBell(int countdown)
    {
      return countdown > 0 ? countdown : null;
    }
  }
}
