using System;
using System.Linq;
using System.Collections.Generic;
using Euroland.FlipIT.SData.API.GraphQL.Selections;
using HotChocolate.Types;
using HotChocolate.Resolvers;
using HotChocolate.Data.Projections.Context;


namespace Microsoft.Extensions.DependencyInjection;

public static class IResolverContextSelectionExtensions
{
  public static IReadOnlyList<ResolverProcessingSelection> GetResolverProcessingSelections(this IResolverContext resolverContext)
  {
    if (resolverContext == null)
    {
      return Enumerable.Empty<ResolverProcessingSelection>().ToList();
    }

    return GetChildSelections(resolverContext!)?.ToList();
  }

  public static ResolverProcessingSelection GetTotalCountSelectionField(this IResolverContext? context)
  {
    if (context == null)
      return null!;

    var totalCountSelectionField = FindChildSelectionByName(context!, SelectionFieldName.TotalCount, null);
    return totalCountSelectionField;
  }

  private static ResolverProcessingSelection FindChildSelectionByName(IResolverContext? context, string selectionFieldName, ResolverProcessingSelection? parentSelection)
  {
    if (context == null)
      return null!;

    var childSelections = GetChildSelections(context!, parentSelection);
    var resultSelection = childSelections?.FirstOrDefault(
        s => s.SelectionName.Equals(selectionFieldName, StringComparison.OrdinalIgnoreCase)
    )!;

    return resultSelection!;
  }

  private static IEnumerable<ResolverProcessingSelection> GetChildSelections(IResolverContext resolverContext, ResolverProcessingSelection? parentSelection = null)
  {
    var parentFieldSelection = parentSelection?.SelectedField ?? resolverContext.GetSelectedField();

    var possibleTypes = resolverContext.Schema.GetPossibleTypes(parentFieldSelection.Type.NamedType());

    return possibleTypes
      .SelectMany(t => parentFieldSelection.GetFields(t))
      .Select(f => new ResolverProcessingSelection(f));
  }
}
