using System.Threading.Tasks;
using HotChocolate;
using HotChocolate.Resolvers;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Middleware;

/// <summary>
/// Adds value of `adjClose` argument to the ScopedContextData.
/// </summary>
public class AdjustedCloseArgumentMiddleware
{
  private readonly FieldDelegate _next;

  public AdjustedCloseArgumentMiddleware(FieldDelegate next)
  {
    _next = next;
  }

  public async ValueTask InvokeAsync(IMiddlewareContext context)
  {
    try
    {
      var adjClose = context.ArgumentValue<bool?>(AdjCloseArgumentExtensions.ADJ_CLOSE_FIELD);
      if (adjClose != null)
      {
        context.ScopedContextData = !context.ScopedContextData.ContainsKey(AdjCloseArgumentExtensions.ADJ_CLOSE_FIELD) ?
          context.ScopedContextData.Add(AdjCloseArgumentExtensions.ADJ_CLOSE_FIELD, adjClose) :
          context.ScopedContextData.SetItem(AdjCloseArgumentExtensions.ADJ_CLOSE_FIELD, adjClose);
      }
    }
    catch(GraphQLException) {}

    await _next(context).ConfigureAwait(false);
  }
}
