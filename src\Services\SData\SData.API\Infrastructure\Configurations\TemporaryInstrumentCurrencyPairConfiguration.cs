// using Euroland.FlipIT.SData.API.Infrastructure.Entities;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.EntityFrameworkCore.Metadata.Builders;

// namespace Euroland.FlipIT.SData.API.Infrastructure.Configurations;

// public class TemporaryInstrumentCurrencyPairConfiguration : IEntityTypeConfiguration<InstrumentCurrencyPair>
// {
//   public const string TemporaryInstrumentCurrencyPairTableName = nameof(InstrumentCurrencyPair);
//   public void Configure(EntityTypeBuilder<InstrumentCurrencyPair> builder)
//   {
//     builder
//       .ToView(TemporaryInstrumentCurrencyPairTableName)
//       .HasNoKey();

//   }
// }
