using GreenDonut;
using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Resolvers;
using Euroland.FlipIT.SData.API.Infrastructure;
using entity = Euroland.FlipIT.SData.API.Infrastructure.Entities;
using System;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Data.SqlClient;
using Euroland.FlipIT.SData.API.Helpers;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using System.Threading.Tasks;
using HotChocolate;
using System.Threading;
using Euroland.FlipIT.SData.API.GraphQL.Types.CurrencyRate;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;

namespace Microsoft.Extensions.DependencyInjection;

public class PerformanceShareResolvers
{
  public async Task<IQueryable<PerformanceShareDto>?> GetCalculatePerformanceShareAsync(
      [Parent] InstrumentDto instrument,
      [Service] IGraphQLExecutionContext executionContext,
      CurrencyRateByCurrencyPairDataLoader dataLoader,
      IResolverContext resolverContext,
      SharkDbContextBase sharkDbContext,
      PerformancePeriodDto period,
      bool? adjClose,
      DateTime? fromDate = default,
      CancellationToken cancellationToken = default
    )
  {
    if(adjClose == null) {
      adjClose = executionContext.UseAdjClose;
    }

    if (period != PerformancePeriodDto.CUSTOM)
    {
      fromDate = GetStartDateFromPeriod(period);
    }
    else
    {
      if (fromDate == null || DateTime.UtcNow.AddDays(-7) < fromDate.Value)
      {
        resolverContext.ReportError("Must provide a valid date for argument `fromDate` when `CUSTOM` period is specified. Input date must be at least 1 week earlier than present");
        return null;
      }
    }

    // Try get validate @exchangeCurrency and get currencyRateId if possible.
    var currencyRateId = default(int?);
    var exchangeCurrency = executionContext.ExchangeCurrency;

    if (
      !string.IsNullOrEmpty(exchangeCurrency)
      && !string.IsNullOrEmpty(instrument.CurrencyCode)
      && !string.Equals(instrument.CurrencyCode, exchangeCurrency, StringComparison.InvariantCultureIgnoreCase))
    {
      var currencyRate = await dataLoader.LoadAsync($"{instrument.CurrencyCode}{exchangeCurrency}".ToLower(), resolverContext.RequestAborted);

      // Input an invalid exchangeCurrencyCode. Need to notify error to client.
      if (currencyRate == null)
      {
        resolverContext.ReportError($"Not found currency pair {instrument.CurrencyCode}{exchangeCurrency} from the system, or our system does not support this currency code.");

        return null;
      }
      if (currencyRateId > 0)
      {
        currencyRateId = currencyRate.Id;
      }
    }

    return LoadPerformanceShareByDateAsync(
      sharkDbContext,
      instrument!,
      fromDate.Value,
      period,
      currencyRateId,
      adjClose ?? false
    )
    .WithAutoMapper()
    .ToProjection<PerformanceShareDto>();
  }

  public static DateTime GetStartDateFromPeriod(PerformancePeriodDto period)
  {
    var today = DateTime.UtcNow.UtcToCest().Date;

    switch (period)
    {
      case PerformancePeriodDto.YTD: return new DateTime(today.Year, 1, 1).Date;
      case PerformancePeriodDto.ONE_YEAR: return today.AddYears(-1).Date;
      case PerformancePeriodDto.TWO_YEARS: return today.AddYears(-2).Date;
      case PerformancePeriodDto.THREE_YEARS: return today.AddYears(-3).Date;
      case PerformancePeriodDto.FIVE_YEARS: return today.AddYears(-5).Date;
      case PerformancePeriodDto.TEN_YEARS: return today.AddYears(-10).Date;

      case PerformancePeriodDto.ONE_MONTH: return today.AddMonths(-1).Date;
      case PerformancePeriodDto.THREE_MONTHS: return today.AddMonths(-3).Date;
      case PerformancePeriodDto.SIX_MONTHS: return today.AddMonths(-6).Date;

      case PerformancePeriodDto.ONE_WEEK: return today.AddDays(-1 * 7).Date;
      case PerformancePeriodDto.TWO_WEEKS: return today.AddDays(-2 * 7).Date;
      case PerformancePeriodDto.FIFTY_TWO_WEEKS: return today.AddDays(-52 * 7).Date;
      default: throw new NotSupportedException($"Not support type of period PerformancePeriodDto.{period}");
    }
  }

  private IQueryable<entity.PerformanceShare> LoadPerformanceShareByDateAsync(
    SharkDbContextBase sharkDbContext,
    InstrumentDto instrument,
    DateTime fromDate,
    PerformancePeriodDto period,
    int? currencyRateId,
    bool adjClose)
  {
    var instrumentPriceTable = sharkDbContext.InstrumentPrice.EntityType.GetSchemaQualifiedTableName();
    var instrumentHistoryTable = sharkDbContext.InstrumentHistory.EntityType.GetSchemaQualifiedTableName();
    var currencyRateTable = sharkDbContext.CurrencyRates.EntityType.GetSchemaQualifiedTableName();
    var currencyRateHistoryTable = sharkDbContext.CurrencyRateHistories.EntityType.GetSchemaQualifiedTableName();

    var fromDateParam = new SqlParameter("fromDate", System.Data.SqlDbType.SmallDateTime)
    {
      Value = fromDate
    };
    var periodParam = new SqlParameter("period", System.Data.SqlDbType.Int)
    {
      Value = (int)period
    };

    var instrumentIdParam = new SqlParameter("instrumentId", System.Data.SqlDbType.Int)
    {
      Value = instrument.Id
    };

    var currencyRateIdParam = new SqlParameter("currencyRateId", System.Data.SqlDbType.Int)
    {
      Value = currencyRateId != null ? currencyRateId.Value : DBNull.Value
    };
    var adjCloseParam = new SqlParameter("adjClose", System.Data.SqlDbType.Bit)
    {
      Value = adjClose
    };

    var sqlQuery = BuildSqlQuery(
      currencyRateTable!,
      currencyRateHistoryTable!,
      instrumentPriceTable!,
      instrumentHistoryTable!
    );
    return sharkDbContext.Set<entity.PerformanceShare>().FromSqlRaw(
      sqlQuery,
      fromDateParam,
      periodParam,
      instrumentIdParam,
      currencyRateIdParam,
      adjCloseParam
    ).TagWith(CorporateActionAdjustmentFactorCTEDbCommandInterceptor.TOKEN);
  }

  /// <summary>
  /// Builds SQL query to return the change, lowest, highest of price at which
  /// a security has traded during the time period.
  /// Query includes 3 <see cref="SqlParameter"/>(s) for parameterizing:
  ///   @json: The input of period in form of string: e.g. [{"Period":0,"FromDate":"1989-07-27"},{"Period":5,"FromDate":"2024-04-27"},{"Period":4,"FromDate":"2024-06-27"}]
  ///   @currencyRateId: The ID of currency-pair (instrument currency-quote currency) if we want to convert instrument's price into another currency
  ///   @instrumentId: The ID of instrument to get prices.
  /// Query will return the table formed with 4 column: [Period, FromDate, High, Low, ChangePercentage]
  /// </summary>
  /// <param name="currencyRateTableName">The [{currencyRateTableName}] table</param>
  /// <param name="currencyRateHistoryTableName">The [{currencyRateHistoryTableName}] table</param>
  /// <param name="instrumentPriceTableName">The InstrumentPrice table</param>
  /// <param name="instrumentHistoryTableName">The {instrumentHistoryTableName} table</param>
  /// <returns></returns>
  private static string BuildSqlQuery(
    string currencyRateTableName,
    string currencyRateHistoryTableName,
    string instrumentPriceTableName,
    string instrumentHistoryTableName)
  {
    // Query includes 3 parameters:
    //	@json VARCHAR(MAX)
    //	@currencyRateId INT
    //	@instrumentId INT
    return @$"
      SELECT
        @period AS [Period],
        @fromDate AS [FromDate],
        COALESCE(high.[High], 0) AS [High],
        COALESCE(low.[Low], 0) AS [Low],
        COALESCE(performance.ChangePercentage, 0) AS [ChangePercentage]
      FROM (
        SELECT TOP(1)
          ((p.[Last] * COALESCE(cRate.[cRate], 1)) - (h.[Close] * COALESCE(hRate.[hRate], 1))) / (h.[Close] * COALESCE(hRate.[hRate], 1)) * 100
          AS ChangePercentage
        FROM (
          SELECT * FROM rt_InstrumentPrice rt
          UNION ALL
          SELECT * FROM InstrumentPrice nrt
          WHERE NOT EXISTS (SELECT 1 FROM rt_InstrumentPrice WHERE InstrumentId = nrt.InstrumentId)
        ) AS p
        CROSS APPLY (
          SELECT TOP(1)
            CAST(h1.[Close] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Close]
            ,h1.[Date]
          FROM [{instrumentHistoryTableName}] AS h1
          OUTER APPLY (
            SELECT TOP 1 EXP(SUM(LOG(CumulativeAdjustmentFactor))) AS CumulativeAdjustmentFactor
            FROM {CorporateActionAdjustmentFactorCTEDbCommandInterceptor.CumulativeAdjustmentsTable}
            WHERE InstrumentId = h1.InstrumentId AND h1.[Date] < ActionDate
            GROUP BY InstrumentId
          ) AS ca
          WHERE h1.InstrumentId = p.InstrumentId
			      AND CAST(h1.[Date] AS Date) >= CAST(@fromDate AS Date)
          ORDER BY h1.[Date] DESC
        ) AS h
        OUTER APPLY (
          SELECT TOP(1)
            [cRate] AS cRate
          FROM
          (
            SELECT TOP(1)
              [cRate],
              CAST(cDate AS Date) as [cDate]
            FROM [{currencyRateTableName}]
            WHERE cId = @currencyRateId

            UNION ALL

            SELECT
              ch.chRate AS [cRate],
              ch.chDate AS [cDate]
            FROM [{currencyRateHistoryTableName}] as ch
            WHERE ch.cId = @currencyRateId
          ) AS cc WHERE cc.cDate = CAST(p.[Date] AS Date)
        ) as cRate
        OUTER APPLY (
          SELECT chRate AS [hRate]
          FROM [{currencyRateHistoryTableName}] as ch
          WHERE ch.cId = @currencyRateId AND ch.chDate = h.[Date]
        ) as hRate
        WHERE p.InstrumentId = @instrumentId
      ) AS performance

      OUTER APPLY (
        SELECT
          MAX(h.[High] * COALESCE(rate.chRate, 1)) AS [High]
        FROM
        (
          SELECT TOP(1)
            COALESCE(p.[High], p.[Last]) AS [High],
            p.[Date],
            p.[InstrumentId]
          FROM (
            SELECT * FROM rt_InstrumentPrice rt
            UNION ALL
            SELECT * FROM InstrumentPrice nrt
            WHERE NOT EXISTS (SELECT 1 FROM rt_InstrumentPrice WHERE InstrumentId = nrt.InstrumentId)
          ) AS p

          UNION ALL

          SELECT
            CAST(COALESCE(h.[Low], h.[Close]) * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [High],
            h.[Date],
            h.[InstrumentId]
          FROM [{instrumentHistoryTableName}] as h
          OUTER APPLY (
            SELECT TOP 1 EXP(SUM(LOG(CumulativeAdjustmentFactor))) AS CumulativeAdjustmentFactor
            FROM {CorporateActionAdjustmentFactorCTEDbCommandInterceptor.CumulativeAdjustmentsTable}
            WHERE InstrumentId = h.InstrumentId AND h.[Date] < ActionDate
            GROUP BY InstrumentId
          ) AS ca
        ) AS h
        OUTER APPLY (
          SELECT chRate
          FROM [{currencyRateHistoryTableName}] as ch
          WHERE ch.cId = @currencyRateId AND ch.chDate = h.[Date]
        ) as rate
        WHERE h.InstrumentId = @instrumentId
          AND CAST(h.[Date] AS Date) >= CAST(@fromDate AS Date)
      ) AS high

      OUTER APPLY (
        SELECT
          MIN(l.[Low] * COALESCE(rate.chRate, 1)) AS [Low]
        FROM
        (
          SELECT TOP(1)
            COALESCE(p.[Low], p.[Last]) AS [Low],
            p.[Date],
            p.[InstrumentId]
          FROM (
            SELECT * FROM rt_InstrumentPrice rt
            UNION ALL
            SELECT * FROM InstrumentPrice nrt
            WHERE NOT EXISTS (SELECT 1 FROM rt_InstrumentPrice WHERE InstrumentId = nrt.InstrumentId)
          ) AS p

          UNION ALL

          SELECT
            CAST(COALESCE(h.[Low], h.[Close]) * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Low],
            h.[Date],
            h.[InstrumentId]
          FROM [{instrumentHistoryTableName}] as h
          OUTER APPLY (
            SELECT TOP 1 EXP(SUM(LOG(CumulativeAdjustmentFactor))) AS CumulativeAdjustmentFactor
            FROM {CorporateActionAdjustmentFactorCTEDbCommandInterceptor.CumulativeAdjustmentsTable}
            WHERE InstrumentId = h.InstrumentId AND h.[Date] < ActionDate
            GROUP BY InstrumentId
          ) AS ca
        ) AS l
        OUTER APPLY (
          SELECT chRate
          FROM [{currencyRateHistoryTableName}] as ch
          WHERE ch.cId = @currencyRateId AND ch.chDate = l.[Date]
        ) as rate
        WHERE l.InstrumentId = @instrumentId
          AND CAST(l.[Date] AS Date) >= CAST(@fromDate AS Date)
      ) AS low
    ";
  }
}
