using HotChocolate.Execution.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.CompanyName;

public static class CompanyNameRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddCompanyNameType(this IRequestExecutorBuilder builder)
  {
    builder.AddType<CompanyNameType>();

    builder.AddTypeExtension<CompanyTypeExtensions>();

    return builder;
  }
}
