{"Serilog": {"Using": ["Serilog.Sinks.Syslog"], "MinimumLevel": "Warning", "WriteTo": [{"Name": "UdpSyslog", "Args": {"host": "*************", "port": "514", "appName": "FlipIT.SData.API", "restrictedToMinimumLevel": "Warning", "outputTemplate": "[{Timestamp:HH:mm:ss}] {Message:lj}{NewLine}{Exception}"}}], "Properties": {"Application": "FlipIT.SData.API"}, "ConnectionStrings": {"NewsDb": "Server=************;Database=News;User ID=uShark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;Application Name=SData.API", "SharkDb": "Server=************;Database=shark;User ID=uShark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;Application Name=SData.API", "WebcastDb": "Server=************;Database=Webcast;User ID=uShark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=SData.API"}}}