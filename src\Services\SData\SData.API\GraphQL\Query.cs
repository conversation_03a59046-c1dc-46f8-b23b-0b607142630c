using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.Types.Company;
using Euroland.FlipIT.SData.API.GraphQL.Types.Currency;
using Euroland.FlipIT.SData.API.GraphQL.Types.CurrencyRate;
using Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;
using Euroland.FlipIT.SData.API.GraphQL.Types.PressRelease;
using Euroland.FlipIT.SData.API.Infrastructure;
using HotChocolate;
using HotChocolate.Resolvers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL;

public class QueryType
{
  /// <summary>
  /// Gets a company by company code.
  /// </summary>
  /// <param name="id">The company code</param>
  /// <param name="cancellationToken">The cancellation token to be notified a request should be cancelled.</param>
  /// <returns>
  ///   <see cref="CompanyDto"/>
  /// </returns>
  [GraphQLName("company")]
  public async Task<CompanyDto?> GetCompanyAsync(
    CompanyByCodeDataLoader dataLoader,
    [GraphQLDescription("Company code to find")]
    string code,
    IResolverContext resolverContext,
    CancellationToken cancellationToken = default)
    => await dataLoader.LoadAsync(code.ToLower(), cancellationToken);
  //{

  //     IMapper mapper = context.Service<IMapper>();

  //         // ensure projections are only applied once
  //         context.LocalContextData = context.LocalContextData.SetItem(QueryableProjectionProvider.SkipProjectionKey, true);

  //         QueryableProjectionContext visitorContext = new QueryableProjectionContext(context, context.ObjectType, context.Selection.Field.Type.UnwrapRuntimeType(), false);

  //         QueryableProjectionVisitor.Default.Visit(visitorContext);

  // #pragma warning disable CS8631
  //         Expression<Func<CompanyDto?, object?>> projection = visitorContext.Project<CompanyDto?, object?>();
  // #pragma warning restore CS8631

  //     return sharkDbContextBase
  //       .Company
  //       .AsNoTracking()
  //       .Where(c => c.CompanyCode == code)
  //       .WithAutoMapper()
  //       .ToProjection<CompanyDto>();
  // }

  /// <summary>
  /// Gets an instrument by Id.
  /// </summary>
  /// <param name="id">The id of instrument</param>
  /// <param name="cancellationToken">The cancellation token to be notified a request should be cancelled.</param>
  /// <returns>
  ///  <see cref="InstrumentDto"/>
  /// </returns>
  [GraphQLName("instrumentById")]
  public async Task<InstrumentDto?> GetInstrumentById(
    [GraphQLDescription("ID of instrument")] int id,
    IResolverContext resolverContext,
    string? exchangeCurrency,
    InstrumentByIdDataLoader dataloader,
    [GraphQLDeprecated("Will be removed in the next release")]
    bool? useCloud = default,
    bool? adjClose = null,
    CancellationToken cancellationToken = default)
    => await dataloader.LoadAsync(id, cancellationToken);

  [GraphQLName("instrumentByIds")]
  public async Task<IEnumerable<InstrumentDto?>?> GetInstrumentByIds(
    [GraphQLDescription("ID of instrument")] int[] ids,
    IResolverContext resolverContext,
    string? exchangeCurrency,
    InstrumentByIdDataLoader dataloader,
    [GraphQLDeprecated("Will be removed in the next release")]
    bool? useCloud = default,
    bool? adjClose = null,
    CancellationToken cancellationToken = default)
    => ids.Length != 0 ? await dataloader.LoadAsync(ids, cancellationToken) : null;

  /// <summary>
  /// Gets currency by currency code.
  /// </summary>
  /// <param name="currencyCode">The currency code </param>
  /// <param name="cancellationToken">The cancellation token to be notified a request should be cancelled.</param>
  /// <returns>
  ///   <see cref="CurrencyDto"/>
  /// </returns>
  [GraphQLName("currencyByCode")]
  public async Task<CurrencyDto?> GetCurrencyByCodeAsync(
    CurrencyByCodeDataLoader dataLoader,
    [GraphQLDescription("The currency code. E.g: USD")] string currencyCode,
    CancellationToken cancellationToken = default) => await dataLoader.LoadAsync(currencyCode.ToLower(), cancellationToken);

  /// <summary>
  /// Gets exchange rate of a currency pair.
  /// </summary>
  /// <param name="baseCurrency">The base currency</param>
  /// <param name="quoteCurrency">The quote currency</param>
  /// <param name="cancellationToken">The cancellation token to be notified a request should be cancelled.</param>
  /// <returns>
  ///   <see cref="CurrencyRateDto"/>
  /// </returns>
  [GraphQLName("currencyRate")]
  public async Task<CurrencyRateDto?> GetCurrencyRateAsync(
    IResolverContext resolverContext,
    CurrencyRateByCurrencyPairDataLoader dataLoader,
    [GraphQLDescription("The currency code of base currency. E.g: USD")] string baseCurrency,
    [GraphQLDescription("The currency code of quote currency. E.g: GBX")] string quoteCurrency,
    CancellationToken cancellationToken = default)
  {
    if (string.IsNullOrEmpty(baseCurrency) || baseCurrency.Length != 3)
    {
      resolverContext.ReportError("Argument baseCurrency must be 3 character length");
      return null;
    }

    if (string.IsNullOrEmpty(quoteCurrency) || quoteCurrency.Length != 3)
    {
      resolverContext.ReportError("Argument quoteCurrency must be 3 character length");
      return null;
    }

    return await dataLoader.LoadAsync($"{baseCurrency}{quoteCurrency}".ToLower(), cancellationToken);
  }

  /// <summary>
  /// Gets an announcement of a company with given Id.
  /// </summary>
  /// <param name="id">The Id of an announcement</param>
  /// <param name="cancellationToken">The cancellation token to be notified a request should be cancelled.</param>
  /// <returns>
  ///   <see cref="PressReleaseDto"/>
  /// </returns>
  [GraphQLName("pressrelease")]
  public async Task<PressReleaseDto?> GetPressreleaseAsync(
    [Service] IDbContextFactory<NewsContext> newDbContextFactory,
    IResolverContext resolverContext,
    [GraphQLDescription("The ID of pressrelease")]
    long id,
    [GraphQLDescription("Show information of release even it is marked as hidden.")]
    bool? includeHidden = true,
    [GraphQLDescription("If `false`, pressRelease groups will be ungrouped and the types will show up instead of the groups. Default to `false`")]
    bool? groupByMessageType = false,
    CancellationToken cancellationToken = default)
  {
    if (id <= 0)
    {
      resolverContext.ReportError("ID must be a positive number");
      return null;
    }

    return await PressReleaseResolvers.LoadPressReleaseById(
      newDbContextFactory,
      id,
      includeHidden,
      groupByMessageType,
      null,
      cancellationToken
    );
  }
}

