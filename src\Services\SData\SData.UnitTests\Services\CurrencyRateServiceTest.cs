// using Euroland.FlipIT.SData.API;
// using Euroland.FlipIT.SData.API.Constants;
// using Euroland.FlipIT.SData.API.Helpers;
// using Euroland.FlipIT.SData.API.Infrastructure;
// using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
// using Euroland.FlipIT.SData.API.Infrastructure.Entities;
// using Euroland.FlipIT.SData.API.Services;
// using Euroland.FlipIT.SData.API.Services.Interfaces;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.Extensions.Caching.Distributed;
// using Microsoft.Extensions.DependencyInjection;
// using Microsoft.Extensions.Options;
// using Moq;
// using System;
// using System.Linq;
// using System.Threading;
// using System.Threading.Tasks;
// using Xunit;
// using Euroland.FlipIT.SData.API.Infrastructure.Factories;

// namespace UnitTest.SData.Application.Services
// {
//     class InMemoryDbContextFactory: ISharkDbContextFactory
//     {
//         public ISharkDbContext CreateDbContext(bool useRealtimeDb = false)
//         {
//             var options = new DbContextOptionsBuilder<DefaultSharkDbContext>()
//                             .UseInMemoryDatabase(databaseName: "InMemorySharkDatabase")
//                             .ConfigureWarnings(b => b.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.InMemoryEventId.TransactionIgnoredWarning))
//                             .Options;
//             var dbContext = new DefaultSharkDbContext(options);

//             return dbContext;
//         }
//     }

//     public class CurrencyRateServiceTest
//     {

//         [Theory]
//         [InlineData(null, null)]
//         public void Ctor_throw_ArgumentNullException(ISharkDbContextFactory dbContextFactory, IDistributedCache cache)
//         {
//             Assert.Throws<ArgumentNullException>(() => new CurrencyRateService(null, null, Options.Create<DistributedCachingConfiguration>(new DistributedCachingConfiguration())));
//         }

//         [Theory]
//         [InlineData(null, "EUR")]
//         [InlineData(null, "")]
//         [InlineData("USD", null)]
//         [InlineData("", null)]
//         [InlineData("", "")]
//         public async Task GetCurrentRateAsync_Throw_ArgumentNullException(string fromCurrency, string toCurrency) {
//             // Arrange
//             var mockCache = new Mock<IDistributedCache>();
//             var factory = new InMemoryDbContextFactory();
//             var srv = new CurrencyRateService(factory, mockCache.Object, null);

//             // Act
//             Func<Task<Euroland.FlipIT.SData.API.DTOs.Deprecated.Currencies.CurrencyRate>> action = () => srv.GetCurrentRateAsync(fromCurrency, toCurrency);

//             // Assert
//             await Assert.ThrowsAsync<ArgumentNullException>(action);
//         }

//         [Theory]
//         [InlineData("USDK", "EUR")]
//         [InlineData("USD", "EURK")]
//         [InlineData("US", "EU")]
//         public async Task GetCurrentRateAsync_Throw_ArgumentException_If_CurrencyLength_NotEqual_3(string fromCurrency, string toCurrency) {
//             // Arrange
//             var mockCache = new Mock<IDistributedCache>();
//             var factory = new InMemoryDbContextFactory();
//             var srv = new CurrencyRateService(factory, mockCache.Object, null);

//             // Act
//             Func<Task<Euroland.FlipIT.SData.API.DTOs.Deprecated.Currencies.CurrencyRate>> action = () => srv.GetCurrentRateAsync(fromCurrency, toCurrency);

//             // Assert
//             await Assert.ThrowsAnyAsync<ArgumentOutOfRangeException>(action);
//         }

//         [Fact]
//         public async Task GetCurrentRateAsync_Return_Latest_Rage() {
//             // Arrange
//             var mockCache = new Mock<IDistributedCache>();
//             var factory = new InMemoryDbContextFactory();
//             using (var db = factory.CreateDbContext() as DefaultSharkDbContext)
//             {
//                 db.Database.EnsureDeleted();
//                 db.Database.EnsureCreated();

//                 db.Set<CurrencyRate>().Add(new CurrencyRate
//                 {
//                     Currencies = "USDEUR",
//                     Date = DateTime.SpecifyKind(DateTime.Parse("2023-01-03"), DateTimeKind.Utc).ToCEST(),
//                     ID = 1,
//                     Rate = 0.5m
//                 });

//                 await db.SaveChangesAsync();
//             }


//             var srv = new CurrencyRateService(factory, mockCache.Object, null);

//             // Act
//             var rate = await srv.GetCurrentRateAsync("USD", "EUR");

//             // Assert
//             Assert.NotNull(rate);
//             Assert.Equal(0.5m, rate.Value);
//         }

//         [Fact]
//         public async Task GetCurrentRateAsync_Return_Null_IfNotFound_Currency() {
//             // Arrange
//             var mockCache = new Mock<IDistributedCache>();
//             var factory = new InMemoryDbContextFactory();
//             var srv = new CurrencyRateService(factory, mockCache.Object, null);

//             // Act
//             var rate = await srv.GetCurrentRateAsync("NOT", "NOT");

//             // Assert
//             Assert.Null(rate);
//         }

//         [Theory]
//         [InlineData(null, "EUR")]
//         [InlineData(null, "")]
//         [InlineData("USD", null)]
//         [InlineData("", null)]
//         [InlineData("", "")]
//         public async Task GetRateHistoryAsync_Throw_ArgumentNullException(string fromCurrency, string toCurrency) {
//             // Arrange
//             var mockCache = new Mock<IDistributedCache>();
//             var factory = new InMemoryDbContextFactory();
//             var srv = new CurrencyRateService(factory, mockCache.Object, null);

//             // Act
//             Func<Task<System.Collections.Generic.IEnumerable<Euroland.FlipIT.SData.API.DTOs.Deprecated.Currencies.CurrencyRate>>> action = () => srv.GetRateHistoryAsync(fromCurrency, toCurrency, DateTime.UtcNow.AddDays(-1));

//             // Assert
//             await Assert.ThrowsAsync<ArgumentNullException>(action);
//         }

//         [Theory]
//         [InlineData("USDK", "EUR")]
//         [InlineData("USD", "EURK")]
//         [InlineData("US", "EU")]
//         public async Task GetRateHistoryAsync_Throw_ArgumentException_If_CurrencyLength_NotEqual_3(string fromCurrency, string toCurrency) {
//             // Arrange
//             var mockCache = new Mock<IDistributedCache>();
//             var factory = new InMemoryDbContextFactory();
//             var srv = new CurrencyRateService(factory, mockCache.Object, null);

//             // Act
//             Func<Task<System.Collections.Generic.IEnumerable<Euroland.FlipIT.SData.API.DTOs.Deprecated.Currencies.CurrencyRate>>> action = () => srv.GetRateHistoryAsync(fromCurrency, toCurrency, DateTime.UtcNow.AddDays(-1));

//             // Assert
//             await Assert.ThrowsAnyAsync<ArgumentOutOfRangeException>(action);
//         }

//         [Theory]
//         [InlineData("1969-01-01", "2023-01-03")]
//         [InlineData("2023-01-03", "2023-01-01")]
//         public async Task GetRateHistoryAsync_Throw_ArgumentException_If_Input_Dates_IsNotValid(string fromDateUTCStr, string toDateUTCStr) {
//             // Arrange
//             var mockCache = new Mock<IDistributedCache>();
//             var factory = new InMemoryDbContextFactory();
//             var srv = new CurrencyRateService(factory, mockCache.Object, null);

//             var fromDateUTC = DateTime.SpecifyKind(DateTime.Parse(fromDateUTCStr), DateTimeKind.Utc);
//             var toDateUTC = DateTime.SpecifyKind(DateTime.Parse(toDateUTCStr), DateTimeKind.Utc);

//             // Act
//             Func<Task<System.Collections.Generic.IEnumerable<Euroland.FlipIT.SData.API.DTOs.Deprecated.Currencies.CurrencyRate>>> action = () => srv.GetRateHistoryAsync("USD", "EUR", fromDateUTC, toDateUTC);

//             // Assert
//             await Assert.ThrowsAnyAsync<ArgumentException>(action);
//         }

//         [Fact]
//         public async Task GetRateHistoryAsync_Input_fromDateUTC_And_toDateUTC_ToBeNull() {
//             // Arrange
//             var mockCache = new Mock<IDistributedCache>();
//             var factory = new InMemoryDbContextFactory();
//             var srv = new CurrencyRateService(factory, mockCache.Object, null);
//             var dayNum = 10;
//             var startDate = DateTime.UtcNow.AddDays(-dayNum).Date;
//             var i = 0;
//             var fromDateUTC = startDate.AddDays(dayNum/2);
//             var toDateUTC = (Nullable<DateTime>)null;
//             using(var db = factory.CreateDbContext() as DefaultSharkDbContext)
//             {
//                 db.Database.EnsureDeleted();
//                 db.Database.EnsureCreated();

//                 db.Set<CurrencyRate>().Add(new CurrencyRate {
//                     Currencies = "USDEUR",
//                     Date = DateTime.UtcNow,
//                     ID = 1,
//                     Rate = 0.5m
//                 });

//                 do
//                 {
//                     db.Set<CurrencyRateHistory>().Add(new CurrencyRateHistory
//                     {
//                         ID = DateTime.UtcNow.Ticks,
//                         CurrencyRateID = 1,
//                         Rate = (decimal)(new Random().NextDouble()),
//                         Date = startDate.AddDays(i)
//                     });

//                 } while (++i < dayNum);

//                 await db.SaveChangesAsync();
//             }

//             // Act
//             var list = (await srv.GetRateHistoryAsync("USD", "EUR", fromDateUTC, toDateUTC)).ToList();
//             var min = list.Min(r => r.Date);
//             //var max = list.Max(r => r.Date);
//             // Assert
//             Assert.Equal(fromDateUTC, min);
//         }

//         [Fact]
//         public async Task GetRateHistoryAsync_Input_fromDateUTC_And_toDateUTC() {
//             // Arrange
//             var mockCache = new Mock<IDistributedCache>();
//             var factory = new InMemoryDbContextFactory();
//             var srv = new CurrencyRateService(factory, mockCache.Object, null);
//             var dayNum = 10;
//             var startDate = DateTime.UtcNow.AddDays(-dayNum).Date;
//             var i = 0;
//             var fromDateUTC = startDate.AddDays(dayNum/2);
//             var toDateUTC = fromDateUTC.AddDays(2);
//             using(var db = factory.CreateDbContext() as DefaultSharkDbContext)
//             {
//                 db.Database.EnsureDeleted();
//                 db.Database.EnsureCreated();

//                 db.Set<CurrencyRate>().Add(new CurrencyRate {
//                     Currencies = "USDEUR",
//                     Date = DateTime.UtcNow.ToCEST(),
//                     ID = 1,
//                     Rate = 0.5m
//                 });

//                 do
//                 {
//                     db.Set<CurrencyRateHistory>().Add(new CurrencyRateHistory
//                     {
//                         ID = DateTime.UtcNow.Ticks,
//                         CurrencyRateID = 1,
//                         Rate = (decimal)(new Random().NextDouble()),
//                         Date = startDate.AddDays(i)
//                     });

//                 } while (++i < dayNum);

//                 await db.SaveChangesAsync();
//             }

//             // Act
//             var list = (await srv.GetRateHistoryAsync("USD", "EUR", fromDateUTC, toDateUTC)).ToList();

//             // Assert
//             Assert.Equal(toDateUTC.Subtract(fromDateUTC).Days + /* Including rate at fromDateUTC */ 1, list.Count);
//         }

//         [Fact]
//         public async Task GetRateHistoryAsync_Input_fromDateUTC_NotAvailableFromDataSource_ButLook_ClosestOneBehind_And_toDateUTC_ToBeNull() {
//             // Arrange
//             var mockCache = new Mock<IDistributedCache>();
//             var factory = new InMemoryDbContextFactory();
//             var srv = new CurrencyRateService(factory, mockCache.Object, null);
//             var dayNum = 10;
//             var startDate = DateTime.UtcNow.AddDays(-dayNum).Date;
//             var i = 0;
//             var fromDateUTC = startDate.AddDays(-1d); // This filter date is not available from datasource.
//             var availableDataClosestToFromDateUTC = fromDateUTC.AddDays(-2);
//             var toDateUTC = (Nullable<DateTime>)null;
//             using(var db = factory.CreateDbContext() as DefaultSharkDbContext)
//             {
//                 db.Database.EnsureDeleted();
//                 db.Database.EnsureCreated();

//                 db.Set<CurrencyRate>().Add(new CurrencyRate {
//                     Currencies = "USDEUR",
//                     Date = DateTime.UtcNow,
//                     ID = 1,
//                     Rate = 0.5m
//                 });

//                 do
//                 {
//                     db.Set<CurrencyRateHistory>().Add(new CurrencyRateHistory
//                     {
//                         ID = DateTime.UtcNow.Ticks,
//                         CurrencyRateID = 1,
//                         Rate = (decimal)(new Random().NextDouble()),
//                         Date = startDate.AddDays(i)
//                     });

//                 } while (++i < dayNum);

//                 // Add one more older currency rate than fromDateUTC,
//                 // So when getting data with filtering fromDateUTC value, but not available from
//                 // datasource, the closest date to the fromDateUTC should be picked and returned.
//                 db.Set<CurrencyRateHistory>().Add(new CurrencyRateHistory
//                 {
//                     ID = 10L,
//                     CurrencyRateID = 1,
//                     Rate = (decimal)(new Random().NextDouble()),
//                     Date = availableDataClosestToFromDateUTC
//                 });
//                 // Add one more older than previous to check the correctness
//                 db.Set<CurrencyRateHistory>().Add(new CurrencyRateHistory
//                 {
//                     ID = 9L,
//                     CurrencyRateID = 1,
//                     Rate = (decimal)(new Random().NextDouble()),
//                     Date = availableDataClosestToFromDateUTC.AddDays(-1)
//                 });
//                 await db.SaveChangesAsync();
//             }

//             // Act
//             var result = await srv.GetRateHistoryAsync("USD", "EUR", fromDateUTC, toDateUTC);
//             var firstRate = result.OrderBy(r => r.Date).First();

//             // Assert
//             Assert.Equal(availableDataClosestToFromDateUTC, firstRate.Date);
//         }

//         [Fact]
//         public async Task GetRateHistoryAsync_Input_fromDateUTC_NotAvailableFromDataSource_ButLook_ClosestOneAhead_And_toDateUTC_ToBeNull() {
//             // Arrange
//             var mockCache = new Mock<IDistributedCache>();
//             var factory = new InMemoryDbContextFactory();
//             var srv = new CurrencyRateService(factory, mockCache.Object, null);
//             var dayNum = 10;
//             var startDate = DateTime.UtcNow.AddDays(-dayNum).Date;
//             var i = 0;
//             var fromDateUTC = startDate.AddDays(-2); // This filter date is not available from datasource.
//             var availableDataClosestToFromDateUTC = startDate;
//             var toDateUTC = (Nullable<DateTime>)null;
//             using(var db = factory.CreateDbContext() as DefaultSharkDbContext)
//             {
//                 db.Database.EnsureDeleted();
//                 db.Database.EnsureCreated();

//                 db.Set<CurrencyRate>().Add(new CurrencyRate {
//                     Currencies = "USDEUR",
//                     Date = DateTime.UtcNow.ToCEST(),
//                     ID = 1,
//                     Rate = 0.5m
//                 });

//                 do
//                 {
//                     db.Set<CurrencyRateHistory>().Add(new CurrencyRateHistory
//                     {
//                         ID = DateTime.UtcNow.Ticks,
//                         CurrencyRateID = 1,
//                         Rate = (decimal)(new Random().NextDouble()),
//                         Date = startDate.AddDays(i)
//                     });

//                 } while (++i < dayNum);


//                 await db.SaveChangesAsync();
//             }

//             // Act
//             var result = await srv.GetRateHistoryAsync("USD", "EUR", fromDateUTC, toDateUTC);
//             var firstRate = result.OrderBy(r => r.Date).First();

//             // Assert
//             Assert.Equal(availableDataClosestToFromDateUTC, firstRate.Date);
//         }

//         [Fact]
//         public async Task GetRateHistoryAsync_ReturnEmpty_IfNotFound_Currencies() {
//             // Arrange
//             var mockCache = new Mock<IDistributedCache>();
//             var factory = new InMemoryDbContextFactory();
//             var srv = new CurrencyRateService(factory, mockCache.Object, null);
//             var dayNum = 10;
//             var startDate = DateTime.UtcNow.AddDays(-dayNum).Date;
//             var i = 0;
//             var fromDateUTC = startDate.AddDays(-2); // This filter date is not available from datasource.
//             var toDateUTC = (Nullable<DateTime>)null;
//             using(var db = factory.CreateDbContext() as DefaultSharkDbContext)
//             {
//                 db.Database.EnsureDeleted();
//                 db.Database.EnsureCreated();

//                 db.Set<CurrencyRate>().Add(new CurrencyRate {
//                     Currencies = "USDEUR",
//                     Date = DateTime.UtcNow.ToCEST(),
//                     ID = 1,
//                     Rate = 0.5m
//                 });

//                 do
//                 {
//                     db.Set<CurrencyRateHistory>().Add(new CurrencyRateHistory
//                     {
//                         ID = DateTime.UtcNow.Ticks,
//                         CurrencyRateID = 1,
//                         Rate = (decimal)(new Random().NextDouble()),
//                         Date = startDate.AddDays(i)
//                     });

//                 } while (++i < dayNum);


//                 await db.SaveChangesAsync();
//             }

//             // Act
//             var result = await srv.GetRateHistoryAsync("EUR", "USD", fromDateUTC, toDateUTC);

//             // Assert
//             Assert.Empty(result);
//         }

//         [Fact]
//         public async Task GetRateHistoryAsync_CustomRate_Return_CustomRate_FromConfiguration() {
//             // Arrange
//             //serviceScopeFactory.Setup(srv => srv.CreateScope()).Returns
//             var mockCache = new Mock<IDistributedCache>();
//             var factory = new InMemoryDbContextFactory();

//             var dayNum = 10;
//             var startDate = DateTime.UtcNow.AddDays(-dayNum).Date;
//             var i = 0;
//             var fromDateUTC = startDate; // This filter date is not available from datasource.
//             var toDateUTC = (Nullable<DateTime>)null;

//             var currencyRateTransformConfiguration = new CurrencyTransformationConfiguration() {
//                 Enabled = true
//             };

//             var CUSTOM_RATE = 100F;

//             currencyRateTransformConfiguration.Transforms.Add(
//                 new CustomRateTransformation("GBP", "GBX", CUSTOM_RATE)
//             );

//             var serviceProvider = new Mock<IServiceProvider>();
//             serviceProvider
//                 .Setup(x => x.GetService(typeof(IOptions<CurrencyTransformationConfiguration>)))
//                 .Returns(Options.Create(currencyRateTransformConfiguration));

//             var serviceScope = new Mock<IServiceScope>();
//             serviceScope.Setup(srv => srv.ServiceProvider).Returns(serviceProvider.Object);

//             var serviceScopeFactory = new Mock<IServiceScopeFactory>();
//             serviceScopeFactory.Setup(f => f.CreateScope()).Returns(serviceScope.Object);

//             serviceProvider.Setup(x => x.GetService(typeof(IServiceScopeFactory))).Returns(serviceScopeFactory.Object);

//             var srv = new CurrencyRateService(factory, mockCache.Object, null, serviceScopeFactory.Object);
//             serviceProvider
//                 .Setup(x => x.GetService(typeof(ICurrencyRateTransformationStrategy)))
//                 .Returns(srv);

//             using(var db = factory.CreateDbContext() as DefaultSharkDbContext)
//             {
//                 db.Database.EnsureDeleted();
//                 db.Database.EnsureCreated();

//                 db.Set<CurrencyRate>().Add(new CurrencyRate {
//                     Currencies = "GBPGBX",
//                     Date = DateTime.UtcNow.ToCEST(),
//                     ID = 1,
//                     Rate = 0.5m
//                 });

//                 do
//                 {
//                     db.Set<CurrencyRateHistory>().Add(new CurrencyRateHistory
//                     {
//                         ID = DateTime.UtcNow.Ticks,
//                         CurrencyRateID = 1,
//                         Rate = (decimal)(new Random().NextDouble()),
//                         Date = startDate.AddDays(i)
//                     });

//                 } while (++i < dayNum);


//                 await db.SaveChangesAsync();
//             }

//             // Act
//             var result = await srv.GetRateHistoryAsync("GBP", "GBX", fromDateUTC, toDateUTC);

//             // Assert
//             Assert.Contains(result, rate => rate.Value == (decimal)CUSTOM_RATE);
//         }

//         [Fact]
//         public async Task GetRateHistoryAsync_CustomRate_Return_ActualRate_When_ConfigurationNotContains() {
//             // Arrange
//             //serviceScopeFactory.Setup(srv => srv.CreateScope()).Returns
//             var mockCache = new Mock<IDistributedCache>();
//             var factory = new InMemoryDbContextFactory();

//             var dayNum = 10;
//             var startDate = DateTime.UtcNow.AddDays(-dayNum).Date;
//             var i = 0;
//             var fromDateUTC = startDate; // This filter date is not available from datasource.
//             var toDateUTC = (Nullable<DateTime>)null;

//             var currencyRateTransformConfiguration = new CurrencyTransformationConfiguration() {
//                 Enabled = true
//             };

//             var CUSTOM_RATE = 100F;
//             var ACTUAL_RATE = 0.1F;

//             currencyRateTransformConfiguration.Transforms.Add(
//                 // another one defined custom rate for "GBXGBP" other than "GBPGBX"
//                 new CustomRateTransformation("GBX", "GBP", CUSTOM_RATE)
//             );

//             var serviceProvider = new Mock<IServiceProvider>();
//             serviceProvider
//                 .Setup(x => x.GetService(typeof(IOptions<CurrencyTransformationConfiguration>)))
//                 .Returns(Options.Create(currencyRateTransformConfiguration));

//             var serviceScope = new Mock<IServiceScope>();
//             serviceScope.Setup(srv => srv.ServiceProvider).Returns(serviceProvider.Object);

//             var serviceScopeFactory = new Mock<IServiceScopeFactory>();
//             serviceScopeFactory.Setup(f => f.CreateScope()).Returns(serviceScope.Object);

//             serviceProvider.Setup(x => x.GetService(typeof(IServiceScopeFactory))).Returns(serviceScopeFactory.Object);

//             var srv = new CurrencyRateService(factory, mockCache.Object, null, serviceScopeFactory.Object);
//             serviceProvider
//                 .Setup(x => x.GetService(typeof(ICurrencyRateTransformationStrategy)))
//                 .Returns(srv);

//             using(var db = factory.CreateDbContext() as DefaultSharkDbContext)
//             {
//                 db.Database.EnsureDeleted();
//                 db.Database.EnsureCreated();

//                 db.Set<CurrencyRate>().Add(new CurrencyRate {
//                     Currencies = "GBPGBX",
//                     Date = DateTime.UtcNow.ToCEST(),
//                     ID = 1,
//                     Rate = 0.5m
//                 });

//                 do
//                 {
//                     db.Set<CurrencyRateHistory>().Add(new CurrencyRateHistory
//                     {
//                         ID = DateTime.UtcNow.Ticks,
//                         CurrencyRateID = 1,
//                         Rate = (decimal)(ACTUAL_RATE),
//                         Date = startDate.AddDays(i)
//                     });

//                 } while (++i < dayNum);


//                 await db.SaveChangesAsync();
//             }

//             // Act
//             var result = await srv.GetRateHistoryAsync("GBP", "GBX", fromDateUTC, toDateUTC);

//             // Assert
//             Assert.DoesNotContain(result, rate => rate.Value == (decimal)CUSTOM_RATE);
//         }

//         [Fact]
//         public async Task GetRateHistoryAsync_CustomRate_Return_ActualRateMultiplyWithFactor_ForMatched_BaseCurrency() {
//             // Arrange
//             //serviceScopeFactory.Setup(srv => srv.CreateScope()).Returns
//             var mockCache = new Mock<IDistributedCache>();
//             var factory = new InMemoryDbContextFactory();

//             var dayNum = 10;
//             var startDate = DateTime.UtcNow.AddDays(-dayNum).Date;
//             var i = 0;
//             var fromDateUTC = startDate; // This filter date is not available from datasource.
//             var toDateUTC = (Nullable<DateTime>)null;

//             var currencyRateTransformConfiguration = new CurrencyTransformationConfiguration() {
//                 Enabled = true
//             };

//             var FACTOR = 100F;
//             var ACTUAL_RATE = 10M;

//             currencyRateTransformConfiguration.Transforms.Add(
//                 new BaseCurrencyTransformation("GBP", "GBX", FACTOR)
//             );

//             var serviceProvider = new Mock<IServiceProvider>();
//             serviceProvider
//                 .Setup(x => x.GetService(typeof(IOptions<CurrencyTransformationConfiguration>)))
//                 .Returns(Options.Create(currencyRateTransformConfiguration));

//             var serviceScope = new Mock<IServiceScope>();
//             serviceScope.Setup(srv => srv.ServiceProvider).Returns(serviceProvider.Object);

//             var serviceScopeFactory = new Mock<IServiceScopeFactory>();
//             serviceScopeFactory.Setup(f => f.CreateScope()).Returns(serviceScope.Object);

//             serviceProvider.Setup(x => x.GetService(typeof(IServiceScopeFactory))).Returns(serviceScopeFactory.Object);

//             var srv = new CurrencyRateService(factory, mockCache.Object, null, serviceScopeFactory.Object);
//             serviceProvider
//                 .Setup(x => x.GetService(typeof(ICurrencyRateTransformationStrategy)))
//                 .Returns(srv);

//             using(var db = factory.CreateDbContext() as DefaultSharkDbContext)
//             {
//                 db.Database.EnsureDeleted();
//                 db.Database.EnsureCreated();

//                 db.Set<CurrencyRate>().Add(new CurrencyRate {
//                     Currencies = "GBXEUR", // Defines base/quote currencies
//                     Date = DateTime.UtcNow.ToCEST(),
//                     ID = 1,
//                     Rate = 0.5m
//                 });

//                 do
//                 {
//                     db.Set<CurrencyRateHistory>().Add(new CurrencyRateHistory
//                     {
//                         ID = DateTime.UtcNow.Ticks,
//                         CurrencyRateID = 1,
//                         Rate = ACTUAL_RATE,
//                         Date = startDate.AddDays(i)
//                     });

//                 } while (++i < dayNum);


//                 await db.SaveChangesAsync();
//             }

//             // Act
//             var result = await srv.GetRateHistoryAsync(
//                 "GBP", // baseCurrency to be transformed to GBX
//                 "EUR",
//                 fromDateUTC,
//                 toDateUTC
//             );

//             // Assert
//             Assert.NotEmpty(result);
//             Assert.Equal(result.First().Value, ACTUAL_RATE * (decimal)FACTOR);
//         }

//         [Fact]
//         public async Task GetRateHistoryAsync_CustomRate_Return_ActualRateMultiplyWithFactor_ForMatched_QuoteCurrency() {
//             // Arrange
//             //serviceScopeFactory.Setup(srv => srv.CreateScope()).Returns
//             var mockCache = new Mock<IDistributedCache>();
//             var factory = new InMemoryDbContextFactory();

//             var dayNum = 10;
//             var startDate = DateTime.UtcNow.AddDays(-dayNum).Date;
//             var i = 0;
//             var fromDateUTC = startDate; // This filter date is not available from datasource.
//             var toDateUTC = (Nullable<DateTime>)null;

//             var currencyRateTransformConfiguration = new CurrencyTransformationConfiguration() {
//                 Enabled = true
//             };

//             var FACTOR = 0.01F;
//             var ACTUAL_RATE = 10M;

//             currencyRateTransformConfiguration.Transforms.Add(
//                 new QuoteCurrencyTransformation("GBP", "GBX", FACTOR)
//             );

//             var serviceProvider = new Mock<IServiceProvider>();
//             serviceProvider
//                 .Setup(x => x.GetService(typeof(IOptions<CurrencyTransformationConfiguration>)))
//                 .Returns(Options.Create(currencyRateTransformConfiguration));

//             var serviceScope = new Mock<IServiceScope>();
//             serviceScope.Setup(srv => srv.ServiceProvider).Returns(serviceProvider.Object);

//             var serviceScopeFactory = new Mock<IServiceScopeFactory>();
//             serviceScopeFactory.Setup(f => f.CreateScope()).Returns(serviceScope.Object);

//             serviceProvider.Setup(x => x.GetService(typeof(IServiceScopeFactory))).Returns(serviceScopeFactory.Object);

//             var srv = new CurrencyRateService(factory, mockCache.Object, null, serviceScopeFactory.Object);
//             serviceProvider
//                 .Setup(x => x.GetService(typeof(ICurrencyRateTransformationStrategy)))
//                 .Returns(srv);

//             using(var db = factory.CreateDbContext() as DefaultSharkDbContext)
//             {
//                 db.Database.EnsureDeleted();
//                 db.Database.EnsureCreated();

//                 db.Set<CurrencyRate>().Add(new CurrencyRate {
//                     Currencies = "EURGBX", // Defines base/quote currencies
//                     Date = DateTime.UtcNow.ToCEST(),
//                     ID = 1,
//                     Rate = 0.5m
//                 });

//                 do
//                 {
//                     db.Set<CurrencyRateHistory>().Add(new CurrencyRateHistory
//                     {
//                         ID = DateTime.UtcNow.Ticks,
//                         CurrencyRateID = 1,
//                         Rate = ACTUAL_RATE,
//                         Date = startDate.AddDays(i)
//                     });

//                 } while (++i < dayNum);


//                 await db.SaveChangesAsync();
//             }

//             // Act
//             var result = await srv.GetRateHistoryAsync(
//                 "EUR",
//                 "GBP", // quoteCurrency to be transformed to GBX
//                 fromDateUTC,
//                 toDateUTC
//             );

//             // Assert
//             Assert.NotEmpty(result);
//             Assert.Equal(result.First().Value, ACTUAL_RATE * (decimal)FACTOR);
//         }
//     }
// }
