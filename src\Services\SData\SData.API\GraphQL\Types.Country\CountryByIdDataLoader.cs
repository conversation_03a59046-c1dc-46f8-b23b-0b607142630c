using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using Euroland.FlipIT.SData.API.Infrastructure.Factories;
using Euroland.FlipIT.Shared.CachingManager;
using GreenDonut;
using HotChocolate.Resolvers;
using HotChocolate.Types;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Country;

public class CountryByIdDataLoader : BatchDataLoader<int, CountryDto>
{
  private readonly ISharkDbContextAbstractFactory _factory;
  private readonly IGraphQLExecutionContext _queryRequestContext;
  private readonly IConfigurableCacheManager _configurableCacheManager;

  public CountryByIdDataLoader(
    ISharkDbContextAbstractFactory factory,
    IGraphQLExecutionContext queryRequestContext,
    IConfigurableCacheManager configurableCacheManager,
    IBatchScheduler batchScheduler,
    DataLoaderOptions? options = null)
    : base(batchScheduler, options)
  {
    _factory = factory ?? throw new ArgumentNullException(nameof(factory));
    _queryRequestContext = queryRequestContext ?? throw new ArgumentNullException(nameof(queryRequestContext));
    _configurableCacheManager = configurableCacheManager ?? throw new ArgumentNullException(nameof(configurableCacheManager));
  }

  protected override async Task<IReadOnlyDictionary<int, CountryDto>> LoadBatchAsync(IReadOnlyList<int> keys, CancellationToken cancellationToken)
  {
    var dtosFromCache = new List<CountryDto>();
    var idsNeedToFetchNew = new List<int>();

    foreach (var id in keys.Distinct())
    {
      var dto = _configurableCacheManager.GetCache<CountryDto, string>(
        CacheKeyHelper.GenerateKeyByPropOfDtoObject<CountryDto, int>(id.ToString(), c => c.Id)
      );
      if (dto != null)
      {
        dtosFromCache.Add(dto);
      }
      else
      {
        idsNeedToFetchNew.Add(id);
      }
    }

    await using var sharkDbContext = await _factory.CreateDbContextAsync(_queryRequestContext.UseRealtime, _queryRequestContext.UseCloud);

    var data = idsNeedToFetchNew.Count > 0
      ? await sharkDbContext.Country
        .AsNoTracking()
        .Where(c => idsNeedToFetchNew.Contains(c.Id))
        .WithAutoMapper()
        .ToProjection<CountryDto>()
        .ToListAsync(cancellationToken)
      : Enumerable.Empty<CountryDto>();

    foreach (var country in data)
    {

      _configurableCacheManager.SetCache(
        country,
        CacheKeyHelper.GenerateKeyByPropOfDtoObject<CountryDto, int>(country.Id.ToString(), c => c.Id)
      );

      dtosFromCache.Add(country);
    }

    return dtosFromCache.ToDictionary(x => x.Id);
  }
}
