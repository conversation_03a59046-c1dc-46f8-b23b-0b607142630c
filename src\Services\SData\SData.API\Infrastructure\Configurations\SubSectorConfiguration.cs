using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Euroland.FlipIT.SData.API.Infrastructure.Configurations;

public class SubSectorConfiguration: IEntityTypeConfiguration<SubSector>
{
  private const string ViewName = "SubSector3";
  public void Configure(EntityTypeBuilder<SubSector> builder)
  {
    builder.ToView(ViewName);
  }
}
