using HotChocolate.Types;

namespace Microsoft.Extensions.DependencyInjection;

public static class AddUseCloudArgumentExtensions
{
  public static IObjectFieldDescriptor AddUseCloudArgument(this IObjectFieldDescriptor descriptor)
  {
    return descriptor.Argument("useCloud", desc =>
      desc
        .Deprecated("Will be removed in the next release")
        .Description("Request application uses Cloud database to populate data")
        .Type<BooleanType>()
        .DefaultValue(null)
    );
  }
}
