
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using Euroland.FlipIT.SData.API.Infrastructure.Factories;
using Euroland.FlipIT.Shared.CachingManager;
using GreenDonut;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Timezone;

public class TimezoneByNameDataLoader : BatchDataLoader<string, TimezoneDto>
{
  private readonly ISharkDbContextAbstractFactory _factory;
  private readonly IGraphQLExecutionContext _queryRequestContext;
  private readonly IConfigurableCacheManager _configurableCacheManager;

  public TimezoneByNameDataLoader(
    ISharkDbContextAbstractFactory factory,
    IGraphQLExecutionContext queryRequestContext,
    IConfigurableCacheManager configurableCacheManager,
    IBatchScheduler batchScheduler,
    DataLoaderOptions? options = null)
    : base(batchScheduler, options)
  {
    _factory = factory ?? throw new ArgumentNullException(nameof(factory));
    _queryRequestContext = queryRequestContext ?? throw new ArgumentNullException(nameof(queryRequestContext));
    _configurableCacheManager = configurableCacheManager ?? throw new ArgumentNullException(nameof(configurableCacheManager));
  }

  protected override async Task<IReadOnlyDictionary<string, TimezoneDto>> LoadBatchAsync(IReadOnlyList<string> keys, CancellationToken cancellationToken)
  {
    var timezoneDTOsFromCache = new List<TimezoneDto>();
    var timezoneIDsNeedToFetchNew = new List<string>();

    foreach (var tname in keys.Distinct())
    {
      var dto = _configurableCacheManager.GetCache<TimezoneDto, string>(
        CacheKeyHelper.GenerateKeyByPropOfDtoObject<TimezoneDto, string>(tname.ToLower(), tz => tz.Name)
      );
      if (dto != null)
      {
        timezoneDTOsFromCache.Add(dto);
      }
      else
      {
        timezoneIDsNeedToFetchNew.Add(tname);
      }
    }

    await using var sharkDbContext = await _factory.CreateDbContextAsync(_queryRequestContext.UseRealtime, _queryRequestContext.UseCloud);

    var data = timezoneIDsNeedToFetchNew.Count > 0
      ? await sharkDbContext.Timezone
        .AsNoTracking()
        .Where(t => timezoneIDsNeedToFetchNew.Contains(t.Name.Trim()))
        .OrderBy(t => t.Id)
        .WithAutoMapper()
        .ToProjection<TimezoneDto>()
        .ToListAsync(cancellationToken)
      : new List<TimezoneDto>();

    foreach (var timezone in data.DistinctBy(t => t.Name))
    {
      timezone.NameIANA = WindowsTimezoneToIANA.Convert(timezone.Name, null, null);
      _configurableCacheManager.SetCache(
        timezone,
        CacheKeyHelper.GenerateKeyByPropOfDtoObject<TimezoneDto, string>(timezone.Name.ToLower(), tz => tz.Name)
      );

      timezoneDTOsFromCache.Add(timezone);
    }

    return timezoneDTOsFromCache.ToDictionary(x => x.Name.ToLower());
  }
}
