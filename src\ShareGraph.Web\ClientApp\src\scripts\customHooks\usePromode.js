
const isIFrame = window.self !== window.top;


export const openCreateAlert = () => {
  console.log("openCreateAlert");
  const component = window.euroland.components.PromodeCreateAlertComponent({
    onConfirm: () => {
      console.log("onConfirm");
    }
  });

  const integrationLayoutPosition = window.xprops
    ? window.xprops.layout.middle
    : "#middleLayout";

  if (isIFrame) {
    component.renderTo(window.parent, integrationLayoutPosition);
  } else {
    let middle = document.getElementById("middleLayout");
    if (!middle) {
      middle = document.createElement("div");
      middle.id = "middleLayout";
      document.body.appendChild(middle);
    }
    component.renderTo(window.parent, integrationLayoutPosition);
  }
};

