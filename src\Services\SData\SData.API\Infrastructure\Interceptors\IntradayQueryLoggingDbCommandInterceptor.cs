using System;
using System.Collections.Concurrent;
using System.Data.Common;
using System.Diagnostics;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Logging;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities;

public class IntradayQueryLoggingDbCommandInterceptor : DbCommandInterceptor
{
  private readonly Regex _intradayReg = new Regex(@"\<intraday_log\>", RegexOptions.IgnoreCase | RegexOptions.Compiled);

  private readonly ILogger<IntradayQueryLoggingDbCommandInterceptor> _logger;

  private readonly ConcurrentDictionary<Guid, Stopwatch> _stopwatches = new();

  public IntradayQueryLoggingDbCommandInterceptor(ILogger<IntradayQueryLoggingDbCommandInterceptor> logger)
  {
    _logger = logger;
  }

  public override InterceptionResult<DbDataReader> ReaderExecuting(DbCommand command, CommandEventData eventData,
    InterceptionResult<DbDataReader> result)
  {
    if (ContainsIntradayTag(command.CommandText))
    {
      StartStopwatch(eventData.CommandId);
      _logger.LogInformation($"[Before Execution] Intraday query detected");
    }

    return base.ReaderExecuting(command, eventData, result);
  }

  public override DbDataReader ReaderExecuted(DbCommand command, CommandExecutedEventData eventData, DbDataReader result)
  {
    if (ContainsIntradayTag(command.CommandText))
    {
      var stopwatch = StopStopwatch(eventData.CommandId);
      if (stopwatch != null)
        _logger.LogInformation($"[After Execution] Intraday query executed in {stopwatch.ElapsedMilliseconds} ms.");
    }

    return base.ReaderExecuted(command, eventData, result);
  }

  public override ValueTask<InterceptionResult<DbDataReader>> ReaderExecutingAsync(DbCommand command,
    CommandEventData eventData, InterceptionResult<DbDataReader> result, CancellationToken cancellationToken = default)
  {
    if (ContainsIntradayTag(command.CommandText))
    {
      StartStopwatch(eventData.CommandId);
      _logger.LogInformation($"[Before Execution] Intraday query detected");
    }

    return base.ReaderExecutingAsync(command, eventData, result, cancellationToken);
  }

  public override ValueTask<DbDataReader> ReaderExecutedAsync(DbCommand command, CommandExecutedEventData eventData,
    DbDataReader result, CancellationToken cancellationToken = default)
  {
    if (ContainsIntradayTag(command.CommandText))
    {
      var stopwatch = StopStopwatch(eventData.CommandId);
      _logger.LogInformation($"[After Execution] Intraday query executed in {stopwatch.ElapsedMilliseconds} ms.");
    }

    return base.ReaderExecutedAsync(command, eventData, result, cancellationToken);
  }

  private bool ContainsIntradayTag(string commandText)
  {
    return _intradayReg.IsMatch(commandText);
  }

  private void StartStopwatch(Guid commandId)
  {
    var stopwatch = new Stopwatch();
    stopwatch.Start();
    _stopwatches[commandId] = stopwatch;
  }

  private Stopwatch? StopStopwatch(Guid commandId)
  {
    if (_stopwatches.TryRemove(commandId, out var stopwatch))
    {
      stopwatch.Stop();
      return stopwatch;
    }

    return null;
  }
}
