export const API_PATHS = {
  SEARCH: '/Search',
  SEARCH_SSE: '/Search/stream',
  SEARCH_HISTORIES: '/Search/histories',
  SEARCH_HISTORY_BY_ID: (historyId: number) => `/Search/histories/${historyId}`,
  USER_PROFILE: '/User/profile-by-companycode',
  FOLLOW_COMPANY: '/User/follow-company',
  COMPANY_INFO: '/Setting/company-configuration'
} as const;

export type ICompanyInfo = {
  companyCode: string;
  logo: string;
  companyName: string;
  primaryColor: string | null;
  industry: string | null;
  timezone: string
};

export interface ICompanyNameResponse {
  id: number;
  companyCode: string;
  language: string;
  name: string;
}

export interface ICompanyInfoResponse {
  data: {
    companyCode: string;
    companyName: ICompanyNameResponse;
    industry: string;
    primaryColor: string | null;
    timezone: string | null;
    defaultLanguage: string;
    enabledLanguages: string[];
    companyHomePage: string | null;
    companyLogo: string | null;
    useLocalTimezone: boolean;
  }
}
