namespace Euroland.FlipIT.ApiGateway
{
    /// <summary>
    /// Represents the setting of a remote graphql schema.
    /// </summary>
    public class WellKnownSchemaSetting
    {
        /// <summary>
        /// Specifies whether the schema is root type. A root type is always started with Query type.
        /// Only one remote schema is set as root type a time. Otherwise, an exception will be throw.
        /// </summary>
        public bool IsRootType { get; set; }
        /// <summary>
        /// The wellknown name of schema.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// The url to get the remote schema. E.g: https://tools.euroland.com/tools/sdata/graphql
        /// </summary>
        public string RemoteUrl { get; set; }
    }
}
