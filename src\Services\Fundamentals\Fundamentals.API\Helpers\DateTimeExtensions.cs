﻿using TimeZoneConverter;

namespace Euroland.FlipIT.SData.API.Helpers
{
    public static class DateTimeExtensions
    {
        // Works on Windows only
        //static TimeZoneInfo _cetTZ = TimeZoneInfo.FindSystemTimeZoneById("Central European Standard Time");

        // Works on Linux only
        //static TimeZoneInfo _cetTZ = TimeZoneInfo.FindSystemTimeZoneById("Europe/Budapest");

        // Retrieves a System.TimeZoneInfo object given a valid Windows or IANA time zone
        // identifier, regardless of which platform the application is running on.
        static readonly TimeZoneInfo _cetTZ = TZConvert.GetTimeZoneInfo("Central European Standard Time");

        /// <summary>
        /// Converts a <see cref="DateTime"/> from CEST to UTC
        /// and convert kind of output <see cref="DateTime"/> to <see cref="DateTimeKind.Utc"/>.
        /// </summary>
        /// <param name="cestDateTime">DateTime in CEST timezone</param>
        /// <returns></returns>
        public static DateTime CestToUtc(this DateTime cestDateTime)
        {
          if (cestDateTime.Kind != DateTimeKind.Unspecified)
            {
                cestDateTime = DateTime.SpecifyKind(cestDateTime, DateTimeKind.Unspecified);
            }
            var utcDate = TimeZoneInfo.ConvertTimeToUtc(cestDateTime, _cetTZ);
            return DateTime.SpecifyKind(utcDate, DateTimeKind.Utc);
        }
    }
}
