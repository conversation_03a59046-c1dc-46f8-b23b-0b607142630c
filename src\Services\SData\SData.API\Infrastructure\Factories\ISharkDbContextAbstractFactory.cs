using System.Threading.Tasks;
using HotChocolate.Resolvers;

namespace Euroland.FlipIT.SData.API.Infrastructure.Factories;

/// <summary>
/// Defines a factory interface for creating instances of <see cref="SharkDbContextBase"/>.
/// </summary>
public interface ISharkDbContextAbstractFactory
{
  /// <summary>
  /// Asynchronously creates a new instance of <see cref="SharkDbContextBase"/> with the specified configuration.
  /// </summary>
  /// <param name="enableRealtime">Indicates whether real-time features should be enabled for the context.</param>
  /// <param name="useCloudDb">Indicates whether to use a cloud-based database.
  /// If <paramref name="enableRealtime"/> is true, useCloudDb will be ignored and cloud DB is picked by default.
  /// </param>
  /// <returns>A task that represents the asynchronous operation. The task result contains the created <see cref="SharkDbContextBase"/> instance.</returns>
  Task<SharkDbContextBase> CreateDbContextAsync(bool enableRealtime, bool useCloudDb);
}
