using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Euroland.FlipIT.SData.API.Infrastructure.Configurations;

public class FcEventTypeConfiguration: IEntityTypeConfiguration<FCEventType>
{
  private const string ViewName = "CS_FCEventTypes";
  public void Configure(EntityTypeBuilder<FCEventType> builder)
  {
    builder.ToView(ViewName);
  }
}
