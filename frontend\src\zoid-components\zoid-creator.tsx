import { DeepPartial, ensureDefined } from "@/helpers";
import { PropDefinition, ZoidComponentOptions } from "@/zoid";
import { FC, useEffect, useMemo, useRef, useState } from "react";
import { merge, snakeCase } from "es-toolkit";
import { XProps } from "@/zoid";
import { searchParams } from "@/config/appSettings";
import { ComponentZoidInstance, ZoidInstanceOptions } from "./zoid-instance";
import { baseUrl } from "@/lib/base-url";
import { joinPath } from "@/lib/join-path";

const __globalZoidInstances = window as unknown as {
  __globalZoidInstances: Map<string, ComponentZoidInstance<object>>;
};
const globalZoidInstances =
  __globalZoidInstances.__globalZoidInstances ?? new Map();
__globalZoidInstances.__globalZoidInstances = globalZoidInstances;

function validateZoidOptions(
  options: ZoidComponentOptions,
  instanceOptions: Partial<ZoidInstanceOptions>
) {
  const opacity = options.template?.styles?.opacity;
  if (opacity && parseFloat(opacity) == 0) {
    const animate = instanceOptions.animate;
    if (animate?.show && !animate.show?.opacity) {
      throw new Error("opacity is 0, but no opacity animation is defined");
    }
  }
}

export function zoidComponentCreator<P extends object>(
  Component: FC<XProps<P>>,
  config: DeepPartial<Omit<ZoidComponentOptions, "props">> & {
    props?: Record<keyof P, PropDefinition>;
  },
  layoutPosition?: ZoidInstanceOptions["layoutPosition"]
) {
  const displayName = snakeCase(
    ensureDefined(Component.displayName ?? Component.name)
  );

  const url = new URL(joinPath(baseUrl, displayName));
  Array.from(searchParams.entries()).forEach(([key, value]) =>
    url.searchParams.append(key, value)
  );

  const options = merge(
    {
      tag: displayName,
      url: url.toString(),
      autoResize: {
        width: false,
        height: false,
        element: "body",
      },
      dimensions: {
        width: "50vw",
        height: "100vh",
      },
      template: {
        name: "dynamic",
        styles: {
          position: "fixed",
          bottom: "0px",
          right: "0px",
          "z-index": "20001",
          opacity: "0",
        },
      },
    } satisfies ZoidComponentOptions,
    config as ZoidComponentOptions
  );

  window.euroland.createComponent(displayName, options);

  function ComponentWrapper() {
    const forceRender = useState([])[1];

    useEffect(() => {
      window.xprops.onProps(() => forceRender([]));
    }, []);
    return <Component {...((window.xprops ?? {}) as unknown as XProps<P>)} />;
  }

  const init = (
    props: P,
    instanceOptions: Partial<Omit<ZoidInstanceOptions, "layoutPosition">> = {}
  ) => {
    const { defaultHidden = true } = instanceOptions;
    let instance = globalZoidInstances.get(displayName);
    if (instance) return instance as ComponentZoidInstance<P>;
    const component = window.euroland.components[displayName](props);
    validateZoidOptions(options, instanceOptions);
    instance = new ComponentZoidInstance<P>(
      component,
      props,
      layoutPosition ? { ...instanceOptions, layoutPosition } : instanceOptions
    );
    instance.render();
    if (defaultHidden) {
      instance.hide();
    } else {
      instance.show();
    }
    globalZoidInstances.set(displayName, instance);
    return instance;
  };

  const use = (
    props: P,
    instanceOptions: Partial<Omit<ZoidInstanceOptions, "layoutPosition">> = {}
  ) => {

    const propsRef = useRef(props);

    propsRef.current = props;

    const instance = useMemo(() => {
      // Ensure that any function prop uses the latest callback reference from propsRef,
      // avoiding issues with stale closures.
      const forwardProps = Object.fromEntries(
        Array.from(Object.entries(props)).map(([key, value]) => {
          if (typeof value === "function")
            return [
              key,
              (...args: any[]) =>
                (propsRef.current as unknown as any)[key](...args),
            ];

          return [key, value];
        })
      ) as P;
      return init(forwardProps, instanceOptions)
    }, []);

    return instance;
  }

  return {
    ComponentWrapper,
    init,
    use,
    path: displayName,
  };
}
