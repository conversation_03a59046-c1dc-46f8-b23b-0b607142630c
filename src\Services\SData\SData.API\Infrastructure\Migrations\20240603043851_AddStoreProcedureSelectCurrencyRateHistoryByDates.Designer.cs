﻿// <auto-generated />
using System;
using Euroland.FlipIT.SData.API.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Euroland.FlipIT.SData.API.Infrastructure.Migrations
{
    [DbContext(typeof(DefaultSharkDbContext))]
    [Migration("20240603043851_AddStoreProcedureSelectCurrencyRateHistoryWithDates")]
    partial class AddStoreProcedureSelectCurrencyRateHistoryWithDates
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.17")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Euroland.FlipIT.SData.API.DTOs.LatestShareTradesDto", b =>
                {
                    b.Property<decimal>("Close")
                        .HasColumnType("money")
                        .HasColumnName("Close");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime")
                        .HasColumnName("Date");

                    b.Property<long?>("Size")
                        .HasColumnType("bigint")
                        .HasColumnName("Size");

                    b.ToTable("LatestShareTrades");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.ActiveFnTradesHistory_RT", b =>
                {
                    b.Property<decimal>("Close")
                        .HasColumnType("decimal(18,4)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("High")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal>("Low")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal>("Open")
                        .HasColumnType("decimal(18,4)");

                    b.Property<long?>("Size")
                        .HasColumnType("bigint")
                        .HasColumnName("Volume");

                    b.ToTable("ActiveFnTradesHistories");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Company", b =>
                {
                    b.Property<string>("CompanyCode")
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("cCode");

                    b.Property<string>("CompanyCountry")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("cCountry");

                    b.Property<short>("CompanyCustomer")
                        .HasColumnType("smallint")
                        .HasColumnName("cCustomer");

                    b.Property<string>("CompanyEMail")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("cEMail");

                    b.Property<string>("CompanyFax")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("cFax");

                    b.Property<string>("CompanyHomePage")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("cHomePage");

                    b.Property<int>("CompanyId")
                        .HasColumnType("int")
                        .HasColumnName("cId");

                    b.Property<short>("CompanyMarket")
                        .HasColumnType("smallint")
                        .HasColumnName("cMarket");

                    b.Property<string>("CompanyName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("cName");

                    b.Property<string>("CompanyTel")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("cTel");

                    b.Property<string>("CompanyTown")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("cTown");

                    b.HasKey("CompanyCode");

                    b.ToTable("Company");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.CompanyNames", b =>
                {
                    b.Property<string>("CompanyCode")
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("cCode")
                        .HasColumnOrder(0);

                    b.Property<string>("CompanyLang")
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("cLang");

                    b.Property<string>("CompanyName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("cName");

                    b.HasKey("CompanyCode", "CompanyLang");

                    b.ToTable("CompanyName");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Currency", b =>
                {
                    b.Property<string>("Code")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int?>("DecimalPlace")
                        .HasColumnType("int");

                    b.Property<bool?>("IsRegionMajor")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte?>("RegionId")
                        .HasColumnType("tinyint");

                    b.Property<string>("Symbol")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TranslationId")
                        .HasColumnType("int");

                    b.HasKey("Code");

                    b.HasIndex("TranslationId")
                        .IsUnique()
                        .HasFilter("[TranslationId] IS NOT NULL");

                    b.ToTable("Currency");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.CurrencyRate", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("cId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Currencies")
                        .IsRequired()
                        .HasColumnType("char(6)")
                        .HasColumnName("cCurr");

                    b.Property<DateTime?>("Date")
                        .HasColumnType("datetime2")
                        .HasColumnName("cDate");

                    b.Property<decimal?>("Rate")
                        .HasColumnType("numeric(21,7)")
                        .HasColumnName("cRate");

                    b.HasKey("ID");

                    b.ToTable("CurrencyRate");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.CurrencyRateHistory", b =>
                {
                    b.Property<long>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("chID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("ID"));

                    b.Property<int>("CurrencyRateID")
                        .HasColumnType("int")
                        .HasColumnName("cId");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2")
                        .HasColumnName("chDate");

                    b.Property<decimal>("Rate")
                        .HasColumnType("numeric(28,10)")
                        .HasColumnName("chRate");

                    b.HasKey("ID");

                    b.HasIndex("CurrencyRateID");

                    b.ToTable("CurrencyRateHistory");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.DailyHistory", b =>
                {
                    b.Property<decimal>("Close")
                        .HasColumnType("decimal(18,4)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("High")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal>("Low")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal>("Open")
                        .HasColumnType("decimal(18,4)");

                    b.Property<long?>("Volume")
                        .HasColumnType("bigint");

                    b.ToTable("DailyHistory");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Dividend", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("rID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("Date")
                        .HasColumnType("datetime2")
                        .HasColumnName("exDate");

                    b.Property<decimal?>("GrossDivAdj")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("InstrumentId")
                        .HasColumnType("int")
                        .HasColumnName("TId");

                    b.HasKey("ID");

                    b.HasIndex("InstrumentId");

                    b.ToTable("Dividends_ExchDataFeed");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.DividendEvt", b =>
                {
                    b.Property<DateTime>("dDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("dDividend")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("dDate");

                    b.ToTable("DividendEvts");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.FCEvent", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("fce_Id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Arabic")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fce_arabic_Event");

                    b.Property<string>("Chinese")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fce_chinese_Event");

                    b.Property<string>("Danish")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fce_danish_Event");

                    b.Property<string>("Dutch")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fce_dutch_Event");

                    b.Property<string>("English")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fce_english_Event");

                    b.Property<string>("Finnish")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fce_finnish_Event");

                    b.Property<string>("French")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fce_french_Event");

                    b.Property<string>("German")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fce_german_Event");

                    b.Property<string>("Icelandic")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fce_icelandic_Event");

                    b.Property<string>("Italian")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fce_italian_Event");

                    b.Property<string>("Japanese")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fce_japanese_Event");

                    b.Property<string>("Korean")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fce_korean_Event");

                    b.Property<string>("Norwegian")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fce_norwegian_Event");

                    b.Property<string>("Polish")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fce_polish_Event");

                    b.Property<string>("Russian")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fce_russian_Event");

                    b.Property<string>("Spanish")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fce_spanish_Event");

                    b.Property<string>("Swedish")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fce_swedish_Event");

                    b.Property<string>("Taiwanese")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fce_taiwanese_Event");

                    b.Property<int>("TypeId")
                        .HasColumnType("int")
                        .HasColumnName("fce_Type");

                    b.Property<string>("Vietnamese")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fce_vietnamese_Event");

                    b.HasKey("Id");

                    b.ToTable("CS_FCEvents");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.FCalendar", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("fc_ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CompanyCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("fc_cCode");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("fc_DateTime");

                    b.Property<int?>("Event")
                        .HasColumnType("int")
                        .HasColumnName("fc_Event");

                    b.Property<int?>("Location")
                        .HasColumnType("int");

                    b.Property<int?>("Timezone")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Event")
                        .IsUnique()
                        .HasFilter("[fc_Event] IS NOT NULL");

                    b.ToTable("CS_FCalendar_V4");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Instrument", b =>
                {
                    b.Property<int>("InstrumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("InstrumentId"));

                    b.Property<DateTime?>("Agentupdate")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("AllTimeHigh")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("AllTimeLow")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("BetaFactor")
                        .HasColumnType("decimal(18,4)");

                    b.Property<double?>("BookValueOfShare")
                        .HasColumnType("float");

                    b.Property<int?>("CompanyID")
                        .HasColumnType("int");

                    b.Property<decimal?>("Correlation")
                        .HasColumnType("decimal(18,4)");

                    b.Property<string>("CurrencyCode")
                        .HasColumnType("nvarchar(450)");

                    b.Property<byte?>("Customer")
                        .HasColumnType("tinyint");

                    b.Property<decimal?>("DPS")
                        .HasColumnType("decimal(18,4)");

                    b.Property<int>("DataSourceID")
                        .HasColumnType("int");

                    b.Property<decimal?>("EPS")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("EurCat")
                        .HasColumnType("int");

                    b.Property<decimal?>("FiveYearsChange")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("5YearsChange");

                    b.Property<decimal?>("High52W")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("52wHigh");

                    b.Property<decimal?>("HighYTD")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("Highest52w")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("52Highest");

                    b.Property<string>("ISIN")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("InstrumentType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("LastRowChange")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ListID")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ListedFrom")
                        .HasColumnType("datetime2");

                    b.Property<int?>("LotSize")
                        .HasColumnType("int");

                    b.Property<decimal?>("Low52W")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("52wLow");

                    b.Property<decimal?>("LowYTD")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("Lowest52w")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("52Lowest");

                    b.Property<bool?>("MainIndex")
                        .HasColumnType("bit");

                    b.Property<int?>("MarCat")
                        .HasColumnType("int");

                    b.Property<decimal?>("MarketCapEUR")
                        .HasColumnType("decimal(18,4)");

                    b.Property<short>("MarketID")
                        .HasColumnType("smallint");

                    b.Property<string>("MarketSpecSign")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Month")
                        .HasColumnType("decimal(18,2)");

                    b.Property<double?>("NetInComeGrowth")
                        .HasColumnType("float");

                    b.Property<decimal?>("NetIncome")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("NoShares")
                        .HasColumnType("decimal(19,0)");

                    b.Property<DateTime?>("NumSharesDate")
                        .HasColumnType("datetime2");

                    b.Property<long?>("NumberOfUnlistedShares")
                        .HasColumnType("bigint");

                    b.Property<double?>("PayoutRatio")
                        .HasColumnType("float");

                    b.Property<decimal?>("Percent52W")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("52wChange");

                    b.Property<decimal?>("PrevMid")
                        .HasColumnType("decimal(18,4)");

                    b.Property<byte?>("PrimaryMarket")
                        .HasColumnType("tinyint");

                    b.Property<int?>("RegionID")
                        .HasColumnType("int");

                    b.Property<string>("RicCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("SPS")
                        .HasColumnType("float");

                    b.Property<string>("ShareName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Name");

                    b.Property<double?>("SplitRatio")
                        .HasColumnType("float");

                    b.Property<decimal?>("ThreeMonthChange")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("3MonthChange");

                    b.Property<decimal?>("ThreeMonthHigh")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("3MonthHigh");

                    b.Property<decimal?>("ThreeMonthLow")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("3MonthLow");

                    b.Property<string>("Ticker")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<short?>("TokyoEurope")
                        .HasColumnType("smallint");

                    b.Property<decimal?>("TotalMarketCap")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("Turnover")
                        .HasColumnType("decimal(18,4)");

                    b.Property<double?>("TurnoverGrowth")
                        .HasColumnType("float");

                    b.Property<decimal?>("TwoWeek")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("2Week");

                    b.Property<bool>("Visible")
                        .HasColumnType("bit");

                    b.Property<decimal?>("Volatility")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("VolumeTurnover")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("VolumeTurnoverUSD")
                        .HasColumnType("decimal(18,4)");

                    b.Property<string>("WKN")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Week")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("YTD")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("YahooSymbol")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("InstrumentId");

                    b.HasIndex("CurrencyCode")
                        .IsUnique()
                        .HasFilter("[CurrencyCode] IS NOT NULL");

                    b.HasIndex("ListID");

                    b.HasIndex("MarketID")
                        .IsUnique();

                    b.ToTable("Instrument");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.InstrumentDailyData", b =>
                {
                    b.Property<long>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("ID"));

                    b.Property<decimal>("Close")
                        .HasColumnType("decimal(19,4)")
                        .HasColumnName("hClose");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2")
                        .HasColumnName("hDate");

                    b.Property<int>("InstrumentID")
                        .HasColumnType("int")
                        .HasColumnName("InstrumentID");

                    b.Property<long?>("Volume")
                        .HasColumnType("bigint")
                        .HasColumnName("hSize");

                    b.HasKey("ID");

                    b.ToTable("daily_history", (string)null);
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.InstrumentHistory", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<decimal>("Close")
                        .HasColumnType("decimal(18,4)");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("datetime")
                        .HasColumnName("Date");

                    b.Property<decimal?>("High")
                        .HasColumnType("decimal(18,4)");

                    b.Property<int>("InstrumentId")
                        .HasColumnType("int");

                    b.Property<decimal?>("Low")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("Open")
                        .HasColumnType("decimal(18,4)");

                    b.Property<long?>("Volume")
                        .HasColumnType("bigint");

                    b.HasKey("ID");

                    b.HasIndex("InstrumentId");

                    b.ToTable("InstrumentHistory");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.InstrumentPrice", b =>
                {
                    b.Property<int>("InstrumentId")
                        .HasColumnType("int");

                    b.Property<decimal?>("Ask")
                        .HasColumnType("decimal(18,4)");

                    b.Property<int?>("AskSize")
                        .HasColumnType("int");

                    b.Property<decimal?>("Bid")
                        .HasColumnType("decimal(18,4)");

                    b.Property<int?>("BidSize")
                        .HasColumnType("int");

                    b.Property<decimal?>("Change")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("ChangePercentage")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("High")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("Last")
                        .HasColumnType("decimal(18,4)");

                    b.Property<DateTime?>("LastRowChange")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("LastUpdatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("Date");

                    b.Property<decimal?>("Low")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("Mid")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("OfficialClose")
                        .HasColumnType("decimal(18,4)");

                    b.Property<DateTime?>("OfficialCloseDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("Open")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("PrevClose")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("TodayTurnover")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("VWAP")
                        .HasColumnType("decimal(18,4)");

                    b.Property<long?>("Volume")
                        .HasColumnType("bigint");

                    b.HasKey("InstrumentId");

                    b.ToTable("InstrumentPrice", (string)null);
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.List", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Arabic")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Chinese")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Danish")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Dutch")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Finnish")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("French")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("German")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Icelandic")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Italian")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Japanese")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Korean")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ListName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("List");

                    b.Property<byte?>("MarketID")
                        .HasColumnType("tinyint");

                    b.Property<string>("Norwegian")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Polish")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Russian")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Spanish")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Swedish")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Taiwanese")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Vietnamese")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.ToTable("List");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Market", b =>
                {
                    b.Property<short>("MarketNumber")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("smallint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<short>("MarketNumber"));

                    b.Property<bool>("BusinessDaysStoT")
                        .HasColumnType("bit");

                    b.Property<int>("CityId")
                        .HasColumnType("int");

                    b.Property<string>("DataSource")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Delay")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MarketAbbreviation")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Abbreviation");

                    b.Property<string>("MarketCloseTimeLocal")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MarketOpenTimeLocal")
                        .HasColumnType("nvarchar(max)");

                    b.Property<short>("TimeDiff")
                        .HasColumnType("smallint");

                    b.Property<string>("TimeZone")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TimezoneID")
                        .HasColumnType("int");

                    b.Property<int>("TranslationId")
                        .HasColumnType("int");

                    b.HasKey("MarketNumber");

                    b.HasIndex("TranslationId")
                        .IsUnique();

                    b.ToTable("Market");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.MarketDepth", b =>
                {
                    b.Property<DateTime>("FiRowUpdated")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("Fid_Best_Ask_1")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("Fid_Best_Ask_10")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Fid_Best_Ask_10_Size")
                        .HasColumnType("int");

                    b.Property<int?>("Fid_Best_Ask_1_Size")
                        .HasColumnType("int");

                    b.Property<decimal?>("Fid_Best_Ask_2")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Fid_Best_Ask_2_Size")
                        .HasColumnType("int");

                    b.Property<decimal?>("Fid_Best_Ask_3")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Fid_Best_Ask_3_Size")
                        .HasColumnType("int");

                    b.Property<decimal?>("Fid_Best_Ask_4")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Fid_Best_Ask_4_Size")
                        .HasColumnType("int");

                    b.Property<decimal?>("Fid_Best_Ask_5")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Fid_Best_Ask_5_Size")
                        .HasColumnType("int");

                    b.Property<decimal?>("Fid_Best_Ask_6")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Fid_Best_Ask_6_Size")
                        .HasColumnType("int");

                    b.Property<decimal?>("Fid_Best_Ask_7")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Fid_Best_Ask_7_Size")
                        .HasColumnType("int");

                    b.Property<decimal?>("Fid_Best_Ask_8")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Fid_Best_Ask_8_Size")
                        .HasColumnType("int");

                    b.Property<decimal?>("Fid_Best_Ask_9")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Fid_Best_Ask_9_Size")
                        .HasColumnType("int");

                    b.Property<decimal?>("Fid_Best_Bid_1")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("Fid_Best_Bid_10")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Fid_Best_Bid_10_Size")
                        .HasColumnType("int");

                    b.Property<int?>("Fid_Best_Bid_1_Size")
                        .HasColumnType("int");

                    b.Property<decimal?>("Fid_Best_Bid_2")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Fid_Best_Bid_2_Size")
                        .HasColumnType("int");

                    b.Property<decimal?>("Fid_Best_Bid_3")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Fid_Best_Bid_3_Size")
                        .HasColumnType("int");

                    b.Property<decimal?>("Fid_Best_Bid_4")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Fid_Best_Bid_4_Size")
                        .HasColumnType("int");

                    b.Property<decimal?>("Fid_Best_Bid_5")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Fid_Best_Bid_5_Size")
                        .HasColumnType("int");

                    b.Property<decimal?>("Fid_Best_Bid_6")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Fid_Best_Bid_6_Size")
                        .HasColumnType("int");

                    b.Property<decimal?>("Fid_Best_Bid_7")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Fid_Best_Bid_7_Size")
                        .HasColumnType("int");

                    b.Property<decimal?>("Fid_Best_Bid_8")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Fid_Best_Bid_8_Size")
                        .HasColumnType("int");

                    b.Property<decimal?>("Fid_Best_Bid_9")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Fid_Best_Bid_9_Size")
                        .HasColumnType("int");

                    b.Property<int>("TId")
                        .HasColumnType("int");

                    b.ToTable("MarketDepth");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Splits", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<DateTime>("EffectiveDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ISIN")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("InsertedTime")
                        .HasColumnType("datetime2");

                    b.Property<byte>("Market")
                        .HasColumnType("tinyint");

                    b.Property<string>("Ratio")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SplitType")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.ToTable("Splits");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Translation", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("AR")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CA")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CN")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DE")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DK")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EN")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ES")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ET")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FI")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FO")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FR")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HE")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IE")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IT")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("JP")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("KR")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("KU")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LT")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NL")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NO")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PL")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PT")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RO")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RU")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SV")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TW")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VI")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.ToTable("Translation");
                });

            modelBuilder.Entity("IceTradesHistory_RT", b =>
                {
                    b.Property<decimal>("Close")
                        .HasColumnType("decimal(18,4)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("High")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal>("Low")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal>("Open")
                        .HasColumnType("decimal(18,4)");

                    b.Property<long?>("Size")
                        .HasColumnType("bigint")
                        .HasColumnName("Volume");

                    b.ToTable("IceTradesHistories");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.CompanyNames", b =>
                {
                    b.HasOne("Euroland.FlipIT.SData.API.Infrastructure.Entities.Company", "Company")
                        .WithMany("CompanyNames")
                        .HasForeignKey("CompanyCode")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Currency", b =>
                {
                    b.HasOne("Euroland.FlipIT.SData.API.Infrastructure.Entities.Translation", "Translation")
                        .WithOne("Currency")
                        .HasForeignKey("Euroland.FlipIT.SData.API.Infrastructure.Entities.Currency", "TranslationId");

                    b.Navigation("Translation");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.CurrencyRateHistory", b =>
                {
                    b.HasOne("Euroland.FlipIT.SData.API.Infrastructure.Entities.CurrencyRate", "CurrencyRate")
                        .WithMany()
                        .HasForeignKey("CurrencyRateID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CurrencyRate");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Dividend", b =>
                {
                    b.HasOne("Euroland.FlipIT.SData.API.Infrastructure.Entities.Instrument", "Instrument")
                        .WithMany("DividendEvents")
                        .HasForeignKey("InstrumentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Instrument");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.FCalendar", b =>
                {
                    b.HasOne("Euroland.FlipIT.SData.API.Infrastructure.Entities.FCEvent", "FCEvent")
                        .WithOne("FCalendar")
                        .HasForeignKey("Euroland.FlipIT.SData.API.Infrastructure.Entities.FCalendar", "Event");

                    b.Navigation("FCEvent");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Instrument", b =>
                {
                    b.HasOne("Euroland.FlipIT.SData.API.Infrastructure.Entities.Currency", "Currency")
                        .WithOne("Instrument")
                        .HasForeignKey("Euroland.FlipIT.SData.API.Infrastructure.Entities.Instrument", "CurrencyCode");

                    b.HasOne("Euroland.FlipIT.SData.API.Infrastructure.Entities.List", "List")
                        .WithMany()
                        .HasForeignKey("ListID");

                    b.HasOne("Euroland.FlipIT.SData.API.Infrastructure.Entities.Market", "Market")
                        .WithOne("Instrument")
                        .HasForeignKey("Euroland.FlipIT.SData.API.Infrastructure.Entities.Instrument", "MarketID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Currency");

                    b.Navigation("List");

                    b.Navigation("Market");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.InstrumentHistory", b =>
                {
                    b.HasOne("Euroland.FlipIT.SData.API.Infrastructure.Entities.Instrument", "Instrument")
                        .WithMany("InstrumentHistories")
                        .HasForeignKey("InstrumentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Instrument");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.InstrumentPrice", b =>
                {
                    b.HasOne("Euroland.FlipIT.SData.API.Infrastructure.Entities.Instrument", null)
                        .WithOne("InstrumentPrice")
                        .HasForeignKey("Euroland.FlipIT.SData.API.Infrastructure.Entities.InstrumentPrice", "InstrumentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Market", b =>
                {
                    b.HasOne("Euroland.FlipIT.SData.API.Infrastructure.Entities.Translation", "Translation")
                        .WithOne("Market")
                        .HasForeignKey("Euroland.FlipIT.SData.API.Infrastructure.Entities.Market", "TranslationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Translation");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Company", b =>
                {
                    b.Navigation("CompanyNames");
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Currency", b =>
                {
                    b.Navigation("Instrument")
                        .IsRequired();
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.FCEvent", b =>
                {
                    b.Navigation("FCalendar")
                        .IsRequired();
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Instrument", b =>
                {
                    b.Navigation("DividendEvents");

                    b.Navigation("InstrumentHistories");

                    b.Navigation("InstrumentPrice")
                        .IsRequired();
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Market", b =>
                {
                    b.Navigation("Instrument")
                        .IsRequired();
                });

            modelBuilder.Entity("Euroland.FlipIT.SData.API.Infrastructure.Entities.Translation", b =>
                {
                    b.Navigation("Currency")
                        .IsRequired();

                    b.Navigation("Market")
                        .IsRequired();
                });
#pragma warning restore 612, 618
        }
    }
}
