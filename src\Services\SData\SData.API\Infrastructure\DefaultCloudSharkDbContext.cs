using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.SData.API.Infrastructure;

public class DefaultCloudSharkDbContext : SharkDbContextBase
{
  public DefaultCloudSharkDbContext(DbContextOptions<DefaultCloudSharkDbContext> options) : base(options)
  {

  }

  protected override void ConfigureModel(ModelBuilder modelBuilder)
  {
    var dailyHistoryEntityBuilder = modelBuilder.Entity<InstrumentDailyData>();

    dailyHistoryEntityBuilder.ToTable("daily_history");
    dailyHistoryEntityBuilder.Property(c => c.ID).HasColumnName("hId");
    dailyHistoryEntityBuilder.Property(c => c.InstrumentID).HasColumnName("InstrumentID");
    dailyHistoryEntityBuilder.Property(c => c.Date).HasColumnName("hDate");
    dailyHistoryEntityBuilder.Property(c => c.Close).HasColumnName("hClose").HasColumnType("decimal(19,4)");
    dailyHistoryEntityBuilder.Property(c => c.Volume).HasColumnName("hSize");

    modelBuilder.Entity<InstrumentPrice>()
        .ToTable("InstrumentPrice");
  }
}
