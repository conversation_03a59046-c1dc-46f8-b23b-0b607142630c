using AutoMapper;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Helpers;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;

namespace Euroland.FlipIT.SData.API.AutoMapper.Profiles;

public class SubSectorMapperProfile: Profile
{
  public SubSectorMapperProfile()
  {
    var cultureName = "en-gb";

    CreateMap<SubSector, SubSectorDto>()
      .ForMember(dto => dto.MarCat, conf =>
        conf.MapFrom(ol => (
          cultureName.IsCulture("en-us") ? ol.Description :
          cultureName.IsCulture("fr-fr") ? ol.French :
          cultureName.IsCulture("ar-sa") ? ol.Arabic :
          cultureName.IsCulture("ar-ae") ? ol.Arabic :
          cultureName.IsCulture("fi-fi") ? ol.Finnish :
          cultureName.IsCulture("sv-se") ? ol.Swedish :
          cultureName.IsCulture("de-de") ? ol.German :
          cultureName.IsCulture("es-mx") ? ol.Spanish :
          cultureName.IsCulture("it-it") ? ol.Italian :
          cultureName.IsCulture("nl-nl") ? ol.Dutch :
          cultureName.IsCulture("ru-ru") ? ol.Russian :
          cultureName.IsCulture("pl-pl") ? ol.Polish :
          cultureName.IsCulture("zh-tw") ? ol.Taiwanese :
          cultureName.IsCulture("zh-cn") ? ol.Chinese :
          cultureName.IsCulture("ko-kr") ? ol.Korean :
          cultureName.IsCulture("da-dk") ? ol.Danish :
          cultureName.IsCulture("is-is") ? ol.Icelandic :
          cultureName.IsCulture("vi-vn") ? ol.Vietnamese :
          cultureName.IsCulture("ja-jp") ? ol.Japanese :
          ol.Description) ?? ol.Description));
  }
}
