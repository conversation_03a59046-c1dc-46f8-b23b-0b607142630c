using Euroland.FlipIT.SData.API.Infrastructure.Entities.Timescale;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Euroland.FlipIT.SData.API.Infrastructure.Configurations.Timescale;

public class CurrencyRateTimescaleConfiguration : IEntityTypeConfiguration<CurrencyRateTimescale>
{
    public void Configure(EntityTypeBuilder<CurrencyRateTimescale> builder)
    {
        builder.ToTable("currency_rates");

        builder.<PERSON><PERSON><PERSON>(cr => cr.Id);

        builder.Property(cr => cr.Id)
            .HasColumnName("id")
            .IsRequired();

        builder.Property(cr => cr.Currencies)
            .HasColumnName("currencies")
            .HasColumnType("char(6)")
            .IsRequired();

        builder.Property(cr => cr.Rate)
            .HasColumnName("rate")
            .HasColumnType("numeric(21,7)")
            .IsRequired(false);

        builder.Property(cr => cr.Date)
            .HasColumnName("date")
            .IsRequired();
    }
}
