using HotChocolate.Resolvers;
using Microsoft.Extensions.Logging;

namespace Microsoft.Extensions.DependencyInjection;

public static class CreateLoggerIResolverContextExtensions
{
  public static ILogger CreateLogger(this IResolverContext resolverContext)
  {
    var loggerFactory = resolverContext.RequestServices.GetRequiredService<ILoggerFactory>();
    return loggerFactory.CreateLogger("GraphQLCustomLogger");
  }

  public static ILogger CreateLogger(this IResolverContext resolverContext, string categoryName)
  {
    var loggerFactory = resolverContext.RequestServices.GetRequiredService<ILoggerFactory>();
    return loggerFactory.CreateLogger(categoryName);
  }

  public static ILogger<T> CreateLogger<T>(this IResolverContext resolverContext)
  {
    var loggerFactory = resolverContext.RequestServices.GetRequiredService<ILoggerFactory>();
    return loggerFactory.CreateLogger<T>();
  }
}
