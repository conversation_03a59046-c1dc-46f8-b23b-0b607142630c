using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.Fundamental.API.Persistance.FinCalender;

[Table("EventType")]
public class FinancialEventType
{
  [Key]
  public int Id { get; set; }
  public string CompanyCode { get; set; }
  public byte Order { get; set; }
  public bool IsActive { get; set; }
  public bool IsDeleted { get; set; }
  public bool IsCustom { get; set; }
  [Column("OLD_ID")]
  public int? OldId { get; set; }
}
