import { FC, useEffect } from "react";
import { toast } from "sonner";
import { ToastContent } from "./ToastContent";
import { FOLLOWING_STATUS } from "@/helpers/constants";
import { i18n } from "@euroland/libs";

interface ConfirmFollowProps {
  onFollow: () => void;
  userName: string;
  followStatus: FOLLOWING_STATUS;
}

export const ConfirmFollow: FC<ConfirmFollowProps> = ({ onFollow, userName }) => {
  useEffect(() => {
    const toastId = toast(
      <ToastContent
        showIcon={false}
        text={
          <div>
            <span dangerouslySetInnerHTML={{__html: i18n.translate("notification.welcome", { userName: `<span class="capitalize font-medium">${userName}</span>` })}}></span>
            {" "}
            <button
              className="px-2 py-px border border-blue-500 text-blue-500 rounded hover:bg-blue-500 hover:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition duration-200"
              onClick={onFollow}
            >
              {i18n.translate("notification.follow")}
            </button>
          </div>
        }
        handleClose={() => toast.dismiss(toastId)}
      />,
      {
        duration: 50000,
        className:"wise-notification-bubble"
      }
    );

    return () => {
      toast.dismiss(toastId);
    };
  }, [userName, onFollow]);

  return null;
}; 