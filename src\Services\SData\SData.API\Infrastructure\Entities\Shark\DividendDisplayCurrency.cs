using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
  [Table("Dividends_ExchDataFeed_DisplayCurrency")]
  public class DividendDisplayCurrency
  {
    [Key]
    public int ID { get; set; }
    public int InstrumentId { get; set; }
    public string DisplayCurrency { get; set; }
    public int PriorityOrder { get; set; }
  }
}
