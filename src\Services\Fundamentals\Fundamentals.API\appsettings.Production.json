{"Serilog": {"Using": ["Serilog.Sinks.Syslog"], "MinimumLevel": "Warning", "WriteTo": [{"Name": "UdpSyslog", "Args": {"host": "*************", "port": "514", "appName": "FlipIT.Fundamentals.API", "restrictedToMinimumLevel": "Warning", "outputTemplate": "[{Timestamp:HH:mm:ss}] {Message:lj}{NewLine}{Exception}"}}], "Properties": {"Application": "FlipIT.Fundamentals.API"}}, "ConnectionStrings": {"AnalystEstimatesDb": "Server=************;Database=AnalystEstimates;User ID=ushark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=Fundamentals.API;"}, "EarningEstimateApiUrl": "http://**************:3003"}