import { FC, useEffect } from "react";
import { Toaster } from "@/components/ui/sonner";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";
import { i18n } from "@euroland/libs";
import { FOLLOWING_STATUS } from "@/helpers/constants";
import { zoidComponentCreator } from "@/zoid-components";
import { appSettings } from "@/config/appSettings";
import clsx from "clsx";

import { ToastContent } from "./components/ToastContent";
import { ConfirmFollow } from "./components/ConfirmFollow";
import { AlertContent } from "./components/AlertContent";

interface AlertWidgetProps {
  followStatus: FOLLOWING_STATUS;
  changeFollowStatus: (status: FOLLOWING_STATUS) => void;
  isLogout: boolean;
  userName: string
}

const AlertWidget: FC<AlertWidgetProps> = ({
  followStatus,
  changeFollowStatus,
  isLogout,
  userName
}) => {

  useEffect(() => {
    if (!isLogout) return;
    const newNotification = {
      key: uuidv4(),
      type: "logout",
      text: i18n.translate("notification.logout"),
    };
    const toastId = toast(
      <ToastContent
        type={newNotification.type}
        text={newNotification.text}
        handleClose={() => toast.dismiss(toastId)}
      />,
      {
        className: clsx("wise-notification-bubble wise-notification-info"),
      }
    );
  }, [isLogout]);

  return (
    <div className="wise-notifications-container">
      <Toaster position="bottom-center" offset={{ bottom: 0 }} />
      {userName ? (
        followStatus === FOLLOWING_STATUS.UN_FOLLOW ? (
          <ConfirmFollow
            followStatus={followStatus}
            userName={userName}
            onFollow={() => {
              console.log("following");
              changeFollowStatus(FOLLOWING_STATUS.FOLLOWING);
            }}
          />
        ) : (
          <AlertContent />
        )
      ) : null}
    </div>
  );
};

export const ZoidAlertWidget = zoidComponentCreator(AlertWidget, {
  dimensions: {
    width: "340px",
    height: "50vh",
  },
  autoResize: {
    width: false,
    height: false,
    element: "body",
  },
  template: {
    name: "dynamic",
    backdrop: false,
    styles: {
      position: "fixed",
      bottom: "100px",
      [appSettings.isRTL ? "left" : "right"]: "20px",
      maxWidth: "344px",
      maxHeight: "50vh",
      "z-index": "19999",
      opacity: "0",
    },
  },
  props: {
    changeFollowStatus: {
      type: "function",
      required: true,
    },
    followStatus: {
      type: "number",
      required: true,
    },
    isLogout: {
      type: "boolean",
      required: true,
    },
    userName: {
      type: 'string'
    }
  },
});

export default AlertWidget;
