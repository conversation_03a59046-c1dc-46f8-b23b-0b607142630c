using System;
using Euroland.FlipIT.SData.API.DTOs.Deprecated;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.SData.API.Infrastructure;

public interface ISharkDbContext: IAsyncDisposable, IDisposable
{
    public DbSet<ActiveFnTradesHistory_RT> ActiveFnTradesHistories { get; set; }
    public DbSet<IceTradesHistory_RT> IceTradesHistories { get; set; }

    // TODO: Why DTO comes here
    public DbSet<LatestShareTradesDto> LatestShareTrades { get; set; }
    public DbSet<Entities.Instrument> Instrument { get; set; }
    public DbSet<InstrumentPrice> InstrumentPrice { get; set; }
    public DbSet<Market> Market { get; set; }
    public DbSet<Timezone> Timezone { get; set; }
    public DbSet<City> City { get; set; }
    public DbSet<Country> Country { get; set; }
    public DbSet<MarketDepth> MarketDepth { get; set; }
    public DbSet<Translation> Translation { get; set; }
    public DbSet<List> List { get; set; }
    public DbSet<SubSector> SubSector { get; set; }
    public DbSet<InstrumentHistory> InstrumentHistory { get; set; }
    public DbSet<Company> Company { get; set; }
    public DbSet<CompanyNames> CompanyNames { get; set; }
    public DbSet<Dividend> Dividends { get; set; }
    public DbSet<InstrumentDailyData> InstrumentDailyData { get; set; }

    // from stored procedures
    public DbSet<DailyHistory> DailyHistory { get; set; }
    public DbSet<DividendEvt> DividendEvts { get; set; }

    public DbSet<FCEvent> FCEvents { get; set; }
    public DbSet<FCalendar> FCalendars { get; set; }
    public DbSet<Entities.Currency> Currency { get; set; }
    public DbSet<Splits> Splits { get; set; }

    public DbSet<Entities.CurrencyRate> CurrencyRates { get; set; }
    public DbSet<CurrencyRateHistory> CurrencyRateHistories { get; set; }
    public DbSet<CustomerType> CustomerType { get; set; }
}
