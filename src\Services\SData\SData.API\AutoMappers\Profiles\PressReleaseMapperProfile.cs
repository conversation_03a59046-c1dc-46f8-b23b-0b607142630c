﻿using AutoMapper;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;

namespace Euroland.FlipIT.SData.API.AutoMapper.Profiles
{
  public class PressReleaseMapperProfile : Profile
  {
    public PressReleaseMapperProfile()
    {
      CreateMap<MessageType, MessageTypeDto>();

      CreateMap<PressRelease, PressReleaseDto>(MemberList.Destination)
        .ForMember(
          dto => dto.Date,
          conf => conf.MapFrom(ol => ol.DateTime.Date.ToString("yyyy-MM-dd"))
        )
        .ForMember(p => p.MessageType, cfg => cfg.MapFrom(c => c.MessageType));
    }
  }
}
