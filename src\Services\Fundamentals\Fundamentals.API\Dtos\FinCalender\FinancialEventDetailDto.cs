using Euroland.FlipIT.Fundamental.API.Dtos.Common;
using Euroland.FlipIT.SData.API.Helpers;

namespace Euroland.FlipIT.Fundamental.API.Dtos.FinCalender;

public class FinancialEventDetailDto: IDtoObject
{
  #region Common

  public int EventId { get; set; }
  public string CompanyCode { get; set; }
  public int EventTypeId { get; set; }

  // public byte DateTypeId { get; set; }

  private DateTime _startDate;
  public DateTime StartDate
  {
    get
    {
      return _startDate;
    }
    set
    {
      if(value != null)
      {
        _startDate = value.CestToUtc();
      }
    }
  }

  private DateTime? _endDate;
  public DateTime? EndDate
  {
    get
    {
      return _endDate;
    }
    set
    {
      if(value != null)
      {
        _endDate = value.Value.CestToUtc();
      }
    }
  }

  public bool IsAllDayEvent { get; set; }
  // public byte? StatusId { get; set; }
  public bool IsDeleted { get; set; }
  // public int? OldId { get; set; }
  public DateTime? LastUpdated { get; set; }
  public bool IsHighlighted { get; set; }

  #endregion

  #region Detail

  public int EventDetailId { get; set; }
  public int LanguageId { get; set; }
  public string Title { get; set; }
  public string? Description { get; set; }
  public string? Location { get; set; }
  public string? LinkUrl { get; set; }
  public string? LinkDescription { get; set; }

  #endregion
}
