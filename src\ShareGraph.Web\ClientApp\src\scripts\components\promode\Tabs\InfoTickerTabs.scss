.InfoTickerTabs {
  width: 100%;
  height: 100%;

  &__content {
    h3 {
      margin: 0 0 20px 0;
      color: #333333;
      font-size: 18px;
      font-weight: 600;
    }
  }

  &__editor {
    .InfoTickerTabs__code-editor {
      width: 100%;
      min-height: 200px;
      padding: 12px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-size: 14px;
      resize: vertical;
      background-color: #fafafa;

      &:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
      }
    }

    .InfoTickerTabs__editor-actions {
      margin-top: 12px;
      display: flex;
      gap: 8px;
    }
  }

  &__tester {
    .InfoTickerTabs__tester-settings {
      margin-bottom: 20px;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .InfoTickerTabs__setting-group {
      display: flex;
      flex-direction: column;
      gap: 6px;

      label {
        font-weight: 500;
        color: #333333;
        font-size: 14px;
      }
    }
  }

  &__replay {
    .InfoTickerTabs__replay-controls {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
      flex-wrap: wrap;
    }

    .InfoTickerTabs__replay-info {
      p {
        margin: 4px 0;
        color: #666666;
        font-size: 14px;
      }
    }
  }

  &__trading {
    .InfoTickerTabs__order-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .InfoTickerTabs__order-row {
      display: flex;
      flex-direction: column;
      gap: 6px;

      label {
        font-weight: 500;
        color: #333333;
        font-size: 14px;
      }
    }

    .InfoTickerTabs__order-actions {
      display: flex;
      gap: 12px;
      margin-top: 8px;
    }
  }

  &__input,
  &__select {
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    background-color: #ffffff;

    &:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    &::placeholder {
      color: #999999;
    }
  }

  &__btn {
    padding: 8px 16px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #ffffff;
    color: #333333;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f8f8f8;
      border-color: #d0d0d0;
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    &--primary {
      background-color: #007bff;
      border-color: #007bff;
      color: #ffffff;

      &:hover {
        background-color: #0056b3;
        border-color: #0056b3;
      }
    }

    &--buy {
      background-color: #28a745;
      border-color: #28a745;
      color: #ffffff;

      &:hover {
        background-color: #1e7e34;
        border-color: #1e7e34;
      }
    }

    &--sell {
      background-color: #dc3545;
      border-color: #dc3545;
      color: #ffffff;

      &:hover {
        background-color: #c82333;
        border-color: #c82333;
      }
    }
  }
} 