import { AnimateOptions, ZoidComponentInstance } from "@/zoid";
import { cloneDeep, merge } from "es-toolkit";
import { isMatch } from 'es-toolkit/compat'
export type ZoidInstanceOptions = {
  defaultHidden?: boolean;
  layoutPosition: keyof typeof window.xprops.layout;
  // If not provided, the component will be animated with the default animate options
  animate: {
    close: AnimateOptions;
    show: AnimateOptions;
  };
};

export const defaultAnimateOptions: ZoidInstanceOptions = {
  layoutPosition: 'top',
  animate: {
    close: {
      duration: 0,
      opacity: [1, 0],
    },
    show: {
      duration: 0,
      opacity: [0, 1],
    },
  },
};

export class ComponentZoidInstance<P extends object> {
  public displayed: Promise<boolean>;
  public rendered: Promise<boolean>; // Promise that resolves once the zoid component has fully rendered, ensuring that animations start only after rendering is complete.
  public isShown: boolean | undefined;
  protected options: ZoidInstanceOptions;

  constructor(
    public component: ZoidComponentInstance<P>,
    protected props: P,
    options?: Partial<ZoidInstanceOptions>
  ) {
    this.options = merge(cloneDeep(defaultAnimateOptions), options ?? {});

    this.rendered = new Promise((resolve) => {
      component.event.on(window.euroland.EVENT.RENDERED, () => {
        setTimeout(() => {
          resolve(true);
        }, 10);
      });
    });

    this.displayed = new Promise((resolve) => {
      component.event.once(window.euroland.EVENT.DISPLAY, () => {
        resolve(true);
      });
    });
  }
  render() {
    return this.component.renderTo(window.parent, window.xprops.layout[this.options.layoutPosition]);
  }

  protected isAnimating = 0;

  async animate(rest: AnimateOptions) {
    try {
      this.isAnimating++;
      await this.component.event.trigger(window.euroland.EVENT.ANIMATE, rest);
    } finally {
      this.isAnimating--;
    }
  }

  async hide() {
    if (this.isShown === false) return;
    this.isShown = false;

    await this.animate(this.options.animate.close);
    await this.component.hide();
  }

  async show(autoFocus = false) {
    if (this.isShown === true) return;
    this.isShown = true;

    await this.component.show();
    await this.rendered;

    await this.animate(this.options.animate.show);
    if (autoFocus) {
      await this.component.focus();
    }
  }

  updateProps(props: Partial<P>) {
    if (isMatch(this.props, props)) return;
    
    const mergeProps = {...this.props, ...props}

    if (import.meta.env.DEV) {
      Array.from(Object.entries(mergeProps)).forEach(([keyBy, value]) => {
        if(value === undefined) console.warn(`Zoid component does not support update prop "${keyBy}" with value undefined`);
      })
    }

    this.props = mergeProps
    return this.component.updateProps(mergeProps);
  }

  onBackdropClicked(callback: () => void) {
    return this.component.event.on(window.euroland.EVENT.BACKDROP_CLICKED, callback);
  }
}
