﻿using Microsoft.EntityFrameworkCore;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
  [Keyless]
    public class MarketDepth
    {
        [Column("TId")]
        public int InstrumentId { get; set; }
        // RowUpdated
        private DateTime _FiRowUpdated;

        public DateTime FiRowUpdated
        {
            get
            {
                return _FiRowUpdated;
            }
            set
            {
                _FiRowUpdated = DateTime.SpecifyKind(value, DateTimeKind.Unspecified);
            }
        }

        // Ask
        public decimal? Fid_Best_Ask_1 { get; set; }
        public int? Fid_Best_Ask_1_Size { get; set; }

        public decimal? Fid_Best_Ask_2 { get; set; }
        public int? Fid_Best_Ask_2_Size { get; set; }

        public decimal? Fid_Best_Ask_3 { get; set; }
        public int? Fid_Best_Ask_3_Size { get; set; }

        public decimal? Fid_Best_Ask_4 { get; set; }
        public int? Fid_Best_Ask_4_Size { get; set; }

        public decimal? Fid_Best_Ask_5 { get; set; }
        public int? Fid_Best_Ask_5_Size { get; set; }

        public decimal? Fid_Best_Ask_6 { get; set; }
        public int? Fid_Best_Ask_6_Size { get; set; }

        public decimal? Fid_Best_Ask_7 { get; set; }
        public int? Fid_Best_Ask_7_Size { get; set; }

        public decimal? Fid_Best_Ask_8 { get; set; }
        public int? Fid_Best_Ask_8_Size { get; set; }

        public decimal? Fid_Best_Ask_9 { get; set; }
        public int? Fid_Best_Ask_9_Size { get; set; }

        public decimal? Fid_Best_Ask_10 { get; set; }
        public int? Fid_Best_Ask_10_Size { get; set; }

        // Bid
        public decimal? Fid_Best_Bid_1 { get; set; }
        public int? Fid_Best_Bid_1_Size { get; set; }

        public decimal? Fid_Best_Bid_2 { get; set; }
        public int? Fid_Best_Bid_2_Size { get; set; }

        public decimal? Fid_Best_Bid_3 { get; set; }
        public int? Fid_Best_Bid_3_Size { get; set; }

        public decimal? Fid_Best_Bid_4 { get; set; }
        public int? Fid_Best_Bid_4_Size { get; set; }

        public decimal? Fid_Best_Bid_5 { get; set; }
        public int? Fid_Best_Bid_5_Size { get; set; }

        public decimal? Fid_Best_Bid_6 { get; set; }
        public int? Fid_Best_Bid_6_Size { get; set; }

        public decimal? Fid_Best_Bid_7 { get; set; }
        public int? Fid_Best_Bid_7_Size { get; set; }

        public decimal? Fid_Best_Bid_8 { get; set; }
        public int? Fid_Best_Bid_8_Size { get; set; }

        public decimal? Fid_Best_Bid_9 { get; set; }
        public int? Fid_Best_Bid_9_Size { get; set; }

        public decimal? Fid_Best_Bid_10 { get; set; }
        public int? Fid_Best_Bid_10_Size { get; set; }
    }
}
