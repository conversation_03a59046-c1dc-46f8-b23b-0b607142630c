using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Euroland.FlipIT.SData.API.Infrastructure.Configurations;

public class ListConfiguration: IEntityTypeConfiguration<List>
{
  private const string ViewName = "List";
  public void Configure(EntityTypeBuilder<List> builder)
  {
    builder.ToView(ViewName);
  }
}
