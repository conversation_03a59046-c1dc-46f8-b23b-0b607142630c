using System;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.SData.API.Infrastructure;
using HotChocolate;
using HotChocolate.Resolvers;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;

public class InstrumentPriceResolvers
{
  public async Task<InstrumentDto> GetInstrument(
    [Parent] InstrumentPriceDto instrument,
    InstrumentByIdDataLoader dataLoader,
    IResolverContext resolverContext,
    CancellationToken cancellationToken = default
  ) => await dataLoader.LoadAsync(instrument.InstrumentId, cancellationToken);

  public async Task<DisplayDataForTickerDto> GetPrevData(
    [Parent] InstrumentPriceDto instrument,
    [Service] IGraphQLExecutionContext executionContext,
    SharkDbContextBase sharkDbContext,
    IResolverContext resolverContext)
  {
    var prevClose = await GetPrevCloseByCurrencyRate(instrument, sharkDbContext, resolverContext, executionContext.UseRealtime, executionContext.ExchangeCurrency);
    var lastPrice = await GetLastPriceOfInstrumentByCurrencyRate(instrument, sharkDbContext, resolverContext, executionContext.UseRealtime, executionContext.ExchangeCurrency);

    return new DisplayDataForTickerDto
    {
      Last = lastPrice,
      PrevClose = prevClose,
      Change = lastPrice - prevClose ?? 0,
      ChangePercentage = (lastPrice - prevClose ?? 0) / (prevClose ?? 1) * 100
    };
  }

  private async Task<decimal?> GetPrevCloseByCurrencyRate(
    InstrumentPriceDto instrumentPrice,
    SharkDbContextBase sharkDbContext,
    IResolverContext resolverContext,
    bool useRealtime,
    string exchangeCurrency
  )
  {
    var instrumentIdParam = new SqlParameter("instrumentId", System.Data.SqlDbType.Int)
    { Value = instrumentPrice.InstrumentId };
    var useRealtimeDataParam = new SqlParameter("realtime", System.Data.SqlDbType.Bit)
    { Value = useRealtime };

    var prevClose = new SqlParameter
    {
      ParameterName = "prevClose",
      SqlDbType = System.Data.SqlDbType.Decimal,
      Precision = 21,
      Scale = 7,
      Direction = System.Data.ParameterDirection.Output
    };

    var query = @"
        DECLARE @hCloseDate date
        DECLARE @lasthClose numeric(21,7)
        DECLARE @lasthCloseDate date
        DECLARE @maxHistoryDate date
        DECLARE @currencyBase varchar(3)

        DECLARE @marketId INT;
        DECLARE @marketTimezone varchar(50)

        SELECT @marketId = MarketID FROM Instrument WHERE Id = @instrumentId;

        SELECT @marketTimezone = TimeZone FROM Market WHERE MarketNumber = @marketId;

        SELECT @hCloseDate = p.[Date] AT TIME ZONE 'Central European Standard Time' AT TIME ZONE @marketTimezone
        FROM (
            SELECT *
	          FROM rt_InstrumentPrice
	          WHERE @realtime = 1
	            AND InstrumentId = @instrumentId
	          UNION ALL
	          SELECT *
	          FROM InstrumentPrice
	          WHERE InstrumentId = @instrumentId
	            AND (@realtime = 0 OR NOT EXISTS (SELECT 1 FROM rt_InstrumentPrice WHERE @realtime = 1 AND InstrumentId = @instrumentId))
        ) AS p

        SELECT @MaxHistoryDate = MAX([Date]) FROM InstrumentHistory WHERE InstrumentID = @instrumentId;

        IF (@MaxHistoryDate >= CAST(@hCloseDate AS Date))
            SET @hCloseDate = @MaxHistoryDate;

        SELECT TOP 1
        @lasthClose = [Close],
        @lasthCloseDate = [Date]
        FROM dbo.InstrumentHistory
        WHERE [InstrumentId] = @instrumentId AND [DATE] < @hCloseDate
        ORDER BY [Date] DESC;
      ";

    if (!string.IsNullOrEmpty(exchangeCurrency))
    {
      query += @"
            SELECT TOP 1 @currencyBase = CurrencyCode
            FROM dbo.Instrument
            WHERE Id = @instrumentId;

            SET @prevClose = @lasthClose * dbo.fn_SGH3_GetCurrencyRateByDate(@currencyBase, @currencyQuote, @lasthCloseDate);
        ";

      var exchangeCurrencyParam = new SqlParameter("currencyQuote", System.Data.SqlDbType.VarChar, 3)
      { Value = exchangeCurrency };

      await sharkDbContext.Database.ExecuteSqlRawAsync(query, instrumentIdParam, useRealtimeDataParam,
        exchangeCurrencyParam, prevClose);
    }
    else
    {
      query += "SET @prevClose = @lasthClose;";

      await sharkDbContext.Database.ExecuteSqlRawAsync(query, instrumentIdParam, useRealtimeDataParam, prevClose);
    }

    return (decimal)prevClose.Value;
  }

  private async Task<decimal?> GetLastPriceOfInstrumentByCurrencyRate(
    InstrumentPriceDto instrumentPrice,
    SharkDbContextBase sharkDbContext,
    IResolverContext resolverContext,
    bool useRealtime,
    string exchangeCurrency
  )
  {
    var instrumentIdParam = new SqlParameter("instrumentId", System.Data.SqlDbType.Int)
    { Value = instrumentPrice.InstrumentId };
    var useRealtimeDataParam = new SqlParameter("realtime", System.Data.SqlDbType.Bit)
    { Value = useRealtime };
    var exchangeCurrencyParam = new SqlParameter("currencyQuote", System.Data.SqlDbType.VarChar, 3)
    { Value = !string.IsNullOrEmpty(exchangeCurrency) ? exchangeCurrency : DBNull.Value };

    var lastPrice = new SqlParameter
    {
      ParameterName = "lastPrice",
      SqlDbType = System.Data.SqlDbType.Decimal,
      Precision = 21,
      Scale = 7,
      Direction = System.Data.ParameterDirection.Output
    };

    await sharkDbContext.Database.ExecuteSqlRawAsync(@"
        DECLARE @ipCloseDate date
        DECLARE @ipLast money
        DECLARE @lasthCloseDate date
        DECLARE @lasthClose money
        DECLARE @maxHistoryDate date
        DECLARE @currencyBase varchar(3)

        DECLARE @marketId INT;
        DECLARE @marketTimezone varchar(50)

        SELECT @marketId = MarketID FROM Instrument WHERE Id = @instrumentId;
        SELECT @marketTimezone = TimeZone FROM Market WHERE MarketNumber = @marketId;

        SELECT TOP 1 @currencyBase = CurrencyCode FROM dbo.Instrument WHERE Id = @instrumentId;

        SELECT @ipCloseDate = p.[Date] AT TIME ZONE 'Central European Standard Time' AT TIME ZONE @marketTimezone,
               @ipLast = p.[Last]
        FROM (
            SELECT *
	          FROM rt_InstrumentPrice
	          WHERE @realtime = 1
	            AND InstrumentId = @instrumentId
	          UNION ALL
	          SELECT *
	          FROM InstrumentPrice
	          WHERE InstrumentId = @instrumentId
	            AND (@realtime = 0 OR NOT EXISTS (SELECT 1 FROM rt_InstrumentPrice WHERE @realtime = 1 AND InstrumentId = @instrumentId))
        ) AS p;

        SELECT @maxHistoryDate = MAX([Date]) FROM InstrumentHistory WHERE InstrumentID = @instrumentId;

        IF (@maxHistoryDate >= Cast(@ipCloseDate AS Date))
        BEGIN
            SELECT TOP 1
                @lasthClose = [Close],
                @lasthCloseDate = [Date]
            FROM dbo.InstrumentHistory
            WHERE [InstrumentId] = @instrumentId
            ORDER BY [Date] DESC;

            SET @lastPrice =
                CASE
                    WHEN @currencyQuote IS NULL THEN @lasthClose
                    ELSE @lasthClose * dbo.fn_SGH3_GetCurrencyRateByDate(@currencyBase, @currencyQuote, @lasthCloseDate)
                END;
        END
        ELSE
        BEGIN
           SET @lastPrice =
            CASE
                WHEN @currencyQuote IS NULL THEN @ipLast
                ELSE @ipLast * dbo.fn_SGH3_GetCurrencyRateByDate(@currencyBase, @currencyQuote, @ipCloseDate)
            END;
        END;
    ", instrumentIdParam, useRealtimeDataParam, exchangeCurrencyParam, lastPrice);

    return (decimal)lastPrice.Value;
  }
}
