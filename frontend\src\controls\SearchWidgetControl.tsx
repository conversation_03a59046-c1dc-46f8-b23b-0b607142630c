import { useAuth } from "@/auth";
import { ZoidSearchWidget } from "@/components/SearchWidget";
import {LayoutPosition} from "@/config/interface";
import { getAvatarInitials } from "@/helpers";
import useZoidEvents from "@/hooks/useZoidEvents";
import { ICompanyInfo } from "@/services/apiEndpoints";
import {AISearchSettings} from "@/services/getAISearchSettings";
import { useAppStore } from "@/store/useAppStore";
import { AnimateValue } from "@/zoid";
import { useEffect } from "react";

function searchWidgetAnimation(position: LayoutPosition) {
  let show: AnimateValue;
  let close: AnimateValue;

  switch (position) {
    case LayoutPosition.LEFT:
      show = ["translateX(-50vw)", "translateX(0vw)"];
      close = ["translateX(0vw)", "translateX(-50vw)"];
      break;
    case LayoutPosition.RIGHT:
      show = ["translateX(50vw)", "translateX(0vw)"];
      close = ["translateX(0vw)", "translateX(50vw)"];
      break;
    case LayoutPosition.BOTTOM:
      show = ["translateY(100vh)", "translateY(0vh)"];
      close = ["translateY(0vh)", "translateY(100vh)"];
      break;
    case LayoutPosition.CENTER:
      show = ["translateY(100vh)", "translateY(0vh)"];
      close = ["translateY(0vh)", "translateY(100vh)"];
      break;
    default:
      throw new Error("Invalid animation type");
  }

  return { show, close };
}


const SearchWidgetControl = ({
  companyInfo,
  position = LayoutPosition.RIGHT,
  aiSearchSettings
}: {
  companyInfo: ICompanyInfo;
    position: LayoutPosition
  aiSearchSettings: AISearchSettings
}) => {
  const auth = useAuth();
  const isOpen = useAppStore((state) => state.searchWidget.isOpen);
  const setOpen = useAppStore((state) => state.searchWidget.setOpen);
  const setLogoutOpen = useAppStore((state) => state.confirm.setLogoutOpen);
  const status = useAppStore(state => state.userProfile.status);
  const updateStatus = useAppStore(state => state.userProfile.updateStatus);
  const setExternalLinkOpen = useAppStore(
    (state) => state.confirm.setExternalLinkOpen
  );
  const avatarUrl = useAppStore((state) => state.userProfile.avatar) ?? "";
  const fullName = useAppStore((state) => state.userProfile.fullName) ?? "";
  const shortName = getAvatarInitials(fullName);

  const { show, close } = searchWidgetAnimation(position);

  const searchWidget = ZoidSearchWidget.use(
    {
      onCloseWindow: () => setOpen(false),
      onSignOutSuccess: () => setLogoutOpen(true),
      onLogin: () => auth.login(),
      onOpenExternalLink: () => setExternalLinkOpen(true),
      getAccessToken: () =>
        auth.getUser().then((user) => user?.access_token ?? ""),
      updateStatus: (status) => updateStatus(status),
      isLogin: auth.isAuthenticated,
      avatarUrl,
      companyInfo,
      isOpen,
      shortName,
      position,
      userFollowStatus: status,
      aiSearchSettings
    },
    {
      animate: {
        show: {
          transform: show,
          easing: "linear",
          duration: 200,
        },
        close: {
          transform: close,
          easing: "linear",
          duration: 200,
        },
      },
    }
  );

  useZoidEvents(searchWidget, {
    BACKDROP_CLICKED: () => setOpen(false),
  });

  useEffect(() => {
    if (isOpen) {
      searchWidget.show(true);
    } else {
      searchWidget.hide();
    }
  }, [isOpen]);

  useEffect(() => {
    searchWidget.updateProps({
      isLogin: auth.isAuthenticated,
      avatarUrl,
      companyInfo,
      isOpen,
      shortName,
      position,
      userFollowStatus: status,
      aiSearchSettings
    });
  }, [
    auth.isAuthenticated,
    avatarUrl,
    companyInfo,
    isOpen,
    shortName,
    position,
    status,
    aiSearchSettings
  ]);

  return null;
};

export default SearchWidgetControl;
