using System;
using System.Collections.Generic;
using HotChocolate.Resolvers;
using HotChocolate.Types.Pagination;

namespace Euroland.FlipIT.SData.API.GraphQL.Selections;

[Flags]
public enum SelectionNameFlags : short
{
  SelectedNames = 0,
  DependencyNames = 1,
  All = SelectedNames | DependencyNames
};

public interface ISelectionParamContext
{
  IResolverContext ResolverContext { get; }

  /// <summary>
  /// Current GraphQL selection fields.
  /// </summary>
  IReadOnlyList<IResolverProcessingSelection> SelectionFields { get; }

  /// <summary>
  /// Current GraphQL selection names.
  /// </summary>
  /// <value></value>
  IReadOnlyList<string> SelectionNames { get; }

  IReadOnlyList<IResolverProcessingSelection> GetSelectionFieldsFor<TObjectType>();

  IEnumerable<string> GetSelectionMappedNamesFor<TObjectType>(SelectionNameFlags flags = SelectionNameFlags.All);

  IReadOnlyList<string> ArgumentNames { get; }

  IReadOnlyList<IArgumentValue> Arguments { get; }

  IReadOnlyList<ISortOrderField> SortArguments { get; }

  CursorPagingArguments PagingArgs { get; }

  CursorPagingArguments CursorPagingArgs { get; }

  OffsetPagingArguments OffsetPagingArgs { get; }

  ResolverProcessingSelection TotalCountSelection { get; }

  bool IsTotalCountRequested { get; }
}
