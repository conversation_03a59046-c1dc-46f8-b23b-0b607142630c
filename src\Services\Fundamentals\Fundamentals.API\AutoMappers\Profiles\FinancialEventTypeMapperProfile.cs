using AutoMapper;
using Euroland.FlipIT.Fundamental.API.Dtos.FinCalender;
using Euroland.FlipIT.Fundamental.API.Persistance.FinCalender;

namespace Euroland.FlipIT.Fundamental.API.AutoMappers.Profiles;

public class FinancialEventTypeMapperProfile : Profile
{
  public FinancialEventTypeMapperProfile()
  {
    CreateMap<FinancialEventTypeDetail, FinancialEventTypeDetailDto>()
      .ForMember(dto => dto.Id, opt => opt.MapFrom(src => src.FinancialEventTypeId))
      .ForMember(dto => dto.CompanyCode, opt => opt.MapFrom(src => src.FinancialEventType.CompanyCode))
      .ForMember(dto => dto.Order, opt => opt.MapFrom(src => src.FinancialEventType.Order))
      .ForMember(dto => dto.IsActive, opt => opt.MapFrom(src => src.FinancialEventType.IsActive))
      .ForMember(dto => dto.IsDeleted, opt => opt.MapFrom(src => src.FinancialEventType.IsDeleted))
      .ForMember(dto => dto.IsCustom, opt => opt.MapFrom(src => src.FinancialEventType.IsCustom));
  }
}
