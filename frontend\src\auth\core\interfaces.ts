import { User } from "oidc-client-ts";

export interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: Error | null;
  activeNavigator: string | null;
}

/**
 * Props for the LoginComponent.
 */
export declare interface LoginComponentProps {
  /**
   * Callback function to be called when login is successful.
   * @param user {User} - The result of the successful login.
   */
   
  onLoginSuccess: (user: User) => void;

  /**
   * Callback function to be called when login fails.
   * @param error - The error that occurred during login.
   */
  onLoginFail: (error: Error) => void;
};

export declare interface ZoidComponent {
  canRenderTo(container: Window | string): Promise<void>;
}

/**
 * Properties for the OpenLogin component.
 */
export declare interface OpenLoginProps {
   
  LoginComponent: ZoidComponent;
};

/**
 * Props for the LogoutComponent.
 */
export declare interface LogoutComponentProps {
  /**
   * Callback function to be called when logout is successful.
   * @param result - The result of the logout operation.
   */
   
  onLogoutSuccess: (result: any) => void;

  /**
   * Callback function to be called when logout fails.
   * @param error - The error that occurred during the logout operation.
   */
  onLogoutFail: (error: Error) => void;
};

/**
 * Properties for the OpenLoout component.
 *
 */
export declare interface OpenLooutProps {
   
  LogoutComponent: ZoidComponent;
};

/**
 * Represents the different types of actions that can be performed in the authentication process.
 *
 * @typedef {('INITIALISED' | 'USER_LOADED' | 'USER_UNLOADED' | 'NAVIGATOR_INIT' | 'NAVIGATOR_CLOSE' | 'ERROR')} AuthActionType
 *
 * @property {'INITIALISED'} INITIALISED - Indicates that the authentication process has been initialized.
 * @property {'USER_LOADED'} USER_LOADED - Indicates that the user has been successfully loaded.
 * @property {'USER_UNLOADED'} USER_UNLOADED - Indicates that the user has been unloaded.
 * @property {'NAVIGATOR_INIT'} NAVIGATOR_INIT - Indicates that the navigator has been initialized.
 * @property {'NAVIGATOR_CLOSE'} NAVIGATOR_CLOSE - Indicates that the navigator has been closed.
 * @property {'ERROR'} ERROR - Indicates that an error has occurred in the authentication process.
 */
export declare type AuthActionType = 'INITIALISED' | 'USER_LOADED' | 'USER_UNLOADED' | 'NAVIGATOR_INIT' | 'NAVIGATOR_CLOSE' | 'ERROR';

export type AuthPros = {
  onLoginSuccess: (user: User) => void,
  onLoginFail: (error: Error) => void,
  onLogoutSuccess: () => void,
  onLogoutFail: (error: Error) => void,
  animate: (rest: object) => Promise<void>
}

// declare global {
//   /**
//    * Extends the Window interface to include xprops, which contains callback functions
//    * for handling login and logout events.
//    *
//    * @property {Object} xprops - An object containing callback functions.
//    * @property {(user: User) => void} xprops.onLoginSuccess - Callback function to be called when login is successful.
//    * @property {(error: Error) => void} xprops.onLoginFail - Callback function to be called when login fails.
//    * @property {() => void} xprops.onLogoutSuccess - Callback function to be called when logout is successful.
//    * @property {(error: Error) => void} xprops.onLogoutFail - Callback function to be called when logout fails.
//    */
//   interface Window {
//     xprops: {
//       layout: {
//         top: string;
//         middle: string;
//         bottom: string;
//       }
//       onLoginSuccess: (user: User) => void,
//       onLoginFail: (error: Error) => void,
//       onLogoutSuccess: () => void,
//       onLogoutFail: (error: Error) => void,
//       animate: (rest: object) => Promise<void>
//     }
//   }
// }


