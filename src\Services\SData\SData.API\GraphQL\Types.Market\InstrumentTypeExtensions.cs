using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Market;

public class InstrumentTypeExtensions: ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(Instrument.InstrumentType.Name);

    descriptor.Field("market")
      .Type<MarketType>()
      .ResolveWith<MarketResolvers>(
        resolvers => resolvers.GetMarketByInstrument(default!, default!, default!, default!)
      );
  }
}
