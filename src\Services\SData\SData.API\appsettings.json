{
  "LanguageMapFilePath": "../Common/LanguageMap.xml",
  "IntraDayCacheExpireTime": 1,
  "InstrumentHistoryCacheExpireTime": 10,
  "AllowedHosts": "*",
  "Serilog": {
    "Using": [ "Serilog.Sinks.Syslog" ],
    "MinimumLevel": "Warning",
    "WriteTo": [
      {
        "Name": "UdpSyslog",
        "Args": {
          "host": "*************",
          "port": "514",

          "appName": "FlipIT.SData.API",
          "restrictedToMinimumLevel": "Warning",
          "outputTemplate": "[{Timestamp:HH:mm:ss}] {Message:lj}{NewLine}{Exception}"
        }
      }
    ],

    "Properties": {
      "Application": "FlipIT.SData.API"
    }
  },

  "ConnectionStrings": {
    "NewsDb": "Server=************;Database=News;User ID=uShark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=SData.API",
    "SharkDb": "Server=************;Database=shark;User ID=uShark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=SData.API",
    "SharkRealtimeDb": "Server=tcp:eurolandeurope.database.windows.net,1433;Database=shark;User ID=uShark;Password=**********;Trusted_Connection=False;TrustServerCertificate=True;MultipleActiveResultSets=True;Application Name=SData.API",
    "WebcastDb": "Server=************;Database=Webcast;User ID=uShark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=SData.API",
    "TimeScaleDb": "Host=***********;Port=5432;Database=eurolandtimescale;Username=postgres;Password=**********;Trust Server Certificate=true"
  },

  "DistributedCachingConfiguration": {
    "UseRedisCache": false,
    "RedisConnectionString": "localhost:7000",
    "RedisInstanceName ": "redis_01",

    //"UseNCache": false
    //"UseSQLServerCache": false

    "CacheSizeLimit": 0, // case value = 0 we don't limit cache size
    "CompactionPercentage": 0, // the amount to compact the cache by when the maximum size is exceeded
    "ExpirationTime": 30 // 30s
  }
}
