using AutoMapper;
using Euroland.FlipIT.Fundamental.API.Dtos.FinCalender;
using Euroland.FlipIT.Fundamental.API.Persistance.FinCalender;

namespace Euroland.FlipIT.Fundamental.API.AutoMappers.Profiles;

public class FinancialEventMapperProfile : Profile
{
  public FinancialEventMapperProfile()
  {
    CreateMap<FinancialEventDetail, FinancialEventDetailDto>()
      .ForMember(dto => dto.EventId, opt => opt.MapFrom(src => src.FinancialEventId))
      .ForMember(dto => dto.EventDetailId, opt => opt.MapFrom(src => src.Id))
      .ForMember(dto => dto.CompanyCode, opt => opt.MapFrom(src => src.FinancialEvent.CompanyCode))
      .ForMember(dto => dto.EventTypeId, opt => opt.MapFrom(src => src.FinancialEvent.EventTypeId))
      .ForMember(dto => dto.StartDate, opt => opt.MapFrom(src => src.FinancialEvent.StartDate))
      .ForMember(dto => dto.EndDate, opt => opt.MapFrom(src => src.FinancialEvent.EndDate))
      .ForMember(dto => dto.IsAllDayEvent, opt => opt.MapFrom(src => src.FinancialEvent.IsAllDayEvent))
      .ForMember(dto => dto.IsDeleted, opt => opt.MapFrom(src => src.FinancialEvent.IsDeleted))
      .ForMember(dto => dto.LastUpdated, opt => opt.MapFrom(src => src.FinancialEvent.LastUpdated))
      .ForMember(dto => dto.IsHighlighted, opt => opt.MapFrom(src => src.FinancialEvent.IsHighlighted));
  }
}
