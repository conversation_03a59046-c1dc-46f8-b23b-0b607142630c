namespace Euroland.FlipIT.SData.API.Dto;

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
public class MarketDto: HasTranslation, IDtoObject
{
  public short Id { get; set; }
  public int? RegionId { get; set; }
  public short? ParentId { get; set; }
  public string? OpenTimeLocal { get; set; }
  public string? CloseTimeLocal { get; set; }
  public short TimeDiff { get; set; }
  public string? Delay { get; set; }
  public string TimezoneName { get; set; }
  public bool BusinessDaysStoT { get; set; }
  public string? Abbreviation { get; set; }
  public int CityId { get; set; }
  public int TimezoneId { get; set; }
  public int? TranslationId { get; set; }
  //public CityDto City { get; set;}
  public TimezoneDto? Timezone { get; set; }
  public MarketStatusDto? Status { get; set; }
}

public class MarketStatusDto: IDtoObject
{
  public int MarketId { get; set; }
  public bool IsOpened { get; set; }
  public float RemainingTime { get; set; }
}
