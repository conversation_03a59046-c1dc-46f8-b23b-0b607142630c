using HotChocolate.Execution.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.SubSector;

public static class SubSectorRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddSubSectorType(this IRequestExecutorBuilder builder)
  {
    builder.AddType<SubSectorType>();
    builder.AddTypeExtension<InstrumentTypeExtensions>();

    return builder;
  }
}
