import { FC, useEffect, useRef, useState } from "react";
import { AiSearchStreamResponse, IAiSearchHistory } from "@/services/ai";
import clsx from "clsx";
import { i18n, updateDefaultDateTime } from "@euroland/libs";
import CommonQuestions from "./CommonQuestions";
import { SearchIcon, XMark } from "@/components/Common";
import { useSearchWidgetPropsContext } from "../context/propContext";
import { appSettings } from "@/config/appSettings";
import { apiClient } from "@/services/clientInstance";
import { API_PATHS } from "@/services/apiEndpoints";
import { v4 as uuidv4 } from "uuid";

import {useSearchHistory} from "../context/useSearchHistory";
import AiResponse from "./AiResponse";
import {AUTH_ENABLED} from "@/helpers/constants";
interface SearchPanelProps {
  queryLimit: number;
}

type SearchSSEChunk = {
  Type: "Message" | "Result" | "Error";
  Value: string;
};

export const SearchPanel: FC<SearchPanelProps> = ({ queryLimit }) => {
  const companyInfo = useSearchWidgetPropsContext((s) => s.companyInfo);
  updateDefaultDateTime({
    timeZone: companyInfo.timezone,
    settingFormat: {},
    useLatinNumbers: true,
  });

  const { saveSearchResult} = useSearchHistory()
  const isLogin = useSearchWidgetPropsContext((s) => s.isLogin);
  const onLogin = useSearchWidgetPropsContext((s) => s.onLogin);
  const isOpen = useSearchWidgetPropsContext((s) => s.isOpen);
  const aiSearchSettings = useSearchWidgetPropsContext((s) => s.aiSearchSettings);
  const companyName =
    aiSearchSettings.placeholders || i18n.translate("defaultCompany");
  const [search, setSearch] = useState<string>("");
  const [result, setResult] = useState<IAiSearchHistory | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [processName, setProcessName] = useState<string>("");
  const [error, setError] = useState<string | null>(null);

  const onSubmitQuestion = async (customQuestion?: string) => {
    const question = customQuestion || search;
    const searchTrimmed = question.trim();
    if (searchTrimmed === "" || loading) return;

    setLoading(true);
    setError(null);
    try {
      for await (const chunk of apiClient.instance.sse<SearchSSEChunk>(API_PATHS.SEARCH_SSE, {
        companyCode: appSettings.companyCode || "",
        question: searchTrimmed
      })) {

        if(chunk.Type === "Message") {
          setProcessName(chunk.Value);
        } else if(chunk.Type === "Result") {
          const result = JSON.parse(chunk.Value) as AiSearchStreamResponse;
          const formattedResult: IAiSearchHistory = {
            id: uuidv4(),
            question: question,
            aiResult: result?.original_response || "",
            searchTime: new Date().toISOString(),
            qaLanguage: result?.qaLanguage || "",
            aiResultLanguage: result?.aiResultLanguage || "",
            resultShow: result?.answer || "",
          };
          setResult(formattedResult);
          saveSearchResult(formattedResult, isLogin);
        } else if(chunk.Type === "Error") {
          setError(chunk.Value);
          setResult(null);
        }
      }
    } catch (e) {
      setError(i18n.translate("error.somethingWentWrong"));
    }

    setLoading(false);
  };




  const removeQuestion = () => {
    if (loading) return;
    setResult(null);
    setError(null);
    setSearch("");
  };

  const handleChangeSearchInput = (value: string) => {
    const isDeleteQuestion = !value;
    setSearch(value);
    if (isDeleteQuestion) {
      removeQuestion();
    }
  };

  const showCommonQuestions = !loading && !result && !error;
  const inputRef = useRef<HTMLInputElement>(null);

  const handleKeyDown = async (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key !== "Enter") return;
    onSubmitQuestion();
  };

  const handleSubmitQuestion = (question: string) => {
    handleChangeSearchInput(question);
    onSubmitQuestion(question);
  };

  useEffect(() => {
    if (!isOpen) return;
    const timer = setTimeout(() => {
      if (inputRef.current) {
        inputRef.current?.focus();
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [isOpen]);

  return (
    <div className="tab-panel active">
      <div className="wise-widget-search">
        {!isLogin && AUTH_ENABLED && (
          <div className="mb-4 text-neutral-600 text-sm mt-4">
            {i18n.translate("remainingQueries", {
              queryLimit: queryLimit.toString(),
            })}{" "}
            <a
              href="#"
              className="text-primary font-medium"
              onClick={(e) => (e.preventDefault(), onLogin())}
            >
              {i18n.translate("signIn")}
            </a>{" "}
            {i18n.translate("forUnlimitedQueries")}
          </div>
        )}
        <div className="wise-widget-search__container">
          <input
            type="text"
            ref={inputRef}
            placeholder={i18n.translate("placeholder", { companyName })}
            value={search}
            onChange={(e) => handleChangeSearchInput(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={loading}
            className="wise-widget-search__input"
          />
          {search && (
            <button
              className="wise-widget-search__clear"
              onClick={removeQuestion}
            >
              <XMark />
            </button>
          )}
          <button
            className="wise-widget-search__button wise-gradient-button"
            onClick={() => onSubmitQuestion()}
          >
            <span className="search-text">{i18n.translate("search")}</span>
            <SearchIcon className="w-6 h-6" />
          </button>
        </div>
        <CommonQuestions
          showCommonQuestions={showCommonQuestions}
          onSubmitQuestion={handleSubmitQuestion}
        />
      </div>
      <div className={clsx("wise-widget-loading", { active: loading })}>
        <div className="loading-spinner"></div>
        <div className="loading-text">
          <div
            className="translation-status"
            style={{ opacity: 0.7, fontSize: "0.9em" }}
          >
            {loading ? i18n.translate(processName) ?? processName : ""}
          </div>
        </div>
      </div>
      <div className="wise-widget-results">
        {!loading && result && (
          <AiResponse
            qaLanguage={result.qaLanguage}
            aiResultLanguage={result.aiResultLanguage}
            answer={result.resultShow}
          />
        )}
        {!loading && error && (
          <div className="p-4 bg-red-50 rounded-md border border-red-200">
            <div className="text-red-600 text-sm">{error}</div>
          </div>
        )}
      </div>
    </div>
  );
};
