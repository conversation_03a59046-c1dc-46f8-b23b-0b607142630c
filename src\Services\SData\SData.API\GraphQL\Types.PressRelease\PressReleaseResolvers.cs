using System.Linq;
using System.Threading;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure;
using entity = Euroland.FlipIT.SData.API.Infrastructure.Entities;
using HotChocolate;
using HotChocolate.Resolvers;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.PressRelease;

public class PressReleaseResolvers
{
  public IQueryable<PressReleaseDto> GetCompanyPressReleases(
    [Parent] CompanyDto company,
    NewsContext newsDbContext,
    IResolverContext resolverContext,
    bool groupByMessageType = false,
    CancellationToken cancellationToken = default)
  {
    var ungroupGroup = !groupByMessageType;

    var pressReleases = newsDbContext.PressReleases.AsNoTracking();
    var messageTypes = newsDbContext.MessageTypes.AsNoTracking();

    var resultQuery = (
      from p in pressReleases
      where p.CompanyCode == company.Code.Trim()
      join m1 in messageTypes
        on new { mtTypeId = p.MessageTypeId, p.SourceId } equals new { mtTypeId = m1.Id, m1.SourceId } into mtTypeGrouping
      from g1 in mtTypeGrouping.DefaultIfEmpty()
      join m2 in messageTypes
        on new { messageTypeGroup = g1.MessageGroupId, g1.SourceId } equals new { messageTypeGroup = m2.MessageGroupId, m2.SourceId } into mtTypeGroupGrouping
      from g2 in mtTypeGroupGrouping.Where(t => t.MessageGroupTypeId == null && t.MessageGroupId != null).DefaultIfEmpty()
      select new entity.PressRelease
      {
        Id = p.Id,
        CompanyCode = p.CompanyCode,
        DateTime = p.DateTime,
        InsertedDate = p.InsertedDate,
        IsHidden = p.IsHidden,
        // We instantiate a new PressRelease object here to remove [Message] out of SELECT clause
        // because of [Message] has large content and not good performance to select multiple records with [Message].
        Message = null,
        HasAttachment = p.HasAttachment,
        LanguageId = p.LanguageId,
        MessageGroupId = p.MessageGroupId,
        MessageTypeId = p.MessageTypeId == null ? 0 : (g1.MessageGroupId == null || ungroupGroup) ? g1.Id : g2.Id,
        SourceId = p.MessageTypeId == null ? p.SourceId : g1.MessageGroupId == null ? g1.SourceId : g2.SourceId,
        Title = p.Title,
        MessageType = p.MessageTypeId == null ? null : ungroupGroup ? g1 : g2
      }
    );

    return resultQuery
      .OrderByArgumentOrDefault(resolverContext, p => p.OrderByDescending(p1 => p1.DateTime.Date))
      .WithAutoMapper()
      .ToProjection<PressReleaseDto>();
  }

  public async Task<PressReleaseDto> GetPressReleaseById(
    [Service] IDbContextFactory<NewsContext> newDbContextFactory,
    [Parent] CompanyDto company,
    IResolverContext resolverContext,
    int id,
    bool? includeHidden = false,
    bool? groupByMessageType = false,
    CancellationToken cancellationToken = default
  ) => await LoadPressReleaseById(newDbContextFactory, id, includeHidden, groupByMessageType, company.Code, cancellationToken);

  public async Task<PressReleaseDto> GetPressReleaseByAttachment(
    [Service] IDbContextFactory<NewsContext> newDbContextFactory,
    [Parent] AttachmentDto attachment,
    IResolverContext resolverContext,
    bool groupByMessageType = false,
    CancellationToken cancellationToken = default
  ) => await LoadPressReleaseById(newDbContextFactory, attachment.PressreleaseId, true, groupByMessageType, null, cancellationToken);

  /// <summary>
  /// Gets years that have pressreleases
  /// </summary>
  /// <param name="company"></param>
  /// <param name="newsDbContext"></param>
  /// <param name="resolverContext"></param>
  /// <param name="groupByMessageType"></param>
  /// <param name="languageId"></param>
  /// <param name="sourceId"></param>
  /// <param name="from"></param>
  /// <param name="to"></param>
  /// <param name="cancellationToken"></param>
  /// <returns></returns>
  public PressReleaseAvailableYearRangeDto? GetAvailablePressReleaseYearRangeByCompany(
    [Parent] CompanyDto company,
    NewsContext newsDbContext,
    int languageId = 32,
    int? sourceId = null,
    DateTime? from = null,
    DateTime? to = null,
    CancellationToken cancellationToken = default)
  {
    var result = newsDbContext.PressReleases
      .AsNoTracking()
      .Where(p =>
        p.CompanyCode == company.Code.Trim()
        && p.LanguageId == languageId
        && (sourceId == null || p.SourceId == sourceId.Value)
        && (from == null || p.DateTime.Date >= from.Value.Date)
        && (to == null || p.DateTime.Date <= to.Value.Date)
      )
      .GroupBy(p => 0)
      .Select(grouping => new
      {
        MinYear = grouping.Min(p => p.DateTime.Year),
        MaxYear = grouping.Max(p => p.DateTime.Year),
      })
      .FirstOrDefault();

    return result != null
      ? new PressReleaseAvailableYearRangeDto { FromYear = result.MinYear, ToYear = result.MaxYear }
      : null;
  }

  public static async Task<PressReleaseDto> LoadPressReleaseById(
    IDbContextFactory<NewsContext> newDbContextFactory,
    long id,
    bool? includeHidden = true,
    bool? groupByMessageType = false,
    string? companyCode = null,
    CancellationToken cancellationToken = default)
  {
    using var newsContext = await newDbContextFactory.CreateDbContextAsync();

    var ungroupGroup = !(groupByMessageType ?? false);

    var pressReleases = newsContext.PressReleases.AsNoTracking();
    var messageTypes = newsContext.MessageTypes.AsNoTracking();

#pragma warning disable S3358 // Ternary operators should not be nested
#pragma warning disable S3358 // Ternary operators should not be nested
    var resultQuery = (
      from p in pressReleases
      where p.Id == id && (companyCode == null || companyCode == p.CompanyCode)
      join m1 in messageTypes
        on new { mtTypeId = p.MessageTypeId, p.SourceId } equals new { mtTypeId = m1.Id, m1.SourceId } into mtTypeGrouping
      from g1 in mtTypeGrouping.DefaultIfEmpty()
      join m2 in messageTypes
        on new { messageTypeGroup = g1.MessageGroupId, g1.SourceId } equals new { messageTypeGroup = m2.MessageGroupId, m2.SourceId } into mtTypeGroupGrouping
      from g2 in mtTypeGroupGrouping.Where(t => t.MessageGroupTypeId == null).DefaultIfEmpty()
      select new entity.PressRelease
      {
        Id = p.Id,
        CompanyCode = p.CompanyCode,
        DateTime = p.DateTime,
        InsertedDate = p.InsertedDate,
        IsHidden = p.IsHidden,
        // We instantiate a new PressRelease object here to remove [Message] out of SELECT clause
        // because of [Message] has large content and not good performance to select multiple records with [Message].
        Message = null,
        HasAttachment = p.HasAttachment,
        LanguageId = p.LanguageId,
        MessageGroupId = p.MessageGroupId,
        MessageTypeId = p.MessageTypeId == null ? 0 : ungroupGroup ? g1.Id : g2.Id,
        SourceId = p.SourceId,
        Title = p.Title,
        MessageType = p.MessageTypeId == null ? null : ungroupGroup ? g1 : g2
      }
    );
#pragma warning restore S3358 // Ternary operators should not be nested
#pragma warning restore S3358 // Ternary operators should not be nested

    return await resultQuery
      .WithAutoMapper()
      .ToProjection<PressReleaseDto>()
      .FirstOrDefaultAsync(cancellationToken);
  }
}
