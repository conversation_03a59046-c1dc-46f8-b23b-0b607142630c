using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.Middleware;
using HotChocolate.Configuration;
using HotChocolate.Resolvers;
using HotChocolate.Types.Descriptors.Definitions;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Interceptor;

/// <summary>
/// AD
/// </summary>
public class PerformanceShareScopedDataContextInterceptor : TypeInterceptor
{
  public override void OnBeforeCompleteType(ITypeCompletionContext completionContext, DefinitionBase definition)
  {
    if (definition is ObjectTypeDefinition objectTypeDefinition)
    {
      foreach (var fieldDefinition in objectTypeDefinition.Fields)
      {
        if (
          fieldDefinition.ResultType == typeof(InstrumentDto)
          || fieldDefinition.ResultType == typeof(Task<InstrumentDto>)
          || fieldDefinition.ResultType == typeof(IEnumerable<InstrumentDto>)
          || fieldDefinition.ResultType == typeof(Task<IEnumerable<InstrumentDto>>))
        {
          var middlewareDefinition = new FieldMiddlewareDefinition(FieldClassMiddlewareFactory.Create<PerformanceShareScopedDataContextMiddleware>());
            fieldDefinition.MiddlewareDefinitions.Add(middlewareDefinition);
        }
      }
    }
  }
}
