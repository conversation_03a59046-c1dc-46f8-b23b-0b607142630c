﻿using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.WiseSearch.Infrastructure
{
    public interface IRepositoryBase<TDbContext, TEntity, TKeyType> where TEntity : class where TDbContext : DbContext
    {
        Task<TEntity> GetByKeyAsync(TKeyType id);
        Task<IEnumerable<TEntity>> GetAllAsync();
        void Add(TEntity entity);
        Task AddAsync(TEntity entity, CancellationToken cancellationToken = default);
        void Update(TEntity entity);
        void Remove(TEntity entity);
        void RemoveRange(IEnumerable<TEntity> entities);
    }

    public abstract class RepositoryBase<TDbContext, TEntity, TKeyType> : IRepositoryBase<TDbContext, TEntity, TKeyType> where TEntity : class where TDbContext : DbContext
    {
        protected readonly TDbContext _dbContext;

        public RepositoryBase(TDbContext context)
        {
            _dbContext = context;
        }

        public virtual async Task<TEntity> GetByKeyAsync(TKeyType id)
        {
            return await _dbContext.Set<TEntity>().FindAsync(id);
        }

        public virtual async Task<IEnumerable<TEntity>> GetAllAsync()
        {
            return await _dbContext.Set<TEntity>().ToListAsync();
        }

        public virtual void Add(TEntity entity)
        {
            _dbContext.Set<TEntity>().Add(entity);
        }

        public virtual void Update(TEntity entity)
        {
            _dbContext.Set<TEntity>().Update(entity);
        }

        public virtual void Remove(TEntity entity)
        {
            _dbContext.Set<TEntity>().Remove(entity);
        }

        public virtual void RemoveRange(IEnumerable<TEntity> entities)
        {
            _dbContext.Set<TEntity>().RemoveRange(entities);
        }

        public virtual async Task AddAsync(TEntity entity, CancellationToken cancellationToken = default)
        {
            await _dbContext.Set<TEntity>().AddAsync(entity, cancellationToken);
        }
    }
}
