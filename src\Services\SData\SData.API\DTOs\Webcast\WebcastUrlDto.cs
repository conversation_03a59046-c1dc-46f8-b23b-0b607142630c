namespace Euroland.FlipIT.SData.API.Dto.Webcast;

public class WebcastUrlDto: AuditDto, IDtoObject
{
  public long Id { get; set; }
  public long WebcastId { get; set; }
  public virtual WebcastDto Webcast { get; set; } = null!;
  public int WebcastHostId { get; set; }
  public virtual WebcastHostDto WebcastHost { get; set; } = null!;
  public int UploadStatusId { get; set; }
  public virtual UploadStatusDto UploadStatus { get; set; } = null!;
  public string? OriginalId { get; set; }
}
