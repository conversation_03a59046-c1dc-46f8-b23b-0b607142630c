using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.PressRelease;

/// <summary>
/// Extends 'pressRelease' field to type <see cref="Attachment.AttachmentType"/>
/// </summary>
public class AttachmentTypeExtensions: ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(Attachment.AttachmentType.Name);

    descriptor.Field("pressRelease")
      .Argument("groupByMessageType", cfg =>
        cfg.Type<BooleanType>()
          .DefaultValue(false)
          .Description("If `false`, pressRelease groups will be ungrouped and the types will show up instead of the groups. Default to `false`.")
      )
      .ResolveWith<PressReleaseResolvers>(
        resolvers => resolvers.GetPressReleaseByAttachment(default!, default!, default!, default!, default)
      );
  }
}
