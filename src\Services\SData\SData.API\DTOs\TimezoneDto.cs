namespace Euroland.FlipIT.SData.API.Dto;

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
public class TimezoneDto: HasTranslation, IDtoObject
{
  public int Id { get; set; }
  public string Name { get; set; }
  public string? NameIANA { get; set; }
  public double Hour { get; set; }
  public short Diff { get; set; }
  public short? DiffFromServerTime { get; set; }
  public int? TranslationId { get; set; }
}
