﻿using AutoMapper;
using Euroland.FlipIT.SData.API.DTOs.Deprecated;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Mappers.Deprecated.Profiles;
using System.Linq;

namespace Euroland.FlipIT.SData.API.Mappers.Deprecated
{
    public static class CompanyMappers
    {
        internal static IMapper Mapper { get; }
        static CompanyMappers()
        {
            Mapper = new MapperConfiguration(cfg => {
                cfg.AddProfile<CompanyMapperProfile>();
            }).CreateMapper();
        }

        public static IQueryable<CompanyInfo> ToModel(this IQueryable<Company> instrument, string lang)
        {
            return Mapper.ProjectTo<CompanyInfo>(instrument, new { lang = lang });
        }

    }
}
