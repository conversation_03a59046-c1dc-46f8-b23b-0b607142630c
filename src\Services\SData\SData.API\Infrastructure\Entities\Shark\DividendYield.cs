using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities;

// public class EverageSharePrice
// {
//   public int InstrumentId { get; set; }
//   public int Year { get; set; }
//   public decimal AveragePrice { get; set; }
// }

public class TotalDividendPerShare
{
  public int InstrumentId { get; set; }
  public int? Year { get; set; }

  [Column(TypeName = "Decimal(18,4)")]
  public decimal? TotalDividend { get; set; }

  /// <summary>
  /// Original dividend currency
  /// </summary>
  /// <value></value>
  public string? Currency { get; set; }

  /// <summary>
  /// Exchange currency (if provided) that price is converted from original dividend currency into this.
  /// </summary>
  /// <value></value>
  public string? ExchangeCurrency { get; set; }

  [Column(TypeName = "Decimal(18,4)")]
  public decimal? Percentage { get; set; }
}
