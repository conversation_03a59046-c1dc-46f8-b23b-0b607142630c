using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

namespace Euroland.FlipIT.SData.API.Infrastructure.TempTables;

public class DefaultTempTableNameProvider: ITempTableNameProvider
{
  public static readonly ITempTableNameProvider Instance = new DefaultTempTableNameProvider();

  public ITempTableNameLease LeaseName(DbContext ctx, IEntityType entityType)
      {
         if (entityType == null)
            throw new ArgumentNullException(nameof(entityType));

         var tableName = entityType.GetSchemaQualifiedTableName();

         return new TempTableName(tableName);
      }
}
