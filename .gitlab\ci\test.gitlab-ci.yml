test-merge-request:
  tags:
    - vietnam-dev-shell
  extends:
    - .default-retry
    - .disable-artifact-passing
  stage: test
  script:
    - 'dotnet test core-api-services.sln'
  only:
    refs:
      - merge_requests
    variables:
      - '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"'

# test-qa:
#   tags:
#     - vietnam-dev-shell
#   extends:
#     - .default-retry
#   stage: test
#   script:
#     - 'dotnet test core-api-services.sln'
#   only:
#     refs:
#       - develop

# test-release:
#   tags:
#     - ee-buildtest-shell
#   extends:
#     - .default-retry
#   stage: test
#   script:
#     - 'dotnet test core-api-services.sln'
#   only:
#     refs:
#       - next
#       - master
