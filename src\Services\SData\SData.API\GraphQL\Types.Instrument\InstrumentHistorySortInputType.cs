using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Data.Sorting;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;

/// <summary>
/// Custom sort type to enable ordering on <see cref="InstrumentHistoryDto"/> fields.
/// </summary>
public class InstrumentHistorySortInputType: SortInputType<InstrumentHistoryDto>
{
  protected override void Configure(ISortInputTypeDescriptor<InstrumentHistoryDto> descriptor)
  {
    descriptor.BindFieldsExplicitly();
    descriptor.Field(f => f.DateTime).Type<DefaultSortEnumType>();
    descriptor.Field(f => f.Close).Type<DefaultSortEnumType>();
    descriptor.Field(f => f.Volume).Type<DefaultSortEnumType>();
  }
}
