using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.CurrencyRate;

/// <summary>
/// Extends 'currencyRate' field to type <see cref="CurrencyRateHistory.CurrencyRateHistoryType"/>
/// </summary>
public class CurrencyRateHistoryTypeExtensions: ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(CurrencyRateHistory.CurrencyRateHistoryType.Name);

    descriptor.Field("currencyRate")
      .UseFirstOrDefault()
      .UseProjection()
      .ResolveWith<CurrencyRateResolvers>(p => p.GetCurrencyRateByCurrencyRateHistory(default!, default!, default!, default!));
  }
}
