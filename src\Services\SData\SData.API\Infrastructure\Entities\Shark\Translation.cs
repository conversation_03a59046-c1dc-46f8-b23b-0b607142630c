﻿namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
  public class Translation
    {
        public int ID { get; set; }
        public string EN { get; set; }
        public string DE { get; set; }
        public string ES { get; set; }
        public string FR { get; set; }
        public string IT { get; set; }
        public string NL { get; set; }
        public string FI { get; set; }
        public string SV { get; set; }
        public string RU { get; set; }
        public string PL { get; set; }
        public string CN { get; set; }
        public string KR { get; set; }
        public string JP { get; set; }
        public string IE { get; set; }
        public string NO { get; set; }
        public string? DK { get; set; }
        public string? FO { get; set; }
        public string? ET { get; set; }
        public string? AR { get; set; }
        public string? VI { get; set; }
        public string? TW { get; set; }
        public string? PT { get; set; }
        public string? HE { get; set; }
        public string? KU { get; set; }
        public string? CA { get; set; }
        public string? RO { get; set; }
        public string? LT { get; set; }

        public Market Market { get; set; }
        public Currency Currency { get; set; }
    }
}
