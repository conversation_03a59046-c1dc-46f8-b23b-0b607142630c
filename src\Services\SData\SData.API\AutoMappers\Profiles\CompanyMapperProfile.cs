using AutoMapper;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;

namespace Euroland.FlipIT.SData.API.AutoMapper.Profiles;

public class CompanyMapperProfile : Profile
{
  /// <summary>
  /// AutoMapper Profile: <see cref="CompanyDto"/>
  /// </summary>
  public CompanyMapperProfile()
  {
    CreateMap<Company, CompanyDto>()
      .ForMember(dest => dest.Id, conf => conf.MapFrom(ol => ol.CompanyId))
      .ForMember(dest => dest.Name, conf => conf.MapFrom(ol => ol.CompanyName))
      .ForMember(dest => dest.Code, conf => conf.MapFrom(ol => ol.CompanyCode))
      .ForMember(dest => dest.Country, conf => conf.MapFrom(ol => ol.CompanyCountry))
      .ForMember(dest => dest.Email, conf => conf.MapFrom(ol => ol.CompanyEMail))
      .ForMember(dest => dest.Fax, conf => conf.MapFrom(ol => ol.CompanyFax))
      .ForMember(dest => dest.Town, conf => conf.MapFrom(ol => ol.CompanyTown))
      .ForMember(dest => dest.MarketId, conf => conf.MapFrom(ol => ol.CompanyMarket))
      .ForMember(dest => dest.Tel, conf => conf.MapFrom(ol => ol.CompanyTel))
      .ForMember(dest => dest.HomePage, conf => conf.MapFrom(ol => ol.CompanyHomePage))
      .ForMember(dest => dest.CustomerTypeId, conf => conf.MapFrom(ol => ol.CompanyCustomer));

    CreateMap<CompanyNames, CompanyNameDto>()
      .ForMember(d => d.Language, c => c.MapFrom(e => e.CompanyLang))
      .ForMember(d => d.Address1, c => c.MapFrom(e => e.CompanyAdr1))
      .ForMember(d => d.Address2, c => c.MapFrom(e => e.CompanyAdr2))
      .ForMember(d => d.Country, c => c.MapFrom(e => e.CompanyCountry))
      .ForMember(d => d.EMail, c => c.MapFrom(e => e.CompanyEmail))
      .ForMember(d => d.Fax, c => c.MapFrom(e => e.CompanyFax))
      .ForMember(d => d.Name, c => c.MapFrom(e => e.CompanyName))
      .ForMember(d => d.Tel, c => c.MapFrom(e => e.CompanyTel))
      .ForMember(d => d.Town, c => c.MapFrom(e => e.CompanyTown))
      .ForMember(d => d.Zip, c => c.MapFrom(e => e.CompanyZip));
  }
}

