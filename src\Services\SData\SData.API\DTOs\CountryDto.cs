namespace Euroland.FlipIT.SData.API.Dto;

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
public class CountryDto: HasTranslation, IDtoObject
{
  public int Id { get; set; }
  public string? Name { get; set; }
  public bool IsEnabled { get; set; }
  public short CallingCode { get; set; }
  public int? TranslationId { get; set; }
}
