import { OpenLooutProps } from "./interfaces.ts";


/**
 * Opens a logout component and handles the logout process.
 *
 * @param {Object} props - The properties for the logout component.
 * @param {React.ComponentType} props.LogoutComponent - The logout component to be rendered.
 * @returns {Promise<void>} A promise that resolves when the logout process is completed.
 *
 * @example
 * openLogout({ LogoutComponent })
 *   .then((closedProgrammatically) => {
 *     if (closedProgrammatically) {
 *       console.log('Logout window closed programmatically.');
 *     } else {
 *       console.log('Logout window closed by user.');
 *     }
 *   })
 *   .catch((error) => {
 *     console.error('Logout process failed:', error);
 *   });
 *
 * @remarks
 * This function handles both passive and active closure of the logout window.
 * - Passive closure: The signout window is closed programmatically.
 * - Active closure: The signout window is closed manually by the user.
 *
 * For more information on integration events, refer to:
 * https://gitlab.euroland.com/tools/flipit/integration/-/blob/master/doc/instance.md#event
 */
export function openLogout({ LogoutComponent }: OpenLooutProps) {
  if (!LogoutComponent) {
    Promise.reject('Integration LogoutComponent is required');
  }

  const renderPromise = new Promise<boolean>((resolve, reject) => {
    const isIFrame = window.top !== window.self;
    let closedProgrammatically = false;

    let internalWindowError = null as unknown as Error;

     
    const instance = (LogoutComponent as any)({
        onLogoutSuccess: () => closedProgrammatically = true,
        onLogoutFail: (error: Error) => internalWindowError = error
    });

    // Tracks for logout window closed passively or actively, but need to recheck
    // one more time to ensure whether'suser session has ended.
    //  + closed passively: Signout window is closed programmatically.
    //  + closed actively: Signout window is closed by hand.
    // For integration event references, check it out at: https://gitlab.euroland.com/tools/flipit/integration/-/blob/master/doc/instance.md#event
    instance.event.on(window.euroland.EVENT.CLOSE, () => {
      resolve(closedProgrammatically);
    });

    instance.event.on(window.euroland.EVENT.ERROR, (error: Error) => {
      reject({
        internalError: internalWindowError,
        windowError: error
      });
    });

    // Renders as a popup to the parent window
    if (isIFrame) {
        instance.renderTo(window.parent, window.xprops?.layout?.middle, 'popup');
    } else {
        let middle = document.getElementById('middleLayout');
        if (!middle) {
            middle = document.createElement('div');
            middle.id = 'middleLayout';
            document.body.appendChild(middle);
        }

        instance.renderTo(window.parent, '#middleLayout', 'popup');
    }
  });

  // Check if component can render to parent window, otherwise, host window is blocking
  // popup. In this case, we should has a warning message out to the screen.
  const renderableCheckPromise = LogoutComponent.canRenderTo(window.parent);

  return Promise.all([renderableCheckPromise, renderPromise]).then((results) => results[1]);
}
