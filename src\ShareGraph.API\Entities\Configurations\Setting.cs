using Euroland.FlipIT.ShareGraph.API.Entities.Configurations;
using Euroland.FlipIT.ShareGraph.API.Entities.Configurations.Accessibilities;
using System.Collections.Generic;

namespace Euroland.FlipIT.ShareGraph.API.Entities
{
  public class Setting
  {
    public string Layout { get; set; }

    public string TimeZone { get; set; }

    public bool UseLatinNumber { get; set; }

    public string StyleURI { get; set; }

    public bool GoogleAnalyticsEnabled { get; set; }

    public bool GoogleTagEnabled { get; set; }

    public int? StreamUpdateDelay { get; set; }

    public int? TickerRefreshSeconds { get; set; }

    public CurrenciesConfig Currencies { get; set; }
    public IntervalConfig IntervalOptions { get; set; }

    public IEnumerable<Instrument> Instruments { get; set; }

    public Ticker Ticker { get; set; }

    public ShareDetails ShareDetails { get; set; }

    public PeersConfig Peers { get; set; }

    public IEnumerable<Index> Indices { get; set; } = new List<Index>();

    public Performance Performance { get; set; }

    public Chart Chart { get; set; }

    public Format Format { get; set; }

    public string CustomPhrases { get; set; }

    public CompanyLogo CompanyLogo { get; set; }

    public string CompanyName { get; set; }

    public PressReleases PressReleases { get; set; }

    public Accessibilities Accessibilities { get; set; }

    public Trade Trade { get; set; }

    public VideoSetting VideoSetting { get; set; }

    public OrderDepth OrderDepth { get; set; }

    public ColorBlindMode ColorBlindMode { get; set; }

    public ForeignOwnership ForeignOwnership { get; set; }

    public bool UsePrevCloseDayForMcap { get; set; }

  }
}
