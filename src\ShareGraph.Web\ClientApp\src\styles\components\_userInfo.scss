.user-info {
  position: relative;
  display: inline-block;

  &__container {
    display: flex;
    justify-content: flex-end;
    flex-direction: row;
  }

  &__promode-button {
    border-radius: pxToRem(3);
    background: $color-white;
    padding: pxToRem(7) pxToRem(11);
    border: pxToRem(1) solid $lighter-gray;
    font-size: pxToRem(14);
    font-weight: 500;
    cursor: pointer;

  }

  &__name-info {
    display: flex;
    align-items: center;
    gap: pxToRem(8);
  }

  &__arrow {
    font-size: pxToRem(8);
    transform: rotate(180deg);

    transition: transform 0.2s ease;

    &.open {
      transform: unset;
    }
  }

  &__trigger {
    display: flex;
    align-items: center;
    gap: pxToRem(4);
    padding: 0;
    background: $color-white;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: inherit;
    font-weight: 400;
    color: $color-neutral-800;
    min-height: pxToRem(40);
    font-size: pxToRem(14);



    // &:focus-visible {
    //   outline: none;
    //   border-color: #007bff;
    //   box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    // }

    // &[aria-expanded="true"] {
    //   background: #f8f9fa;
    //   border-color: #d0d0d0;
    // }
  }

  &__avatar {
    position: relative;
    width: pxToRem(40);
    height: pxToRem(40);
    border-radius: 50%;
    overflow: hidden;
    background: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  &__avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
  }

  &__avatar-initials {
    color: $color-white;
    font-size: pxToRem(12);
    font-weight: 600;
    text-transform: uppercase;
  }

  &__name {
    color: $color-neutral-800;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: pxToRem(150);
    letter-spacing: 0.02em;
  }

  &__arrow {
    transition: transform 0.2s ease;
    flex-shrink: 0;

    &--open {
      transform: rotate(180deg);
    }
  }

  &__menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: pxToRem(4);
    background: $color-white;
    border: 1px solid $light-light-gray;
    border-radius: pxToRem(8);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    min-width: pxToRem(120);
    z-index: 1000;
    overflow: hidden;
    animation: fadeIn 0.2s ease;
    width: max-content;

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(pxToRem(-8));
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    &.user-info__menu-modal {
      width: 140px;
      top: unset;
      bottom: 2px;
      left: 120%;
    }
  }

  &__menu-item {
    display: block;
    width: 100%;
    padding: pxToRem(10) pxToRem(16);
    background: none;
    border: none;
    text-align: left;
    cursor: pointer;
    color: $color-neutral-800;
    font-size: pxToRem(14);
    font-weight: 400;
    font-family: inherit;
    transition: background-color 0.2s ease;
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    &:hover {
      background: #f8f9fa;
    }

    &:focus {
      outline: none;
      background: $color-rose-500;
    }

    &--focused {
      background: $color-rose-500;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: pxToRem(3);
        background: #007bff;
      }
    }

    &:active {
      background: #dee2e6;
    }

    &:not(:last-child) {
      border-bottom: 1px solid #f0f0f0;
    }

    // Special styling for logout button
    &:last-child {
      color: #dc3545;

      &:hover {
        background: #f8d7da;
      }

      &:focus,
      &--focused {
        background: #f5c6cb;
      }

      &:active {
        background: #f1b0b7;
      }

      &.user-info__menu-item--focused {
        &::before {
          background: #dc3545;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .user-info {
    &__trigger {
      padding: pxToRem(6) pxToRem(10);
      min-height: pxToRem(36);
    }

    &__avatar {
      width: pxToRem(24);
      height: pxToRem(24);
    }

    &__avatar-initials {
      font-size: pxToRem(10);
    }

    &__name {
      font-size: pxToRem(13);
      max-width: pxToRem(120);
    }

    &__menu {
      min-width: pxToRem(110);
      width: max-content;
    }

    &__menu-item {
      padding: pxToRem(8) pxToRem(12);
      font-size: pxToRem(13);
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .user-info {
    &__trigger {
      border-color: $color-neutral-800;

      &:focus {
        border-color: $black;
        box-shadow: 0 0 0 2px $black;
      }
    }

    &__menu {
      border-color: $color-neutral-800;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    }

    &__menu-item {
      border-color: $color-neutral-800;
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .user-info {
    &__trigger,
    &__arrow,
    &__menu-item {
      transition: none;
    }

    &__menu {
      animation: none;
    }
  }
}

// // Focus visible for better keyboard navigation
// .user-info__trigger:focus-visible {
//   outline: pxToRem(2) solid #007bff;
//   outline-offset: pxToRem(2);
// }

// .user-info__menu-item:focus-visible {
//   outline: pxToRem(2) solid #007bff;
//   outline-offset: pxToRem(-2);
// }
