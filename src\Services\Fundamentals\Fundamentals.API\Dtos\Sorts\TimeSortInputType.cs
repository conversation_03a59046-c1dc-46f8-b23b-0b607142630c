using System.Text.Json;
using HotChocolate.Data.Sorting;
using HotChocolate.Resolvers;
using Newtonsoft.Json.Linq;

namespace Euroland.FlipIT.Fundamental.API.Dtos.Sorts;

public class TimeSortInputType : SortInputType<TimeSortInput>
{
  public static JArray Sort(JArray data, IResolverContext resolverContext)
  {
    var sortingContextList = resolverContext.GetSortingContext()?.ToList();
    if (sortingContextList is not null && sortingContextList.Count > 0)
    {
      var sortingContextDictionary = sortingContextList[0];
      if (sortingContextDictionary.TryGetValue("date", out var dateSorting) &&
        dateSorting is string dateSortingString)
      {
        if (dateSortingString == "DESC")
        {
          //data = data.OrderByDescending(x => x.SelectToken("date").Value<DateTime>()).ToList();
          return new JArray(data.OrderByDescending(x => x.SelectToken("date").Value<DateTime>()));
        }
        else
        {
          //data = data.OrderBy(x => x.GetProperty("date").GetDateTime()).ToList();
          return new JArray(data.OrderBy(x => x.SelectToken("date").Value<DateTime>()));
        }
      }
    }

    return data;
  }
}
