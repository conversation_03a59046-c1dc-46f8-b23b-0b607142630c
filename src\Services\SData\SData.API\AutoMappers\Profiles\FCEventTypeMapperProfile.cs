using AutoMapper;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Helpers;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;

namespace Euroland.FlipIT.SData.API.AutoMapper.Profiles;

public class FCEventTypeMapperProfile : Profile
{
  public FCEventTypeMapperProfile()
  {
    var cultureName = "en-gb";

#pragma warning disable S3358 // Ternary operators should not be nested
    CreateMap<FCEventType, FCEventTypeDto>()
      .ForMember(dto => dto.Name, conf =>
        conf.MapFrom(ol => cultureName.IsCulture("en-us") ? ol.English :
          cultureName.IsCulture("fr-fr") ? ol.French :
          cultureName.IsCulture("ar-sa") ? ol.Arabic :
          cultureName.IsCulture("ar-ae") ? ol.Arabic :
          cultureName.IsCulture("fi-fi") ? ol.Finnish :
          cultureName.IsCulture("sv-se") ? ol.Swedish :
          cultureName.IsCulture("de-de") ? ol.German :
          cultureName.IsCulture("es-mx") ? ol.Spanish :
          cultureName.IsCulture("it-it") ? ol.Italian :
          cultureName.IsCulture("nl-nl") ? ol.Dutch :
          cultureName.IsCulture("ru-ru") ? ol.Russian :
          cultureName.IsCulture("pl-pl") ? ol.Polish :
          cultureName.IsCulture("zh-cn") ? ol.Chinese :
          cultureName.IsCulture("ko-kr") ? ol.Korean :
          cultureName.IsCulture("da-dk") ? ol.Danish :
          cultureName.IsCulture("is-is") ? ol.Icelandic :
          cultureName.IsCulture("vi-vn") ? ol.Vietnamese :
          cultureName.IsCulture("ja-jp") ? ol.Japanese :
          cultureName.IsCulture("pt-pt") ? ol.Portuguese :
          cultureName.IsCulture("et-ee") ? ol.Estonian :
          cultureName.IsCulture("pt-br") ? ol.Portuguese :
          cultureName.IsCulture("he-il") ? ol.Hebrew :
          cultureName.IsCulture("ku-arab") ? ol.Kurdish :
          cultureName.IsCulture("ca-es") ? ol.Catalan :
          cultureName.IsCulture("ro-ro") ? ol.Romanian :
          cultureName.IsCulture("lt-lt") ? ol.Lithuanian :
          cultureName.IsCulture("el-gr") ? ol.Greek :
          ol.English));
#pragma warning restore S3358 // Ternary operators should not be nested
  }
}
