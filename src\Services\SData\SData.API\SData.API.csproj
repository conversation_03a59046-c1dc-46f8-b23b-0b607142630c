﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <Import Project="..\..\..\Version.props" />
  <Import Project="..\EFCore.targets" />
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <LangVersion>10.0</LangVersion>
    <RootNamespace>Euroland.FlipIT.SData.API</RootNamespace>
    <AssemblyName>Euroland.FlipIT.SData.API</AssemblyName>
    <DockerComposeProjectPath>..\..\..\..\docker-compose.dcproj</DockerComposeProjectPath>
    <UserSecretsId>************************************</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..\..\..</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
	<PackageReference Include="Euroland.NetCore.ToolsFramework.Localization" Version="3.0.0" />
  <PackageReference Include="Euroland.NetCore.ToolsFramework.Translation" Version="2.0.8" />
	<!-- <PackageReference Include="GraphQL.Server.Ui.Voyager" Version="7.7.1" /> -->
	<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="7.0.1" />
	<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.17.0" />
	<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="7.0.11" />
	<PackageReference Include="OpenTelemetry.Exporter.Jaeger" Version="1.4.0-rc.1" />
	<PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.4.0-rc.1" />
	<PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.0.0-rc9.10" />
	  <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.0.0-rc9.10" />
	  <PackageReference Include="OpenTelemetry.Instrumentation.SqlClient" Version="1.0.0-rc9.10" />
	  <PackageReference Include="LinqKit" Version="1.2.3" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.12" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
    <PackageReference Include="System.Linq" Version="4.3.0" />
    <PackageReference Include="System.Linq.Async" Version="6.0.1" />
    <PackageReference Include="TimeZoneConverter" Version="6.0.1" />
    <PackageReference Include="Z.EntityFramework.Plus.EFCore" Version="*********" />
  </ItemGroup>
  <ItemGroup Condition=" '$(Configuration)' == 'Debug' ">
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Common\CachingManager\CachingManager.csproj" />
  </ItemGroup>
  <!--
    Exclude certain appSettings.*.json from publishing
  -->
  <!-- <ItemGroup>
    <Content Update="appSettings.*.json" CopyToPublishDirectory="Never" />
    <Content Update="appSettings.$(EnvironmentName).json" CopyToPublishDirectory="PreserveNewest" />
  </ItemGroup> -->
</Project>
