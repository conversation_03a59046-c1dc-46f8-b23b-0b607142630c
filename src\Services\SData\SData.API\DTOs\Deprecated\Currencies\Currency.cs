﻿using HotChocolate;

namespace Euroland.FlipIT.SData.API.DTOs.Deprecated.Currencies
{
    [GraphQLName("Currency_Deprecated")]
    public class Currency
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public bool? IsRegionMajor { get; set; }
        public byte? RegionId { get; set; }
        public int? TranslationId { get; set; }
        public int? DecimalPlace { get; set; }
    }
}
