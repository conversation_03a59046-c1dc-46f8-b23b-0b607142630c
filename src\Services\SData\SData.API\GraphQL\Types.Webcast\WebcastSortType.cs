using Euroland.FlipIT.SData.API.Dto.Webcast;
using HotChocolate.Data.Sorting;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Webcast;

/// <summary>
/// Custom sort type to enable ordering on <see cref="WebcastDto.PublishDate"/> only.
/// </summary>
public class WebcastSortType: SortInputType<WebcastDto>
{
  protected override void Configure(ISortInputTypeDescriptor<WebcastDto> descriptor)
  {
    descriptor.BindFieldsExplicitly();

    descriptor.Field(f => f.PublishDate).Type<DefaultSortEnumType>();
  }
}
