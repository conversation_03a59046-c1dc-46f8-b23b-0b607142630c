using Euroland.FlipIT.Fundamental.API.GraphQL.Types.FinCalender;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class FinCalenderTypeRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddFinCalenderType(this IRequestExecutorBuilder builder)
  {
    builder
      .AddType<FinancialEventType>()
      .AddType<FinancialEventTypeType>()
      .AddTypeExtension<FundamentalTypeExtensions>();

    return builder;
  }
}
