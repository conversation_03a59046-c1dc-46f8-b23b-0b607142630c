
using System;
using HotChocolate;

namespace Euroland.FlipIT.SData.API.DTOs.Deprecated
{
    [GraphQLName("Instrument_Deprecated")]
    public class Instrument : Infrastructure.Entities.Instrument
    {
        // -- scalar function
        // dbo.fnGetRelativeVolume(i.Id, @dayPeriod)
        public decimal? VolumeChange { get; set; }

        // -- scalar function
        // dbo.fnGetTotalTrades(i.Id, ip.Date)
        public int? TotalTrades { get; set; }

        // -- calc field
        // Ip.[Last] * I.NoShares AS MarketCap
        public decimal? MarketCap { get; set; }

        // -- calc field
        // CASE I.EPS WHEN 0 THEN 0 WHEN NULL THEN 0 ELSE Ip.[Last]/EPS
        [GraphQLName("pE")]
        public decimal? PE { get; set; }

        // -- scalar function
        // dbo.fnSSSelectSubSector(I.MarCat, I.MarketID, @LanguageId)
        public string? Industry { get; set; }

        // -- scalar function
        // dbo.fnSSSelectSectorNewSubSectors(I.EurCat, @LanguageId)
        public int? IndustryTranslationID { get; set; }

        // List name
        // CASE WHEN [ListInCurrentCulture] IS NULL THEN [i.List.EN]
        public string? ListName { get; set; }

        public string? Currency { get; set; }

        public decimal? SixMonthsChange { get; set; }
        public decimal? TwoYearsChange { get; set; }
        public decimal? ThreeYearsChange { get; set; }
        public decimal? TenYearsChange { get; set; }

        public decimal? PriceWeekAgo { get; set; }
        public decimal? PriceMonthAgo { get; set; }
        public decimal? PriceThreeMonthAgo { get; set; }
        public decimal? PriceBeginOfYear { get; set; }
        public decimal? Price52WeekAgo { get; set; }
        public decimal? Price2YearsAgo { get; set; }
        public decimal? Price5YearAgo { get; set; }

        // ------------------
        // Instrument History
        // -----------------

        // Scalar function db.fnGetStartingDate(@instruemtnId)
        public DateTime? StartingDate { get; set; }

        // Scalar function db.fnGetLatestDate(@instruemtnId)
        public DateTime? LatestDate { get; set; }

        // -----------------
        // Instrument Price
        // -----------------

        public decimal? Bid { get; set; }
        public decimal? Ask { get; set; }
        public decimal? Open { get; set; }
        public decimal? Last { get; set; }
        public decimal? High { get; set; }
        public decimal? Low { get; set; }
        public long? Volume { get; set; }
        public decimal? Mid { get; set; }
        public decimal? PrevClose { get; set; }
        public decimal? Change { get; set; }
        public decimal? ChangePercentage { get; set; }
        public decimal? TodayTurnover { get; set; }
        [GraphQLName("vWAP")]
        public decimal? VWAP { get; set; }
        public int? BidSize { get; set; }
        public int? AskSize { get; set; }
        public decimal? OfficialClose { get; set; }
        public DateTime? LastUpdatedDate { get; set; }
        public DateOnly? OfficialCloseDate { get; set; }

        // ------------------
        // Market Information
        // ------------------
        public short MarketNumber { get; set; }

        // CASE WHEN [MarketInCurrentCulture] IS NULL THEN [Market.Translation.EN]
        public string? MarketName { get; set; }

        public string? MarketAbbreviation { get; set; }

        // -- scalar function
        // dbo.fn_shgGetMarketStatus(i.MarketID, default)
        public string? MarketStatus { get; set; }

        public int? CountdownToTheOpeningBell { get; set; }
        public int? CountdownToTheClosingBell { get; set; }

        public string? MarketTimeZone { get; set; }
        public bool BusinessDaysStoT { get; set; }
        public string? NormalDailyOpen { get; set; }
        public string? NormalDailyClose { get; set; }
        public string? Symbol { get; set; }
        public decimal? AYearAgo { get; set; }
    }
}
