using System.Linq;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.Types.Instrument.Dtos;
using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using HotChocolate;
using HotChocolate.Resolvers;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;

#pragma warning disable CS8601 // Possible null reference assignment.

internal partial class InstrumentResolvers
{
  public IQueryable<InstrumentHistoryDto> GetInstrumentHistory(
    [Parent] InstrumentDto instrument,
    [Service]IGraphQLExecutionContext executionContext,
    SharkDbContextBase sharkDbContext,
    IResolverContext resolverContext
  )
  {
    var hasAdjustedClosePrice = executionContext.UseAdjClose;

    var useRealtimeData = executionContext.UseRealtime;
    var exchangeCurrency = executionContext.ExchangeCurrency;
    var hasExchangeCurrency = !string.IsNullOrEmpty(exchangeCurrency);
    if (!hasExchangeCurrency || string.IsNullOrEmpty(instrument.CurrencyCode))
    {
      var histories = GetInstrumentHistory(sharkDbContext, instrument.Id, hasAdjustedClosePrice, useRealtimeData)
        .TagWith($"{CorporateActionAdjustmentFactorCTEDbCommandInterceptor.TOKEN}");

      return histories
        .OrderByArgumentOrDefault(
          resolverContext,
          (q) => q.OrderByDescending(p => p.DateTime)
        )
        .WithAutoMapper()
        .ToProjection<InstrumentHistoryDto>();
    }

    var currencyPair = $"{instrument.CurrencyCode}{exchangeCurrency}";
    var factor = 1f;
    var customRate = default(float?);

    var rateView = sharkDbContext.InstrumentCurrencyPairQueryable(currencyPair);
    var inst = (
      from history in GetInstrumentHistory(sharkDbContext, instrument.Id, hasAdjustedClosePrice, useRealtimeData)
        .TagWith($"{CorporateActionAdjustmentFactorCTEDbCommandInterceptor.TOKEN}")
      from rate in rateView.Where(r => r.cDate.Date == history.DateTime.Date).DefaultIfEmpty()
      select new { history, rate }
    ).Select(i => new AdjustedInstrumentHistory {
      Close = i.history.Close * (i.rate != null ? i.rate.cRate : 1),
      //AdjustedClose = i.history.AdjustedClose * (i.rate != null ? i.rate.cRate : 1),
      High = i.history.High * (i.rate != null ? i.rate.cRate : 1),
      Open = i.history.Open * (i.rate != null ? i.rate.cRate : 1),
      Low = i.history.Low * (i.rate != null ? i.rate.cRate : 1),
      DateTime = i.history.DateTime,
      Volume = i.history.Volume,
      InstrumentId = i.history.InstrumentId
    });

    return inst.OrderByArgumentOrDefault(
        resolverContext,
        (q) => q.OrderByDescending(p => p.DateTime)
      )
      .WithAutoMapper()
      .ToProjection<InstrumentHistoryDto>();
  }

  private IQueryable<AdjustedInstrumentHistory> GetInstrumentHistory(
    SharkDbContextBase sharkDbContext,
    int instrumentId,
    bool hasAdjustedClosePrice,
    bool useRealtimeData)
  {
    var instrumentHistoryTable = sharkDbContext.InstrumentHistory.EntityType.GetSchemaQualifiedTableName();

    var paramsForInstrumentHistoryQuery = GetParamsForInstrumentHistoryQuery(sharkDbContext, instrumentId);

    // The column [InstrumentPrice].[Date] is in CEST timezone
    // it needs to convert to UTC timezone as well.
    // Hotchocolate just outputs the DateTime as kind of UTC, but not actually convert timezone to UTC.
    return sharkDbContext.AdjustedInstrumentHistories.FromSqlRaw($@"
      SELECT
        r.[ID],
        r.[InstrumentId],
        r.[Date],
        CAST(r.[Close] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Close],
        CAST(r.[High] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [High],
        CAST(r.[Low] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Low],
        CAST(r.[Open] * COALESCE(ca.CumulativeAdjustmentFactor, 1) AS MONEY) AS [Open],
        r.[Volume]
      FROM (
        SELECT TOP(1)
          0 AS [ID],
          p.[InstrumentId],
          p.[Date],
          COALESCE(p.[Last], 0) AS [Close],
          p.[High],
          p.[Low],
          p.[Open],
          COALESCE(p.[Volume], 0) AS [Volume]
        FROM (
          SELECT
            rt.[InstrumentId]
            ,CAST(rt.[Date] AT TIME ZONE 'Central European Standard Time' AT TIME ZONE @marketTimezone as DATE) AS [Date] -- truncate time to be compatible with history table
            ,rt.[Last]
            ,rt.[High]
            ,rt.[Low]
            ,rt.[Open]
            ,rt.[Volume]
          FROM rt_InstrumentPrice rt WHERE @realtime = 1
          UNION ALL
          SELECT
            nrt.[InstrumentId]
            ,CAST(nrt.[Date] AT TIME ZONE 'Central European Standard Time' AT TIME ZONE @marketTimezone AS DATE) AS [Date]
            ,nrt.[Last]
            ,nrt.[High]
            ,nrt.[Low]
            ,nrt.[Open]
            ,nrt.[Volume]
          FROM InstrumentPrice nrt
          WHERE @realtime = 0 OR NOT EXISTS (SELECT 1 FROM rt_InstrumentPrice WHERE @realtime = 1 AND InstrumentId = nrt.InstrumentId)
        ) AS p WHERE InstrumentId = @instrumentId

        UNION ALL

        SELECT
          h.[ID],
          h.[InstrumentId],
          h.[Date],
          h.[Close],
          h.[High],
          h.[Low],
          h.[Open],
          h.[Volume]
        FROM [{instrumentHistoryTable}] h
        WHERE h.InstrumentId = @instrumentId AND [Date] <> (SELECT MAX(CAST([Date] AT TIME ZONE 'Central European Standard Time' AT TIME ZONE @marketTimezone AS DATE)) FROM (
            -- Remove duplicated record from InstrumentPrice table
            SELECT [Date] FROM rt_InstrumentPrice rt WHERE InstrumentId = @instrumentId AND @realtime = 1
            UNION ALL
            SELECT [Date] FROM InstrumentPrice nrt
            WHERE InstrumentId = @instrumentId AND (@realtime = 0 OR NOT EXISTS (SELECT 1 FROM rt_InstrumentPrice WHERE @realtime = 1 AND InstrumentId = nrt.InstrumentId))
          ) AS p1
        )
      ) AS r
      OUTER APPLY (
          SELECT TOP 1 EXP(SUM(LOG(CumulativeAdjustmentFactor))) AS CumulativeAdjustmentFactor
          FROM {CorporateActionAdjustmentFactorCTEDbCommandInterceptor.CumulativeAdjustmentsTable}
          WHERE InstrumentId = r.InstrumentId AND r.[Date] < ActionDate
          GROUP BY InstrumentId
        ) AS ca
      ",
      new SqlParameter(CorporateActionAdjustmentFactorCTEDbCommandInterceptor.ADJ_CLOSED_PARAMETER_NAME, System.Data.SqlDbType.Bit) { Value = hasAdjustedClosePrice },
      new SqlParameter("instrumentId", System.Data.SqlDbType.Int) { Value = instrumentId },
      new SqlParameter("realtime", System.Data.SqlDbType.Bit) { Value = useRealtimeData },
      new SqlParameter("marketTimezone", System.Data.SqlDbType.NVarChar) { Value = paramsForInstrumentHistoryQuery.MarketTimezone }
    );
  }

  private GetParamsForInstrumentHistoryQueryInput GetParamsForInstrumentHistoryQuery(SharkDbContextBase sharkDbContext, int instrumentId)
  {
    var instrumentIdParam = new SqlParameter("instrumentId", System.Data.SqlDbType.Int) { Value = instrumentId };
    var marketTimezone = new SqlParameter
    {
      ParameterName = "marketTimezone",
      SqlDbType = System.Data.SqlDbType.NVarChar,
      Size = 1000,
      Direction = System.Data.ParameterDirection.Output
    };

    sharkDbContext.Database.ExecuteSqlRaw(@"
        DECLARE @marketId INT;

        SELECT @marketId = MarketID
        FROM Instrument
        WHERE Id = @instrumentId;

        SELECT @marketTimezone = TimeZone
        FROM Market
        WHERE MarketNumber = @marketId;
    ", instrumentIdParam,
      marketTimezone);

    return new GetParamsForInstrumentHistoryQueryInput()
    {
      MarketTimezone = marketTimezone.Value.ToString()
    };
  }
}
#pragma warning restore CS8601 // Possible null reference assignment.
