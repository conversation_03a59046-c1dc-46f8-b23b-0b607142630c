import { LayoutPosition } from "@/config/interface";
import { FOLLOWING_STATUS } from "@/helpers/constants";
import { ICompanyInfo } from "@/services/apiEndpoints";
import { AISearchSettings } from "@/services/getAISearchSettings";
import { XProps } from "@/zoid";
import { FC, memo, useContext, useLayoutEffect, useState } from "react";
import { createContext, useSyncExternalStore,  } from "react";

export type SearchWidgetPropsContext = XProps<{
  isOpen: boolean;
  isLogin: boolean;
  onLogin: () => void;
  onCloseWindow: () => void;
  onSignOutSuccess: () => void;
  onOpenExternalLink: () => void;
  avatarUrl: string;
  companyInfo: ICompanyInfo;
  getAccessToken: () => Promise<string>;
  shortName: string;
  position: LayoutPosition;
  userFollowStatus: FOLLOWING_STATUS;
  updateStatus: (status: FOLLOWING_STATUS) => void;
  aiSearchSettings: AISearchSettings;
}>;

interface ContextWrapper {
  context: SearchWidgetPropsContext;
  subscribe: (listener: () => void) => () => void;
  unsubscribe: (listener: () => void) => void;
  setContext: (context: SearchWidgetPropsContext) => void;
  getContext: () => SearchWidgetPropsContext;
}


const SearchWidgetPropsContext = createContext<ContextWrapper>(
  undefined as unknown as ContextWrapper
);

export const useSearchWidgetPropsContext = function <T = SearchWidgetPropsContext>(
  selector: (context: SearchWidgetPropsContext) => T = (context) => context as T
): T {
  const context = useContext(SearchWidgetPropsContext);
  return useSyncExternalStore(context.subscribe, () => selector(context.getContext()));
};

export const wrapWithContext = (Component: FC) => {
  const MemoizedComponent = memo(Component);

  const Wrapper = ((props) => {
    const [contextWrapper] = useState(() => {
      let context: SearchWidgetPropsContext = props;
      let listeners: (() => void)[] = [];

      const unsubscribe = (listener: () => void) => {
        listeners = listeners.filter((l) => l !== listener);
      };

      const subscribe = (listener: () => void) => {
        listeners.push(listener);
        return () => {
          unsubscribe(listener);
        };
      };

      const fire = () => {
        listeners.forEach((listener) => listener());
      };
      
      const setContext = (newContext: SearchWidgetPropsContext) => {
        if (context === newContext) return;
        context = newContext;
        fire();
      };

      const getContext = () => context;

      return { context, subscribe, unsubscribe, setContext, getContext };
    });

    useLayoutEffect(() => {
      contextWrapper.setContext(props);
    }, [props]) 

    return (
      <SearchWidgetPropsContext.Provider value={contextWrapper}>
        <MemoizedComponent />
      </SearchWidgetPropsContext.Provider>
    );
  }) as FC<SearchWidgetPropsContext>;

  Wrapper.displayName = `${Component.displayName || Component.name}`;
  return Wrapper;
};
