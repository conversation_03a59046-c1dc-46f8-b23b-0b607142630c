using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Company;

#pragma warning disable CS8625
public class CompanyType : ObjectType<CompanyDto>
{
  public const string Name = "Company";
  protected override void Configure(IObjectTypeDescriptor<CompanyDto> descriptor)
  {
    descriptor.Name(Name);
    descriptor.Description("Represents a company in the Euroland system.");

    descriptor.Field(f => f.Id).IsProjected(true);
    descriptor.Field(f => f.Code).IsProjected(true);
    descriptor.Field(f => f.CustomerTypeId).IsProjected(true);
    descriptor.Field(f => f.MarketId).IsProjected(true);
  }
}
