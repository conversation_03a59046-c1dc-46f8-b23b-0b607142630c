using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Euroland.FlipIT.SData.API.Infrastructure.Configurations;

public class MarketDepthConfiguration: IEntityTypeConfiguration<MarketDepth>
{
    public void Configure(EntityTypeBuilder<MarketDepth> builder)
    {
        builder.Property(o=>o.Fid_Best_Ask_1).HasColumnType("money");
        builder.Property(o=>o.Fid_Best_Ask_2).HasColumnType("money");
        builder.Property(o=>o.Fid_Best_Ask_3).HasColumnType("money");
        builder.Property(o=>o.Fid_Best_Ask_4).HasColumnType("money");
        builder.Property(o=>o.Fid_Best_Ask_5).HasColumnType("money");
        builder.Property(o=>o.Fid_Best_Ask_6).HasColumnType("money");
        builder.Property(o=>o.Fid_Best_Ask_7).HasColumnType("money");
        builder.Property(o=>o.Fid_Best_Ask_8).HasColumnType("money");
        builder.Property(o=>o.Fid_Best_Ask_9).HasColumnType("money");
        builder.Property(o=>o.Fid_Best_Ask_10).HasColumnType("money");

        builder.Property(o=>o.Fid_Best_Bid_1).HasColumnType("money");
        builder.Property(o=>o.Fid_Best_Bid_2).HasColumnType("money");
        builder.Property(o=>o.Fid_Best_Bid_3).HasColumnType("money");
        builder.Property(o=>o.Fid_Best_Bid_4).HasColumnType("money");
        builder.Property(o=>o.Fid_Best_Bid_5).HasColumnType("money");
        builder.Property(o=>o.Fid_Best_Bid_6).HasColumnType("money");
        builder.Property(o=>o.Fid_Best_Bid_7).HasColumnType("money");
        builder.Property(o=>o.Fid_Best_Bid_8).HasColumnType("money");
        builder.Property(o=>o.Fid_Best_Bid_9).HasColumnType("money");
        builder.Property(o=>o.Fid_Best_Bid_10).HasColumnType("money");
    }
}