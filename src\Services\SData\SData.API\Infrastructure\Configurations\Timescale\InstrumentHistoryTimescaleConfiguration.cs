using Euroland.FlipIT.SData.API.Infrastructure.Entities.Shark;
using Euroland.FlipIT.SData.API.Infrastructure.Entities.Timescale;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Euroland.FlipIT.SData.API.Infrastructure.Configurations.Timescale;

public class InstrumentHistoryTimescaleConfiguration : IEntityTypeConfiguration<InstrumentHistoryTimescale>
{
  public void Configure(EntityTypeBuilder<InstrumentHistoryTimescale> builder)
  {
    builder.HasNo<PERSON>ey();
    builder.ToTable("ohlcv_group_by_second");
    builder.Property(o => o.InstrumentId).HasColumnName("instrument_id");
    builder.Property(o => o.DateTime).HasColumnName("second_bucket_grouped");
    builder.Property(o => o.Open).HasColumnName("open").HasColumnType("money");
    builder.Property(o => o.High).HasColumnName("high").HasColumnType("money");
    builder.Property(o => o.Low).HasColumnName("low").HasColumnType("money");
    builder.Property(o => o.Close).HasColumnName("close").HasColumnType("money");
    builder.Property(o => o.Volume).HasColumnName("volume").HasColumnType("bigint");
  }
}
