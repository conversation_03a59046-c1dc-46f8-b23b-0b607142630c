namespace Euroland.FlipIT.SData.API.Dto;

/// <summary>
/// Represents the information of a currency of the Euroland system.
/// </summary>
public class CurrencyDto: HasTranslation, IDtoObject
{
  public string Code { get; set; }
  public string Name { get; set; }
  public bool? IsRegionMajor { get; set; }
  public byte? RegionId { get; set; }
  public int? TranslationId { get; set; }
  public int? DecimalPlace { get; set; }
  public string? Symbol { get; set; }
}
