{"Urls": "https://*:5000", "Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": "Debug", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}, "WellKnownSchemas": [{"IsRootType": true, "Name": "sdata", "RemoteUrl": "https://localhost:5005/graphql"}, {"Name": "fundamental", "RemoteUrl": "https://localhost:5007/graphql"}]}