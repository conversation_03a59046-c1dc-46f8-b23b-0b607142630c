import {useAuth} from "@/auth";
import { ZoidAlertWidget } from "@/components/AlertWidget";
import {FOLLOWING_STATUS} from "@/helpers/constants";
import usePrevious from "@/hooks/usePrevious";
import {useAppStore} from "@/store/useAppStore";
import {useEffect} from "react";

const AlertControl = () => {
  const isNotificationOpen = useAppStore(state => state.isNotificationOpen)
  const updateStatus = useAppStore(state => state.userProfile.updateStatus);
  const status = useAppStore(state => state.userProfile.status);
  const auth = useAuth()
 
  const isAuthenticated = auth.isAuthenticated
  const isAuthenticatedPrevious = usePrevious(auth.isAuthenticated);
  const userName = auth.user?.profile.given_name ?? '';
  const isLogout = isAuthenticatedPrevious === true && isAuthenticated === false

  
  const alertWidget = ZoidAlertWidget.use({
    followStatus: status,
    changeFollowStatus: (status: FOLLOWING_STATUS) => {
      updateStatus(status);
    },
    isLogout,
    userName
  }, { defaultHidden: false })

  useEffect(() => {
    if(isNotificationOpen) {
      alertWidget.show();
    } else {
      alertWidget.hide();
    }
  }, [alertWidget, isNotificationOpen]);

  useEffect(() => {
    alertWidget.updateProps({
      followStatus: status,
      isLogout,
      userName
    })
  }, [status, isLogout, userName])

  return null
};

export default AlertControl;