using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using HotChocolate;
using HotChocolate.Resolvers;
using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;

public class InstrumentHistoryType: ObjectType<InstrumentHistoryDto>
{
  public const string ADJ_CLOSE_FIELD = "adjClose";
  protected override void Configure(IObjectTypeDescriptor<InstrumentHistoryDto> descriptor)
  {
    descriptor.Name("InstrumentHistory");

    descriptor.Field(f => f.InstrumentId).IsProjected();

    descriptor.Field("instrument")
      .Type<ObjectType<InstrumentDto>>()
      .ResolveWith<Resolvers>(p => p.GetInstrument(default!, default!, default!, default));
  }

  private class Resolvers
  {
    public async Task<InstrumentDto> GetInstrument(
      IResolverContext resolverContext,
      [Parent] InstrumentHistoryDto historyDto,
      InstrumentByIdDataLoader dataLoader,
      CancellationToken cancellationToken = default
    ) => await dataLoader.LoadAsync(historyDto.InstrumentId, cancellationToken);
  }
}
