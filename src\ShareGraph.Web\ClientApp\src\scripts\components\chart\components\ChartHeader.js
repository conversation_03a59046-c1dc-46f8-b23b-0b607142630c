import useChartTitle from '../../../customHooks/useChartTitle';
import ChartShowRange from './ChartShowRange';
import ChartTitle from './ChartTitle';
import useComparisonWrapperStyle from '../../../customHooks/useComparisonWrapperStyle';
import { useSelectedTicker } from '../../../customHooks/useTickers';

const ChartHeader = () => {
  const chartTitleData = useChartTitle();
  const { chartHeaderRef } = useComparisonWrapperStyle();
  const selectedTicker = useSelectedTicker();
  return (
    <div className="chart-header" ref={chartHeaderRef}>
      

      <div className="chart-header__title-wrapper">
        <div className="chart-header__title-add-instrument">
          <ChartTitle chartTitleData={chartTitleData} />
          <euroland-add-instrument-button instrumentId={selectedTicker.instrumentId}></euroland-add-instrument-button>
        </div>
        <div className="chart-header__show-range">
          <euroland-add-instrument-button instrumentId={selectedTicker.instrumentId}></euroland-add-instrument-button>
          <ChartShowRange />
        </div>
      </div>
    </div>
  );
};

export default ChartHeader;
