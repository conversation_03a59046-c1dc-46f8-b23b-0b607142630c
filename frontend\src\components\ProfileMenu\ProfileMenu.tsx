import { zoidComponentCreator } from "@/zoid-components";
import { i18n } from "@euroland/libs";
import { ExternalLink, LogOut } from "../Common";
import { FC } from "react";
import { appSettings } from "@/config/appSettings";
import clsx from "clsx";

interface ProfileMenuProps {
  onLogout: () => void;
  onExternalLink: () => void;
}

const ProfileMenu: FC<ProfileMenuProps> = ({ onLogout, onExternalLink }) => {

  return (
    <div
      className={clsx(
        "profile-menu bg-white rounded-md shadow-lg py-1",
        appSettings.isRTL ? "left-0" : "right-0"
      )}
    >
      <div
        className="wise-widget-profile-menu-item cursor-pointer"
        onClick={() => onExternalLink()}
      >
        <ExternalLink className="w-4 h-4" />
        <span>{i18n.translate("openMy")} EurolandID</span>
      </div>
      <div className="wise-widget-profile-menu-divider" />
      <div
        className="wise-widget-profile-menu-item cursor-pointer"
        onClick={() => onLogout()}
      >
        <LogOut className="w-4 h-4 fill-current text-[#666]" />
        <span>{i18n.translate("logout")}</span>
      </div>
    </div>
  );
};

export const ZoidProfileMenu = zoidComponentCreator(ProfileMenu, {
  props: {
    onLogout: {
      type: "function",
      required: true,
    },
    onExternalLink: {
      type: "function",
      required: true,
    },
  },
  dimensions: {
    width: "180px",
  },
  autoResize: {
    height: true,
    element: "#root",
  },
  template: {
    styles: {
      bottom: "100px",
      ...(appSettings.isRTL
        ? { left: "20px" }
        : { right: "20px" }),
      "border-radius": "8px",
      "box-shadow": "0 4px 12px rgba(0,0,0,0.15)",
    },
    backdrop: true,
    backdropBgColor: "transparent",
  },
});

export default ProfileMenu;
