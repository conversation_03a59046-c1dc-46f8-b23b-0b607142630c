using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;

public class InstrumentPriceType : ObjectType<InstrumentPriceDto>
{
  protected override void Configure(IObjectTypeDescriptor<InstrumentPriceDto> descriptor)
  {
    descriptor.Name("InstrumentPrice");

    descriptor.Field(f => f.InstrumentId).IsProjected();

    descriptor.Field("instrument")
      .ResolveWith<InstrumentPriceResolvers>(r => r.GetInstrument(default!, default!, default!, default));

    descriptor.Field("tickerData")
      .Description("Recalculate the last price, previous close price, change, change percentage of the instrument for using to get same data as chart.")
      .ResolveWith<InstrumentPriceResolvers>(r => r.GetPrevData(default!, default!, default!, default!));
  }
}
