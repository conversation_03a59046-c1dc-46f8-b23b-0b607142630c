using Serilog;
using Polly;
using Euroland.FlipIT.ApiGateway;

var builder = WebApplication.CreateBuilder(args);

var logger = new LoggerConfiguration()
          .WriteTo.Console()
          .ReadFrom.Configuration(builder.Configuration)
          .CreateLogger();

builder.Logging.ClearProviders();
builder.Logging.AddSerilog(logger);

// Graphql Schema stitching/federation needs HttpClient.
var remoteSchemas = builder.Configuration
    .GetSection("WellKnownSchemas")
    .Get<IEnumerable<WellKnownSchemaSetting>>();

builder.Services.AddHttpContextAccessor();
// TODO: Add health-check to downstream API(s)
builder.Services.AddHealthChecks();
builder.Services.AddTransient<ForwardRequestHttpClientHandler>();

builder.Services.AddCors(o => o.AddPolicy("AllowAllCorsPolicy", builder => {
  builder.AllowAnyOrigin()
    .AllowAnyMethod()
    .AllowAnyHeader();
}));

var graphqlExecutorBuilder = builder.Services.AddGraphQLServer();

// Disable Instrospection on Production
if(builder.Environment.IsDevelopment() || builder.Environment.IsEnvironment("QA") || builder.Environment.IsEnvironment("Gamma"))
{
    graphqlExecutorBuilder
      .AllowIntrospection(true);
}
else
{
    graphqlExecutorBuilder
        .AllowIntrospection(false)
        .ModifyRequestOptions(o => {
            o.Complexity.Enable = true;
            o.Complexity.ApplyDefaults = true;
            o.Complexity.DefaultComplexity = 1;
            o.Complexity.DefaultResolverComplexity = 5;
        });
}

if(remoteSchemas.Count(p => p.IsRootType) != 1) {
  throw new InvalidOperationException("Only one remote schema can be set as a root type. Verify it at appSettings.json -> WellKnownSchemas -> IsRootType");
}

foreach (var schema in remoteSchemas)
{
    // Add remote schema
    graphqlExecutorBuilder.AddRemoteSchema(schema.Name, ignoreRootTypes: !schema.IsRootType);
    // Register HttpClient for remote schema.
    builder.Services
        .AddHttpClient(schema.Name, httpClient => {
            httpClient.BaseAddress = new System.Uri(schema.RemoteUrl);
        })
        .AddHttpMessageHandler<ForwardRequestHttpClientHandler>()
        .AddTransientHttpErrorPolicy(policyBuilder =>
            policyBuilder.WaitAndRetryAsync(3, retryNumber => System.TimeSpan.FromMilliseconds(600))
        );
}

graphqlExecutorBuilder
  // .AddCacheControl()
  // .UseQueryCachePipeline()
  .InitializeOnStartup();

var app = builder.Build();

app.UseCors("AllowAllCorsPolicy");
app.UseRouting();
app.UseEndpoints(endpoints => {
      endpoints.MapGraphQL();
      endpoints.MapHealthChecks("/health");
  });
// if(builder.Environment.IsDevelopment()
//   || builder.Environment.IsEnvironment("QA")
//   || builder.Environment.IsEnvironment("Gamma"))
// {
//   app.UseEndpoints(endpoints => {
//       endpoints.MapGraphQL();
//   });
// } else {
//   app.UseEndpoints(endpoints => {
//       endpoints.MapGraphQL().WithOptions(new HotChocolate.AspNetCore.GraphQLServerOptions {
//         EnableGetRequests = false,
//         Tool = { Enable = false }
//       });
//   });
// }

app.Run();
