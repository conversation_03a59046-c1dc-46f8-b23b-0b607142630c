﻿using Euroland.FlipIT.SData.API.DTOs.Deprecated.Webcasts;
using Euroland.FlipIT.SData.API.Infrastructure;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Euroland.FlipIT.SData.API.Services.Interfaces
{
    public interface IWebcastService: IAsyncDisposable
    {
        Task<IQueryable<WebcastDetailOutputDto>> GetWebcast(WebcastContext webcastContext, string companyCode, CancellationToken cancellationToken = default);
    }
}
