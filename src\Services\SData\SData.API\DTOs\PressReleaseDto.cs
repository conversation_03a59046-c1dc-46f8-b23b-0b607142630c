using System;

namespace Euroland.FlipIT.SData.API.Dto;

public class PressReleaseDto: IDtoObject
{
  public long Id { get; set; }
  public DateTime DateTime { get; set; }
  public string CompanyCode { get; set; }
  public int LanguageId { get; set; }
  //public string? Message { get; set; }
  public string? Title { get; set; }
  public int? MessageTypeId { get; set; }
  public bool HasAttachment { get; set; }
  public int SourceId { get; set; }
  public bool? IsHidden { get; set; }
  public DateTime InsertedDate { get; set; }
  public MessageTypeDto? MessageType { get; set; }

  [Obsolete]
  public string? Date {get; set;}
}
