using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Infrastructure.Configurations.Timescale;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Npgsql;
using NpgsqlTypes;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities;

public class TemporaryCurrencyRateHistoryDbCommandInterceptorTimescale : DbCommandInterceptor
{
  public const string END_TAG_PLACEHOLDER = "//END_TAG//";

  private static readonly Regex currencyPairReg = new Regex(@"\<(currencyPair|factor|fixedRate):([^><]*)\>",
    RegexOptions.IgnoreCase | RegexOptions.Compiled);

  public override InterceptionResult<DbDataReader> ReaderExecuting(DbCommand command, CommandEventData eventData,
    InterceptionResult<DbDataReader> result)
  {
    ManipulateCommand(command);
    return base.ReaderExecuting(command, eventData, result);
  }

  public override ValueTask<InterceptionResult<DbDataReader>> ReaderExecutingAsync(DbCommand command,
    CommandEventData eventData, InterceptionResult<DbDataReader> result, CancellationToken cancellationToken = default)
  {
    ManipulateCommand(command);
    return base.ReaderExecutingAsync(command, eventData, result, cancellationToken);
  }

  private static void ManipulateCommand(DbCommand command)
  {
    var originalCmdText = command.CommandText;
    var placeholderIndex = GetPlaceholderIndex(originalCmdText);

    if (placeholderIndex < 0)
      return;

    var matches = currencyPairReg.Matches(originalCmdText);

    if (matches.Count > 0)
    {
      var parameters = new List<NpgsqlParameter>();

      for (int i = 0; i < matches.Count; i++)
      {
        if (matches[i].Groups[1].Value == "currencyPair" && matches[i].Groups[2].Value.Length == 6)
        {
          var currencyPairParam = new NpgsqlParameter("currencyPair", NpgsqlDbType.Char, 6)
          {
            Value = matches[i].Groups[2].Value
          };
          parameters.Add(currencyPairParam);
        }
      }

      var hasCountClause = originalCmdText.IndexOf("COUNT(*)", StringComparison.InvariantCultureIgnoreCase) > 0;

      var hasCountParam = new NpgsqlParameter("hasCount", NpgsqlDbType.Boolean)
      {
        Value = hasCountClause
      };
      parameters.Add(hasCountParam);

      command.Parameters.AddRange(parameters.ToArray());

      var newCommandText = originalCmdText.Insert(placeholderIndex, Environment.NewLine);
      newCommandText =
        newCommandText.Insert(placeholderIndex + Environment.NewLine.Length, CreateTempTable(hasCountClause));

      newCommandText += $@";
                {Environment.NewLine}
                DROP TABLE IF EXISTS {TemporaryCurrencyRateHistoryTimescaleConfiguration.TemporaryCurrencyRateHistoryTableName};
            ";

      command.CommandText = newCommandText;
    }
  }

  private static int GetPlaceholderIndex(string commandText)
  {
    var startIndex = commandText.IndexOf(
      $"-- {TemporaryCurrencyRateHistoryTimescaleConfiguration.TemporaryCurrencyRateHistoryTableName}",
      StringComparison.Ordinal);

    if (startIndex != -1)
    {
      var newIndex = commandText.IndexOf(END_TAG_PLACEHOLDER, startIndex, StringComparison.Ordinal);
      if (newIndex != -1)
      {
        newIndex += END_TAG_PLACEHOLDER.Length;
        return newIndex;
      }
    }

    return -1;
  }

  private static string CreateTempTable(bool hasCountClause)
  {
    var tableName = TemporaryCurrencyRateHistoryTimescaleConfiguration.TemporaryCurrencyRateHistoryTableName;

    if (hasCountClause)
    {
      return $@"
                CREATE TEMP TABLE {tableName}
                (
                    id bigint
                    rate NUMERIC(21,7),
                    date TIMESTAMP WITHOUT TIME ZONE
                );
            ";
    }

    return $@"
            CREATE TEMP TABLE {tableName} AS
            WITH currency_rate_id_cte AS (
                SELECT id AS cid
                FROM currency_rates
                WHERE currencies = @currencyPair
                LIMIT 1
            )
            SELECT
                c.id,
                c.rate,
                c.date
            FROM (
                (SELECT 0 AS id, rate, date
                    FROM currency_rates
                    WHERE id = (SELECT cid FROM currency_rate_id_cte)
                    LIMIT 1)

                UNION ALL

                SELECT id, rate, date
                  FROM currency_rate_history
                  WHERE currency_rate_id = (SELECT cid FROM currency_rate_id_cte)
            ) AS c;
        ";
  }
}
