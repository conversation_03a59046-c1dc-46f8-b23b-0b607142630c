﻿using System;
using System.Collections.Generic;
using HotChocolate;

namespace Euroland.FlipIT.SData.API.DTOs.Deprecated
{
  [GraphQLName("StockData_Deprecated")]
    public class StockData
    {
        public int InstrumentId { get; set; }
        public DateTime DateTime { get; set; }
        public string Date { get; set; }

        public decimal? FirstPrice { get; set; }
        public decimal? LastPrice { get; set; }

        public decimal? Change { get; set; }
        public decimal? ChangePercentage { get; set; }

        public decimal? HighestPrice { get; set; }
        public DateTime? HighestPriceDate { get; set; }
        public decimal? LowestPrice { get; set; }
        public DateTime? LowestPriceDate { get; set; }

        public long? TotalVolume { get; set; }
        public DateTime? HighestVolumeDate { get; set; }
        public DateTime? LowestVolumeDate { get; set; }


        public decimal? TotalReturn { get; set; }
        public bool DividendEvent { get; set; }
        public string SplitEvent { get; set; }

        // Moving Averages
        [GraphQLName("mA10")]
        public decimal? MA10 { get; set; }

        [GraphQLName("mA20")]
        public decimal? MA20 { get; set; }

        [GraphQLName("mA50")]
        public decimal? MA50 { get; set; }

        // Peers & Indices
        public IEnumerable<CompareData>? Compares { get; set; }

    }

    public class CompareData
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public decimal? ChangePercentage { get; set; }
        public DateTime DateTime { get; set; }
    }

    public class CompareRawData
    {
        public decimal? ChangePercentage { get; set; }
        public DateTime DateTime { get; set; }
    }

    public class CompareInfo
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public IEnumerable<CompareRawData> Data { get; set; }
    }

    public class StockRawData
    {
        public int InstrumentId { get; set; }
        public DateTime RoundDate { get; set; }
        public DateTime DateTime { get; set; }
        public decimal? Open { get; set; }
        public decimal? High { get; set; }
        public decimal? Low { get; set; }
        public decimal? Close { get; set; }
        public long? Volume { get; set; }

    }

    public enum TIME_SERIES
    {
        DAILY,
        WEEKLY,
        MONTHLY,
        INTRADAY
    }

    public enum INTRADAY_INTERVAL
    {
        ONE_MINUTE,
        FIVE_MINUTES,
        TEN_MINUTES,
        ONE_HOUR,
    }

    public enum INTRADAY
    {
        ONE_DAY,
        FIVE_DAYS,
        CUSTOM_RANGE
    }

    public enum Historical
    {
        DAILY,
        WEEKLY,
        MONTHLY
    }

    public enum Periods
    {
        ONE_MONTH,
        THREE_MONTHS,
        SIX_MONTHS,
        YTD,
        ONE_YEAR,
        THREE_YEARS,
        FIVE_YEARS,
        ALL,
        CUSTOM_RANGE
    }

    public enum MovingAverages
    {
        MA10,
        MA20,
        MA50
    }

}
