using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Types;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.City;

/// <summary>
/// Extends 'city' field to type <see cref="Market.MarketType"/>
/// </summary>
public class MarketTypeExtensions : ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(Market.MarketType.Name);

    descriptor.Field("city")
      .Type<CityType>()
      .Resolve(async ctx => await ctx.DataLoader<CityByIdDataLoader>().LoadAsync(ctx.Parent<MarketDto>().CityId));
  }
}
