import { OidcAuthContext  } from './OidcAuthContext';
import { useContext } from 'react';

/**
 * Custom hook to access the OidcAuthContext.
 *
 * This hook provides the authentication context for the application.
 * It must be used within a component that is a child of an <OidcAuthProvider>.
 *
 * @returns {import('./OidcAuthContext').OidcAuthContextProps} The authentication context.
 * @throws {Error} If the hook is used outside of an <OidcAuthProvider>.
 */
export function useAuth() {
  const context = useContext(OidcAuthContext);

  if (!context) {
    throw new Error('OidcAuthProvider context is undefined, please verify you are calling useAuth() as child of a <OidcAuthProvider> component.');
  }

  return context;
};
