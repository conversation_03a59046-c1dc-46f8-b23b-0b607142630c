using Euroland.FlipIT.SData.API.GraphQL.Types.CurrencyRateHistory;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class CurrencyRateHistoryRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddCurrencyRateHistoryType(this IRequestExecutorBuilder builder)
  {
     builder.AddType<CurrencyRateHistoryType>();
     builder.AddTypeExtension<CurrencyRateTypeExtensions>();
     return builder;
  }
}
