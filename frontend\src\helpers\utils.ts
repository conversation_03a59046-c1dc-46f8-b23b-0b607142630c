/**
 * Returns the first path segment of the current router
 * @param href - The current URL
 * @param subPath - The subPath path
 * @returns The first path segment of the current router
 */
export function currentRouter (href: string, subPath: string = '') {
  const subPathUrl = new URL(subPath, href).toString()
  const pathname = new URL(href.replace(subPathUrl, ''), href).pathname
  return pathname.split('/').filter(Boolean)
}

/**
 * Ensures that value is defined.
 * Throws if the value is undefined, returns the original value otherwise.
 *
 * @param value - The value, or undefined.
 * @returns The passed value, if it is not undefined
 */
export function ensureDefined(value: undefined): never;
export function ensureDefined<T>(value: T | undefined): T;
export function ensureDefined<T>(value: T | undefined): T {
	if (value === undefined) {
		throw new Error('Value is undefined');
	}

	return value;
}

/**
 * Ensures that the value is not null.
 * Throws if the value is null, returns the original value otherwise.
 *
 * @param value - The value, possibly null.
 * @returns The passed value, if it is not null.
 */
export function ensureNotNull(value: null): never;
export function ensureNotNull<T>(value: T | null ): T;
export function ensureNotNull<T>(value: T | null ): T {
  if (value === null) {
    throw new Error('Value is null');
  }
  return value;
}

export type DeepPartial<T> = {
	[P in keyof T]?: T[P] extends (infer U)[] ? DeepPartial<U>[] : T[P] extends readonly (infer X)[] ? readonly DeepPartial<X>[] : DeepPartial<T[P]>;
};

export function getAvatarInitials(username: string): string {
  if (!username) return "";
  const trimmed = username.trim();
  const words = trimmed.split(/\s+/);
  let initials = "";
  if (words.length > 1) {
      initials = words[0][0] + words[words.length - 1][0];
  } else {
      initials = trimmed.slice(0, 2);
  }
  return initials.toUpperCase();
}
