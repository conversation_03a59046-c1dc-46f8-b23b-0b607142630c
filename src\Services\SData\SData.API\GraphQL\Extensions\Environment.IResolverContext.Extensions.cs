using HotChocolate.Resolvers;
using Microsoft.Extensions.Hosting;

namespace Microsoft.Extensions.DependencyInjection;

public static class EnvironmentIResolverContextExtensions
{
  public static IHostEnvironment Environment(this IResolverContext resolverContext)
  {
    return resolverContext.RequestServices.GetRequiredService<IHostEnvironment>();
  }

  public static bool IsDevelopment(this IResolverContext resolverContext)
  {
    return resolverContext.Environment().IsDevelopment();
  }

  public static bool IsProduction(this IResolverContext resolverContext)
  {
    return resolverContext.Environment().IsProduction();
  }

  public static bool IsStaging(this IResolverContext resolverContext)
  {
    return resolverContext.Environment().IsProduction();
  }

  public static bool IsEnvironment(this IResolverContext resolverContext, string environmentName)
  {
    return resolverContext.Environment().IsEnvironment(environmentName);
  }
}
