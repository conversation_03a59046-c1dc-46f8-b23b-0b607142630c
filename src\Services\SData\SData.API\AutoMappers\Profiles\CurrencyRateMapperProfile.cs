using AutoMapper;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;

namespace Euroland.FlipIT.SData.API.AutoMapper.Profiles;

public class CurrencyRateMapperProfile : Profile
{
  public CurrencyRateMapperProfile()
  {
    CreateMap<CurrencyRate, CurrencyRateDto>()
      .ForMember(d => d.Pair, cfg => cfg.MapFrom(e => e.Currencies))
      .ForMember(d => d.Value, cfg => cfg.MapFrom(e => e.Rate))
      .ForMember(d => d.Date, cfg => cfg.MapFrom(e => e.Date!.Value));
  }
}
