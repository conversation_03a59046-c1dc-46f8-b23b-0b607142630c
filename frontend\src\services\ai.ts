export type AiSearchStreamResponse = {
  aiResultLanguage: string;
  answer: string;
  key_values: string;
  original_response: string;
  prompt: string;
  qaLanguage: string;
  source: string;
}

export type IAiSearchHistory = {
  id: string;
  question: string;
  aiResult: string;
  searchTime: string;
  companyCode?: string;
  companyName?: string;
  qaLanguage: string;
  aiResultLanguage: string;
  resultShow: string;
}


export const aiSearch = async (company_code: string, question: string): Promise<IAiSearchHistory> => {
  const result = await fetch(`https://se-wise-api.euroland.com/api/v1/query`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      company_code,
      question,
      "response_model": "openai",
      "semantic_count": 3
  }),
  })
  return result.json()
}
