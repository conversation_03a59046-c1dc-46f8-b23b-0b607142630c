<Project Sdk="Microsoft.NET.Sdk.Web">
  <Import Project="..\..\Version.props" />
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>Euroland.FlipIT.ApiGateway</RootNamespace>
    <AssemblyName>Euroland.FlipIT.ApiGateway</AssemblyName>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="8.0.7" />
  </ItemGroup>
</Project>
