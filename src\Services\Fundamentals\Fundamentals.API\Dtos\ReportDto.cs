namespace Euroland.FlipIT.Fundamental.API.Dtos;

public class ReportDto
{
    public long Id { get; set; }
    public string Title { get; set; }
    public string? SubTitle { get; set; }
    public string LanguageCode { get; set; }
    public string? LanguageName { get; set; }
    public string? FileLocation { get; set; }
    public DateTime UploadedDate { get; set; }
    public DateTime UploadedDateUTC
    {
        get => TimeZoneInfo.ConvertTimeToUtc(UploadedDate);
        set => UploadedDate = value;
    }
    public string ReportParentTypeDisplayName{get;set; }
    public string ReportTypeName{get;set;}
    public string ThumbnailFileLocation{get;set; }

    public string ThumbnailFileLocationFull{get;set;}

    public int ReportTypeId{get;set; }
    public int ReportParentTypeId{get;set; }
    public int Year{get;set; }
    public int ReportParentTypeOrder{get;set; }
    public int ReportTypeOrder{get;set; }
}
