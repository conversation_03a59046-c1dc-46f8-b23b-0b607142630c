using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Reflection;
using Euroland.FlipIT.SData.API.Dto;
using GreenDonut;
using HotChocolate.Resolvers;
using HotChocolate.Types;
using Microsoft.Extensions.DependencyInjection;
using Euroland.FlipIT.SData.API.Infrastructure;
using Microsoft.Data.SqlClient;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Threading;
using Euroland.FlipIT.SData.API.Infrastructure.Factories;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.Shared.CachingManager;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;


namespace Euroland.FlipIT.SData.API.GraphQL.Types.Market;

public class MarketStatusByMarketIdDataLoader : BatchDataLoader<int, MarketStatusDto>
{
  static MethodInfo marketStatusMethodInfo = typeof(SharkDbContextBase).GetMethod(nameof(SharkDbContextBase.GetMarketStatus));

  private readonly ISharkDbContextAbstractFactory _factory;
  private readonly IGraphQLExecutionContext _queryRequestContext;
  private readonly IConfigurableCacheManager _configurableCacheManager;

  public MarketStatusByMarketIdDataLoader(
    ISharkDbContextAbstractFactory factory,
    IGraphQLExecutionContext queryRequestContext,
    IConfigurableCacheManager configurableCacheManager,
    IBatchScheduler batchScheduler,
    DataLoaderOptions? options = null
    )
    : base(batchScheduler, options)
  {
    _factory = factory ?? throw new ArgumentNullException(nameof(factory));
    _queryRequestContext = queryRequestContext ?? throw new ArgumentNullException(nameof(queryRequestContext));
    _configurableCacheManager = configurableCacheManager ?? throw new ArgumentNullException(nameof(configurableCacheManager));
  }

  protected override async Task<IReadOnlyDictionary<int, MarketStatusDto>> LoadBatchAsync(IReadOnlyList<int> keys, CancellationToken cancellationToken)
  {
    var marketDTOsFromCache = new List<MarketStatusDto>();
    var marketIDsNeedToFetchNew = new List<int>();

    foreach (var mid in keys.Distinct())
    {
      var dto = _configurableCacheManager.GetCache<MarketStatusDto, string>(
        CacheKeyHelper.GenerateKeyByPropOfDtoObject<MarketStatusDto, int>(mid.ToString(), m => m.MarketId)
      );
      if (dto != null)
      {
        marketDTOsFromCache.Add(dto);
      }
      else
      {
        marketIDsNeedToFetchNew.Add(mid);
      }
    }

    using var sharkDbContext = await _factory.CreateDbContextAsync(_queryRequestContext.UseRealtime, _queryRequestContext.UseCloud);

    var functionName = GetMarketStatusFunctionName(marketStatusMethodInfo);
    if (functionName != null)
    {
      var result = marketIDsNeedToFetchNew.Count > 0
        ? await sharkDbContext.Set<MarketStatus>().FromSqlRaw(
          $@"SELECT
              CAST([value] AS INT) AS [Id],
              {functionName}(CAST([value] AS INT), @CheckNextDay) AS [RemainingTime]
              FROM OPENJSON(@json)
            ",
          new SqlParameter("json", System.Data.SqlDbType.NVarChar) { Value = System.Text.Json.JsonSerializer.Serialize(marketIDsNeedToFetchNew) },
          new SqlParameter("CheckNextDay", System.Data.SqlDbType.Int) { Value = 0 }
          ).ToListAsync(cancellationToken)
        : Enumerable.Empty<MarketStatus>();

      var statuses = result.Select(p => new MarketStatusDto
      {
        MarketId = p.Id,
        IsOpened = p.RemainingTime > 0,
        RemainingTime = Math.Abs(p.RemainingTime)
      });

      if (statuses.Any())
      {
        foreach (var item in statuses)
        {
          _configurableCacheManager.SetCache(
            item,
            CacheKeyHelper.GenerateKeyByPropOfDtoObject<MarketStatusDto, int>(item.MarketId.ToString(), m => m.MarketId),
            TimeSpan.FromSeconds(5)
          );

          marketDTOsFromCache.Add(item);
        }
      }

      return marketDTOsFromCache.ToDictionary(p => p.MarketId);
    }

    return Enumerable.Empty<MarketStatusDto>().ToDictionary(p => p.MarketId);
  }

  private static string GetMarketStatusFunctionName(MethodInfo? methodInfo)
  {
    if (methodInfo == null)
    {
      return null;
    }

    var dbFunctionAttribute = methodInfo.GetCustomAttributes(typeof(DbFunctionAttribute))?.FirstOrDefault() as DbFunctionAttribute;
    if (dbFunctionAttribute != null)
    {
      return $"{dbFunctionAttribute.Schema}.{dbFunctionAttribute.Name}";
    }

    return null;
  }
}
