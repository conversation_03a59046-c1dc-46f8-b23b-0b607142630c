using System;
using System.Collections.Generic;

namespace Euroland.FlipIT.SData.API
{
  public abstract class CurrencyTransformationBase
    {
        protected CurrencyTransformationBase(string baseCurrency, string quoteCurrency)
        {
            BaseCurrency = baseCurrency;
            QuoteCurrency = quoteCurrency;
        }

        public string BaseCurrency { get; }
        public string QuoteCurrency { get; }

        protected abstract void Validate();
    }

    public class CustomRateTransformation: CurrencyTransformationBase
    {
        public CustomRateTransformation(string baseCurrency, string quoteCurrency, float customRate)
            : base(baseCurrency, quoteCurrency)
        {
            CustomRate = customRate;
        }
        public float CustomRate { get; }

        protected override void Validate()
        {
            throw new NotImplementedException();
        }
    }

    public class QuoteCurrencyTransformation: CurrencyTransformationBase {
        public QuoteCurrencyTransformation(string quoteCurrency, string transformTo, float factor)
            : base(null, quoteCurrency)
        {
            TransformTo = transformTo ?? throw new ArgumentNullException(nameof(transformTo));
            Factor = factor;
        }

        public float Factor { get; }
        public string TransformTo { get; }

        protected override void Validate()
        {
            throw new NotImplementedException();
        }
    }

    public class BaseCurrencyTransformation: CurrencyTransformationBase {
        public BaseCurrencyTransformation(string baseCurrency, string transformTo, float factor)
            : base(baseCurrency, null)
        {
            TransformTo = transformTo ?? throw new ArgumentNullException(nameof(transformTo));
            Factor = factor;
        }

        public float Factor { get; }
        public string TransformTo { get; }

        protected override void Validate()
        {
            throw new NotImplementedException();
        }
    }

    public class CurrencyTransformationConfiguration
    {
        public CurrencyTransformationConfiguration() {
            Transforms = new List<CurrencyTransformationBase>();
        }
        public bool Enabled { get; set; }

        public List<CurrencyTransformationBase> Transforms { get; }

    }
}
