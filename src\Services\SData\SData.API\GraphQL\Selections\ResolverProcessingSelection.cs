using System;
using System.Reflection;
using HotChocolate.Data.Projections.Context;
using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Selections;

/// <summary>
/// Class to support mapping both <see cref="ObjectType"/> and <see cref="InterfaceType"/> for selection and projection.
/// </summary>
public class ResolverProcessingSelection : IHasName, IResolverProcessingSelection
{
  public ResolverProcessingSelection(ISelectedField selectedField)
  {
    SelectedField = selectedField ?? throw new ArgumentNullException(nameof(selectedField));

    if(SelectedField.Field == null) {
      throw new ArgumentException(nameof(SelectedField.Field));
    }
  }
  public ISelectedField SelectedField { get; }

  public Type RuntimeType => SelectedField.Field.RuntimeType;

  public MemberInfo? ClassMemberInfo => SelectedField.Field.Member;

  public string SelectionName => Name;

  public string SelectionMemberName => ClassMemberInfo?.Name ?? Name;

  public string SelectionMemberNameOrDefault => ClassMemberInfo?.Name! ?? SelectionName;

  public string Name => SelectedField.Field.Name;

  public override string ToString()
  {
    return $"{SelectedField.Field.DeclaringType.Name}:{SelectionName}";
  }
}
