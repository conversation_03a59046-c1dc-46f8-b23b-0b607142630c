﻿using Microsoft.EntityFrameworkCore;

namespace Euroland.FlipIT.WiseSearch.Models.Setting
{
    public class CompanyConfiguration
    {
        public string CompanyCode { get; set; }
        public CompanyNameTranslation? CompanyName { get; set; }
        public string? Industry { get; set; }
        public string? Timezone { get; set; }
        public string? DefaultLanguage { get; set; }
        public string? EnabledLanguages { get; set; }
        public string? CompanyLogo { get; set; }
        public bool? UseLocalTimezone { get; set; }
    }

    public class CompanyNameTranslation
    {
        public int Id { get; set; }

        [Unicode(false)]
        public string CompanyCode { get; set; } = null!;

        [Unicode(false)]
        public string Language { get; set; } = null!;

        public string Name { get; set; } = null!;
        public string? HomePage { get; set; }
    }
}
