/*----------------------------------------------------------------------------------*/
/* Created [AnhNT] [Feb 20 2024] : Create a new SP based on dbo.shg_GetTodaysShareTradesByIsinAndMarket store */
/*----------------------------------------------------------------------------------*/

USE [shark]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [dbo].[shg3_GetTodaysShareTradesByIsinAndMarket]
@InstrumentId INT
AS
BEGIN
	DECLARE @MaxDate datetime
	SET @MaxDate = (SELECT MAX(hDate) FROM daily_history	WHERE InstrumentId =  @InstrumentId)
	SELECT hDate [Date], 
			hClose [Close], 
			COALESCE(hSize,0) [Size]
	FROM dbo.daily_history daily		
	WHERE InstrumentId =  @InstrumentId
	AND DATEDIFF(day, hDate,@MaxDate) = 0
	ORDER BY hDate DESC

END