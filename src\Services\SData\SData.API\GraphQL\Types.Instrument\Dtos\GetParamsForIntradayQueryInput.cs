using System;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument.Dtos;

public class GetParamsForIntradayQueryInput
{
  public string MarketTimezone { get; set; }
  public string MinutesDifference { get; set; }
  public string MarketOpenTimeLocal { get; set; }
  public double MarketOpenTimeInputTimezone { get; set; }
  public DateTime? LatestRealTimeDailyHistoryDateTime { get; set; }
  public DateTime? LatestRealTimeDailyHistoryOHLCDateTime { get; set; }
}
