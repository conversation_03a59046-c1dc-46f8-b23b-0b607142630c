import React, {
  useCallback,
  useEffect,
  useRef,
  useState,
  useMemo,
  useReducer
} from 'react';
import { User, UserManager, Log, Logger, OidcClientSettings } from 'oidc-client-ts';
import { authAction, authReducer } from './reducer';
import { openLogin as openIntegrationLogin } from './openLogin';
import { openLogout as openIntegrationLogout } from './openLogout';
import { navigator<PERSON>eys, OidcAuthContext, UserManagerContext, userManagerContextKeys } from './OidcAuthContext';
import { DefaultLoginComponent, DefaultLogoutComponent } from '../integration-component';
import {AuthState} from './interfaces.ts';

if(import.meta.env.DEV) {
  Log.setLevel(Log.DEBUG);
  Log.setLogger(console);
}

const initialAuthState: AuthState = {
  isLoading: true,
  isAuthenticated: false,
  user: null,
  error: null,
  activeNavigator: null
};

/**
 * Checks if the current location URL contains authentication parameters.
 *
 * This function inspects the URL for authentication parameters in both the query string
 * and the fragment. It looks for the presence of `code` or `error` parameters along with
 * a `state` parameter to determine if authentication parameters are present.
 *
 * @param {Location} [location=window.location] - The location object to inspect. Defaults to `window.location`.
 * @returns {boolean} - Returns `true` if authentication parameters are found, otherwise `false`.
 */
const hasAuthParams = (location = window.location) => {
  // response_mode: query
  let params = new URLSearchParams(location.search);

  let searchParams = new URLSearchParams();
  for (const [name, value] of params) {
    searchParams.append(name.toLowerCase(), value);
  }
  if ((searchParams.get('code') || searchParams.get('error')) &&
    searchParams.get('state')) {
    return true;
  }

  // response_mode: fragment
  params = new URLSearchParams(location.hash.replace('#', '?'));

  searchParams = new URLSearchParams();
  for (const [name, value] of params) {
    searchParams.append(name.toLowerCase(), value);
  }
  if ((searchParams.get('code') || searchParams.get('error')) &&
    searchParams.get('state')) {
    return true;
  }

  return false;
};

/**
 * Normalizes an error by ensuring it is an instance of `Error`.
 * If the provided error is not an instance of `Error`, it returns a new `Error` with the given fallback message.
 *
 * @param fallbackMessage - The message to use for the new `Error` if the provided error is not an instance of `Error`.
 * @returns A function that takes an error and returns it if it is an instance of `Error`, otherwise returns a new `Error` with the fallback message.
 */

const normalizeErrorFn = (fallbackMessage: any): ((error: Error) => Error) => (error: Error) => {
  if (error instanceof Error) {
    return error;
  }
  return new Error(fallbackMessage);
};

export const UserManagerImpl = typeof window === 'undefined' ? null : UserManager;



const loginError = normalizeErrorFn('Login failed');

/**
 * OidcAuthProvider component provides OpenID Connect (OIDC) authentication context to its children.
 *
 * @param {Object} props - The properties object.
 * @param {React.ReactNode} props.children - The child components that require authentication context.
 * @param {UserManager} [props.userManagerProp] - Optional custom UserManager instance.
 * @param {React.Component} props.integrationLoginComponent - Component to be used for login integration.
 * @param {React.Component} props.integrationLogoutComponent - Component to be used for logout integration.
 * @param {Object} [props.clientSettings] - Optional settings for the UserManager instance.
 * @param {string} [props.clientSettings.authority] - The URL of the OIDC authority.
 * @param {string} [props.clientSettings.client_id] - The client ID for the OIDC application.
 * @param {string} [props.clientSettings.redirect_uri] - The redirect URI for the OIDC application.
 * @param {boolean} [props.enabled=true] - Flag to enable or disable the OIDC authentication.
 *
 * @returns {React.Element} The OIDC authentication provider component.
 */
const OidcAuthProvider: React.FC<{
  children?: React.ReactNode,
  userManagerProp?: UserManager,
  clientSettings: OidcClientSettings,

  integrationLoginComponent?: any,

  integrationLogoutComponent?: any,
  enabled?: boolean
}> = ({
  children,
  userManagerProp,
  integrationLoginComponent = null,
  integrationLogoutComponent,
  clientSettings = {} as OidcClientSettings,
  enabled = true
}) => {
    const [userManager] = useState<UserManager>(() => {
      return (userManagerProp ||
        (UserManagerImpl
          ? new UserManagerImpl(clientSettings as OidcClientSettings)
          : clientSettings
        )) as UserManager;
    });

    const [authState, dispatch] = useReducer(authReducer, initialAuthState);

    const didInitialize = useRef(false);

    const userManagerContext = useMemo(
      () =>
        Object.assign(
          {
            settings: userManager.settings,
            events: userManager.events
          },
          Object.fromEntries(
            userManagerContextKeys.map((key) => [
              key,
              (userManager)[key].bind(userManager)
            ])
          ),
          Object.fromEntries(
            navigatorKeys.map((key) => [
              key,
              async (args: Parameters<UserManager[typeof key]>[0]) => {
                dispatch(authAction.navigatorInit(key));
                try {

                  return await userManager[key](args as any);
                } catch (error) {
                  dispatch(authAction.error(error as Error));
                  return null;
                } finally {
                  dispatch(authAction.navigatorClose());
                }
              }
            ])
          )
        ),
      [userManager]
    ) as unknown as UserManagerContext;

    if (typeof (integrationLoginComponent) !== 'function') {
      integrationLoginComponent = DefaultLoginComponent();
    }

    if (typeof (integrationLogoutComponent) !== 'function') {
      integrationLogoutComponent = DefaultLogoutComponent();
    }

    // Check auth status at loaded
    useEffect(() => {
      if (!enabled || !userManager || didInitialize.current) {
        return;
      }

      didInitialize.current = true;

      void (async () => {
        let user: User | null = null;

        try {
          // check if returning back from authority server
          if (hasAuthParams()) {
            user = (await userManager.signinCallback()) || null;
          }

          user = !user ? await userManager.getUser() : user;
          if (!user || user.expired) {
            // User object might not be presents at sessionStorage once user closed the browser,
            // or current login session is expired but user still be authorized at Authorization Server.
            // We do a request for sure of current login status at Authorization Sever this case.
            // It's not good to have always a request to check this status, however, this is the best solution
            // we have at this time.
            //user = await userManager.signinSilent();
          }

          dispatch(authAction.initialised(user!));

        } catch (error: any) {
          if(error && error.error !== 'login_required') {
            dispatch(authAction.error(loginError(error)));
          }
          // TODO: we've a hard work to try to refesh/check current login session
          // one at the first load and still got unluckly with the result.
          // Shall we do signout user out of current session?
        }
      })();
    }, [userManager]);

    // register to userManager events
    useEffect(() => {
      if (!userManager) return undefined;

      const handleUserLoaded = (user: User) => {
        Logger.info('OidcAuthProvider', 'user loaded', user);
        dispatch(authAction.userLoaded(user));
        userManager.getUser().then(() => {
          Logger.info('OidcAuthProvider', 'getUser() loaded user after userLoaded event fired');
        });
      };

      const handleUserUnloaded = () => {
        Logger.info('OidcAuthProvider', 'user unloaded');
        dispatch(authAction.userUnloaded());
      };

      const handleSilentRenewError = (error: Error) => {
        dispatch(authAction.error(error));
      };

      const handleAccessTokenExpiring = () => {
        Logger.info('OidcAuthProvider', 'token expiring');
        // maybe do this code manually if automaticSilentRenew doesn't work
        /*userManager.signinSilent().then((user) => {

        }).catch((error) => {

        });*/
      };
      const handleAccessTokenExpired = () => {
        Logger.info('OidcAuthProvider', 'token expired');
      };

      const handleUserSignedOut = () => {
        Logger.info('OidcAuthProvider', 'user logged out of the token server');
      };

      const handleUserSignedIn = () => {
        Logger.info('OidcAuthProvider', 'user logged in to the token server');
      };

      userManager.events.addUserLoaded(handleUserLoaded);
      userManager.events.addUserUnloaded(handleUserUnloaded);
      userManager.events.addSilentRenewError(handleSilentRenewError);
      userManager.events.addAccessTokenExpiring(handleAccessTokenExpiring);
      userManager.events.addAccessTokenExpired(handleAccessTokenExpired);
      userManager.events.addUserSignedIn(handleUserSignedIn);
      userManager.events.addUserSignedOut(handleUserSignedOut);

      return () => {
        userManager.events.removeUserLoaded(handleUserLoaded);
        userManager.events.removeUserUnloaded(handleUserUnloaded);
        userManager.events.removeSilentRenewError(handleSilentRenewError);
        userManager.events.removeAccessTokenExpiring(handleAccessTokenExpiring);
        userManager.events.removeAccessTokenExpired(handleAccessTokenExpired);
        userManager.events.removeUserSignedIn(handleUserSignedIn);
        userManager.events.removeUserSignedOut(handleUserSignedOut);
      };
    }, [userManager]);

    const signinRedirect = useCallback((state = {}) => {
      void (async () => {
        try {
          await userManager.signinRedirect({ state });

        } catch (error: any) {
          dispatch(authAction.error(error));
        }
      })();
    },
      [userManager]
    );

    const signoutRedirect = useCallback(() => {
      void (async () => {
        try {
          await userManager.signoutRedirect();

        } catch (error: any) {
          dispatch(authAction.error(error));
        }
      })();
    },
      [userManager]
    );

    const renewToken = useCallback((/*user: User*/) => {
      void (async () => {
        try {
          await userManager.signinSilent();
        } catch (error: any) {
          dispatch(authAction.error(error));
        }
      })();
    },
      [userManager]
    );

    const setUser = useCallback((user: User) => {
      if (user) {
        void (async () => {
          // A workaround to store user to browser storage when authentication is done from popup because
          // oidc-client-ts does not raise `userLoaded` event if invoking storeUser() externally.
          await userManager.storeUser(user);
          dispatch(authAction.userLoaded(user));
        })();
      }
    },
      [userManager]
    );

    const openLogin = useCallback(async () => {
      const user = await openIntegrationLogin({ 
        LoginComponent: integrationLoginComponent 
      });
      setUser(new User({ ...user }));
    }, [integrationLoginComponent, setUser]);

    const openLogout = useCallback(async () => {
      const closedProgrammatically = await openIntegrationLogout({ 
        LogoutComponent: integrationLogoutComponent 
      });
      
      try {
        if(closedProgrammatically) return;
        // TODO: Do we actually need a query to sessionstatus to check login status after user logged out?
        await userManager.querySessionStatus();
      } catch (err: any) {
        if (err.error === 'login_required') return;
        throw err;
      } finally {
        await userManager.clearStaleState();
        await userManager.removeUser();
      }
    },
      [userManager]
    );

    const signinSilent = useCallback(async () => {
      try {
        return await userManager.signinSilent();
      } catch (error: any) {
        dispatch(authAction.error(error));
        return Promise.reject(error);
      }
    },
      [userManager]
    );

    return (
      <OidcAuthContext.Provider value={{
        ...authState,
        ...userManagerContext,
        login: openLogin,
        logout: openLogout,
        signinRedirect,
        signoutRedirect,
        signinSilent,
        renewToken
      }}>
        {children}
      </OidcAuthContext.Provider>
    );
  };

export {
  OidcAuthContext,
  OidcAuthProvider
};
