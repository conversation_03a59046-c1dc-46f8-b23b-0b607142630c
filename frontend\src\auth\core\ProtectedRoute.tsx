import { useAuth } from './useAuth';

import { Fragment, ReactNode } from 'react';

export const ProtectedRoute = ({ children }: { children: ReactNode }) => {
  const auth = useAuth();

  if (!auth.isAuthenticated) {
    // TODO: open login window popup programmatically could
    // cause browser blocks it. See more detail at https://gitlab.euroland.com/tools/flipit/integration/-/blob/master/doc/popup-blocker-issue.md.
    // It's better to navigate user to forbidden/unauthentication page where you can click on a login button to open login window.
    auth.login();
  }
  return (
    <Fragment>
      {auth.isAuthenticated ? children : null}
    </Fragment>
  );
};
