using Euroland.FlipIT.WiseSearch.Infrastructure.Repositories;

namespace Euroland.FlipIT.WiseSearch.Infrastructure.UnitOfWorks
{
  public interface ISharkUoW : IUnitOfWorkBase<SharkDbContext>
  {
    ISharkCompanyNameRepository SharkCompanyNameRepository { get; }
    ISharkCompanySettingRepository SharkCompanySettingRepository { get; }
    ISharkCompanySettingsLanguageRepository SharkCompanySettingsLanguageRepository { get; }
  }

  public class SharkUoW(SharkDbContext sharkDbContext,
                        ISharkCompanySettingRepository sharkCompanySettingRepository,
                        ISharkCompanySettingsLanguageRepository sharkCompanySettingsLanguageRepository,
                        ISharkCompanyNameRepository sharkCompanyNameRepository
                        )
      : UnitOfWorkBase<SharkDbContext>(sharkDbContext), ISharkUoW
  {

    public ISharkCompanySettingRepository SharkCompanySettingRepository => sharkCompanySettingRepository;
    public ISharkCompanySettingsLanguageRepository SharkCompanySettingsLanguageRepository => sharkCompanySettingsLanguageRepository;

    public ISharkCompanyNameRepository SharkCompanyNameRepository => sharkCompanyNameRepository;
  }
}
