import { useEffect, useRef } from 'react';
import { useAuth } from './useAuth';

export const useAuthSession = (param: {onSigninSilentFailed?: () => void} = {}) => {
  const auth = useAuth();
  const isFirstSignin = useRef(false);
  const forwardParam = useRef(param);

  // Ensure we can access the latest reference of param
  forwardParam.current = param;

  useEffect(() => {
    // auth variable change frequently, but we just want signinSilent() run once
    if(isFirstSignin.current) return;
    const checkIfExistingSession = async () => {
      /**
       * We need create request to check user login or logout, 
       * that why we can't use isAuthenticated flag in condition
       */
      try {
        if (!auth.isLoading) {
          isFirstSignin.current = true;
          const user = await auth.signinSilent();
          if(!user) {
            forwardParam.current.onSigninSilentFailed?.();
          }
        }
      } catch (error) {
        forwardParam.current.onSigninSilentFailed?.();
      }
    };

    void checkIfExistingSession();
  }, [auth]);

  return auth;
};
