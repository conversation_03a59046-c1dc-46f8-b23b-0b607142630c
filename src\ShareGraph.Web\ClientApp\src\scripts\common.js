export const TEMPLATE_TICKER = /** @type {const} */({
  SINGLE: 'single',
  MULTIPLE: 'multiple'
});

export const MARKET_STATUS = /** @type {const} */({
  OPEN: 'open',
  CLOSE: 'close'
});

export const CHART_SECOND_PERIODS = /** @type {const} */({
  '1S': { type: 'item', label: '1 Sec', tap: 'Layout.setInterval', value: [1, 1, 'second'] },
  '5S': { type: 'item', label: '5 Sec', tap: 'Layout.setInterval', value: [1, 5, 'second'] },
  '10S': { type: 'item', label: '10 Sec', tap: 'Layout.setInterval', value: [1, 10, 'second'] },
  '15S': { type: 'item', label: '15 Sec', tap: 'Layout.setInterval', value: [1, 15, 'second'] },
  '30S': { type: 'item', label: '30 Sec', tap: 'Layout.setInterval', value: [1, 30, 'second'] }
});

export const LAYOUT = /** @type {const} */({
  FULL: 'FULL',
  FIXED: 'FIXED'
});

export const DEVICES = /** @type {const} */({
  XXL: 'XXL',
  XL: 'XL',
  LG: 'LG',
  MD: 'MD',
  SM: 'SM',
  XS: 'XS'
});

export const SWITCHER_TYPE = /** @type {const} */({
  TICKER: 'TICKER',
  COMPARISON: 'COMPARISON'
});

export const TICKER_SWITCH_TYPE = /** @type {const} */({
  GRAPH_TYPE: 'GRAPH',
  TABLE_TYPE: 'TABLE'
});

export const PERFORMANCE_SWITCH_TYPE = /** @type {const} */({
  GRAPH_TYPE: 'GRAPH',
  TABLE_TYPE: 'TABLE'
});

export const PERFORMANCE_SELECT_TYPE = /** @type {const} */({
  SHARE_PRICE_DEVELOPMENT: 'SHARE_PRICE_DEVELOPMENT',
  SHARE_PRICE_DEVELOPMENT_BY_YEARS: 'SHARE_PRICE_DEVELOPMENT_BY_YEARS',
  WEEKS_52_HIGH_LOW: '52_WEEKS_HIGH_LOW'
});

export const COMPARISON_SWITCH_TYPE = /** @type {const} */({
  PEER_TYPE: 'PEER_TYPE',
  INDICES_TYPE: 'INDICES_TYPE'
});

export const TICKER_ANIMATION = /** @type {const} */({
  FADE: 'FADE',
  BLINK_PRICE: 'BLINK_PRICE',
  BLINK_MARKET: 'BLINK_MARKET',
  TRANSFORM: 'TRANSFORM'
});

export const CHART = {
  GROUP_RANGE: [
    {
      defaultPeriod: '1min',
      rangesAvailable: ['1day', '5day'],
      periodsAvailable: ['1min', '5min', '10min', '1h']
    },

    {
      defaultPeriod: '1d',
      rangesAvailable: ['1month', '3month', '6month', '1YTD', '1year', '3year'],
      periodsAvailable: ['1d', '1w', '1mo']
    },

    {
      defaultPeriod: '1w',
      rangesAvailable: ['5year'],
      periodsAvailable: ['1d', '1w', '1mo']
    }
  ],

  TooltipType: {
    DYNAMIC_CALLOUT: 'DYNAMIC_CALLOUT',
    TOOLTIP: 'TOOLTIP',
    STATIC: 'STATIC'
  },

  RANGE: [
    {
      value: '1D',
      key: '1D',
      translateKey: 'oneDay',
      translateKeyAria: 'oneDayAria',
      multiplier: '1',
      base: 'today',
      cls: 'range-item',
      periodicity: {
        period: 1,
        interval: 1,
        timeUnit: 'minute'
      }
    },
    {
      value: '5D',
      key: '5D',
      translateKey: 'fiveDay',
      translateKeyAria: 'fiveDayAria',
      multiplier: '5',
      base: 'day',
      cls: 'range-item',
      periodicity: {
        period: 1,
        interval: 1,
        timeUnit: 'minute'
      }
    },
    {
      value: '1M',
      key: '1M',
      translateKey: 'oneMonth',
      translateKeyAria: 'oneMonthAria',
      multiplier: '1',
      base: 'month',
      cls: 'range-item',
      periodicity: {
        period: 1,
        interval: 1,
        timeUnit: 'day'
      }
    },
    {
      value: '3M',
      key: '3M',
      translateKey: 'threeMonth',
      translateKeyAria: 'threeMonthAria',
      multiplier: '3',
      base: 'month',
      cls: 'range-item',
      periodicity: {
        period: 1,
        interval: 1,
        timeUnit: 'day'
      }
    },
    {
      value: '6M',
      key: '6M',
      translateKey: 'sixMonth',
      translateKeyAria: 'sixMonthAria',
      multiplier: '6',
      base: 'month',
      cls: 'range-item',
      periodicity: {
        period: 1,
        interval: 1,
        timeUnit: 'day'
      }
    },
    {
      value: 'YTD',
      key: 'YTD',
      translateKey: 'ytd',
      translateKeyAria: 'ytdAria',
      multiplier: '1',
      base: 'YTD',
      cls: 'range-item',
      periodicity: {
        period: 1,
        interval: 1,
        timeUnit: 'day'
      }
    },
    {
      value: '1Y',
      key: '1Y',
      translateKey: 'oneYear',
      translateKeyAria: 'oneYearAria',
      multiplier: '1',
      base: 'year',
      cls: 'range-item',
      periodicity: {
        period: 1,
        interval: 1,
        timeUnit: 'day'
      }
    },
    {
      value: '3Y',
      key: '3Y',
      translateKey: 'threeYear',
      translateKeyAria: 'threeYearAria',
      multiplier: '3',
      base: 'year',
      cls: 'range-item',
      periodicity: {
        period: 1,
        interval: 1,
        timeUnit: 'week'
      }
    },
    {
      value: '5Y',
      key: '5Y',
      translateKey: 'fiveMonth',
      translateKeyAria: 'fiveMonthAria',
      multiplier: '5',
      base: 'year',
      cls: 'range-item',
      periodicity: {
        period: 1,
        interval: 1,
        timeUnit: 'week'
      }
    },
    {
      value: 'ALL',
      key: 'All',
      translateKey: 'all',
      translateKeyAria: 'all',
      multiplier: '1',
      base: 'all',
      cls: 'range-item',
      periodicity: {
        period: 1,
        interval: 1,
        timeUnit: 'week'
      }
    },
    {
      value: 'CUSTOM_RANGE',
      key: 'CUSTOM_RANGE',
      translateKey: 'customRange',
      translateKeyAria: 'customRange',
      label: 'Date Range',
      multiplier: '1',
      base: 'all',
      cls: 'range-item',
      periodicity: {
        period: 1,
        interval: 1,
        timeUnit: 'week'
      }
    }
  ],
  // key use get chart type in chartIq defination, value to mapping setting XML, translationKey used to translate key defined
  TYPE: [
    {iconCls: 'mountain', key: 'mountain', value: 'MOUNTAIN', translationKey: 'Mountain' },
    {iconCls: 'candle', key: 'candle', value: 'CANDLE', translationKey: 'Candle' },
    {iconCls: 'line', key: 'line', value: 'LINE', translationKey: 'Line' },
    {iconCls: 'vertex_line', key: 'vertex_line', value: 'VERTEX_LINE', translationKey: 'Vertex Line' },
    {iconCls: 'step', key: 'step', value: 'STEP', translationKey: 'Step' },
    {iconCls: 'histogram', key: 'histogram', value: 'HISTOGRAM', translationKey: 'Histogram' },
    {iconCls: 'colored_bar', key: 'colored_bar', value: 'BAR', translationKey: 'Bar' }
  ],
  MENU_DISPLAY: [
    { type: 'radio', label: 'Candle', setget: 'Layout.ChartType', iconCls: 'candle', value: 'candle', helpId: 'chart_style_candle' },
    { type: 'radio', label: 'Line', setget: 'Layout.ChartType', iconCls: 'line', value: 'line', helpId: 'chart_style_line' },
    { type: 'radio', label: 'Vertex Line', setget: 'Layout.ChartType', iconCls: 'vertex-line', value: 'vertex_line', helpId: 'chart_style_vertex_line' },
    { type: 'radio', label: 'Step', setget: 'Layout.ChartType', iconCls: 'step', value: 'step', helpId: 'chart_style_step' },
    { type: 'radio', label: 'Mountain', setget: 'Layout.ChartType', iconCls: 'mountain', value: 'mountain', helpId: 'chart_style_mountain' },
    { type: 'radio', label: 'Histogram', setget: 'Layout.ChartType', iconCls: 'histogram', value: 'histogram', helpId: 'chart_style_histogram' },
    { type: 'radio', label: 'Bar', setget: 'Layout.ChartType', iconCls: 'colored-bar', value: 'colored_bar', helpId: 'chart_style_colored_bar' },
    { type: 'radio', label: 'Bar', setget: 'Layout.ChartType', iconCls: 'bar', value: 'bar', helpId: 'chart_style_bar' }
  ],
  MENU_MARKERS : [
    { type: 'checkbox', key: 'dividend', label: 'Dividends', setget: 'Markers.MarkerType', value: 'dividend' },
    { type: 'checkbox', key: 'earning', label: 'Earnings',  setget: 'Markers.MarkerType', value: 'earning' },
    { type: 'checkbox', key: 'pressreleases', label: 'Press Releases', setget: 'Markers.MarkerType', value: 'pressRelease' },
    { type: 'checkbox', key: 'video', label: 'Videos',  setget: 'Markers.MarkerType', value: 'video' }
  ],
  MENU_PREFERENCES: [
    { type: 'checkbox', label: 'High/Low Values', setget: 'Layout.HighLowLine', feature: 'highlowline', key: 'high_low_values' },
    { type: 'checkbox', label: 'Range Selector', setget: 'Layout.RangeSlider', feature: 'rangeslider', key: 'range_selector' },
    { type: 'checkbox', label: 'Extended Hours', setget: 'Layout.ExtendedHours', feature: 'extendedhours', key: 'extended_hours' },
    { type: 'radio', label: '% View', setget: 'Layout.ChangeChartScale', value: 'percent', key: 'percent_view' },
    { type: 'radio', label: 'Log Scale', setget: 'Layout.ChangeChartScale', value: 'log', key: 'log_scale' },
    { type: 'radio', label: 'Linear', setget: 'Layout.ChangeChartScale', value: 'linear', key: 'linear' },
    { type: 'checkbox', label: 'Invert', setget: 'Layout.FlippedChart', key: 'invert' },
    { type: 'checkbox', label: 'Stripe Background', setget: 'Layout.StripeBackground', key : 'stripe_background' }
  ],
  MENU_CHART_PREFERENCES: [
    { type: 'checkbox', label: 'High/Low Values', setget: 'Layout.HighLowLine', feature: 'highlowline', key: 'high_low_values' },
    { type: 'checkbox', label: 'Range Selector', setget: 'Layout.RangeSlider', feature: 'rangeslider', key: 'range_selector' }
  ],
  MENU_YAXIS_PREFERENCES: [
    { type: 'radio', label: '% View', setget: 'Layout.ChangeChartScale', value: 'percent', key: 'percent_view' },
    { type: 'radio', label: 'Log Scale', setget: 'Layout.ChangeChartScale', value: 'log', key: 'log_scale' },
    { type: 'radio', label: 'Linear', setget: 'Layout.ChangeChartScale', value: 'linear', key: 'linear' },
    { type: 'checkbox', label: 'Invert', setget: 'Layout.FlippedChart', key: 'invert' },
    { type: 'checkbox', label: 'Stripe Background', setget: 'Layout.StripeBackground', key : 'stripe_background' }
  ],
  EVENTS: [
    { type: 'checkboxCus', key: 'dividend',label: 'Dividends', cmd: 'Markers.showMarkers(\'dividend\')' },
    { type: 'checkboxCus', key: 'earning',label: 'Earnings',  cmd: 'Markers.showMarkers(\'earning\')' },
    { type: 'checkboxCus', key: 'pressreleases', label: 'Press Releases', cmd: 'Markers.showMarkers(\'pressRelease\')' },
    { type: 'checkboxCus', key: 'video',label: 'Videos',  cmd: 'Markers.showMarkers(\'video\')' }
    // { type: 'radioCus', key: 'none', label: 'None', cmd: 'Markers.showMarkers(\'none\')', cls: 'ciq-active' }
  ],
  STUDIES: [
    {
      key: 'BOLLINGER_BANDS',
      value: 'Bollinger Bands'
    },
    {
      key: 'ICHIMOKU_CLOUDS',
      value: 'Ichimoku Clouds'
    },
    {
      key: 'MACD',
      value: 'MACD'
    },
    {
      key: 'RSI',
      value: 'RSI'
    },
    {
      key: 'STOCHASTICS',
      value: 'Stochastics'
    },
    {
      key: 'TYPICAL_PRICE',
      value: 'Typical Price'
    },
    {
      key: 'VOLUME_CHART',
      value: 'Volume Chart'
    },
    {
      key: 'ADX_DMS',
      value: 'ADX/DMS'
    },
    {
      key: 'TOTAL_RETURN',
      value: 'Total Return'
    },
    {
      key: 'MOVING_AVERAGE_CROSS',
      value: 'Moving Average Cross'
    },
    {
      key: 'VOLUME_UNDERLAY',
      value: 'Volume Underlay'
    }
  ]
};

export const EVENT_TYPES = /** @type {const} */({
  DIVIDEND: 'dividend',
  EARNING: 'earning',
  PRESSRELEASES: 'pressRelease',
  VIDEO: 'video'
});

export const PRINT_SHARE_DETAIL_OPTIONS = /** @type {const} */({
  ALWAYS: 'ALWAYS',
  NEVER: 'NEVER',
  CONFIRM: 'CONFIRM'
});

export const PERIOD_TYPES = /** @type {const} */({
  ONE_MONTH: 'ONE_MONTH',
  THREE_MONTHS: 'THREE_MONTHS',
  SIX_MONTHS: 'SIX_MONTHS',
  YTD: 'YTD',
  ONE_YEAR: 'ONE_YEAR',
  THREE_YEARS: 'THREE_YEARS',
  FIVE_YEARS: 'FIVE_YEARS',
  ALL: 'ALL',
  CUSTOM_RANGE: 'CUSTOM_RANGE'
});

export const VIEW_TYPES = /** @type {const} */({
  DAILY: 'DAILY',
  MONTHLY: 'MONTHLY',
  WEEKLY: 'WEEKLY'
});

export const TIME_UNITS = /** @type {const} */({
  DAILY: 'day',
  MONTHLY: 'month',
  WEEKLY: 'week'
});

export const MAP_TYPE_PERIOD_QUERY = {
  onemonth: PERIOD_TYPES.ONE_MONTH,
  threemonths: PERIOD_TYPES.THREE_MONTHS,
  sixmonths: PERIOD_TYPES.SIX_MONTHS,
  ytd: PERIOD_TYPES.YTD,
  oneyear: PERIOD_TYPES.ONE_YEAR,
  threeyears: PERIOD_TYPES.THREE_YEARS,
  fiveyears: PERIOD_TYPES.FIVE_YEARS,
  all: PERIOD_TYPES.ALL
};

export const MOVING_AVERAGES = /** @type {const} */({
  MA_10: {
    key: 'mA10',
    value: '10'
  },
  MA_20: {
    key: 'mA20',
    value: '20'
  },
  MA_50: {
    key: 'mA50',
    value: '50'
  }
});
