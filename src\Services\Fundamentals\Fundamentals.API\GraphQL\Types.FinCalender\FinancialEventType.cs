using Euroland.FlipIT.Fundamental.API.Dtos.FinCalender;
using HotChocolate.Types.Pagination;

namespace Euroland.FlipIT.Fundamental.API.GraphQL.Types.FinCalender;

public class FinancialEventType : ObjectType<FinancialEventDetailDto>
{
  public const string TypeName = "FinancialEvent";

  protected override void Configure(IObjectTypeDescriptor<FinancialEventDetailDto> descriptor)
  {
    descriptor.Name(TypeName);

    descriptor.Field(dto => dto.EventId).IsProjected();
    descriptor.Field(dto => dto.EventDetailId).IsProjected();

    descriptor.Field("attachments")
      .Description("Get all attachments for the event")
      .UsePaging(options: new PagingOptions
      {
        IncludeTotalCount = true,
        MaxPageSize = int.MaxValue,
        DefaultPageSize = int.MaxValue
      })
      .UseProjection()
      .UseFiltering()
      .UseSorting()
      .ResolveWith<FincalenderResolver>(resolvers =>
        resolvers.GetAttachmentsByFinancialEvent(default!, default!));

    descriptor.Field("statistics")
      .UseFirstOrDefault()
      .UseProjection()
      .ResolveWith<FincalenderResolver>(
        resolvers => resolvers.GetStatisticsByFinancialEvent(default!, default!)
      );
  }
}
