﻿using Euroland.FlipIT.WiseSearch.Infrastructure.Entities.WiseSearch;

namespace Euroland.FlipIT.WiseSearch.Infrastructure.Repositories
{
    public interface IWsIngestRepository : IRepositoryBase<WiseSearchDbContext, IngestedActivityEntity, int>
    {

    }
    public class WsIngestRepository : RepositoryBase<WiseSearchDbContext, IngestedActivityEntity, int>, IWsIngestRepository
    {
        public WsIngestRepository(WiseSearchDbContext context) : base(context)
        {
        }
    }
}
