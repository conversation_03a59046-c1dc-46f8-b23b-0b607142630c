using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using Euroland.FlipIT.Shared.CachingManager;
using GreenDonut;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Attachment;

/// <summary>
/// A data loader that batches and caches the retrieval of <see cref="AttachmentDto"/> objects by their IDs.
/// Utilizes a configurable cache manager to minimize database queries and improve performance.
/// </summary>
public class AttachmentsByPressReleaseIdDataLoader : GroupedDataLoader<long, AttachmentDto>
{
  private readonly IDbContextFactory<NewsContext> _dbContextFactory;
  private readonly IConfigurableCacheManager _configurableCacheManager;

  public AttachmentsByPressReleaseIdDataLoader(
    IDbContextFactory<NewsContext> dbContextFactory,
    IConfigurableCacheManager configurableCacheManager,
    IBatchScheduler batchScheduler,
    DataLoaderOptions? options = null)
    : base(batchScheduler, options)
  {
    _dbContextFactory = dbContextFactory ?? throw new ArgumentNullException(nameof(dbContextFactory));
    _configurableCacheManager = configurableCacheManager ?? throw new ArgumentNullException(nameof(configurableCacheManager));
  }
  protected override async Task<ILookup<long, AttachmentDto>> LoadGroupedBatchAsync(
    IReadOnlyList<long> keys,
    CancellationToken cancellationToken)
  {

    var filterKeys = keys.Where(k =>
        _configurableCacheManager.GetCache<List<AttachmentDto>, string>(
          CacheKeyHelper.GenerateKeyByPropOfDtoObject<AttachmentDto, long>(k.ToString(), a => a.PressreleaseId)) == null)
      .ToList();

    if (filterKeys.Count > 0)
    {
      using var newsContext = await _dbContextFactory.CreateDbContextAsync();
      var attachmentTable = newsContext.Attachment.EntityType.GetSchemaQualifiedTableName();
      var pressReleaseTable = newsContext.PressReleases.EntityType.GetSchemaQualifiedTableName();

      // EF7 generate filter condition with keys.Contains() will be translated to SQL query as [column] IN ((CAST x AS BIGINT),...)
      // That is not optimized since there will be hundred number of pressrelease IDs of a query.
      // (this one is optimized for EF8 https://devblogs.microsoft.com/dotnet/announcing-ef8-preview-4/)
      // By parameterize this long IDs we can optimize the input SQL command text which passed to DB server.
      var attachmentsQuery = newsContext.Attachment.FromSqlRaw(
        $@"SELECT * FROM [{attachmentTable}] att
            INNER JOIN [{pressReleaseTable}] pr ON att.atPressreleaseID = pr.prID AND att.atLanguageId = pr.prLanguageId
            WHERE att.atIsInBlob = 1
            AND att.atPressreleaseID IN (SELECT CAST([value] AS BIGINT) FROM OPENJSON(@json))",
        new SqlParameter("json", SqlDbType.VarChar, 5000)
        {
          Value = JsonSerializer.Serialize(filterKeys)
        }
      );

      var fetchedAttachments = await attachmentsQuery.WithAutoMapper()
        .ToProjection<AttachmentDto>()
        .ToListAsync(cancellationToken);

      foreach (var group in fetchedAttachments.GroupBy(a => a.PressreleaseId))
      {
        _configurableCacheManager.SetCache(group.ToList(),
          CacheKeyHelper.GenerateKeyByPropOfDtoObject<AttachmentDto, long>(group.Key.ToString(), a => a.PressreleaseId));
      }
    }

    return keys.SelectMany(key =>
    {
      var cacheKey = CacheKeyHelper.GenerateKeyByPropOfDtoObject<AttachmentDto, long>(key.ToString(), a => a.PressreleaseId);
      var attachments = _configurableCacheManager.GetCache<List<AttachmentDto>, string>(cacheKey);
      return attachments?.Select(attachment => new KeyValuePair<long, AttachmentDto>(key, attachment)) ?? Enumerable.Empty<KeyValuePair<long, AttachmentDto>>();
    }).ToLookup(pair => pair.Key, pair => pair.Value);
  }
}
