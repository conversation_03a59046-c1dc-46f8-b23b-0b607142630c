// TradingView-inspired color palette - Light Theme
$tv-bg-primary: #ffffff;
$tv-bg-secondary: #f7f9fb;
$tv-bg-tertiary: #f0f3fa;
$tv-text-primary: #131722;
$tv-text-secondary: #6a7187;
$tv-text-muted: #9598a1;
$tv-border: #e0e3eb;
$tv-positive: #26a69a;
$tv-negative: #ef5350;
$tv-accent: #2962ff;
$tv-warning: #ff9800;

// Spacing and sizing
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 20px;
$spacing-xxl: 24px;

$border-radius-sm: 4px;
$border-radius-md: 6px;
$border-radius-lg: 8px;

// Typography
$font-size-xs: 11px;
$font-size-sm: 12px;
$font-size-md: 14px;
$font-size-lg: 16px;
$font-size-xl: 18px;
$font-size-xxl: 24px;

$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// Animations
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Main container
.promode-instrument-detail {
  background: $tv-bg-primary;
  color: $tv-text-primary;
  padding: $spacing-sm 0;
  border-radius: $border-radius-lg;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  min-height: 400px;
  animation: fadeIn 0.3s ease-out;

  // Header section
  .instrument-header {
    margin-bottom: $spacing-xl;
    padding-bottom: $spacing-lg;
    border-bottom: 1px solid $tv-border;

    .instrument-title {
      .instrument-name {
        font-size: $font-size-xl;
        font-weight: $font-weight-semibold;
        color: $tv-text-primary;
        margin: 0 0 $spacing-sm 0;
        line-height: 1.2;
      }

      .instrument-meta {
        display: flex;
        gap: $spacing-md;
        flex-wrap: wrap;
        align-items: center;

        .symbol {
          font-size: $font-size-md;
          font-weight: $font-weight-medium;
          color: $tv-accent;
          background: rgba($tv-accent, 0.1);
          padding: $spacing-xs $spacing-sm;
          border-radius: $border-radius-sm;
        }

        .market,
        .currency {
          font-size: $font-size-sm;
          color: $tv-text-secondary;
          background: $tv-bg-secondary;
          padding: $spacing-xs $spacing-sm;
          border-radius: $border-radius-sm;
        }
      }
    }
  }

  // Price section
  .price-section {
    margin-bottom: $spacing-xxl;
    text-align: left;

    .current-price {
      display: flex;
      align-items: baseline;
      gap: $spacing-sm;
      margin-bottom: $spacing-sm;

      .price-value {
        font-size: $font-size-xxl;
        font-weight: $font-weight-bold;
        color: $tv-text-primary;
        line-height: 1;
      }

      .currency-code {
        font-size: $font-size-md;
        color: $tv-text-secondary;
        font-weight: $font-weight-medium;
      }
    }

    .price-change {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      font-size: $font-size-md;
      font-weight: $font-weight-medium;

      &.positive {
        color: $tv-positive;
      }

      &.negative {
        color: $tv-negative;
      }

      .change-value,
      .change-percentage {
        line-height: 1;
      }
    }
  }

  // Market data grid
  .market-data-grid {
    margin-bottom: $spacing-xxl;

    .data-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: $spacing-lg;
      margin-bottom: $spacing-md;

      &:last-child {
        margin-bottom: 0;
      }

      .data-item {
        background: $tv-bg-secondary;
        padding: $spacing-md;
        border-radius: $border-radius-md;
        border: 1px solid $tv-border;
        transition: all 0.2s ease;

        &:hover {
          background: $tv-bg-tertiary;
          border-color: rgba($tv-accent, 0.3);
        }

        .label {
          display: block;
          font-size: $font-size-xs;
          color: $tv-text-secondary;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          margin-bottom: $spacing-xs;
          font-weight: $font-weight-medium;
        }

        .value {
          display: block;
          font-size: $font-size-md;
          color: $tv-text-primary;
          font-weight: $font-weight-medium;
          line-height: 1.2;
        }
      }
    }
  }

  // 52 Week Range section
  .week-range-section {
    margin-bottom: $spacing-xl;
    background: $tv-bg-secondary;
    padding: $spacing-lg;
    border-radius: $border-radius-md;
    border: 1px solid $tv-border;

    .section-title {
      font-size: $font-size-sm;
      color: $tv-text-secondary;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: $spacing-lg;
      font-weight: $font-weight-medium;
    }

    .range-container {
      .range-values {
        display: flex;
        justify-content: space-between;
        margin-bottom: $spacing-sm;
        font-size: $font-size-sm;
        color: $tv-text-secondary;
        font-weight: $font-weight-medium;

        .range-low {
          color: $tv-negative;
        }

        .range-high {
          color: $tv-positive;
        }
      }

      .range-bar {
        margin-bottom: $spacing-md;

        .range-track {
          height: 6px;
          background: $tv-bg-tertiary;
          border-radius: 3px;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 100%;
            background: linear-gradient(to right, $tv-negative, $tv-warning, $tv-positive);
            opacity: 0.3;
          }

          .range-indicator {
            position: absolute;
            top: -2px;
            width: 10px;
            height: 10px;
            background: $tv-text-primary;
            border: 2px solid $tv-bg-primary;
            border-radius: 50%;
            transform: translateX(-50%);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
          }
        }
      }

      .range-performance {
        text-align: center;

        .performance {
          font-size: $font-size-sm;
          font-weight: $font-weight-medium;
          padding: $spacing-xs $spacing-sm;
          border-radius: $border-radius-sm;

          &.positive {
            color: $tv-positive;
            background: rgba($tv-positive, 0.1);
          }

          &.negative {
            color: $tv-negative;
            background: rgba($tv-negative, 0.1);
          }
        }
      }
    }
  }

  // Last updated
  .last-updated {
    text-align: center;
    font-size: $font-size-xs;
    color: $tv-text-muted;
    padding-top: $spacing-md;
    border-top: 1px solid $tv-border;
  }

  // Loading skeleton
  .loading-skeleton {
    .skeleton-header {
      height: 60px;
      background: linear-gradient(90deg, $tv-bg-secondary 25%, $tv-bg-tertiary 50%, $tv-bg-secondary 75%);
      background-size: 200px 100%;
      animation: shimmer 1.5s infinite;
      border-radius: $border-radius-md;
      margin-bottom: $spacing-lg;
    }

    .skeleton-content {
      .skeleton-line {
        height: 40px;
        background: linear-gradient(90deg, $tv-bg-secondary 25%, $tv-bg-tertiary 50%, $tv-bg-secondary 75%);
        background-size: 200px 100%;
        animation: shimmer 1.5s infinite;
        border-radius: $border-radius-md;
        margin-bottom: $spacing-md;

        &:nth-child(2) {
          animation-delay: 0.2s;
        }

        &:nth-child(3) {
          animation-delay: 0.4s;
        }
      }
    }
  }

  // Error state
  .error-state {
    text-align: center;
    padding: $spacing-xxl;

    .error-icon {
      font-size: $font-size-xxl;
      margin-bottom: $spacing-md;
      color: $tv-negative;
    }

    .error-message {
      font-size: $font-size-md;
      color: $tv-text-secondary;
    }
  }

  // Empty state
  .empty-state {
    text-align: center;
    padding: $spacing-xxl;

    .empty-icon {
      font-size: $font-size-xxl;
      margin-bottom: $spacing-md;
      color: $tv-text-muted;
    }

    .empty-message {
      font-size: $font-size-md;
      color: $tv-text-secondary;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .promode-instrument-detail {
    padding: $spacing-md;

    .instrument-header {
      .instrument-title {
        .instrument-name {
          font-size: $font-size-lg;
        }

        .instrument-meta {
          gap: $spacing-sm;
        }
      }
    }

    .price-section {
      .current-price {
        .price-value {
          font-size: $font-size-xl;
        }
      }
    }

    .market-data-grid {
      .data-row {
        grid-template-columns: 1fr;
        gap: $spacing-sm;
      }
    }

    .week-range-section {
      padding: $spacing-md;
    }
  }
}

@media (max-width: 480px) {
  .promode-instrument-detail {
    padding: $spacing-sm;

    .instrument-header {
      margin-bottom: $spacing-md;
      padding-bottom: $spacing-md;

      .instrument-title {
        .instrument-meta {
          flex-direction: column;
          align-items: flex-start;
          gap: $spacing-xs;
        }
      }
    }

    .price-section {
      margin-bottom: $spacing-lg;

      .current-price {
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-xs;
      }
    }

    .market-data-grid {
      margin-bottom: $spacing-lg;

      .data-row {
        .data-item {
          padding: $spacing-sm;
        }
      }
    }

    .week-range-section {
      margin-bottom: $spacing-md;
      padding: $spacing-sm;
    }
  }
}

// Dark theme variations (for different contexts)
.promode-instrument-detail {
  &.theme-light {
    background: #ffffff;
    color: #131722;
    border-color: #e0e3eb;

    .instrument-header {
      border-bottom-color: #e0e3eb;
    }

    .instrument-meta {
      .symbol {
        background: rgba($tv-accent, 0.1);
      }

      .market,
      .currency {
        background: #f7f9fb;
        color: #6a7187;
      }
    }

    .market-data-grid {
      .data-row {
        .data-item {
          background: #f7f9fb;
          border-color: #e0e3eb;

          &:hover {
            background: #f0f3fa;
          }

          .label {
            color: #6a7187;
          }

          .value {
            color: #131722;
          }
        }
      }
    }

    .week-range-section {
      background: #f7f9fb;
      border-color: #e0e3eb;

      .section-title {
        color: #6a7187;
      }

      .range-container {
        .range-track {
          background: #e0e3eb;

          .range-indicator {
            background: #131722;
            border-color: #ffffff;
          }
        }
      }
    }

    .last-updated {
      color: #9598a1;
      border-top-color: #e0e3eb;
    }
  }
}