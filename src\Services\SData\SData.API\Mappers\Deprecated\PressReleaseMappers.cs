﻿using AutoMapper;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Mappers.Deprecated.Profiles;
using System.Linq;

namespace Euroland.FlipIT.SData.API.Mappers.Deprecated
{
  public static class PressReleaseMappers
    {
        internal static IMapper Mapper { get; }
        static PressReleaseMappers()
        {
            Mapper = new MapperConfiguration(cfg => {
                cfg.AddProfile<PressReleaseMapperProfile>();
            }).CreateMapper();
        }

        public static IQueryable<PressRelease> ToModel(this IQueryable<PressRelease> evt)
        {
            return Mapper.ProjectTo<PressRelease>(evt);
        }
    }
}
