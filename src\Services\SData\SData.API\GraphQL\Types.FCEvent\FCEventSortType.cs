using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Data.Sorting;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.FCEvent;

/// <summary>
/// Custom sort type to enable ordering on <see cref="FCalendarDto.DateTime"/>
/// </summary>
public class FCEventSortType : SortInputType<FCalendarDto>
{
  protected override void Configure(ISortInputTypeDescriptor<FCalendarDto> descriptor)
  {
    descriptor.BindFieldsExplicitly();

    descriptor.Field(f => f.DateTime).Type<DefaultSortEnumType>();
  }
}
