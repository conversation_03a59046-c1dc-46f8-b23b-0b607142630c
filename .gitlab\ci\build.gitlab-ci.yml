.build-projects:
  script:
    - 'call "./build/build.bat" --release --sln "src/Services/SData/SData.API" --output-path "bin/Release/net6.0/publish"'
    - 'call "./build/build.bat" --sln "src/Services/Fundamentals/Fundamentals.API" --output-path "bin/Release/net6.0/publish"'
    - 'call "./build/build.bat" --sln "src/ApiGateway/ApiGateway.API" --output-path "bin/Release/net6.0/publish"'

build-merge-request:
  tags:
    - vietnam-dev-shell
  extends:
    - .default-retry
  stage: build
  only:
    refs:
      - merge_requests
    variables:
      - '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"'
  script:
    - 'call "./build/build.bat" --sln core-api-services.sln'

build-qa:
  extends:
    - .default-retry
    - .build-projects
    - .pack-deployment
  tags:
    - vietnam-dev-shell
  stage: build
  only:
    refs:
      - develop
  artifacts:
    expire_in: 1 week

build-staging:
  extends:
    - .default-retry
    - .build-projects
    - .pack-deployment
  tags:
    - ee-buildtest-shell
  stage: build
  only:
    refs:
      - next
  artifacts:
    expire_in: 1 week

build-prod:
  extends:
    - .default-retry
    - .build-projects
    - .pack-deployment
  tags:
    - ee-buildtest-shell
  stage: build
  only:
    refs:
      - master
  artifacts:
    expire_in: 12 mos
