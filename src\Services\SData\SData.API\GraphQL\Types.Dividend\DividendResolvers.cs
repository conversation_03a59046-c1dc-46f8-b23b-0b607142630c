using System.Linq;
using System.Threading;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.Infrastructure;
using entity = Euroland.FlipIT.SData.API.Infrastructure.Entities;
using HotChocolate;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using HotChocolate.Resolvers;
using Microsoft.Data.SqlClient;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Dividend;

public class DividendResolvers
{
  public async Task<InstrumentDto> InstrumentByAnnualDividend(
    [Parent] AnnualDividendDto dividend,
    InstrumentByIdDataLoader dataLoader,
    IResolverContext resolverContext,
    CancellationToken cancellationToken = default
  ) => await dataLoader.LoadAsync(dividend.InstrumentId, cancellationToken);

  public async Task<InstrumentDto> InstrumentByDividendPerShare(
    [Parent] DividendDto dividend,
    InstrumentByIdDataLoader dataLoader,
    IResolverContext resolverContext,
    CancellationToken cancellationToken = default
  ) => await dataLoader.LoadAsync(dividend.InstrumentId, cancellationToken);

  public IQueryable<DividendDto> GetDividends(
    [Parent] InstrumentDto instrument,
    [Service] IGraphQLExecutionContext executionContext,
    IResolverContext resolverContext,
    SharkDbContextBase sharkDbContext,
    CancellationToken cancellationToken = default
  )
  {
    var exchangeCurrency = executionContext.ExchangeCurrency;
    var dividendQuery = GetDividendQuery(sharkDbContext, instrument.Id, exchangeCurrency);

    return dividendQuery
      .OrderByArgumentOrDefault(resolverContext, (q) => q.OrderByDescending(p => p.Date))
      .WithAutoMapper().ToProjection<DividendDto>();
  }

  public IQueryable<AnnualDividendDto> GetAnnualDividends(
    [Parent] InstrumentDto instrument,
    [Service] IGraphQLExecutionContext executionContext,
    IResolverContext resolverContext,
    SharkDbContextBase sharkDbContext,
    CancellationToken cancellationToken = default
  )
  {
    var exchangeCurrency = executionContext.ExchangeCurrency;

    return GetTotalDividendPerShareQuery(
      sharkDbContext,
      instrument,
      exchangeCurrency
    )
    .WithAutoMapper()
    .ToProjection<AnnualDividendDto>();
  }

  private IQueryable<entity.Dividend> GetDividendQuery(SharkDbContextBase sharkDbContext, int instrumentId, string? exchangeCurrency)
  {
    var dividendTableName = sharkDbContext.Dividends.EntityType.GetSchemaQualifiedTableName();
    var dividendDisplayTableName = sharkDbContext.DividendDisplayCurrencies.EntityType.GetSchemaQualifiedTableName();
    var currencyRateHistoryTableName = sharkDbContext.CurrencyRateHistories.EntityType.GetSchemaQualifiedTableName();
    var currencyRateTableName = sharkDbContext.CurrencyRates.EntityType.GetSchemaQualifiedTableName();

    return sharkDbContext.Dividends.FromSqlRaw(@$"
      SELECT
        d.[rID]
			  ,d.currency
        ,divRate.[ExchangeCurrency]
        ,d.[exDate]
        ,d.[recDate]
        ,d.[payDate]
        ,d.[FYEDate]
        ,d.[DivPeriod]
        ,d.[paytype]
        ,d.[GrossDividend]
        ,d.[NetDividend]
        ,d.[TId]
        ,d.[LastRowChange]
        ,d.[GrossDivAdj] * COALESCE(divRate.[Rate], 1) as [GrossDivAdj]
        ,d.[NetDivAdj] * COALESCE(divRate.[Rate], 1) as [NetDivAdj]
        ,d.[SplitNr]
        ,d.[IsProtected]
        ,d.[ShareCapital]
        ,d.[DividendAmount]
        ,d.[BasicEarningsPerShare]
        ,d.[PayoutPer]
        ,d.[isEnabled]
      FROM [{dividendTableName}] AS d
      OUTER APPLY (
        SELECT TOP(1)
          r.[Rate],
          r.[ExchangeCurrency]
        FROM (
          SELECT
          cr.cRate AS [Rate],
          CAST(cr.cDate AS Date) AS [Date],
          cr.cCurr AS [CurrencyPair],
          @exchangeCurrency AS [ExchangeCurrency]
          FROM [{currencyRateTableName}] cr
          UNION
          SELECT
          ch.chRate AS [Rate],
          ch.chDate AS [Date],
          cr.cCurr AS [CurrencyPair],
          @exchangeCurrency AS [ExchangeCurrency]
          FROM [{currencyRateHistoryTableName}] ch
          INNER JOIN [{currencyRateTableName}] cr ON cr.cId = ch.cId
        ) AS r
        WHERE  @exchangeCurrency IS NOT NULL
          AND r.[CurrencyPair] = d.[currency] + @exchangeCurrency
          AND CAST(r.[Date] AS Date) = CAST(d.[exDate] AS Date)
      ) AS divRate
      WHERE d.[TId] = @instrumentId
        AND d.[exDate] IS NOT NULL
        AND d.currency = (
          SELECT TOP(1)
            COALESCE(de.DisplayCurrency, d1.[currency])
          FROM [{dividendTableName}] d1
          LEFT JOIN [{dividendDisplayTableName}] de ON de.InstrumentID = d.TId
          WHERE d1.TId = @instrumentId
          ORDER BY de.PriorityOrder
        )
    ",
    new SqlParameter("exchangeCurrency", System.Data.SqlDbType.Char, 3) {
      Value = !string.IsNullOrEmpty(exchangeCurrency) ? exchangeCurrency : DBNull.Value
    },
    new SqlParameter("instrumentId", System.Data.SqlDbType.Int) {
      Value = instrumentId
    });
  }

  private IQueryable<entity.TotalDividendPerShare> GetTotalDividendPerShareQuery(
    SharkDbContextBase sharkDbContext,
    InstrumentDto instrument,
    string? exchangeCurrency)
  {
    var dividendTableName = sharkDbContext.Dividends.EntityType.GetSchemaQualifiedTableName();
    var dividendDisplayTableName = sharkDbContext.DividendDisplayCurrencies.EntityType.GetSchemaQualifiedTableName();
    var currencyRateHistoryTableName = sharkDbContext.CurrencyRateHistories.EntityType.GetSchemaQualifiedTableName();
    var currencyRateTableName = sharkDbContext.CurrencyRates.EntityType.GetSchemaQualifiedTableName();
    var instrumentHistoryTableName = sharkDbContext.InstrumentHistory.EntityType.GetSchemaQualifiedTableName();

    return sharkDbContext.Set<TotalDividendPerShare>().FromSqlRaw(@$"
      SELECT
        dps.[TId] AS [InstrumentId]
        ,dps.[Currency]
        ,divRate.[ExchangeCurrency]
        ,YEAR(dps.[FYEDate]) AS [Year]
        ,SUM(dps.[GrossDivAdj] * COALESCE(divRate.[Rate], 1)) AS [TotalDividend]
        ,(SUM(dps.[GrossDivAdj] * COALESCE(divRate.[Rate], 1)) / (MAX(dps.[FYESharePrice]) * MAX(COALESCE(shareRate.[Rate], 1)))) * 100 AS [Percentage]
      FROM (
        SELECT
          d.[rID]
          ,d.[TId]
          ,d.[Currency]
          ,d.[exDate]
          ,d.[FYEDate]
          ,d.[GrossDivAdj]
          ,ih.[Close] AS [FYESharePrice]
          ,ih.[Date] AS [FYESharePriceDate]
          ,ih.[Currency] AS [ShareCurrency]
        FROM [{dividendTableName}] AS d
        OUTER APPLY (
          SELECT TOP(1)
            ih1.[Close]
            ,ih1.[Date]
            ,@instrumentCurrency AS [Currency]
          FROM [{instrumentHistoryTableName}] ih1
          WHERE ih1.InstrumentId = d.TId
            AND CAST(ih1.[Date] AS Date) <= CAST(d.FYEDate AS Date)
          ORDER BY ih1.[Date] DESC
        ) AS ih
        WHERE d.FYEDate IS NOT NULL
          AND d.exDate IS NOT NULL
          AND d.currency = (
            SELECT TOP(1)
              COALESCE(de.DisplayCurrency, d1.[currency])
            FROM [{dividendTableName}] d1
            LEFT JOIN [{dividendDisplayTableName}] de ON de.InstrumentID = d.TId
            WHERE d1.TId = @instrumentId
            ORDER BY de.PriorityOrder
          )
      ) AS dps
      OUTER APPLY (
        SELECT TOP(1)
          r.[Rate],
				  r.[ExchangeCurrency]
        FROM (
          SELECT
          cr.cRate AS [Rate],
          CAST(cr.cDate AS Date) AS [Date],
          cr.cCurr AS [CurrencyPair],
          @exchangeCurrency AS [ExchangeCurrency]
          FROM [{currencyRateTableName}] cr
          UNION
          SELECT
          ch.chRate AS [Rate],
          ch.chDate AS [Date],
          cr.cCurr AS [CurrencyPair],
          @exchangeCurrency AS [ExchangeCurrency]
          FROM [{currencyRateHistoryTableName}] ch
          INNER JOIN [{currencyRateTableName}] cr ON cr.cId = ch.cId
        ) AS r
        WHERE  @exchangeCurrency IS NOT NULL
          AND r.[CurrencyPair] = dps.[Currency] + @exchangeCurrency--COALESCE(@exchangeCurrency, @instrumentCurrency)
          AND CAST(r.[Date] AS Date) = CAST(dps.[exDate] AS Date)
      ) AS divRate
      OUTER APPLY (
        SELECT TOP(1) r.[Rate]
        FROM (
          SELECT
          cr.cRate AS [Rate],
          CAST(cr.cDate AS Date) AS [Date],
          cr.cCurr AS [CurrencyPair]

          FROM [{currencyRateTableName}] cr
          UNION
          SELECT
          ch.chRate AS [Rate],
          ch.chDate AS [Date],
          cr.cCurr AS [CurrencyPair]
          FROM [{currencyRateHistoryTableName}] ch
          INNER JOIN [{currencyRateTableName}] cr ON cr.cId = ch.cId
        ) AS r
        WHERE  @exchangeCurrency IS NOT NULL
          AND r.[CurrencyPair] = dps.[ShareCurrency] + COALESCE(@exchangeCurrency, dps.[Currency]) -- Convert share's currency into same dividend's currency
          AND CAST(r.[Date] AS Date) = CAST(dps.[FYESharePriceDate] AS Date)
      ) AS shareRate
      WHERE dps.[TId] = @instrumentId
      GROUP BY dps.[TId], YEAR(dps.[FYEDate]), dps.[Currency], divRate.[ExchangeCurrency]
    ",
    new SqlParameter("exchangeCurrency", System.Data.SqlDbType.Char, 3) {
      Value = !string.IsNullOrEmpty(exchangeCurrency) ? exchangeCurrency : DBNull.Value
    },
    new SqlParameter("instrumentCurrency", System.Data.SqlDbType.Char, 3) {
      Value = !string.IsNullOrEmpty(instrument.CurrencyCode) ? instrument.CurrencyCode : DBNull.Value
    },
    new SqlParameter("instrumentId", System.Data.SqlDbType.Int) {
      Value = instrument.Id
    }
    );
  }
}
