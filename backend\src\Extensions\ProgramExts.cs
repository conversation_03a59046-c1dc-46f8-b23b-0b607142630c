﻿using AISearch.Services;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using Euroland.FlipIT.WiseSearch.Constants;
using Euroland.FlipIT.WiseSearch.Delegates;
using Euroland.FlipIT.WiseSearch.Filters;
using Euroland.FlipIT.WiseSearch.Infrastructure;
using Euroland.FlipIT.WiseSearch.Services;
using Euroland.FlipIT.WiseSearch.Models;

namespace Euroland.FlipIT.WiseSearch.Extensions
{
    public static class ProgramExts
    {
        public static IServiceCollection RegisterAPIServices(this IServiceCollection services)
        {
            services.AddScoped<ISearchService, SearchService>();
            services.AddScoped<ISettingService, SettingService>();
            services.AddScoped<IIngestActivityService, IngestActivityService>();

            return services;
        }

        public static IServiceCollection RegisterRepositories(this IServiceCollection services)
        {
            services.Scan(scan => scan
                .FromAssemblies(AppDomain.CurrentDomain.GetAssemblies())
                .AddClasses(classes => classes.AssignableTo(typeof(IRepositoryBase<,,>)))
                .AsImplementedInterfaces()
                .WithScopedLifetime()
            );

            return services;
        }

        public static IServiceCollection AddUnitOfWorks(this IServiceCollection services)
        {
            services.Scan(scan => scan
                .FromAssemblies(AppDomain.CurrentDomain.GetAssemblies())
                .AddClasses(classes => classes.AssignableTo(typeof(IUnitOfWorkBase<>)))
                .AsImplementedInterfaces()
                .WithScopedLifetime()
            );

            return services;
        }

        public static IServiceCollection RegisterDI(this IServiceCollection services)
        {
            services.AddTransient<HeaderForwardingHandler>();

            return services;
        }

        public static void AddSwaggerService(this IServiceCollection services, Action<SwaggerGenOptions> callback = null)
        {
            services.AddSwaggerGen(c =>
            {
                c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme()
                {
                    Description = "JWT Authorization header using the Bearer scheme. Example: \"Bearer {token}\"",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey,
                    Scheme = "Bearer"
                });
                c.AddSecurityRequirement(new OpenApiSecurityRequirement()
                {
                  {
                    new OpenApiSecurityScheme
                    {
                      Reference = new OpenApiReference
                      {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Bearer"
                      },
                      Scheme = "oauth2",
                      Name = "Bearer",
                      In = ParameterLocation.Header,

                    },
                      new List<string>()
                  }
                });
                c.CustomSchemaIds(type => type.ToString().Replace("`", "").Replace("[", "Of").Replace(",", "And").Replace("]", ""));

                c.SchemaFilter<EnumSchemaFilter>();

                if (callback != null)
                {
                    callback(c);
                }
            });

            services.AddSwaggerGen(c =>
            {
                c.OperationFilter<AddMobileHeadersFilter>();
            });
        }

        public static IServiceCollection AddHttpClientService(this IServiceCollection services, AppSettings settings)
        {
            services.AddHttpClient(HttpClientFactoryNameConst.WiseAPIRequest, client =>
            {
                var wiseBaseUri = settings.HttpClientRequest.WiseApi.BaseUrl
                                                        ?? throw new ArgumentNullException("HttpClientRequest:WiseApi:BaseUrl");
                client.BaseAddress = new Uri(uriString: wiseBaseUri);
                client.DefaultRequestHeaders.UserAgent.ParseAdd("dotnet-docs");
            });

            services.AddHttpClient(HttpClientFactoryNameConst.OpenAIRequest, client =>
            {
                var openAIBaseUri = settings.HttpClientRequest.OpenAI.BaseUrl
                                                        ?? throw new ArgumentNullException("HttpClientRequest:OpenAI:BaseUrl");
                var openAIKey = settings.HttpClientRequest.OpenAI.OpenAIKey
                                                        ?? throw new ArgumentNullException("HttpClientRequest:OpenAI:BaseUrl");

                client.BaseAddress = new Uri(uriString: openAIBaseUri);
                client.DefaultRequestHeaders.Add("Accept", "application/json");
                client.DefaultRequestHeaders.Add("Authorization", $"Bearer {openAIKey}");
                client.DefaultRequestHeaders.UserAgent.ParseAdd("dotnet-docs");
            });

            services.AddHttpClient(HttpClientFactoryNameConst.OidcRequest, client =>
            {
                var ciWiseSearchBaseUrl = settings.HttpClientRequest.Oidc.BaseUrl
                                                        ?? throw new ArgumentNullException("HttpClientRequest:Oidc:BaseUrl");
                client.BaseAddress = new Uri(ciWiseSearchBaseUrl);
                client.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0 (Windows NT 10.0; Win64; x64)");
            });

            return services;
        }
    }
}
