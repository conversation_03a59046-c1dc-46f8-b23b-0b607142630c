using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.NetCore.ToolsFramework.Localization;
using HotChocolate;
using HotChocolate.Resolvers;
using HotChocolate.Types;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Language;

public class QueryTypeExtensions: ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(OperationTypeNames.Query);

    descriptor.Field("systemLanguages")
      .Description("List all languages that supported by Euroland system")
      .UseFiltering()
      .ResolveWith<Resolvers>(p => p.ListAllLanguages(default!));
  }

  private class Resolvers {
    public async Task<IEnumerable<LanguageDto>> ListAllLanguages(
      [Service] IOptions<JsonLocalizationOptions> languageOptions)
    {
      var languageProvider = languageOptions.Value.LanguageToCultureProvider;
      if(!languageProvider.Loaded)
      {
        languageProvider.Load();
      }

      return await Task.Run(() => languageProvider
        .AllSupportedCultures
        .Select(culture => languageProvider.GetLanguage(culture))
        .Where(p => p.SupportedCultures.Count > 0)
        .SelectMany(p => p.SupportedCultures.Select(c => new LanguageDto {
            Id = p.DbId,
            CultureCode = c.Name,
            Name = c.NativeName,
            NameEnglish = c.EnglishName ?? c.DisplayName
          })
        ).DistinctBy(p => p.CultureCode)
      );
    }
  }
}
