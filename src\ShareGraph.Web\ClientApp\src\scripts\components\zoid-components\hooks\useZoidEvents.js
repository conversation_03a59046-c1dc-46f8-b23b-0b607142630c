import {useEffect, useRef} from "react";

export default function useZoidEvents(instance, events) {
  const eventsRef = useRef(events);
  eventsRef.current = events;

  useEffect(() => {
    const clearup = [];
    for(const [key, listener] of Object.entries(events)) {
      const {cancel} = instance.component.event.on(window.euroland.EVENT[key], listener);
      clearup.push(cancel);
    }

    return () => clearup.forEach(fn => fn());
  }, []); 
}