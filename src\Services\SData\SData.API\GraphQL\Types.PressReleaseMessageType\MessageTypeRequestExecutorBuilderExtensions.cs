using Euroland.FlipIT.SData.API.GraphQL.Types.PressReleaseMessageType;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class MessageTypeRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddMessageTypeType(this IRequestExecutorBuilder builder)
  {
    builder.AddType<PressReleaseMessageType>();
    builder.AddTypeExtension<CompanyTypeExtensions>();

    return builder;
  }
}
