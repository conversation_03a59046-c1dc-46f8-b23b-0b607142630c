using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Types;
using HotChocolate.Types.Pagination;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Attachment;

/// <summary>
/// Extends 'attachments' field to type <see cref="PressRelease.PressReleaseType"/>
/// </summary>
public class PressReleaseTypeExtensions: ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(PressRelease.PressReleaseType.Name);

    descriptor.Field("attachments")
      .Description(@"List of all attachments relating to a pressrelease.
        This field may has issue about the loading performance if there are a huge
        attachments need to query at same time. Use `@files` field to have better performance.")
      .UsePaging(
        connectionName: "Attachment",
        options: new PagingOptions
        {
          IncludeTotalCount = true,
          MaxPageSize = int.MaxValue,
          DefaultPageSize = 200
        }
      )
      .UseProjection()
      .UseFiltering()
      .UseSorting(typeof(AttachmentSortInputType))
      .ResolveWith<AttachmentResolvers>(resolvers => resolvers.GetAttachmentsByPressRelease(default!, default!));

    descriptor.Field("files")
      .Description("List of all attachments relating to a pressrelease.")
      .ResolveWith<AttachmentResolvers>(r => r.GetAttachmentsByPressReleaseNonQueryable(default!, default!, default!, default));
  }
}
