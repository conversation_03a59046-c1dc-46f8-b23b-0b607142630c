using System;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using HotChocolate;
using HotChocolate.Resolvers;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Middleware;


/// <summary>
/// Middleware for handling query context within a GraphQL request pipeline.
/// </summary>
/// <remarks>
/// This middleware can be used to inject or manipulate the <see cref="IGraphQLExecutionContext"/>
/// during the execution of a GraphQL field resolver.
/// </remarks>
/// <param name="next">The next delegate in the middleware pipeline.</param>
public class QueryContextMiddleware
{
  public const string REALTIME_HTTP_HEADER = "X-DB-Connection-Kind";
  public const string USE_CLOUD_DB = "useCloud";

  private readonly FieldDelegate _next;

  public QueryContextMiddleware(FieldDelegate next)
  {
    _next = next;
  }

  public async ValueTask InvokeAsync(
    IMiddlewareContext context,
    IGraphQLExecutionContext requestContext,
    IHttpContextAccessor httpContextAccessor
    )
  {
    requestContext.RequestId = GetRequestId(httpContextAccessor);
    requestContext.UseCloud = GetUseCloud(context, httpContextAccessor);
    requestContext.UseRealtime = await context.UseRealtimeDataAsync();
    requestContext.UseAdjClose = context.GetAdjClose();

    if (context.TryGetExchangeCurrency(out string? exchangeCurrency))
    {
      requestContext.ExchangeCurrency = exchangeCurrency;
    }

    await _next(context);
  }

  private string GetRequestId(IHttpContextAccessor httpContextAccessor)
  {
    var httpContext = httpContextAccessor.HttpContext;

    if (httpContext?.Items.TryGetValue("GraphQL.RequestId", out var requestId) == true)
    {
      return requestId?.ToString();
    }

    // Or generate/use HTTP request ID
    return httpContext?.TraceIdentifier;
  }

  private bool GetUseCloud(IResolverContext resolverContext, IHttpContextAccessor httpContextAccessor)
  {
    if (httpContextAccessor.HttpContext.Request.Headers.TryGetValue(REALTIME_HTTP_HEADER, out var dbConnectionKind)
      && string.Equals(dbConnectionKind, "cloud", StringComparison.InvariantCultureIgnoreCase)
    )
    {
      return true;
    }

    if (resolverContext.ScopedContextData.TryGetValue(USE_CLOUD_DB, out var useCloudValue) && useCloudValue != null)
    {
      return (bool)useCloudValue;
    }

    // Swallow GraphQLException since resolverContext.ArgumentOptional<T>()
    // will throw an exception if argument is not available.
    try
    {
      var optional = resolverContext.ArgumentOptional<bool?>(USE_CLOUD_DB);

      if (optional is { HasValue: true, Value: not null })
      {
        return optional.Value.Value;
      }
    }
#pragma warning disable S108
    catch (GraphQLException)
    {
    }
#pragma warning restore S108
    return false;
  }
}
