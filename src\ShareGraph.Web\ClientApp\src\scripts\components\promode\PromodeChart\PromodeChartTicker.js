import { useEffect, useState } from "react";
import { client } from "../../../services/graphql-client";
import { buildOptimizedTickerQuery } from "../../../graphql-queries/tickerQuery";
import './PromodeChartTicker.scss';
import { formatDateTime } from "../../../helper";

const PromodeChartTicker = ({ instrumentId }) => {

  const [tickerData, setTickerData] = useState({});
  const tickerPrice = tickerData?.currentPrice?.tickerData || {};

  useEffect(() => {
    const fetchTickers = async () => {
      try {
        const fields = {
          change: true,
          changePercentage: true,
          currency: true,
          currencyCode: true,
          currencyCodeStr: true,
          instrumentId: true,
          last: true,
          lastUpdatedDate: true,
          marketAbbreviation: true,
          marketName: true,
          marketStatus: true,
          marketTimeZone: true,
          normalDailyClose: true,
          normalDailyOpen: true,
          officialClose: true,
          officialCloseDate: true,
          open: true,
          shareName: true,
          symbol: true,
          ticker: true,
          timezoneIANA: true,
          timezoneName: true,
          volume: true
        };
        const queryString = buildOptimizedTickerQuery(fields);
        const instruments = await client.query(queryString, { ids: [instrumentId], adjClose: false });
        const data = instruments?.data?.instrumentByIds?.[0] || {};
        setTickerData(data);
      } catch (error) {
        console.error("Error fetching ticker data:", error);
      }
    };

    if (instrumentId) {
      fetchTickers();
    }
  }, [instrumentId]);

  const formatPrice = (price) => {
    if (price === null || price === undefined) return 'N/A';
    return Number(price).toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  const formatChange = (change) => {
    if (change === null || change === undefined) return 'N/A';
    const formattedChange = Number(change).toFixed(2);
    return change >= 0 ? `+${formattedChange}` : formattedChange;
  };

  const formatPercentage = (percentage) => {
    if (percentage === null || percentage === undefined) return 'N/A';
    const formattedPercentage = Number(percentage).toFixed(2);
    return percentage >= 0 ? `+${formattedPercentage}%` : `${formattedPercentage}%`;
  };

  const formatTime = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    const timeString = date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
    const timezone = tickerData?.marketTimeZone || 'GMT+7';
    return `At close at ${timeString} ${timezone}`;
  };

  const getChangeClass = () => {
    const change = tickerPrice.change;
    if (change > 0) return 'positive';
    if (change < 0) return 'negative';
    return 'neutral';
  };


  return (
    <div className="promode-ticker-horizontal">
      <div className="ticker-main">
        <span className="ticker-price">
          {tickerData.symbol}
        </span>
        <span className="ticker-price">
          {formatPrice(tickerPrice.last)}
        </span>
        <span className="ticker-currency">
          {tickerData.currency?.code}
        </span>
        <span className={`ticker-change ${getChangeClass()}`}>
          {formatChange(tickerPrice.change)} {formatPercentage(tickerPrice.changePercentage)}
        </span>
      </div>
      <div className="ticker-time">
        {formatDateTime(tickerData.currentPrice?.date)}
      </div>
    </div>
  );
};

export default PromodeChartTicker;
