using Euroland.FlipIT.WiseSearch.Infrastructure.UnitOfWorks;
using Euroland.FlipIT.WiseSearch.Models.Setting;

namespace Euroland.FlipIT.WiseSearch.Services
{
    public interface ISettingService
    {
        Task<CompanyConfiguration?> GetCompanyConfigurationAsync(string companyCode, string language);
    }

    public class SettingService : ISettingService
    {
        private readonly ISharkUoW _sharkUoW;
        private readonly ILogger<SettingService> _logger;

        public SettingService(ISharkUoW sharkUoW,
                              ILogger<SettingService> logger)
        {
            _sharkUoW = sharkUoW;
            _logger = logger;
        }

        public async Task<CompanyConfiguration?> GetCompanyConfigurationAsync(string companyCode, string language)
        {
            var companySetting = await _sharkUoW.SharkCompanySettingRepository.GetCompanySettingByCode(companyCode, language);
            return companySetting;
        }
    }
}
