﻿using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Euroland.FlipIT.WiseSearch.Infrastructure.Entities.Shark;
using Euroland.FlipIT.WiseSearch.Models.Setting;

namespace Euroland.FlipIT.WiseSearch.Infrastructure.Repositories
{
    public interface ISharkCompanySettingRepository : IRepositoryBase<SharkDbContext, CompanySetting, string>
    {
        Task<CompanyConfiguration?> GetCompanySettingByCode(string companyCode, string language);
    }

    public class SharkCompanySettingRepository : RepositoryBase<SharkDbContext, CompanySetting, string>, ISharkCompanySettingRepository
    {
        private readonly IConfiguration _configuration;
        public SharkCompanySettingRepository(SharkDbContext context, IConfiguration configuration) : base(context)
        {
            _configuration = configuration;
        }

        public async Task<CompanyConfiguration?> GetCompanySettingByCode(string companyCode, string language)
        {
            var companySettings = _dbContext.CompanySettings.AsNoTracking();
            var companyNameTranslationSettings = _dbContext.CompanySettingLanguages.AsNoTracking();

            var query = await (from companySetting in companySettings
                               join cNameTrans in companyNameTranslationSettings on companySetting.CompanyCode equals cNameTrans.CompanyCode into cNameTranslations
                               where companySetting.CompanyCode == companyCode
                               select new CompanyConfiguration
                               {
                                   CompanyCode = companySetting.CompanyCode,
                                   CompanyLogo = companySetting.CompanyLogo,
                                   EnabledLanguages = companySetting.EnabledLanguages,
                                   DefaultLanguage = companySetting.DefaultLanguage,
                                   Timezone = companySetting.Timezone,
                                   UseLocalTimezone = companySetting.UseLocalTimezone,
                                   Industry = companySetting.Industry,
                                   CompanyName = cNameTranslations.Select(s => new CompanyNameTranslation
                                   {
                                       Id = s.Id,
                                       CompanyCode = s.CompanyCode,
                                       Language = s.Language,
                                       Name = s.Name,
                                       HomePage = s.HomePage
                                   }).FirstOrDefault(s => s.CompanyCode == companyCode)
                               }
                        ).FirstOrDefaultAsync();

            return query;
        }
    }
}
