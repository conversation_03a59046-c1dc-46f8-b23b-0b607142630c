using HotChocolate;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Euroland.FlipIT.SData.API.Infrastructure.Entities
{
    public class Instrument
    {
        [Key]
        [Column("Id")]
        public int InstrumentId { get; set; }
        public string Ticker { get; set; }
        [Column("Name")]
        public string ShareName { get; set; }
        public short MarketID { get; set; }
        public int? MarCat { get; set; }
        public int? EurCat { get; set; }
        public int? ListID { get; set; }
        [GraphQLName("iSIN")]
        public string ISIN { get; set; }
        public byte? Customer { get; set; }
        public string? CurrencyCode { get; set; } = String.Empty;

        [Column(TypeName = "decimal(18,2)")]
        [GraphQLName("yTD")]
        public decimal? YTD { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Week { get; set; }
        [Column("2Week", TypeName = "decimal(18,2)")]
        public decimal? TwoWeek { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Month { get; set; }
        [Column(TypeName = "decimal(19,0)")]
        public decimal? NoShares { get; set; }
        //public decimal? MarketCap { get; set; }
        public DateTime? Agentupdate { get; set; }
        public string? MarketSpecSign { get; set; }
        [Column("52wHigh", TypeName = "decimal(18,4)")]
        public decimal? High52W { get; set; }
        [Column("52wLow", TypeName = "decimal(18,4)")]
        public decimal? Low52W { get; set; }
        [Column("52wChange", TypeName = "decimal(18,2)")]
        public decimal? Percent52W { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? Volatility { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? Correlation { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? BetaFactor { get; set; }
        public short? TokyoEurope { get; set; }
        public byte? PrimaryMarket { get; set; }
        [Column("3MonthHigh", TypeName = "decimal(18,4)")]
        public decimal? ThreeMonthHigh { get; set; }
        [Column("3MonthLow", TypeName = "decimal(18,4)")]
        public decimal? ThreeMonthLow { get; set; }
        [Column("3MonthChange", TypeName = "decimal(18,2)")]
        public decimal? ThreeMonthChange { get; set; }
        [Column("5YearsChange", TypeName = "decimal(18,2)")]
        public decimal? FiveYearsChange { get; set; }
        public DateTime? NumSharesDate { get; set; }
        public double? SplitRatio { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [GraphQLName("ePS")]
        public decimal? EPS { get; set; }

        [GraphQLName("sPS")]
        public double? SPS { get; set; }

        [Column(TypeName = "decimal(18,4)")]
        [GraphQLName("dPS")]
        public decimal? DPS { get; set; }
        public double? PayoutRatio { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? Turnover { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? NetIncome { get; set; }
        public double? TurnoverGrowth { get; set; }
        public double? NetInComeGrowth { get; set; }
        public double? BookValueOfShare { get; set; }
        public int? LotSize { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? AllTimeHigh { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? AllTimeLow { get; set; }
        public DateTime? ListedFrom { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? TotalMarketCap { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? PrevMid { get; set; }
        [Column("52Highest", TypeName = "decimal(18,4)")]
        public decimal? Highest52w { get; set; }
        [Column("52Lowest", TypeName = "decimal(18,4)")]
        public decimal? Lowest52w { get; set; }
        public DateTime? LastRowChange { get; set; }
        public bool Visible { get; set; }
        public byte InstrumentType { get; set; }
        public bool? MainIndex { get; set; }
        public string? YahooSymbol { get; set; }
        public int? CompanyID { get; set; }
        [NotMapped]
        public string? CompanyCode { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? HighYTD { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? LowYTD { get; set; }
        public int? RegionID { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? MarketCapEUR { get; set; }
        public long? NumberOfUnlistedShares { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? VolumeTurnover { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? VolumeTurnoverUSD { get; set; }
        public int DataSourceID { get; set; }

        [GraphQLName("wKN")]
        public string? WKN { get; set; }
        public string? RicCode { get; set; }

        [GraphQLIgnore]
        public virtual InstrumentPrice? InstrumentPrice { get; set; }

        [GraphQLIgnore]
        public virtual List<InstrumentHistory> InstrumentHistories { get; set; }

        [GraphQLIgnore]
        public virtual Market Market { get; set; }

        [GraphQLIgnore]
        public virtual Currency Currency { get; set; }

        [GraphQLIgnore]
        public virtual List List { get; set; }

        [GraphQLIgnore]
        public virtual List<Dividend> DividendEvents { get; set; }
    }
}
