{"Serilog": {"Using": ["Serilog.Sinks.Syslog"], "MinimumLevel": "Warning", "WriteTo": [{"Name": "UdpSyslog", "Args": {"host": "*************", "port": "514", "appName": "FlipIT.ApiGateway", "restrictedToMinimumLevel": "Warning", "outputTemplate": "[{Timestamp:HH:mm:ss}] {Message:lj}{NewLine}{Exception}"}}], "Properties": {"Application": "FlipIT.ApiGateway"}}, "WellKnownSchemas": [{"IsRootType": true, "Name": "sdata", "RemoteUrl": "http://localhost:8090/tools/sdata-api/graphql"}, {"Name": "fundamental", "RemoteUrl": "http://localhost:8090/tools/fundamental-api/graphql"}]}