using Euroland.FlipIT.Fundamental.API.Dtos;
using Euroland.FlipIT.Fundamental.API.Persistance;

namespace Euroland.FlipIT.Fundamental.API.GraphQL.Types;
public class FundamentalTypeExtensions: ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(FundamentalType.TypeName);

    descriptor.Field("reports")
      .UsePaging(options: new HotChocolate.Types.Pagination.PagingOptions {
        DefaultPageSize = 10
      })
      .UseProjection()
      .UseFiltering()
      .UseSorting()
      .ResolveWith<Resolvers>(p => p.GetReportsByCompanyCode(default!, default!, default!));
  }

  private class Resolvers {
    public IQueryable<ReportDto> GetReportsByCompanyCode(
      [Parent] FundamentalDto fundamental,
      ReportHubDbContext dbContext,
      [Service] IConfiguration configuration
    )
    {
      var thumbnailBlob = configuration["ReportHub:ThumbnailBlob"];
      var query = from R in dbContext.Report
        join C in dbContext.Company on R.CompanyID equals C.CompanyID
        join RT in dbContext.ReportType on R.ReportTypeId equals RT.Id
        join RPT in dbContext.ReportParentType on R.ReportParentTypeId equals RPT.Id
        where C.CompanyCode == fundamental.CompanyCode && R.IsActive && C.IsActive
        select new ReportDto
        {
            Title = R.Title,
            Id = R.Id,
            LanguageCode= R.LanguageCode,
            LanguageName = R.LanguageName,
            SubTitle= R.SubTitle,
            FileLocation = R.FileLocation,
            UploadedDate = R.UploadedDate,
            UploadedDateUTC = R.UploadedDate,
            ReportParentTypeDisplayName = RPT.DisplayName,
            ReportTypeName = RT.Name,
            ThumbnailFileLocation = R.ThumbnailFileLocation,
            ThumbnailFileLocationFull = $"{thumbnailBlob}{R.CompanyID}/{R.ThumbnailFileLocation}",
            ReportTypeId = R.ReportTypeId,
            ReportParentTypeId = R.ReportParentTypeId,
            Year = R.Year,
            ReportParentTypeOrder = RPT.ReportParentTypeOrder,
            ReportTypeOrder = RT.ReportTypeOrder
        };

      return query;
    }
  }
}
