using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using Euroland.FlipIT.SData.API.Infrastructure.Factories;
using Euroland.FlipIT.Shared.CachingManager;
using GreenDonut;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Company;

/// <summary>
/// A data loader for batching and caching the retrieval of <see cref="CompanyDto"/> objects by their company code.
/// Utilizes a cache to minimize database queries and supports batch loading for efficient data access in GraphQL resolvers.
/// </summary>
public class CompanyByCodeDataLoader : BatchDataLoader<string, CompanyDto>
{
  private readonly ISharkDbContextAbstractFactory _factory;
  private readonly IGraphQLExecutionContext _queryRequestContext;
  private readonly IConfigurableCacheManager _cache;
  public CompanyByCodeDataLoader(
    ISharkDbContextAbstractFactory factory,
    IGraphQLExecutionContext queryRequestContext,
    IConfigurableCacheManager configurableCacheManager,
    IBatchScheduler batchScheduler,
    DataLoaderOptions? options = null)
    : base(batchScheduler, options)
  {
    _factory = factory ?? throw new ArgumentNullException(nameof(factory));
    _queryRequestContext = queryRequestContext ?? throw new ArgumentNullException(nameof(queryRequestContext));
    _cache = configurableCacheManager ?? throw new ArgumentNullException(nameof(configurableCacheManager));
  }

  protected override async Task<IReadOnlyDictionary<string, CompanyDto>> LoadBatchAsync(IReadOnlyList<string> keys, CancellationToken cancellationToken)
  {
    var filterKeys = keys.Where(k =>
        _cache.GetCache<CompanyDto, string>(CacheKeyHelper.GenerateKeyByPropOfDtoObject<CompanyDto, string>(k, c => c.Code)) == null)
      .ToList();

    await using var sharkDbContext = await _factory.CreateDbContextAsync(_queryRequestContext.UseRealtime, _queryRequestContext.UseCloud);

    var data = filterKeys.Count > 0
      ? await sharkDbContext
        .Company
        .AsNoTracking()
        .Where(c => filterKeys.Contains(c.CompanyCode))
        .WithAutoMapper()
        .ToProjection<CompanyDto>()
        .ToListAsync(cancellationToken)
      : Enumerable.Empty<CompanyDto>();

    foreach (var company in data)
    {
      _cache.SetCache(
        company,
        CacheKeyHelper.GenerateKeyByPropOfDtoObject<CompanyDto, string>(company.Code, c => c.Code)
      );
    }

    var result = keys.Select(k =>
      _cache.GetCache<CompanyDto, string>(CacheKeyHelper.GenerateKeyByPropOfDtoObject<CompanyDto, string>(k, c => c.Code))).ToList();

    return result.ToDictionary(x => x.Code.Trim().ToLower()!);
  }
}
