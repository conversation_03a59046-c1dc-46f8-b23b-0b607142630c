import { useState, useEffect } from "react";
import { client } from "../../../../services/graphql-client";
import { INSTRUMENT_DETAIL_QUERY } from './IntrusmentInfoQuery';

export const useInstrumentDetail = (instrumentId) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!instrumentId) {
      setData(null);
      setLoading(false);
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);

    client.query(INSTRUMENT_DETAIL_QUERY, {
      ids: [instrumentId],
      adjClose: false
    }).toPromise()
      .then(result => {
        setData(result.data);
        setError(result.error);
        setLoading(false);
      })
      .catch(err => {
        setError(err);
        setLoading(false);
      });
  }, [instrumentId]);

  return {
    data: data?.instrumentByIds?.[0],
    loading,
    error
  };
};
