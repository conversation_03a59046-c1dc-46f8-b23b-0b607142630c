import { FC } from "react";
import { ArrowTrendDown, ArrowTrendUp, ChartLine, LogOut, XMark } from "../../Common";

interface ToastContentProps {
  type?: string;
  text: React.ReactNode;
  handleClose: () => void;
  showIcon?: boolean;
}

export const ToastContent: FC<ToastContentProps> = ({ type, text, handleClose, showIcon = true }) => {
  const getIcon = () => {
    switch (type) {
      case "positive":
        return <ArrowTrendUp className="wise-notification-icon" />;
      case "negative":
        return <ArrowTrendDown className="wise-notification-icon" />;
      case "logout":
        return <LogOut className="wise-notification-icon" />;
      default:
        return <ChartLine className="wise-notification-icon" />;
    }
  };

  return (
    <>
      <div className="wise-notification-content">
        {showIcon && getIcon()}
        <span className="wise-notification-text">{text}</span>
      </div>
      <XMark
        className="wise-notification-close w-4 h-4 text-gray-600 hover:text-gray-800"
        onClick={handleClose}
      />
    </>
  );
}; 