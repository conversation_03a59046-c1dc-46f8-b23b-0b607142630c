using System.Linq.Expressions;
using AutoMapper;
using Euroland.FlipIT.Fundamental.API.AutoMappers.Profiles;
using Euroland.FlipIT.Fundamental.API.Dtos.Common;

namespace Euroland.FlipIT.Fundamental.API.AutoMappers;

public static class IMapperExtensions
{
  public class IQueryableInternalMapper<TEntity> where TEntity : class
  {
    private IQueryable<TEntity> _entities;

    internal IQueryableInternalMapper(IQueryable<TEntity> entities)
    {
      _entities = entities;
    }

    public IQueryable<TDto> ToProjection<TDto>() where TDto : IDtoObject
    {
      return _mapper.ProjectTo<TDto>(_entities);
    }

    public IQueryable<TDto> ToProjection<TDto>(
#pragma warning disable S3427 // Method overloads with default parameter values should not overlap
      object parameters = null,
#pragma warning restore S3427 // Method overloads with default parameter values should not overlap
      params Expression<Func<TDto, object>>[] membersToExpand
    ) where TDto : IDtoObject
    {
      return _mapper.ProjectTo<TDto>(_entities, parameters, membersToExpand);
    }

    public IQueryable<TDto> ToProjection<TDto>(
      IDictionary<string, object> parameters,
      params string[] membersToExpand
    ) where TDto : IDtoObject
    {
      return _mapper.ProjectTo<TDto>(_entities, parameters, membersToExpand);
    }

    public IEnumerable<TDto> Map<TDto>() where TDto : IDtoObject
    {
      return _entities.AsEnumerable().Select(e => _mapper.Map<TEntity, TDto>(e));
    }
  }

  public class IEnumerableInternalMapper<TEntity> where TEntity : class
  {
    private IEnumerable<TEntity> _entities;

    internal IEnumerableInternalMapper(IEnumerable<TEntity> entities)
    {
      _entities = entities;
    }

    public IEnumerable<TDto> Map<TDto>() where TDto : IDtoObject
    {
      return _entities.Select(e => _mapper.Map<TEntity, TDto>(e));
    }
  }

  private readonly static MapperConfiguration _configuration = new(
    cfg =>
    {
      cfg.AddProfile<FinancialEventMapperProfile>();
      cfg.AddProfile<FinancialEventTypeMapperProfile>();
      cfg.AddProfile<FinancialEventAttachmentMapperProfile>();
      cfg.AddProfile<FinancialEventStatisticsMapperProfile>();
    }
  );

  private readonly static IMapper _mapper = new Mapper(_configuration);

  public static IMapper Mapper => _mapper;

  public static IQueryableInternalMapper<TEntity> WithAutoMapper<TEntity>(this IQueryable<TEntity> entities)
    where TEntity : class
  {
    return new IQueryableInternalMapper<TEntity>(entities);
  }

  public static IEnumerableInternalMapper<TEntity> WithAutoMapper<TEntity>(this IEnumerable<TEntity> entities)
    where TEntity : class
  {
    return new IEnumerableInternalMapper<TEntity>(entities);
  }
}
