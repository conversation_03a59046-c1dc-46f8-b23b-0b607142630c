﻿using HotChocolate;
using System.Collections.Generic;

namespace Euroland.FlipIT.SData.API.DTOs.Deprecated
{
  [GraphQLName("InstrumentYearlyPerformancePrice_Deprecated")]
    public class InstrumentYearlyPerformancePrice
    {
        public int InstrumentId { get; set; }
        public string? ShareName { get; set; }
        public string? MarketAbbreviation { get; set; }
        public string? CurrencyCode { get; set; }
        public IEnumerable<YearlyPerformancePrice>? YearlyPerformances { get; set; }
    }

    public class YearlyPerformancePrice
    {
        public decimal? Close { get; set; }
        public decimal? CloseLastYear { get; set; }
        public int Year { get; set; }
        public decimal? ChangePercentage { get; set; }
    }
}
