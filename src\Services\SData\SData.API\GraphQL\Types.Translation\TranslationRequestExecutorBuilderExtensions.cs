using Euroland.FlipIT.SData.API.GraphQL.Types.Translation;
using HotChocolate.Execution.Configuration;

namespace Microsoft.Extensions.DependencyInjection;

public static class TranslationRequestExecutorBuilderExtensions
{
  public static IRequestExecutorBuilder AddTranslationType(this IRequestExecutorBuilder builder)
  {
    return builder
      .AddType<TranslationType>()
      .AddType<HasTranslationType>();
  }
}
