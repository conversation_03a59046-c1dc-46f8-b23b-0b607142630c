using Euroland.FlipIT.SData.API.Dto;
using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Market;

public class MarketStatusType: ObjectType<MarketStatusDto>
{
  protected override void Configure(IObjectTypeDescriptor<MarketStatusDto> descriptor)
  {
    descriptor.Name("MarketStatus").Description("Represents the current status of a Market");
    descriptor.Field(p => p.IsOpened).Description("Indicates that market is currently openning");
    descriptor.Field(p => p.RemainingTime).Description("Remaining time (in minutes) to let the market is going to be opened/closed.");
    descriptor.Field(p => p.MarketId).Ignore();
  }
}
