import { FOLLOWING_STATUS } from "@/helpers/constants";
import { StateCreator } from "zustand";
import { produce } from "immer";
import { apiUserPreferenceClient } from "@/services/clientInstance";
import {API_PATHS} from "@/services/apiEndpoints";

export interface IUserProfile {
  userProfile: {
    avatar: string | null,
    fullName: string,
    status: FOLLOWING_STATUS;
    setStatus: (status: FOLLOWING_STATUS) => void;
    updateStatus: (status: FOLLOWING_STATUS) => Promise<void>;
    setUserProfile: (param: { avatar: string | null, fullName: string }) => void,
    reset: () => void
  };
}

const initialState = {
  avatar: null,
  fullName: "",
  status: FOLLOWING_STATUS.UN_FOLLOW,
}

export const userProfile: StateCreator<IUserProfile, [], [], IUserProfile> = (
  set
) => ({
  userProfile: {
    ...initialState,
    setUserProfile: ({ avatar, fullName }) => set(state => produce(state, draft => {
      draft.userProfile.avatar = avatar
      draft.userProfile.fullName = fullName
    })),
    setStatus: (status) =>
      set((state) =>
        produce(state, (draft) => {
          draft.userProfile.status = status;
        })
      ),
    updateStatus: async (status) => {
      await apiUserPreferenceClient.instance.post(API_PATHS.FOLLOW_COMPANY, {}, {
        status,
      });
      set((state) =>
        produce(state, (draft) => {
          draft.userProfile.status = status;
        })
      );
    },
    reset: () => set(state => produce(state, draft => {
      draft.userProfile = {
        ...draft.userProfile,
        ...initialState,
      }
    }))
  },
});
