using Euroland.FlipIT.Shared.CachingManager;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Microsoft.Extensions.DependencyInjection;

public static class AddCachingManagerExtensions
{
  /// <summary>
  /// Adds in-memory distributed caching services to the specified IServiceCollection.
  /// </summary>
  /// <param name="services">The IServiceCollection to add the caching services to.</param>
  /// <returns>The IServiceCollection with caching services added.</returns>
  public static IServiceCollection AddInMemoryDistributedCachingManager(this IServiceCollection services)
  {
    services.AddDistributedMemoryCache();
    services.TryAdd(ServiceDescriptor.Singleton<ICachingManager, DistributedCacheManager>());
    services.TryAdd(ServiceDescriptor.Singleton<IConfigurableCacheManager, DistributedCacheManager>());

    return services;
  }
}
