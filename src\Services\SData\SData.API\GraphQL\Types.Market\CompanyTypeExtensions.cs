using HotChocolate.Types;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Market;

/// <summary>
/// Extends 'primaryMarket' field to type <see cref="Company.CompanyType"/>
/// </summary>
public class CompanyTypeExtensions : ObjectTypeExtension
{
  protected override void Configure(IObjectTypeDescriptor descriptor)
  {
    descriptor.Name(Company.CompanyType.Name);

    descriptor.Field("primaryMarket")
      .Type<MarketType>()
      .ResolveWith<MarketResolvers>(
        resolvers => resolvers.GetMarketByCompany(default!, default!, default!, default!)
      );
  }
}
