import { useEffect, useState } from "react";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import "./ResizablePanels.scss";
import "./PromodeModal.scss";
import { appSettings } from "../../../../appSettings";
import Promode<PERSON>hart from "../PromodeChart/PromodeChart";
import { AlertIcon, ChatIcon, DataWindowIcon, WatchlistIcon, ChartAssistantIcon, ScreenersIcon, CalendarIcon } from "../icons";
import { useAuth } from "../../../customHooks/useAuth";
import { getInitials, UserMenuModal } from "../../UserInfo/UserMenu";
import { zoidComponentCreator } from "../../zoid-components/zoid-creator";
import InfoTickerTabs from "../Tabs/InfoTickerTabs";
import PromodeInstrumentDetail from "../PromodeInstrumentDetail";

export default function PromodeModal({ onCloseWindow, data, onLogout }) {
  const [activeTab, setActiveTab] = useState("tab1");
  const auth = useAuth();
  const user = auth.user;
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onCloseWindow();
    }
  };


  const handleOverlayKeyDown = (e) => {
    if (e.key === "Escape") {
      onCloseWindow();
    }
  };

  return (
    <div
      className="promode-modal-overlay"
      onClick={handleOverlayClick}
      onKeyDown={handleOverlayKeyDown}
      role="button"
      aria-label="Close modal"
      tabIndex={0}
    >
      <div className="promode-modal">
        <div className="promode-modal__header">
          <h2>Pro Mode</h2>
          <button className="promode-modal__close-btn" onClick={() => onCloseWindow()}>
            &times;
          </button>
        </div>

        <div className="promode-modal__body">
          <div className="promode-modal__tabs">
            <div className="promode-modal__tabs-wrapper">
              <button
                title="Watchlist, details, news"
                className={`promode-modal__tab ${
                  activeTab === "tab1" ? "active" : ""
                }`}
                onClick={() => setActiveTab("tab1")}
              >
                <WatchlistIcon />
              </button>
              <button
                title="Alerts"
                className={`promode-modal__tab ${
                  activeTab === "tab2" ? "active" : ""
                }`}
                onClick={() => setActiveTab("tab2")}
              >
                <AlertIcon />
              </button>
              <button
                title="Object Tree and Data window"
                className={`promode-modal__tab`}
              >
                <DataWindowIcon />
              </button>
              <button
                title="Chats"
                className={`promode-modal__tab`}
              >
                <ChatIcon />
              </button>
            </div>
            <div className="promode-modal__tabs-wrapper-down">
              <button
              title="Screeners"
              className={`promode-modal__tab`}>
                <ScreenersIcon />
              </button>
              <button
                title="Calendars"
                className={`promode-modal__tab`}
              >
                <CalendarIcon />
              </button>
              <button
                title="Chart assistant"
                className={`promode-modal__tab`}
              >
                <ChartAssistantIcon />
              </button>
              <div className="tab-separator" />
              <div className="">
                <UserMenuModal onCloseWindow={onCloseWindow} onLogout={onLogout}/>
              </div>
            </div>
          </div>
          <div className="promode-modal__content">
            <PanelGroup direction="horizontal">
              <Panel className="promode-panel" defaultSize={30}>
                  <PanelGroup direction="vertical">
                    <Panel className="promode-panel" defaultSize={70}>
                      {activeTab === "tab1" && (
                        <div className="promode-panel__content promode-panel__content-watchlist">
                          <h5 className="promode-panel__title">Watchlist</h5>
                          <euroland-watch-list />
                        </div>
                      )}
                      {activeTab === "tab2" && (
                        <div className="promode-panel__content promode-panel__content-share-alert">
                          <euroland-share-alert
                            key={data.mainInstrumentId}
                            instrumentId={data.mainInstrumentId}
                            name={data.shareName}
                            currentPrice={data.price}
                            companyCode={appSettings.companyCode}
                          />
                        </div>
                      )}
                    </Panel>
                    <PanelResizeHandle className="promode-resize-handle" />
                    <Panel className="promode-panel">
                      <div className="promode-panel__content promode-panel__content-news">
                        <PromodeInstrumentDetail />
                      </div>
                    </Panel>
                  </PanelGroup>

              </Panel>

              <PanelResizeHandle className="promode-resize-handle" />
              <Panel className="promode-panel">
                <PanelGroup direction="vertical">
                  <Panel className="promode-panel" defaultSize={70}>
                    <div className="promode-panel__content chart-panel">
                      <PromodeChart mainInstrumentId={data.mainInstrumentId} />
                    </div>
                  </Panel>
                  <PanelResizeHandle className="promode-resize-handle" />
                  <Panel className="promode-panel">
                    <div className="promode-panel__content promode-panel__content-info-ticker">
                      <InfoTickerTabs />
                    </div>
                  </Panel>
                </PanelGroup>
              </Panel>
            </PanelGroup>
          </div>
        </div>
      </div>
    </div>
  );
}

export const ZoidPromodeModal = zoidComponentCreator(
  PromodeModal,
  {
    dimensions: {
      width: "100%",
      height: "100%"
    },
    template: {
      name: "dynamic",
      styles: {
        position: "fixed",
        bottom: "0px",
        "right": "0px",
        "z-index": "200001"
      },
      backdropBgColor: "rgba(0, 0, 0, 0.3)"
    },
    props: {
      onCloseWindow: {
        type: "function",
        required: false
      },
      onLogout: {
        type: "function",
        required: false
      },
      data: {
        type: "object",
        required: true
      }
    }
  }
);
